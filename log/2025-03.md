## 2025.03.01 周六

# 第一周

## 2025.03.03 周一

1. 轮训 letest 的时候带上过期时间 ok

2. 飞书消息模板化，增加可读性 ok
3. 工作流里面音频上传控制上传文件的格式为限制 音频格式 mp3、wav、m4a，同时拖拽的时候增加提示文件 ok
4. 官网增加 "智能体组织架构" ok

## 2025.03.04 周二

1. 官网跳转到 AIPGPT 链接错误 ok
2. pic--> image 上传图片大小限制在 50M ok
3. 新增 IP 人设 Agent ok
4. 知识库去掉 连接方式 创建知识库 ok
5. 音频变速不重新请求工作流数据 ok

6. 生成的文案超过 1500 字，生成音频是做提示并不让生成 ok

## 2025.03.05 周三

1. 首页粘贴进去的东西，全部转成 txt 内容格式（待定）
2. 首页增加语言选择 ok
3. 工作流实现成虚拟列表 (进行中... 在分支：tw_20250305_1)
4. 口播稿字数限制，合成音频的时候判断 ok

## 2025.03.06 周四

1. 工作流虚拟列表(进行中。。。)
2. manus 对接分享页面 流式 加载功能分析 ok
3. 其他

## 2025.03.07 周五

1. 根据不同语言设置不同的字数限制 ok
2. 专属模型时间选项调整、增加价格信息 ok
3. 其他等

# 第二周

## 2025.03.10 周一

1. 工作流虚拟列表
   1. 加载到文案润色断开问题
2. 图片虚拟人：（2）口播与访谈风格非同是存在；（3）增加案例图片：图片、超虚拟人都有； ok
3. 生成的音频支持查看使用的是哪个音频模型
4. 官网修改成 AI 主管、21 位员工 ok
5. 官网修改 修改视频、替换备案号添加链接 ok

## 2025.03.12 周三

1. 新创建账号没有 IP 人设的时候，创建 IP 人设保存没有及时更新
2. 首页 IP 设置开发
3. 官网视频压缩 ok
4. 修改私有模型 模型创建成功的界面

## 2025.03.13 周四

1. AIPGPT 增加协议 ok
2. 官网协议去掉芯后 ok
3. 洗稿 Agent 增加 think 展示
4. 首页设置、音频类型 ok
5. 首页设置语言字段名称不对 ok

## 2025.03.14 周五

1. 设置风格和语言、风格拼凑前面类型内容 ok
2. 知识库-新建抖音账号 删除没反应（接口问题）
3. 首页增加知识库专家 按钮 ok
4. 官网新增账号激活界面，url： key
5. 私有模型里面，上传的视频模型没有 时长，之后数据接口返回视频时长
6. 工作流 重新润色之后字体数量恢复默认 270 个字

# 第三周

## 2025.03.17 周一

1. 官网专家图修改，以及数量修改 ok
2. ~~ 登陆 token 很快失效问题~~
3. 激活码问题 ok
4. 去掉图片生成视频的价格 ok
5. 修改专家头像与专家图保持一致 ok
6. 修改洗稿专家模板 ok
7. 其他 ok

## 2025.03.18 周二

1. 增加 数据图谱按钮 功能
2. 右侧 lastet 接口修改通过 get 请求数据 ok
3. 修改密码对接接口
4. 虚拟形象 去掉二维码增加说明 ok
5. 核销码 核销按钮 增加 loading 状态，防止重复点击 ok
6. check_add 修改成 get 请求接口 ok

## 2025.03.19 周三

## 2025.03.20 周四

1. 通过使用全局使用 map 缓存方式 控制断开长连接
2. 修改密码 上线
3. 当没有权限的时候，直接退出然后到登录界面
4. 选择字数之后，联网会变成 270 默认字数
5. 多语言增加到 8 中
6. 替换私有模型-图片生成视频-案例替换

## 2025.03.21 周五

1. 处理工作流润色文案断联，将工作流里面的 AbortController 设置 zustand 全局使用数组，进行消费 ok
2. 首页设置 根据不同的账号 持久缓存 设置数据 ok
3. 私有模型 增加 关于价格说明 ok

# 第四周

## 2025.03.24 周一

1. agent 显示 json 代码的问题 ok
2. 退出登录 调用退出登录接口
3. 修改密码 上正式环境

## 2025.03.25 周四

1. 音频、视频 工作流弹框
2. 知识图谱
3. 官网修改文案 ok
4. 工作流获取音频时间
5. 工作流点数不足提示报错提醒

## 2025.03.31 周一

1. 官网 AI 员工图片替换 ok
2. 切换账号 点数提示错误 ok
3. 没有点数点击继续报错提示 ok
4. AIPGPT 工作流出现代码
5. 客户放大缩小后 不跟随显示
6. 音频为 0 的时候，选择的音频类型 没有 ---
7. 增加 fengshen 配置

## 2025.03.28 周二

1. ~~开发联系我们~~
2. ~~讨论前端构建环境配置问题~~
