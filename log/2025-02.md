## 2025.02.08 周六

# 第二周

## 2025.02.10 周一

- 侧边栏登录、登出头像用户切换问题 ok
- V3 接口去掉自己添加的输入内容 ok
- 口播稿修改和工作流同步 ok
- 切换账号跳转首页 ok
- 对接 chack_add 接口切换功能
- 选题数据格式对接 ok
- 增加按钮快捷生成音频按钮 ok
- 素材 ok
- 有数据专家之后内容居中问题 ok

## 2025.02.11 周二

- 视频加载封面 没有封面数据 ok
- 按钮重新布局: 链接洗稿、润色文案、直出音频、默认情况、单出选题 ok
- 密码错误 提示 ok

- 等待 continue 继续
- 等待 check_add 链接
- 素材 图片在不了 去掉、去掉文章、图片瀑布流批量下载
- 热点 做成努力为你搜索中... 不是灰色状态，
- 热点 全量弹框形式/抽屉形式 分类展示， title 描述增加

## 2025.02.12 周三

- 热点 做成努力为你搜索中... 不是灰色状态 ok
- 选题增加描述 ok
- quest task 轮询 ok
- 素材选择
- 落地页面 增加编译环境判断 basePath

## 2025.02.13 周四

1. 知识库修改标题
2. 素材缓存
3. 音频数据解析失败
4. 素材错误图片，htps 无法访问 http ok

```js
/**
 * 关于 加载图片 403的情况 ,  前端防盗链机制
 * 1. <meta name="referrer" content="no-referrer" />
 * 2. <img src="http://example.com/image.png" referrerPolicy="no-referrer" alt="描述信息" />
 */
```

## 2025.02.17

1. 音频加切换箭头 ok
2. 工作流视频列 不可以预览
3. 音频模型 更多按钮变大一些 ok
4. 上传音频弹框缓存问题 ok
5. 重新生成视频，没有历史拼接 ok
6. 关于润色文案 处理掉 thibking 的问题 ok
7. 上传音频成功之后，在此上传弹框还存在上次的记录 ok
8. 音频更多按钮需要放大更加明显 ok

## 2025.02.18 周二

1. 官网英文 网站去掉价格 ok
2. macOS 电脑重新配置 ok
3. 工作流点击按钮延迟问题 ok
4. 没有润色文案的问题（润色文案出不来，换成 r1 模型） ok
5. 声音模型删除之后，出现了展示音频数量和对不上的问题。
6. 口播高修改 里面增加 按钮
7. 上传音频 工作流流程不对（在首页新增按钮，直接实现直接上传音频文件）
8. 关首页热点太少之后要做程全热点分类
9. 快速切换点继续，出现数据串的现象

## 2025.02.19 周三

1. 修改文案增加生成音频按钮、首页增加直接上传音频文件 ok
2. 音频模型删除之后，显示的音频和对应的数量对不上 ok
3. 侧边栏右侧 工作流切换之后，显示高亮模式 ok
4. 润色 think 内容，合成音频的时候去掉 think 内容 ok
5. 上传之后视频弹框没有清缓存 ok

## 2025.02.20 周四

1. 工作流里面选择音频模型 通过 clone_name & back_clone_name 过滤一下 ok
2. 选题全推荐理由全展开，并且增加“推荐理由：” 字符 ok

## 2025.02.21  周五

1. 口播稿 主动合成音频文案错别字
2. 切换 IP 一直有报错信息
3. 创建工作流之后，立即切换 IP，导致数据串了
4. 重新生成视频 生成 agent 流程不对

# 第三周

## 2025.02.25 周二

1. 首页 prompt 按钮增加头像 ok
2. 首页 prompt 按钮功能点击文案修改 ok
3. 知识库 文案调整、以及没有知识内容图片替换 ok
4. 对标账号 列多的情况下不能显示全部 ok
5. IP 人设 顶部增加内容 ok
6. 到期提示文案修改、点数显示按照点数不按照时间显示，以及头部调整 ok

## 2025.02.26 周三

1. 知识库 我的增加 icon ok
2. 工作流上传音频限制大小 10M ok
3. 私有模型 页面开发 ok
4. 其他小问题解决和讨论 ok

## 2025.2.27 周四

1. continue 接口不传 doc_langth 字段 ok
2. 点数通过 last 接口轮训更新，实时更新到 currentIpUser ok
3. 解决在 safari 浏览器工作流 右上角不显示展开图标问题 ok

## 2025.2.28 周五

4. 图片制作专属模型、超虚拟人模型 接口数据 ok
5. 知识库 默认请求错误提示效果统一
6. 音频默认 1.2 倍速 （只有生成音频的时候）
7. 修改密码
8. 页面太多 Agent 导致页面白屏
9. 直接生成音频，然后粘贴口播稿件导致，下面 合成音频按钮 不可点击
