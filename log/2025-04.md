# 第一周

## 2025-04-07 周一

- 首页判断 ok
- 选题复制功能 ok
- 提示信息先判断过期时间，在判断段数不足 ok
- 其他

## 2025-04-08 周二

1. 抖音获取用户列数据，逆向解析前端尝试处理 (实现有问题，就是根据前端 js 代码慢慢找出来如何实现 a_bogus 的值)
2. 处理首页初始化 pid 报错问题 ok
3. 处理作品状态

## 2025-04-09 周三

1. 忘记密码获取验证码不需要协议，修改密码的时候按钮增加 loading 状态防止重复提交，增加借口失败提示（出错是因为 cors 错误） ok
2. 官网抖音链接和 token 替换 ok
3. 知识图谱上测试环境 ok
4. 知识库 agent no

## 2025-04-10 周四

1. 点数小于 500 点提示，信息调整
2. manus

## 2025-04-11 周五

1. manus 基础开发完毕 👌
2. 熟悉 IP 人设出现设置人设不显示问题
3. 官网登录和获取抖音需要收集用户信息

# 第二周

## 2025-04-14 周一

1. manus(按钮调整，视频，默认、开始、重新开始、直接到结果) 👌
2. 解决 IP 人设 chat 接口跨域问题 （解决中，需要做成后端接口形式）

## 2025-04-15 周二

1. 工作流里面润色文案，在没有正文的时候不显示口播稿框框

## 2025-04-16 周三

1. fengshen 官网 (开发中。。。)
2. 封神知识库按钮问题 ok

## 2025-04-17 周四

1. fengshen 官网 ok
2. 其他问题处理

## 2025-04-18 周五

1. fengshen 官网的协议 md ok
   - 出现了问题 isSpace 为 define，这是版本导致，解决方案看 github 的提问
2. 提交信息的接口使用封神的 ok
3. 联系购买 按钮需要协议判断 ok

## 第三周

## 2025-04-21 周一

1. 增加知识库的选择 ok
2. 本地部署 mongdb 调试本地的 nodejs ok
<!-- 3. 阿里云服务器部署 nodejs 调试 -->

## 2025-04-22 周二

1. 知识库调整切图 任务缓存 等 ok
2. 调研 nodejs ok
3. 其他 ok
4. 只要在使用 fastify 功能操作 LLM、调试 prompt ok

## 2025-04-23 周三

1. 整理 nodejs 作为后端，需要承接的具体业务功能
2. 知识库新账户 没有我的文件 列 (测试环境 ok)
3. 封神官网移动端 打包上线 ok
4. 封神替换头像上线 ok
5. 知识库 上正式环境

## 2025-04-24 周四

1. 上线 知识库、对标账号、去掉验证码 👌
2. 知识库修改 为空的时候显示状态、知识库对标账号组多一次判断 ok

## 2025-04-27 周日

1. 修改封神工作流渐变遮罩 ok
2. 知识库-对标账号增加连接 过滤中文 👌
3. 封神官网 头部小 icon (出现了小图标构建之后，访问不到)

## 2025-04-28 周一

1. 解决封神官网 头部小 icon 问题 (51 节后 同步)

```tsx
// 替换图标需要主动引入 才生效
import Favicon from "./favicon.ico";
import { Metadata, Viewport } from "next";

export async function generateMetadata({
  params: { locale },
}: {
  params: { locale: string };
}): Promise<Metadata> {
  //   const t = await getTranslations({ locale, namespace: "metadata" });
  return {
    title: "xxxx",
    description: "xxxx",
    icons: [{ rel: "icon", url: Favicon.src }],
    referrer: "no-referrer",
  };
}
```

2. 封神更新之前 AIPGPT 的修改“知识库、对标账号、去掉验证码 等”，（51 节后 同步）
3. AIPGPT 支付功能（图片制作视频、超虚拟人）
4. app/ 自动跳转到/app/home 页面 ok
5. aipgpt 官方视频替换 ok

## 2025-04-29 周二

- aspect、ratio、inspector

1. 支付功能对接 ok

## 2025-04-30 周三

1. 支付历史订单 按钮位置调整 ok
2. 案例 写到各自的 tab 内部 ok
