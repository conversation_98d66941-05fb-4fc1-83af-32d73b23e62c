import json
import openai

client = openai.OpenAI(
    api_key="sk-rELshXQRllC8tS2945152937BaCd410998F7751e98D8Ab1a",
    base_url="https://aws.storypower.ai/v1",
)


# 定义工具（函数）
def get_current_weather(location: str):
    # 这里可以调用实际的天气 API
    return f"The weather in {location} is sunny."


# 定义第二个工具（函数）：获取时间
def get_current_time(timezone: str):
    # 这里可以调用实际的时间 API
    return f"The current time in {timezone} is 12:00 PM."


# 定义工具的描述
tools = [
    {
        "type": "function",
        "function": {
            "name": "get_current_weather",
            "description": "Get the current weather in a given location",
            "parameters": {
                "type": "object",
                "properties": {
                    "location": {
                        "type": "string",
                        "description": "The city and state, e.g. San Francisco, CA",
                    },
                },
                "required": ["location"],
            },
        },
    },
    {
        "type": "function",
        "function": {
            "name": "get_current_time",
            "description": "Get the current time in a given timezone",
            "parameters": {
                "type": "object",
                "properties": {
                    "timezone": {
                        "type": "string",
                        "description": "The timezone, e.g. America/New_York",
                    },
                },
                "required": ["timezone"],
            },
        },
    },
]

# 调用 OpenAI API
response = client.chat.completions.create(
    # model="gpt-4o",
    model="claude-3-5-sonnet-20241022",
    messages=[
        {
            "role": "user",
            "content": "What's the weather like in San Francisco and what's the current time in New York?",
        }
    ],
    tools=tools,
    tool_choice="auto",  # 让模型自动选择是否调用工具
)


# 处理响应
assistant_message = response.choices[0].message
print(json.dumps(assistant_message, indent=4))
# if assistant_message.tool_calls:
#     # 如果有工具调用
#     for tool_call in assistant_message.tool_calls:
#         if tool_call.function.name == "get_current_weather":
#             # 解析天气函数的参数
#             location = json.loads(tool_call.function.arguments)["location"]
#             # 调用天气函数
#             weather_info = get_current_weather(location)
#             print(weather_info)
#         elif tool_call.function.name == "get_current_time":
#             # 解析时间函数的参数
#             timezone = json.loads(tool_call.function.arguments)["timezone"]
#             # 调用时间函数
#             time_info = get_current_time(timezone)
#             print(time_info)
#     print("==============================")
# else:
#     # 如果没有工具调用，直接输出模型的回复
#     print("==============================")
#     print(assistant_message.content)
