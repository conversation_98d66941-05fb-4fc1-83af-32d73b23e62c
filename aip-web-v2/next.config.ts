import type { NextConfig } from "next";
const { codeInspectorPlugin } = require("code-inspector-plugin");

function envs(e: "development" | "production" | "test" | "uat" | "fengshen") {
  const env = {
    production: {
      MSG_CODE: "production",
      BASE_URL: "https://xinhou-aip-admin.aipgpt.cn",
    },
    development: {
      MSG_CODE: "development",
      BASE_URL: "https://test-xinhou-aip-admin.aipgpt.cn",
    },
    test: {
      MSG_CODE: "test",
      BASE_URL: "https://test-xinhou-aip-admin.aipgpt.cn",
    },
    uat: {
      MSG_CODE: "uat",
      BASE_URL: "https://uat-xinhou-aip-admin.aipgpt.cn",
    },
    fengshen: {
      MSG_CODE: "fengshen",
      BASE_URL: "https://admin.fengshenai.com",
    },
  };
  return env[e] || env["development"];
}
const env_info = envs(process.env.NODE_ENV);

console.log("NODE_ENV:", process.env.NODE_ENV); // 输出：value
console.log("MSG_CODE:", env_info.MSG_CODE); // 在开发环境中输出：value
console.log("BASE_URL:", env_info.BASE_URL); // 在开发环境中输出：value
const nextConfig: NextConfig = {
  // turbo: true,
  reactStrictMode: false,
  typescript: {
    // 忽略 TypeScript 构建错误
    ignoreBuildErrors: true,
  },
  eslint: {
    // 忽略 ESLint 错误
    ignoreDuringBuilds: true,
  },
  /* config options here */
  env: {
    SERVER_BASE_URL: env_info.BASE_URL,
    SERVER_ENV: env_info.MSG_CODE,
  },
  basePath: "/app",
  images: {
    remotePatterns: [
      {
        protocol: "http",
        hostname: "*",
        port: "",
      },
      {
        protocol: "https",
        hostname: "*",
        port: "",
      },
    ],
  },
  webpack(config) {
    config.plugins.push(codeInspectorPlugin({ bundler: "webpack" }));
    // Grab the existing rule that handles SVG imports
    const fileLoaderRule = config.module.rules.find((rule: any) =>
      rule.test?.test?.(".svg")
    );

    config.module.rules.push(
      // Reapply the existing rule, but only for svg imports ending in ?url
      {
        ...fileLoaderRule,
        test: /\.svg$/i,
        resourceQuery: /url/, // *.svg?url
      },
      // Convert all other *.svg imports to React components
      {
        test: /\.svg$/i,
        issuer: fileLoaderRule.issuer,
        resourceQuery: { not: [...fileLoaderRule.resourceQuery.not, /url/] }, // exclude if *.svg?url
        use: ["@svgr/webpack"],
      }
    );

    // Modify the file loader rule to ignore *.svg, since we have it handled now.
    fileLoaderRule.exclude = /\.svg$/i;

    return config;
  },
};

export default nextConfig;
