import { cn } from "@/utils/utils"
import { motion } from "framer-motion"

export const LoadingDots = ({ className }: { className?: string }) => {
  return (
    <div className={cn("space-x-1 flex", className)}>
      <motion.span
        className="w-1.5 h-1.5 bg-current rounded-full"
        animate={{ scale: [1, 1.3, 1] }}
        transition={{
          repeat: Infinity,
          duration: 0.8,
          repeatDelay: 0.2,
          times: [0, 0.5, 1]
        }}
      />
      <motion.span
        className="w-1.5 h-1.5 bg-current rounded-full"
        animate={{ scale: [1, 1.3, 1] }}
        transition={{
          repeat: Infinity,
          duration: 0.8,
          delay: 0.2,
          repeatDelay: 0.2,
          times: [0, 0.5, 1]
        }}
      />
      <motion.span
        className="w-1.5 h-1.5 bg-current rounded-full"
        animate={{ scale: [1, 1.3, 1] }}
        transition={{
          repeat: Infinity,
          duration: 0.8,
          delay: 0.4,
          repeatDelay: 0.2,
          times: [0, 0.5, 1]
        }}
      />
    </div>
  )
} 