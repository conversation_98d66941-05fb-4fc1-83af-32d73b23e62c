.header {
  padding: 40px 40px 0 40px;
  font-size: 32px;
  line-height: 28px;
  font-style: normal;
  font-weight: 600;
}
.body {
  padding: 0 40px;
  margin-top: 20px;
  gap: 20px;
}
.footer {
  padding: 20px 40px 40px 40px;
  justify-content: flex-start;
}
.inputBlock {
  display: flex;
  gap: 10px;
  align-items: baseline;
}
.imageBlock {
  position: relative;
  padding-left: 116px;
  height: 100px;
}
.imageBlockSub {
}

.imageBlockItem {
  position: absolute;
  left: 0;
  top: 0;
}
.imageBlockSub > span {
  display: block;
  color: #a1a1aa;
  font-size: 14px;
  line-height: 20px;
  font-weight: 500;
  margin-bottom: 16px;
}
.subtitle,
.inputLabel {
  color: #a1a1aa;
  font-size: 14px;
  font-weight: 700;
  line-height: 20px;
}
