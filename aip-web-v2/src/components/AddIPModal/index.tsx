import { <PERSON><PERSON><PERSON>, FC, SetStateAction, useState } from "react";
import styles from "./style.module.css";
import {
  Input,
  Textarea,
  Modal,
  ModalContent,
  ModalHeader,
  ModalBody,
  Modal<PERSON>ooter,
  Button,
  Image,
} from "@nextui-org/react";
interface AddIpManageProps {
  isOpen: boolean;
  setOpen: Dispatch<SetStateAction<boolean>>;
  clickEvent?: () => void;
}
const AddIp: FC<AddIpManageProps> = ({ isOpen, setOpen }) => {
  const [website, setWebsite] = useState("");
  const [nickname, setNickname] = useState("");
  const [description, setDescription] = useState("");
  return (
    <>
      <Modal
        onClose={() => setOpen(false)}
        aria-labelledby="modal-title"
        isOpen={isOpen}
        size="2xl"
        classNames={{
          header: styles.header,
          body: styles.body,
          footer: styles.footer,
        }}
      >
        <ModalContent>
          {() => (
            <>
              <ModalHeader>新增IP</ModalHeader>
              <ModalBody>
                <div className={styles.subtitle}>请录入基础信息</div>
                <div className={styles.inputBlock}>
                  <Input
                    isRequired
                    errorMessage="请输入正确的抖音号"
                    label="从你的抖音/小红书获取账号信息"
                    labelPlacement="outside"
                    name="website"
                    placeholder="粘贴个人主页网址（电脑端）"
                    type="url"
                    value={website}
                    onChange={(e) => setWebsite(e.target.value)}
                    classNames={{
                      label: styles.inputLabel,
                    }}
                  />
                  <Button>立即同步</Button>
                </div>
                <div className={styles.imageBlock}>
                  <div className={styles.imageBlockItem}>
                    <Image
                      alt="NextUI hero Image"
                      src="https://nextui.org/images/hero-card-complete.jpeg"
                      width={100}
                      height={100}
                    />
                  </div>
                  <div className={styles.imageBlockSub}>
                    <span>
                      At least 300x300 pixels,max. size 5MB,GIF,JPEG or PNG
                    </span>
                    <Button fullWidth>选择文件</Button>
                  </div>
                </div>
                <Input
                  label="账号昵称"
                  labelPlacement="outside"
                  placeholder="Enter your nickname"
                  type="text"
                  name="nickname"
                  value={nickname}
                  onChange={(e) => setNickname(e.target.value)}
                />
                <Textarea
                  isRequired
                  className="w-full"
                  label="描述"
                  name="description"
                  labelPlacement="outside"
                  placeholder="Enter your description"
                  value={description}
                  onChange={(e) => setDescription(e.target.value)}
                />
              </ModalBody>
              <ModalFooter>
                <Button color="primary">保存</Button>
                <Button>继续创建人设</Button>
              </ModalFooter>
            </>
          )}
        </ModalContent>
      </Modal>
    </>
  );
};

export default AddIp;
