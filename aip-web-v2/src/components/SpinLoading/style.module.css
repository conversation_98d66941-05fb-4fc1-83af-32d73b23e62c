.content {
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  /* background-color: rgba(255,255,255,0.2); */
  background-color: transparent;
  --color: rgba(255, 255, 255, 0.9);
  --timer: 1s;
}
.layerContent {
  position: fixed;
  z-index: 1000000;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: rgba(0, 0, 0, 0.3);
  --color: rgba(255, 255, 255, 0.9);
  --timer: 1s;
}
.blockMain {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  border-right: none;
  animation: animations var(--timer) infinite linear;
  margin-right: 8px;
  /* width: 30px;
    height: 30px;
    background-color: var(--color);
    animation: animations var(--timer) infinite ease-in-out; */
}

@keyframes animations {
  0% {
    transform: rotateZ(0deg);
  }
  100% {
    transform: rotateZ(360deg);
  }
}
