import React from "react";
import styles from "./style.module.css";
import { LoadingOutlined } from "@ant-design/icons";

interface TProps {
  loading?: boolean;
  children?: React.ReactNode;
}
const SpinLoading = (props: TProps) => {
  const { loading, children } = props;
  if (loading) {
    return (
      <div className={styles.content}>
        <div className={styles.blockMain}>
          <LoadingOutlined />
        </div>
        <span>加载中...</span>
      </div>
    );
  } else {
    return <>{children}</>;
  }
};

interface LayerTPtops {
  loadingTxt?: string;
}
export const Layers: React.FC<LayerTPtops> = ({ loadingTxt }) => {
  return (
    <div className={styles.layerContent}>
      <div className={styles.blockMain}>
        <LoadingOutlined />
      </div>
      <span>{loadingTxt || "加载中..."} </span>
    </div>
  );
};
export default SpinLoading;
