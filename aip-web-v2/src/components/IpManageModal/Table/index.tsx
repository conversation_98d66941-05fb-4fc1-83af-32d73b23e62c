import {
  Table,
  TableHeader,
  TableBody,
  TableColumn,
  TableRow,
  TableCell,
  Avatar,
  Link,
  Popover,
  PopoverTrigger,
  PopoverContent,
  Button,
} from "@nextui-org/react";
import { Clock9 } from "lucide-react";
import IconSvg from "@/assets/svg";
import styles from "./style.module.css";
import { useUserInfoStore } from "@/store/useUserInfo";
import { updateUserInfoApi } from "@/service/fetch/user";
import useUser from "@/hooks/useUserinfo";
const { SideNavSDSvg } = IconSvg;

const columns = [
  { name: "NAME", uid: "name" },
  { name: "ROL<PERSON>", uid: "role" },
  { name: "STATUS", uid: "status" },
  { name: "ACTIONS", uid: "actions" },
];

const IpManageTable = () => {
  const { currentIpList, userInfo } = useUserInfoStore();
  const { GetUserInfo } = useUser();

  const RoleElement = (item: any) => {
    return (
      <div className={styles.tableCell}>
        <Avatar
          size="sm"
          src="https://i.pravatar.cc/150?u=a04258a2462d826712d"
        />
        <span className={styles.txt}>{item.ip_name}</span>
      </div>
    );
  };

  const NumberElement = (item: any) => (
    <div className={styles.tableCell}>
      <SideNavSDSvg />
      <span className={styles.cellNumber}>{item.remain_point}</span>
    </div>
  );
  const TimeElement = (item: any) => {
    const expireDate = new Date(item.expire_time).getTime();
    const now = Date.now();
    const diffDays = expireDate
      ? String(Math.ceil((expireDate - now) / (1000 * 60 * 60 * 24)) + "天")
      : "未开始计时";

    return (
      <div className={styles.tableCell}>
        <Clock9 className="size-4 text-[#29FFE6]" />
        <span className={styles.cellNumber}>{diffDays}</span>
      </div>
    );
  };

  const OperateElement = (item: any) => {
    return (
      <div className={styles.tableCell}>
        <Popover placement="top">
          <PopoverTrigger>
            <Link
              color="primary"
              href="#"
              isDisabled={userInfo.remain_point <= 0}
              // isDisabled={!isRemainPoint}
            >
              分配
            </Link>
          </PopoverTrigger>
          <PopoverContent>
            <div className="px-4 py-3">
              <div className="text-small font-bold">确认分配点数？</div>
              <div className="mt-2 flex gap-2">
                <Button color="danger" size="sm" variant="light">
                  取消
                </Button>
                <Button
                  color="primary"
                  size="sm"
                  onPress={() => {
                    updateUserInfoApi({ pid: item.id, points: 60 });
                    GetUserInfo();
                  }}
                >
                  确定
                </Button>
              </div>
            </div>
          </PopoverContent>
        </Popover>
      </div>
    );
  };
  return (
    <div>
      <Table 
        hideHeader 
        removeWrapper
        classNames={{
          base: "overflow-visible",
          // table: "min-h-[200px]",
          wrapper: "max-h-[400px] overflow-y-auto"
        }}
      >
        <TableHeader columns={columns}>
          {(column: any) => (
            <TableColumn
              key={column.uid}
              align={column.uid === "actions" ? "center" : "start"}
            >
              {column.name}
            </TableColumn>
          )}
        </TableHeader>
        <TableBody items={currentIpList}>
          {(item: any) => (
            <TableRow key={item.id} className={styles.tableRow}>
              {(columnKey: any) => {
                return (
                  <TableCell>
                    {columnKey === "name" && RoleElement(item)}
                    {columnKey === "role" && NumberElement(item)}
                    {columnKey === "status" && TimeElement(item)}
                    {columnKey === "actions" && OperateElement(item)}
                  </TableCell>
                );
              }}
            </TableRow>
          )}
        </TableBody>
      </Table>
    </div>
  );
};
export default IpManageTable;
