import { Crown } from "lucide-react";
import IconSvg from "@/assets/svg";
import styles from "./style.module.css";
import { useUserInfoStore } from "@/store/useUserInfo";
const { SideNavSDSvg } = IconSvg;
const IpManageNote = () => {
  const { currentIpUser, currentIpList, userInfo } = useUserInfoStore();
  return (
    <div className="mb-[20px]">
      <div className="text-[#FFF] font-[Inter] text-[32px] font-semibold leading-[28px] tracking-[0px] mb-[20px]">
        IP管理
      </div>
      <div className={styles.message}>
        <Crown className="size-4 text-[#A1A1AA] mr-[2px]" />
        <span className={styles.label}>当前团队版</span>
        <SideNavSDSvg className="size-[14px] ml-3 mr-1" />
        <span className={styles.labelNumber}>{userInfo.remain_point}</span>
        <span className={styles.label}>点剩余，还可分配 </span>
        <span className={styles.labelNumber}>{userInfo.remain_point / 60}</span>
        <span className={styles.label}>次，每次分配增加30天时长</span>
      </div>
    </div>
  );
};

export default IpManageNote;
