"use client";
import { createPortal } from "react-dom";
import styles from "./style.module.css";
import { useEffect, useState } from "react";

interface PullTabProps {
    onClick?: (e: React.MouseEvent) => void;
    isModalOpen: boolean;
    onHover?: (e: React.MouseEvent) => void;
}

const PullTab: React.FC<PullTabProps> = ({ onClick, isModalOpen, onHover }) => {
    const [mounted, setMounted] = useState(false);

    useEffect(() => {
        setMounted(true);
        return () => setMounted(false);
    }, []);

    const handleClick = (e: React.MouseEvent) => {
        e.preventDefault();
        e.stopPropagation();
        onClick?.(e);
    };

    if (!mounted || !isModalOpen) return null;

    return createPortal(
        <div
            className={styles.drawerPullTab}
            onClick={handleClick}
        >
            <div
                className={styles.drawerPullTabIcon}
            >
                {'<'}
            </div>
        </div>,
        document.body
    );
};

export default PullTab; 