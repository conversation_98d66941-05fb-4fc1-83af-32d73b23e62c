"use client";
import { App, Modal } from "antd";
import IpManageNote from "./Note";
import { But<PERSON> } from "@nextui-org/react";
import IpManageTable from "./Table";
import { Dispatch, memo, SetStateAction, useRef, useState } from "react";
import ChatDrawer from "./ChatDrawer";
import PullTab from "./PullTab";
import useUser from "@/hooks/useUserinfo";
import { useUserInfoStore } from "@/store/useUserInfo";

interface IpManageProps {
  isOpen: boolean;
  setOpen: Dispatch<SetStateAction<boolean>>;
}

const IpManage: React.FC<IpManageProps> = memo(({ isOpen, setOpen }) => {
  const [showChat, setShowChat] = useState(false);
  const [drawerActivated, setDrawerActivated] = useState(false);

  const { currentIpUser, currentIpList, userInfo } = useUserInfoStore();

  const handleNewIP = () => {
    setShowChat(true);
    setDrawerActivated(true);
  };

  const handleDrawerClose = () => {
    setShowChat(false);
  };

  const handleModalClose = () => {
    setOpen(false);
    setShowChat(false);
    setDrawerActivated(false);
  };



  return (
    <>
      <Modal
        onCancel={handleModalClose}
        open={isOpen}
        classNames={{
          content:
            "w-[640px] h-[504px] min-w-[327px] !border !border-[rgba(229,231,235,0.04)] !rounded-xl !bg-[#18181B] !p-[40px]",
        }}
        footer={null}
      >
        <IpManageNote />
        <div className="mb-[20px]">
          <Button color="primary" onClick={handleNewIP} disabled={userInfo.remain_point <= 0}>
            新增IP
          </Button>
        </div>
        <IpManageTable />
      </Modal>

      {isOpen && drawerActivated && !showChat && (
        <PullTab
          isModalOpen={isOpen}
          onClick={() => {
            setShowChat(true);
          }}
        />
      )}

      <ChatDrawer isOpen={showChat} onClose={handleDrawerClose} />
    </>
  );
});

IpManage.displayName = "IpManage";

export default IpManage;
