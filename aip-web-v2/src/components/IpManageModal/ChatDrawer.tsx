"use client";
import { App, Drawer } from "antd";
import styles from "./style.module.css";
import { useChat } from 'ai/react'
import { useState, useEffect, useRef } from "react";
import { cn } from "@/utils/utils"
import { LoadingDots } from "@/components/commonUI/loading-dots"
import { createIpApi } from "@/service/fetch/ip";
import { useUserInfoStore } from "@/store/useUserInfo";
import { saveIpPromptApi, updateIpPromptApi } from "@/service/fetch/ip_prompt";
import { updateUserInfoApi } from "@/service/fetch/user";
import useUser from "@/hooks/useUserinfo";
import { Avatar } from "@nextui-org/react";
import Image from "next/image";
import { useFullscreen } from "ahooks";
import ReactMarkdown from "react-markdown";
import remarkGfm from "remark-gfm";
import rehypeRaw from "rehype-raw";
import rehypeSanitize from "rehype-sanitize";

interface ChatDrawerProps {
    isOpen: boolean;
    onClose: () => void;
}
import ImageFile from "@/assets/images";

const ChatDrawer: React.FC<ChatDrawerProps> = ({ isOpen, onClose }) => {
    const mindMapRef = useRef<any>(null)
    const { AgentPic_5 } = ImageFile;
    const isInitializedRef = useRef(false)
    const [jsonInfo, setJsonInfo] = useState("")
    const { userInfo } = useUserInfoStore()
    const { GetUserInfo } = useUser()
    const { modal } = App.useApp();
    const mindMapContainerInDrawerRef = useRef<HTMLDivElement>(null);
    const [isFullscreen, { toggleFullscreen }] =
        useFullscreen(mindMapContainerInDrawerRef);

    // 监听容器大小变化
    useEffect(() => {
        const element = mindMapContainerInDrawerRef.current;
        if (!element) return;

        const resizeObserver = new ResizeObserver((entries) => {
            for (const entry of entries) {
                const { width, height } = entry.contentRect;
                console.log('Container size changed:', { width, height });
                if (mindMapRef.current) {
                    mindMapRef.current.resize();
                }
            }
        });

        resizeObserver.observe(element);
        return () => resizeObserver.disconnect();
    }, []);

    const handleExport = async () => {
        if (mindMapRef.current) {
            try {
                const blob = await mindMapRef.current.export('png', {
                    backgroundColor: '#1F1431',
                    paddingX: 20,
                    paddingY: 20
                })
                const url = URL.createObjectURL(blob)
                const a = document.createElement('a')
                a.href = url
                a.download = `${userInfo?.user_name || 'mindmap'}_export.png`
                a.click()
                URL.revokeObjectURL(url)
            } catch (error) {
                console.error('导出失败:', error)
            }
        }
    };

    const { messages, setMessages, input, handleInputChange, handleSubmit, append, isLoading } = useChat({
        api: '/app/api/chat',
        maxSteps: 5,
        async onToolCall({ toolCall }) {
            if (toolCall.toolName === 'updateMapInfo') {
                const args = toolCall.args as { jsonInfo?: string };
                const parsedInfo = JSON.parse(args.jsonInfo || '{}')
                console.log("已调用", parsedInfo)
                setJsonInfo(args.jsonInfo || '')
                return "更新思维导图成功"
            }
        }
    });

    // 初始化思维导图
    useEffect(() => {
        if (isOpen && !mindMapRef.current && !isInitializedRef.current) {
            (async () => {
                const MindMapImport = await import("simple-mind-map");
                const ThemeImport = await import("simple-mind-map-plugin-themes");
                const RainbowLinesImport = await import(
                    // @ts-ignore
                    "simple-mind-map/src/plugins/RainbowLines.js"
                );
                const WatermarkImport = await import(
                    // @ts-ignore
                    "simple-mind-map/src/plugins/Watermark.js"
                );
                const ExportImport = await import(
                    // @ts-ignore
                    "simple-mind-map/src/plugins/Export.js"
                );
                const MindMap = MindMapImport.default;
                const Watermark = WatermarkImport.default;
                const Export = ExportImport.default;
                MindMap.usePlugin(Watermark);
                MindMap.usePlugin(Export);
                const RainbowLines = RainbowLinesImport.default;
                MindMap.usePlugin(RainbowLines);
                const Theme = ThemeImport.default;
                Theme.init(MindMap);
                let initialData = {
                    data: {
                        text: "等待分析中",
                        uid: "root-1",
                        expand: true,
                        isRoot: true,
                    },
                    children: [],
                };
                mindMapRef.current = new MindMap({
                    el: document.getElementById("mindMapContainerInDrawer"),
                    data: initialData,
                    fit: true,
                    mousewheelAction: "zoom",
                    theme: "dark3",
                    themeConfig: {
                        backgroundColor: "#222",
                    },
                    //水印
                    watermarkConfig: {
                        text: "AIPGPT",
                        lineSpacing: 150,
                        textSpacing: 150,
                        angle: 30,
                        textStyle: {
                            color: "#999",
                            opacity: 0.02,
                            fontSize: 48,
                            // @ts-ignore
                            fontWeight: 1000,
                        },
                    },
                    rainbowLinesConfig: {
                        open: true,
                        colorsList: [
                            "rgb(255, 229, 142)",
                            "rgb(254, 158, 41)",
                            "rgb(248, 119, 44)",
                            "rgb(232, 82, 80)",
                            "rgb(182, 66, 98)",
                            "rgb(99, 54, 99)",
                            "rgb(65, 40, 82)",
                        ],
                    },
                });
                setTimeout(() => {
                    mindMapRef.current.resize();
                }, 500);
                setMessages([{
                    id: Date.now().toString(), // 添加唯一id
                    content: "您好，我是您的IP打造顾问！您可以直接分享您想要打造的人设信息（可以是文字描述或与客服的访谈稿），或者我们可以进行一次一对一访谈，深入了解您的需求，为您量身定制最适合的IP方案。",
                    role: "assistant",
                }]);

                // 标记为已初始化
                isInitializedRef.current = true;
            })();
        }
    }, [isOpen])

    // 更新思维导图数据
    useEffect(() => {
        if (mindMapRef.current && jsonInfo) {
            const newData = JSON.parse(jsonInfo)
            mindMapRef.current.updateData(newData);
            mindMapRef.current.render();
        }
    }, [jsonInfo])

    const handleSaveIP = async (ipName: string) => {
        try {
            const response = await createIpApi({
                ip_name: ipName,
                uid: String(userInfo.id)
            });
            const ipId = response.data.id;
            // 保存思维导图数据
            await saveIpPromptApi({
                pid: String(ipId),
                json: jsonInfo,
                ip_name: ipName
            });

            await updateUserInfoApi({ pid: String(ipId), points: 60 });
            await GetUserInfo();
            onClose();

            modal.success({
                title: "保存成功",
                content: "IP人设已保存",
            });
        } catch (error) {
            modal.error({
                title: "保存失败",
                content: "保存IP人设时发生错误",
            });
            throw error; // 向上传递错误
        }
    };

    return (
        <Drawer
            open={isOpen}
            destroyOnClose={true}
            onClose={onClose}
            placement="right"
            title="IP人设访谈室"
            mask
            closeIcon={false}
            classNames={{
                content: styles.modelBase,
                header: cn(styles.modelHeader, styles.modelHeaderText),
                body: styles.modelBody
            }}
            width={"full"}
            extra={
                <div className="flex gap-2">
                    <button
                        onClick={() => {
                            modal.confirm({
                                title: "确定要关闭吗？",
                                content: "关闭后，IP人设访谈室不会主动保存",
                                onOk: () => {
                                    onClose();
                                },
                                okText: "确定",
                                cancelText: "取消"
                            })
                        }}
                        className="px-4 py-1.5 rounded-md border border-white/10 text-white/70 hover:text-white hover:bg-white/5 transition-all text-sm"
                    >
                        取消
                    </button>
                    <button
                        onClick={() => {
                            modal.confirm({
                                title: "保存IP人设",
                                content: (
                                    <div className="mt-4">
                                        <input
                                            id="ipNameInput"
                                            className="w-full p-2 rounded-md border border-white/10 
                                                     bg-black/20 text-white/90 text-sm focus:ring-2 focus:ring-indigo-500/30
                                                     placeholder-white/30"
                                            placeholder="请输入IP名称"
                                        />
                                    </div>
                                ),
                                onOk: async () => {
                                    const ipNameInput = document.getElementById('ipNameInput') as HTMLInputElement;
                                    const ipName = ipNameInput?.value;

                                    if (!ipName?.trim()) {
                                        modal.error({
                                            title: "错误",
                                            content: "请输入IP名称"
                                        });
                                        return false;
                                    }

                                    try {
                                        await handleSaveIP(ipName);
                                    } catch (error) {
                                        return false;
                                    }
                                },
                                okText: "确认保存",
                                cancelText: "取消"
                            });
                        }}
                        className="px-4 py-1.5 rounded-md bg-indigo-500/80 text-white hover:bg-indigo-500 transition-all text-sm"
                    >
                        保存并退出
                    </button>
                </div>
            }
        >
            <section className="h-full grid grid-cols-[2.3fr_1fr] overflow-auto">
                {/* 思维导图区域 */}
                <article className="bg-black/20 overflow-hidden h-full min-h-0 border border-white/10 relative">
                    <div
                        id="mindMapContainerInDrawer"
                        ref={mindMapContainerInDrawerRef}
                        className="h-full w-full"
                    ></div>
                    <div className="absolute bottom-4 left-4 flex gap-2">
                        <button
                            onClick={handleExport}
                            className="hover:bg-white/10 text-white/20 hover:text-white p-2 rounded-md transition-all"
                        >
                            <svg
                                xmlns="http://www.w3.org/2000/svg"
                                width="20"
                                height="20"
                                viewBox="0 0 24 24"
                                fill="none"
                                stroke="currentColor"
                                strokeWidth="2"
                            >
                                <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4" />
                                <polyline points="7 10 12 15 17 10" />
                                <line x1="12" y1="15" x2="12" y2="3" />
                            </svg>
                        </button>
                        <button
                            onClick={() => {
                                toggleFullscreen();
                            }}
                            className="hover:bg-white/10 text-white/20 hover:text-white p-2 rounded-md transition-all"
                        >
                            {isFullscreen ? (
                                <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    width="20"
                                    height="20"
                                    viewBox="0 0 24 24"
                                    fill="none"
                                    stroke="currentColor"
                                    strokeWidth="2"
                                >
                                    <path d="M15 3h6v6M9 3H3v6M3 15v6h6M21 15v6h-6" />
                                </svg>
                            ) : (
                                <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    width="20"
                                    height="20"
                                    viewBox="0 0 24 24"
                                    fill="none"
                                    stroke="currentColor"
                                    strokeWidth="2"
                                >
                                    <path d="M3 9V3h6M21 9V3h-6M3 15v6h6M21 15v6h-6" />
                                </svg>
                            )}
                        </button>
                    </div>
                </article>

                {/* 聊天区域 */}
                <article className="grid grid-rows-[1fr_auto] min-h-0 h-full border border-white/10">
                    <div className="min-h-0 overflow-auto p-6 space-y-4">
                        {messages.map((m, index) => {
                            // 如果没有内容或以 <START> 开头，直接跳过
                            if (!m.content || m.content.startsWith("<START>")) {
                                return null;
                            }
                            return (
                                <div
                                    key={m.id || `message-${index}`} // 使用 m.id 或回退到索引
                                    className={`flex items-start gap-3 ${m.role === "user" ? "flex-row-reverse" : ""
                                        }`}
                                >
                                    {m.role === "user" ? (
                                        <Avatar
                                            key={`avatar-${m.id || index}`}
                                            size="md"
                                            className="shrink-0 grow-0"
                                        />
                                    ) : (
                                        <Image
                                            key={`image-${m.id || index}`}
                                            src={AgentPic_5}
                                            alt="avatar"
                                            className="w-8 h-8 rounded-full flex-shrink-0"
                                        />
                                    )}
                                    <div
                                        className={`p-4 rounded-xl ${m.role === "user"
                                            ? "bg-white/5 border border-white/10"
                                            : "bg-black/20 border border-white/10"
                                            } max-w-[85%]`}
                                    >
                                        <div className="text-white/90 prose prose-invert prose-sm max-w-none">
                                            <ReactMarkdown
                                                remarkPlugins={[remarkGfm]}
                                                rehypePlugins={[rehypeRaw, rehypeSanitize]}
                                                className="break-words"
                                            >
                                                {m.content}
                                            </ReactMarkdown>
                                        </div>
                                    </div>
                                </div>
                            );
                        })}

                        {isLoading && (
                            <div className="flex items-start gap-3">
                                <Image
                                    src={AgentPic_5}
                                    alt="AI Avatar"
                                    className="w-8 h-8 rounded-full flex-shrink-0"
                                />
                                <div className="bg-black/20 border border-white/10 p-4 rounded-xl max-w-[85%]">
                                    <div className="flex items-center space-x-2 text-white/90">
                                        <span className="text-sm">思考中</span>
                                        <LoadingDots />
                                    </div>
                                </div>
                            </div>
                        )}
                    </div>

                    <form
                        onSubmit={handleSubmit}
                        className="p-4 border-t border-white/10"
                    >
                        <div className="relative">
                            <input
                                value={input}
                                onChange={handleInputChange}
                                disabled={isLoading}
                                className={cn(
                                    "w-full p-4 rounded-xl bg-black/20",
                                    "border border-white/10",
                                    "text-sm text-white placeholder-white/30",
                                    "focus:ring-2 focus:ring-white/20 focus:border-white/20",
                                    "transition-all duration-200",
                                    isLoading && "opacity-50 cursor-not-allowed"
                                )}
                                placeholder={isLoading ? "AI 正在思考中..." : "输入您的问题..."}
                            />
                            {isLoading && (
                                <div className="absolute right-4 top-1/2 -translate-y-1/2">
                                    <LoadingDots />
                                </div>
                            )}
                        </div>
                    </form>
                </article>
            </section>
        </Drawer>
    );
};

export default ChatDrawer; 