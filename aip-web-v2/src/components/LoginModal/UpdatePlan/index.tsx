import { Button, Form, Input, message, Tooltip } from "antd";
import Agreement from "../Agreement";
import styles from "./style.module.css";
import { X } from "lucide-react";
import { useLoginStore } from "@/store/store";
import { Dispatch, FC, SetStateAction, useState } from "react";
import { PlanType, VerifyDataType } from "@/typing/types";
import { toast } from "sonner";
interface Props {
  updateType: (t: PlanType) => void;
  onChange: (data: VerifyDataType) => void;
  onSubmit: (data: VerifyDataType) => void;
  btnLoading: boolean;
}
const UpdatePlan: FC<Props> = ({
  onChange,
  updateType,
  onSubmit,
  btnLoading,
}) => {
  const [messageApi, contextHolder] = message.useMessage();
  const updateIsLoginOpen = useLoginStore((state) => state.updateIsLoginOpen);
  const [form] = Form.useForm();
  /**
   * 登录事件
   */
  const ResetPasswordEve = () => {
    form.validateFields().then(async (values) => {
      onChange({ new_password: form.getFieldValue("password") });
      onSubmit({ new_password: form.getFieldValue("password") });
    });
  };
  return (
    <>
      {contextHolder}
      <X
        className={styles.login_close_icon}
        onClick={(e) => {
          updateIsLoginOpen(false);
          e.stopPropagation();
          e.preventDefault();
        }}
      />
      <div className={styles.login_form}>
        <div className={styles.login_title}>重置统一登录密码 </div>
        <Form form={form} onFinish={ResetPasswordEve}>
          <Form.Item
            name="password"
            rules={[
              { required: true, message: "请填写密码" },
              {
                pattern: /^.{8,12}$/,
                message: "密码长度在 8-12 位数",
              },
            ]}
          >
            <Input.Password
              placeholder="请输入新密码"
              style={{ width: 310 }}
              size="large"
            />
          </Form.Item>

          <Form.Item
            name="oncePassword"
            rules={[
              {
                required: true,
                message: "请填写确认密码",
              },
              ({ getFieldValue }) => ({
                validator(_, value) {
                  if (!value || getFieldValue("password") === value) {
                    return Promise.resolve();
                  }
                  return Promise.reject(new Error("两次输入的密码不一致"));
                },
              }),
            ]}
          >
            <Input.Password
              placeholder="请确认新密码"
              style={{ width: 310 }}
              size="large"
            />
          </Form.Item>
          <Form.Item style={{ marginBottom: 16 }}>
            <Button
              type="primary"
              size="large"
              style={{ width: 310 }}
              htmlType="submit"
              loading={btnLoading}
            >
              重置密码
            </Button>

            <div className={styles.link}>
              <span
                onClick={() => {
                  updateType("VERIFY");
                  onChange({
                    new_password: "",
                  });
                }}
              >
                返回
              </span>
            </div>
          </Form.Item>
        </Form>
      </div>
    </>
  );
};
export default UpdatePlan;
