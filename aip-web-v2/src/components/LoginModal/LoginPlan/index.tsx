import {
  <PERSON><PERSON>,
  Config<PERSON><PERSON><PERSON>,
  Form,
  Input as AntdInput,
  message,
  theme,
  Tooltip,
} from "antd";
import Agreement from "../Agreement";
import { X } from "lucide-react";
import { PlanType } from "@/typing/types";
import styles from "./style.module.css";
import useUser from "@/hooks/useUserinfo";
import { useLoginStore } from "@/store/store";
import { useRouter } from "next/navigation";
import { loginService } from "@/service/fetchData";
import { FC, useState } from "react";
import { toast } from "sonner";
interface Props {
  updateType: (t: PlanType) => void;
}
const LoginPlan: FC<Props> = ({ updateType }) => {
  const [messageApi, contextHolder] = message.useMessage();
  const { GetUserInfo } = useUser();
  const updateIsLogin = useLoginStore((state) => state.updateIsLogin);
  const updateIsLoginOpen = useLoginStore((state) => state.updateIsLoginOpen);
  const [checked, setChecked] = useState(false);
  const [form] = Form.useForm();
  const router = useRouter();
  /**
   * 登录事件
   */
  const loginEve = () => {
    form.validateFields().then(async (values) => {
      loginService({
        ...values,
      })
        .then(() => {
          GetUserInfo().then(() => {
            updateIsLogin(true);
            updateIsLoginOpen(false);
            router.push("/home");
          });
        })
        .catch((error) => {
          toast.error(error?.msg ?? "账号密码错误", {
            position: "top-center",
            richColors: true,
            duration: 1500,
          });
        });
    });
  };
  return (
    <>
      {contextHolder}
      <X
        className={styles.login_close_icon}
        onClick={(e) => {
          updateIsLoginOpen(false);
          e.stopPropagation();
          e.preventDefault();
        }}
      />
      <div className={styles.login_form}>
        <div className={styles.login_title}>账号密码登录</div>
        <Form form={form} onFinish={loginEve} className="dark">
          <Form.Item
            name="login_name"
            rules={[{ required: true, message: "请输入账号/手机号" }]}
          >
            <AntdInput placeholder="请输入账号/手机号" size="large" required />
          </Form.Item>

          <Form.Item
            name="login_pwd"
            rules={[{ required: true, message: "请输入密码" }]}
          >
            <AntdInput.Password
              placeholder="请输入密码"
              size="large"
              required
            />
          </Form.Item>
          <Form.Item>
            {!checked ? (
              <Tooltip placement="top" title={"请同意用户协议"} arrow={true}>
                <Button
                  type="primary"
                  size="large"
                  style={{ width: 310 }}
                  disabled={true}
                >
                  立即登录
                </Button>
              </Tooltip>
            ) : (
              <Button
                type="primary"
                size="large"
                style={{ width: 310 }}
                htmlType="submit"
              >
                立即登录
              </Button>
            )}
            <div className={styles.link}>
              <span
                onClick={() => {
                  updateType("VERIFY");
                }}
              >
                忘记密码
              </span>
              {/* <span className="text-[rgba(0,0,0,0.25)] px-2">|</span> */}
              <span
                onClick={() => {
                  updateType("ContactUs");
                }}
              >
                联系购买
              </span>
            </div>
          </Form.Item>
        </Form>
      </div>
      <Agreement
        checked={checked}
        onChange={(val: boolean) => {
          setChecked(val);
        }}
      />
    </>
  );
};
export default LoginPlan;
