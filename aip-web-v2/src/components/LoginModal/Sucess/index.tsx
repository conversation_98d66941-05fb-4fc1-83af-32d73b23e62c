import { CheckCircleFilled } from "@ant-design/icons";
import styles from "./style.module.css";
import { Button } from "antd";
import { PlanType } from "@/typing/types";
import { FC } from "react";
interface Props {
  updateType: (t: PlanType) => void;
}

const SucessItem: FC<Props> = ({ updateType }) => {
  return (
    <div className={styles.successPlan}>
      <div>
        <CheckCircleFilled style={{ color: "#52C41A", fontSize: "80px" }} />
      </div>
      <div className={styles.successTitle}>已提交成功</div>
      <div className={styles.successText}>请耐心等待业务专员联系你</div>
      <Button
        type="primary"
        size="large"
        className="mt-[20px] w-[112px]"
        onClick={() => {
          updateType("LOGIN");
        }}
      >
        返回登录
      </Button>
    </div>
  );
};
export default SucessItem;
