"use client";
import { Form, Button, Input, ConfigProvider, theme, message } from "antd";
import LoginPlan from "./LoginPlan";
import ResetPlan from "./ResetPlan";
import UpdatePlan from "./UpdatePlan";
import ContentUs from "./ContactUs";
import SucessItem from "./Sucess";
import { PlanType, VerifyDataType } from "@/typing/types";
import { updatePassword } from "@/service/fetchData";
import Image from "next/image";
import IconSvg from "@/assets/svg";
import ImageFile from "@/assets/images";
import { useEffect, useState } from "react";
import { useLoginStore } from "@/store/store";
import { GetToken } from "@/service/config";
import styles from "./style.module.css";
const { BgGradient } = ImageFile;
const { Logo, LogoText } = IconSvg;

const LIST = [
  "用 AI 做 IP",
  "AI 直出短视频",
  "多 Agent 系统高质量产出",
  "定制声音模型",
  "数字人形象模型",
  "AI 私域分身",
  "IP 专属知识库模型",
];
interface VerifyDataProps {
  mobile: string;
  verify_code: string;
  new_password: string;
}
const LoginModal = () => {
  const isLoginOpen = useLoginStore((state) => state.isLoginOpen);
  const updateIsLogin = useLoginStore((state) => state.updateIsLogin);
  const updateIsLoginOpen = useLoginStore((state) => state.updateIsLoginOpen);
  const [LoginPlanType, setLoginPlanType] = useState<PlanType>("LOGIN");
  const [verifyData, setVerifyData] = useState<VerifyDataProps>({
    mobile: "",
    verify_code: "",
    new_password: "",
  });
  const [messageApi, contextHolder] = message.useMessage();
  const [verifyBtnLoading, setVerifyBtnLoading] = useState(false);
  const updatePasswordEve = (params: VerifyDataProps) => {
    // 控制修改按钮防止重复点击
    setVerifyBtnLoading(true);
    updatePassword(params)
      .then((res) => {
        if (res.code === 200) {
          setLoginPlanType("LOGIN");
          setVerifyData({
            mobile: "",
            verify_code: "",
            new_password: "",
          });
          messageApi.success("密码修改成功，请重新登录");
        } else {
          messageApi.error(res?.msg);
        }
      })
      .catch((error) => {
        messageApi.error(error?.msg ?? "修改密码失败，请稍后再试");
      })
      .finally(() => {
        setVerifyBtnLoading(false);
      });
  };
  useEffect(() => {
    setTimeout(() => {
      if (GetToken()) {
        updateIsLogin(true);
        updateIsLoginOpen(false);
      }
    });
  }, []);
  return (
    <>
      {contextHolder}
      <div
        id="loginModal"
        className={`${styles.login_content} ${
          isLoginOpen ? styles.login_show : ""
        }`}
      >
        <div className={styles.login_layer}></div>
        <div className={styles.login_box}>
          <div className={styles.login_left}>
            <Image
              src={BgGradient}
              alt="login"
              className={styles.login_gradient}
            />
            <div className={styles.login_logo}>
              <Logo />
              <LogoText />
            </div>
            <div className="text-white text-2xl font-medium mt-6">
              我们的创作平台有什么
            </div>
            <ul className="mt-[30px]">
              {LIST.map((item, index) => (
                <li
                  key={index}
                  className="list-disc list-inside text-[#3870FF] mb-[18px]"
                >
                  <span className="text-white text-base font-medium">
                    {item}
                  </span>
                </li>
              ))}
            </ul>
          </div>
          <div className={styles.login_right}>
            <ConfigProvider
              theme={{
                token: {
                  colorBgElevated: "#27272A", // 弹出层背景色
                  colorText: "#fff", // 主文本色
                  controlItemBgHover: "transparent", // hover 背景色
                  // colorBgContainer: "transparent", // 容器背景色
                  colorBorder: "rgb(217, 217, 217)", // 边框色
                  borderRadius: 5, // 圆角
                  // boxShadow: "0 4px 12px rgba(0, 0, 0, 0)", // 阴影
                },
                components: {
                  Checkbox: {
                    colorBgContainer: "#fff",
                  },
                  Button: {
                    boxShadow: "0 0 0 rgba(0,0,0,0)",
                  },
                  Input: {
                    activeShadow: "0 0 0 transparent",
                  },
                },
              }}
            >
              {LoginPlanType === "LOGIN" && (
                <LoginPlan
                  updateType={(t: PlanType) => {
                    setLoginPlanType(t);
                  }}
                />
              )}
              {LoginPlanType === "VERIFY" && (
                <ResetPlan
                  updateType={(t: PlanType) => {
                    setLoginPlanType(t);
                  }}
                  onChange={(data: VerifyDataType) => {
                    setVerifyData({
                      ...verifyData,
                      ...data,
                    });
                  }}
                />
              )}
              {LoginPlanType === "UPDATE" && (
                <UpdatePlan
                  btnLoading={verifyBtnLoading}
                  updateType={(t: PlanType) => {
                    setLoginPlanType(t);
                  }}
                  onSubmit={(data: VerifyDataType) => {
                    updatePasswordEve({
                      ...verifyData,
                      ...data,
                    });
                  }}
                  onChange={(data: VerifyDataType) => {
                    setVerifyData({
                      ...verifyData,
                      ...data,
                    });
                  }}
                />
              )}
              {LoginPlanType === "ContactUs" && (
                <ContentUs
                  updateType={(t: PlanType) => {
                    setLoginPlanType(t);
                  }}
                />
              )}
              {LoginPlanType === "SUCCESS" && (
                <SucessItem
                  updateType={(t: PlanType) => {
                    setLoginPlanType(t);
                  }}
                />
              )}
            </ConfigProvider>
          </div>
        </div>
      </div>
    </>
  );
};
export default LoginModal;
