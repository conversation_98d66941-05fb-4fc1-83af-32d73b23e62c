.login_content {
  position: fixed;
  z-index: 101;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  visibility: hidden;
}

.login_layer {
  position: absolute;
  z-index: 102;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(12px) saturate(1.5);
  opacity: 0;
}
.login_box {
  display: flex;
  position: absolute;
  z-index: 103;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  margin: auto;
  width: 100%;
  max-width: 882px;
  height: 542px;
  border-radius: 8px;
}
.login_left {
  position: relative;
  flex-grow: 0;
  flex-shrink: 0;
  width: 370px;
  height: 100%;
  padding-top: 3.5rem;
  padding-bottom: 3.5rem;
  padding-left: 3.75rem;
  padding-right: 3.75rem;
  background-image: linear-gradient(rgb(20, 20, 20), rgb(10, 10, 10));
  overflow: hidden;
  opacity: 1;
  visibility: hidden;
  transform: translateY(-120px);
}
.login_gradient {
  position: absolute;
  width: 231px;
  height: 234px;
  pointer-events: none;
  top: 17px;
  left: 157px;
}
.login_right {
  flex-grow: 1;
  flex-shrink: 1;
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: #fff;
  opacity: 0;
  transform: translateY(120px);
  visibility: hidden;
}
.login_logo {
  display: flex;
}
/* .login_form {
  flex-grow: 1;
} */
/* .login_footer {
  padding-top: 22px;
  padding-bottom: 22px;
  text-align: center;
  font-size: 0.875rem;
  line-height: 1.25rem;
  color: rgba(0, 0, 0, 0.4);
  background-color: rgba(0, 0, 0, 0.02);
}
.login_footer > span {
  color: rgb(15, 82, 255);
  cursor: pointer;
} */
/* .login_form {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  justify-content: center;
  align-items: center;
  flex-grow: 1;
} */
/* .login_title {
  color: rgba(0, 0, 0, 0.9);
  font-size: 1rem;
  line-height: 1.5rem;
  font-weight: 500;
  margin-bottom: 28px;
} */
.login_content.login_show {
  visibility: visible;
}
.login_show .login_layer {
  visibility: visible;
  opacity: 1;
  transition: opacity 0.1s ease-in-out 0.3s;
}
.login_show .login_right {
  opacity: 1;
  transform: translateY(0px);
  transition: transform 0.2s ease-in-out, opacity 0.2s ease-in-out;
  visibility: visible;
}
.login_show .login_left {
  opacity: 1;
  transform: translateY(0px);
  transition: transform 0.2s ease-in-out, opacity 0.2s ease-in-out;
  visibility: visible;
}
