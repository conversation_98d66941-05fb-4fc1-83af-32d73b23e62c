import { Button, Form, Input, message, Tooltip } from "antd";
import Agreement from "../Agreement";
import styles from "./style.module.css";
import { X } from "lucide-react";
import { useLoginStore } from "@/store/store";
import { FC, useRef, useState } from "react";
import { PlanType, VerifyDataType } from "@/typing/types";
import { getPhoneVerifyCode, precontractCreate } from "@/service/fetchData";
import { toast } from "sonner";
interface Props {
  updateType: (t: PlanType) => void;
}

const ContactUs: FC<Props> = ({ updateType }) => {
  const [btnLoading, setBtnLoading] = useState(false);
  const [intervalTime, setIntervalTime] = useState(0);
  const [codeState, setCodeState] = useState(false);
  const [messageApi, contextHolder] = message.useMessage();
  const updateIsLoginOpen = useLoginStore((state) => state.updateIsLoginOpen);
  const [checked, setChecked] = useState(false);
  const [form] = Form.useForm();
  const intervalRef = useRef<any>(null);
  const intervalNumRef = useRef(60);

  const getCodeEvent = () => {
    form.validateFields(["mobile"]).then(async (values) => {
      // 按钮不可重复点击
      setCodeState(true);
      // 获取验证码
      const mobileStr = form.getFieldValue("mobile");

      // 获取验证码
      getPhoneVerifyCode(mobileStr)
        .then((result) => {
          if (result.code === 200) {
            // 倒计时
            setIntervalTime(60);
            intervalRef.current = setInterval(() => {
              if (intervalNumRef.current <= 0 && intervalRef.current) {
                clearInterval(intervalRef.current);
                setCodeState(false);
                return;
              }
              intervalNumRef.current = intervalNumRef.current - 1;
              setIntervalTime(intervalNumRef.current);
            }, 1000);
          } else {
            clearInterval(intervalRef.current);
            toast.error(result.msg ?? "获取验证码失败", {
              position: "top-center",
              richColors: true,
              duration: 1500,
            });
            setCodeState(false);
          }
          console.log("result:", result);
        })
        .catch((error) => {
          clearInterval(intervalRef.current);
          toast.error(error?.msg ?? "获取验证码失败", {
            position: "top-center",
            richColors: true,
            duration: 1500,
          });
          setCodeState(false);
        });
    });
  };
  /**
   * 下一步
   */
  const loginEvent = () => {
    form.validateFields().then(async (values) => {
      setBtnLoading(true);
      precontractCreate({ ...values })
        .then((res) => {
          if (res.code === 200) {
            updateType("SUCCESS");
          } else {
            toast.error(res?.msg ?? "获取验证码失败", {
              position: "top-center",
              richColors: true,
              duration: 1500,
              description: res?.data?.message,
            });
            setCodeState(false);
          }
        })
        .finally(() => {
          setBtnLoading(false);
        });
    });
  };
  return (
    <>
      {contextHolder}
      <X
        className={styles.login_close_icon}
        onClick={(e) => {
          updateIsLoginOpen(false);
          e.stopPropagation();
          e.preventDefault();
        }}
      />
      <div className={styles.login_form}>
        <div className={styles.login_title}>联系购买 </div>
        <Form
          form={form}
          onFinish={loginEvent}
          style={{ width: 310 }}
          layout="vertical"
        >
          <Form.Item
            label={<span className="text-[rgba(0,0,0,0.9)]">手机号</span>}
            style={{ marginBottom: 16 }}
            name="mobile"
            rules={[
              { required: true, message: "请输入手机号" },
              {
                pattern: /^1[3-9]\d{9}$/,
                message: "手机号码格式不正确!",
              },
            ]}
          >
            <Input
              placeholder="请输入手机号"
              style={{ width: "100%" }}
              size="large"
              required
            />
          </Form.Item>
          {/* <Form.Item style={{ marginBottom: 16 }}>
            <div className={styles.code}>
              <Form.Item
                noStyle
                name="verify_code"
                rules={[
                  { required: true, message: "请输入验证码" },
                  {
                    pattern: /^\d{4}$/,
                    message: "请输入正确的验证码",
                  },
                ]}
                style={{ width: 174, flexShrink: 0, flexGrow: 0 }}
              >
                <Input
                  placeholder="请输入短信验证码"
                  width="100%"
                  size="large"
                />
              </Form.Item>
              <Button
                type="primary"
                onClick={() => {
                  getCodeEvent();
                }}
                style={{
                  height: 38,
                  boxShadow: "0 0 0 rgba(0,0,0,0)",
                  width: 120,
                  flexGrow: 0,
                  flexShrink: 0,
                }}
                disabled={codeState}
              >
                {intervalTime > 0 ? `${intervalTime}秒` : "发送验证码"}
              </Button>
            </div>
          </Form.Item> */}
          <Form.Item style={{ marginBottom: 0 }} noStyle>
            <div className={styles.subtitle}>
              提供更多信息，我们为您提前规划方案
            </div>
          </Form.Item>
          <Form.Item style={{ marginBottom: 16 }} name="business_name">
            <Input
              placeholder="请输入公司名称"
              style={{ width: "100%" }}
              size="large"
              required
            />
          </Form.Item>
          <Form.Item style={{ marginBottom: 16 }}>
            <div className="w-full flex justify-between">
              <Form.Item
                style={{
                  display: "inline-block",
                  width: "calc(56% - 8px)",
                  marginBottom: 0,
                }}
              >
                <Input placeholder="请输入联系人姓名" size="large" />
              </Form.Item>
              <Form.Item
                name="city"
                style={{
                  display: "inline-block",
                  width: "calc(44% - 8px)",
                  marginBottom: 0,
                }}
              >
                <Input placeholder="所在城市" size="large" />
              </Form.Item>
            </div>
          </Form.Item>
          <Form.Item style={{ marginBottom: 0 }}>
            <Button
              type="primary"
              size="large"
              style={{ width: "100%" }}
              onClick={loginEvent}
              loading={btnLoading}
            >
              完成提交
            </Button>
            {/* {!checked ? (
              <Button
                type="primary"
                size="large"
                style={{ width: "100%" }}
                disabled={true}
              >
                完成提交
              </Button>
            ) : (
              // <Tooltip placement="top" title={"请同意用户协议"} arrow={true}>
              //   {" "}
              //   <Button
              //     type="primary"
              //     size="large"
              //     style={{ width: "100%" }}
              //     disabled={true}
              //   >
              //     完成提交
              //   </Button>
              // </Tooltip>
              <Button
                type="primary"
                size="large"
                style={{ width: "100%" }}
                onClick={loginEvent}
                loading={btnLoading}
              >
                完成提交
              </Button>
            )} */}

            <div className={styles.link}>
              <span
                onClick={() => {
                  updateType("LOGIN");
                }}
              >
                返回登录
              </span>
            </div>
          </Form.Item>
        </Form>
      </div>
      {/* <Agreement
        checked={checked}
        onChange={(val: boolean) => {
          setChecked(val);
        }}
      /> */}
    </>
  );
};
export default ContactUs;
