import { FC } from "react";
import styles from "./style.module.css";
import Image from "next/image";
interface AvatarListProps {
  avatar: any[];
  label?: string;
}
const AvatarList: FC<AvatarListProps> = ({ avatar, label }) => {
  return (
    <div className={styles.promptBtnContent}>
      {avatar.map((av, index) => (
        <div key={`av_${index}`} className={styles.avatarGroup}>
          <Image src={av} alt="" height={20} width={20} />
        </div>
      ))}
      {label && <span className="ml-1">{label}</span>}
    </div>
  );
};
export default AvatarList;
