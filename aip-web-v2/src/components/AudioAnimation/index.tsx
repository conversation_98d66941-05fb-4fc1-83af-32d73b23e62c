import { cn } from "@/utils";
import {
  Dispatch,
  FC,
  SetStateAction,
  useEffect,
  useRef,
  useState,
} from "react";

interface IProps {
  className?: string;
  audioSrc: string;
  isPlaying: boolean;
  durationCallback?: (duration: number) => void;
  setDuration?: Dispatch<SetStateAction<number>>;
  durationNumber?: number;
  setDurationNumber?: Dispatch<SetStateAction<number>>;
}

const DEF_HEIGHT = [2, 8, 14, 4, 16, 14, 10, 10, 10, 14, 10, 16, 10, 4, 2];

const AudioAnimation: FC<IProps> = ({
  audioSrc,
  className,
  isPlaying,
  durationCallback,
  setDuration,
  durationNumber,
}) => {
  const boxRef = useRef<HTMLDivElement | null>(null);
  const audioRef = useRef<HTMLAudioElement | null>(null);

  useEffect(() => {
    const box = boxRef.current;
    if (!box) return;

    const count = 15;
    const mid = Math.floor(count / 2);

    for (let i = 0; i < count; i++) {
      const bar = document.createElement("div");
      const baseHeight = DEF_HEIGHT[i];
      bar.className =
        "bg-white/60 w-[2px] absolute top-1/2 -translate-y-1/2 rounded-full transition-all duration-200";
      bar.style.height = `${baseHeight}px`;
      bar.style.left = `${i * 7}px`;

      let scale = Math.random() * 0.5 + 0.5;
      scale *= 1 - Math.abs(i - mid) / mid;

      bar.style.setProperty("--scale", scale.toString());
      bar.style.setProperty("--base-height", `${baseHeight}px`);
      bar.style.animationDelay = `${Math.random() * 200}ms`;

      box.appendChild(bar);
    }
    // 监听音频播放进度
    const timeupdateEve = () => {
      if (audioRef.current && setDuration) {
        setDuration(audioRef.current.currentTime);
      }
    };
    if (audioRef.current) {
      audioRef.current.addEventListener("timeupdate", timeupdateEve);
    }
    return () => {
      audioRef.current?.removeEventListener("timeupdate", timeupdateEve);
    };
  }, []);
  // useEffect(() => {
  //   if (durationCallback) {
  //     durationCallback(durationNumber);
  //   }
  // }, [durationNumber]);

  useEffect(() => {
    const bars = boxRef.current?.children;
    if (bars) {
      Array.from(bars).forEach((bar) => {
        if (isPlaying) {
          bar.classList.add("audio-animate");
        } else {
          bar.classList.remove("audio-animate");
        }
      });
    }

    if (audioRef.current) {
      if (isPlaying) {
        if (durationNumber) {
          audioRef.current.currentTime = durationNumber;
        } else {
          audioRef.current.currentTime = 0;
        }

        audioRef.current.play();
      } else {
        audioRef.current.pause();
        // audioRef.current.currentTime = 0;
      }
    }
  }, [isPlaying, audioSrc]);

  return (
    <div className={cn("relative", className)}>
      <div ref={boxRef} className="relative w-full h-[22px]"></div>
      {audioSrc && <audio ref={audioRef} src={audioSrc} loop />}
    </div>
  );
};

export default AudioAnimation;
