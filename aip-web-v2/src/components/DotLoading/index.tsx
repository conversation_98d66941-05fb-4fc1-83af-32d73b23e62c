import { FC } from "react";
import styles from "./style.module.css";
interface DotLoading {
  loadingTxt?: string;
}
const DotLoading: FC<DotLoading> = ({ loadingTxt }) => {
  return (
    <div className={styles.loadingContent}>
      <div className={styles.loadingDots}>
        <span className={styles.dot}></span>
        <span className={styles.dot}></span>
        <span className={styles.dot}></span>
      </div>
      {loadingTxt && <div className={styles.loadingTxt}>{loadingTxt}</div>}
    </div>
  );
};
export default DotLoading;
