.loadingContent {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  height: 100%;
  width: 100%;
}
.loadingDots {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 36px;
}
.loadingTxt {
  font-size: 14px;
  line-height: 36px;
  color: rgba(255, 255, 255, 0.8);
}
.dot {
  width: 8px;
  height: 8px;
  margin: 0 4px;
  background-color: #333;
  border-radius: 50%;
  animation: colorChange 1.4s infinite ease-in-out;
}

.dot:nth-child(2) {
  animation-delay: 0.2s;
}

.dot:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes colorChange {
  0%,
  100% {
    background-color: #333;
  }
  50% {
    background-color: #ccc;
  }
}
