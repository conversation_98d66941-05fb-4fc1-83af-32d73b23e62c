.carouselContainer {
  position: relative;
  height: 100%;
  width: 100%;
  overflow: hidden;
}

.carouselTrack {
  display: flex;
  height: 100%;
  transition: transform 0.5s ease-in-out;
}

.slide {
  min-width: 100%;
  height: 100%;
  background-size: cover;
  background-position: center;
}

.navBtn {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  color: white;
  cursor: pointer;
  font-size: 12px;

  width: 20px;
  height: 20px;
  border-radius: 4px;
  border: 1px solid #4b4b4b;
  background: #343434;
  z-index: 100;
}

.prevBtn {
  left: -26px;
}

.nextBtn {
  right: -26px;
}

.indicators {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 10px;
}

.indicator {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.5);
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.indicator.active {
  background-color: white;
}
