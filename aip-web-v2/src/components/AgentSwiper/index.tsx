import React, { useState, useEffect, FC } from "react";
import styles from "./style.module.css";
import { isNumber } from "lodash";
interface VideoSwiperProps {
  nodes: React.ReactNode[];
  currentKey: number;
}
const VideoSwiper: FC<VideoSwiperProps> = ({ nodes, currentKey }) => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isAutoPlay, setIsAutoPlay] = useState(true);

  // 处理自动播放
  //   useEffect(() => {
  //     let interval;
  //     if (isAutoPlay) {
  //       interval = setInterval(() => {
  //         setCurrentIndex((prev) => (prev + 1) % nodes.length);
  //       }, 3000);
  //     }
  //     return () => interval && clearInterval(interval);
  //   }, [isAutoPlay, nodes.length]);

  // 切换幻灯片
  const goToSlide = (index: number) => {
    setCurrentIndex(index);
  };

  // 上一页
  const goToPrev = () => {
    setCurrentIndex((prev) => (prev === 0 ? nodes.length - 1 : prev - 1));
  };

  // 下一页
  const goToNext = () => {
    setCurrentIndex((prev) => (prev + 1) % nodes.length);
  };
  useEffect(() => {
    if (isNumber(currentKey)) {
      setCurrentIndex(currentKey);
    }
  }, [currentKey]);

  return (
    <div className="relative w-full h-full">
      {/* 导航箭头 */}
      <button
        className={`${styles.navBtn} ${styles.prevBtn}`}
        onClick={goToPrev}
      >
        &lt;
      </button>
      <button
        className={`${styles.navBtn} ${styles.nextBtn}`}
        onClick={goToNext}
      >
        &gt;
      </button>

      <div
        className={styles.carouselContainer}
        onMouseEnter={() => setIsAutoPlay(false)}
        onMouseLeave={() => setIsAutoPlay(true)}
      >
        <div
          className={styles.carouselTrack}
          style={{ transform: `translateX(-${currentIndex * 100}%)` }}
        >
          {nodes.map((img, index) => (
            <div key={index} className={styles.slide}>
              {img}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default VideoSwiper;
