import { useConfigStore } from "@/store/store";
import { notification } from "antd";
import { useEffect } from "react";

const MessageModal = () => {
  const [api, contextHolder] = notification.useNotification();
  const notificationInformation = useConfigStore(
    (state) => state.notificationInformation
  );
  useEffect(() => {
    if (notificationInformation.type && notificationInformation.message) {
      api[notificationInformation.type]({
        message: notificationInformation.message,
        description: notificationInformation.description,
        placement: "topRight",
      });
    }
  }, [notificationInformation]);
  return <>{contextHolder}</>;
};
export default MessageModal;
