import {
  DownloadOutlined,
  ExclamationCircleOutlined,
  SyncOutlined,
} from "@ant-design/icons";
import { Button, Modal } from "antd";
import { useWorkflowInformation } from "@/hooks/useWorkflowhooks";
import { useUserInfoStore, useWorkflowStore } from "@/store/store";
import { downloadAssets } from "@/utils/helper";
import { useState } from "react";

const RegenerateVideo = () => {
  const [modal, contextHolder] = Modal.useModal();
  const [btnLoading, setBtnLoading] = useState(false);
  const [btnDisabled, setBtnDisabled] = useState(false);
  const { senderQuery } = useWorkflowInformation();
  const { chatLoading, WResult } = useWorkflowStore((state) => state);
  const currentIpUser = useUserInfoStore((state) => state.currentIpUser);
  const confirm = () => {
    modal.confirm({
      title: "确定重新生成视频",
      icon: <ExclamationCircleOutlined />,
      content: "重新生成视频将消耗视频生成时长，且不可撤销，请谨慎操作！",
      okText: "确认",
      cancelText: "取消",
      onOk() {
        setBtnDisabled(true);
        setBtnLoading(true);
        localStorage.setItem("continueVideo", `true`);
        senderQuery({
          question: "生成视频",
          params: {
            pid: currentIpUser?.id,
            query: "生成视频",
            is_pass: 0,
            task_id: localStorage.getItem("workflow_task_id")
              ? Number(localStorage.getItem("workflow_task_id"))
              : undefined,
          },
          lastWResult: WResult,
          callback: () => {
            setBtnDisabled(false);
            setBtnLoading(false);
          },
        });
      },
    });
  };
  return (
    <>
      <Button
        icon={<SyncOutlined />}
        onClick={confirm}
        type="primary"
        loading={btnLoading}
        disabled={chatLoading || btnDisabled}
      >
        重新生成
      </Button>
      {contextHolder}
    </>
  );
};
export default RegenerateVideo;
