import IconSvg from "@/assets/svg";
import styles from "./style.module.css";
const { CommonPlaySvg, CommonPauseSvg } = IconSvg;
interface IVideoBtnProps {
  style?: React.CSSProperties;
  onClick?: (e?: any) => void;
}
export function VideoPauseBtn({ style, onClick }: IVideoBtnProps) {
  return (
    <div style={style} onClick={onClick} className={styles.videoBtn}>
      <CommonPauseSvg className={styles.icon} />
    </div>
  );
}
export function VideoPlayBtn({ style, onClick }: IVideoBtnProps) {
  return (
    <div style={style} onClick={onClick} className={styles.videoBtn}>
      <CommonPlaySvg className={styles.icon} />
    </div>
  );
}
