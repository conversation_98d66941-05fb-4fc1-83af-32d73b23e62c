import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export function getDeviceType() {
  const isMobile = navigator.userAgent.match(
    /(phone|pad|pod|iPhone|iPod|ios|iPad|Android|Mobile|BlackBerry|IEMobile|MQQBrowser|JUC|Fennec|wOSBrowser|BrowserNG|WebOS|Symbian|Windows Phone)/i,
  )

  return isMobile ? 'mobile' : 'pc'
}

export function hideMobile(mobile: string) {
  if (!mobile) return '-'
  return mobile?.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2')
}