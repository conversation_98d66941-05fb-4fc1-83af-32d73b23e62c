import douyinIcon from '@/assets/images/hot_icon/douyin.png';
import kuaishouIcon from '@/assets/images/hot_icon/kuaishou.png';
import xiaohongshuIcon from '@/assets/images/hot_icon/xiaohongshu.png';
import zhihuIcon from '@/assets/images/hot_icon/zhihu.png';
import myFolderIcon from '@/assets/images/hot_icon/myFolder.png';
import shipinhaoIcon from '@/assets/images/hot_icon/shipinhao.png';
import wxIcon from '@/assets/images/hot_icon/wx.png';

export const dictToHotIcon = (platform_type: string) => {
  switch (platform_type) {
    //抖音
    case "1":
      return "/app/hot_icon/douyin.png";

    //好看
    case "2":
      return "/app/hot_icon/haokan.png";

    //快手
    case "3":
      return "/app/hot_icon/kuaishou.png";

    //必应
    case "4":
      return "/app/hot_icon/bing.png";

    //头条
    case "5":
      return "/app/hot_icon/toutiao.png";
    // default:
    //   return '"'
  }
};

export const dictToHotIconInPersonalData = (type: string) => {
  const iconMap: { [key: string]: any } = {
    '1': myFolderIcon.src,  // 我的文件
    '2': kuaishouIcon.src,  // 快手
    '3': douyinIcon.src,    // 抖音
    '4': xiaohongshuIcon.src, // 小红书
    '5': zhihuIcon.src,     // 知乎
    '6': shipinhaoIcon.src, // 视频号
    '7': wxIcon.src,        // 微信
  };
  
  // 添加一个平台名称到图标的映射，用于fallback
  const platformMap: { [key: string]: any } = {
    '我的抖音': douyinIcon.src,
    '我的快手': kuaishouIcon.src,
    '我的小红书': xiaohongshuIcon.src,
  };
  
  // 先尝试通过type匹配，如果没有匹配到，则通过平台名称匹配
  return iconMap[type] || platformMap[item?.knowledge_source] || wxIcon.src;
};
