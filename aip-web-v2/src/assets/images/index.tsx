import ContactUs from "./contact_us.png";

import Bing from "./hot_icon/bing.png";
import Douyin from "./hot_icon/douyin.png";
import Haokan from "./hot_icon/haokan.png";
import <PERSON><PERSON>in<PERSON><PERSON> from "./hot_icon/shipinhao.png";
import <PERSON><PERSON><PERSON><PERSON><PERSON> from "./hot_icon/xiaohongshu.png";
import Ku<PERSON><PERSON><PERSON> from "./hot_icon/kuaishou.png";
import TouTiao from "./hot_icon/toutiao.png";
import WX from "./hot_icon/wx.png";
import ZhiHu from "./hot_icon/zhihu.png";
import AipLogo from "./agent_pic/AipLogo.png";

import ImageError from "./agent_pic/imgError.png";
import COPYWRITER from "./agent_pic/COPYWRITER.png";
import CHOICE from "./agent_pic/CHOICE.png";
import AgentPic_3 from "./agent_pic/avatar3.png";
import UPLOAD from "./agent_pic/UPLOAD.png";
import AgentPic_5 from "./agent_pic/avatar5.png";
import AgentPic_6 from "./agent_pic/avatar6.png";
import AgentPic_7 from "./agent_pic/avatar7.png";
import SEARCH1 from "./agent_pic/SEARCH1.png";
import STRUCTURE from "./agent_pic/STRUCTURE.png";
import WRITER from "./agent_pic/WRITER.png";
import OUTSET from "./agent_pic/OUTSET.png";
import AgentPic_13 from "./agent_pic/avatar13.png";
import BI from "./agent_pic/BI.png";
import STYLE from "./agent_pic/STYLE.png";
import TITLE from "./agent_pic/TITLE.png";
import CRAWLER from "./agent_pic/CRAWLER.png";
import AgentPic_17 from "./agent_pic/avatar17.png";
import AgentPic_18 from "./agent_pic/avatar18.png";
import HOOK from "./agent_pic/HOOK.png";
import VIEWPOINT from "./agent_pic/VIEWPOINT.png";
import VOICE1 from "./agent_pic/VOICE1.png";
import RAGDATA from "./agent_pic/RAGDATA.png";
import VIDEO1 from "./agent_pic/VIDEO1.png";
import PERSON from "./agent_pic/PERSON.png";

import PromptPic_7 from "./agent_pic/prompt_7.png";
import BUSINESS from "./agent_pic/BUSINESS.png";
import PromptPic_77 from "./agent_pic/prompt_77.png";

const ImageFile = {
  ContactUs,

  ImageError,
  // plat icon
  Bing,
  Douyin,
  Haokan,
  ShiPinHao,
  XiaoHongShu,
  KuaiShou,
  TouTiao,
  WX,
  ZhiHu,
  AgentPic_3,
  AgentPic_5,
  AgentPic_6,
  AgentPic_7,
  AgentPic_13,
  AgentPic_17,
  AgentPic_18,

  PromptPic_7,
  PromptPic_77,
  /** 专家头像 */
  AipLogo: {
    name: "API 主管",
    pic: AipLogo,
  },
  /** 人设专家 */
  PERSON: {
    name: "IP人设",
    pic: PERSON,
  },
  /** 选题专家 */
  CHOICE: {
    name: "选题专家",
    pic: CHOICE,
  },
  /** 结构专家 */
  STRUCTURE: {
    name: "结构专家",
    pic: STRUCTURE,
  },
  /** 文案专家 */
  WRITER: {
    name: "文案专家",
    pic: WRITER,
  },
  /** 风格润色 */
  STYLE: {
    name: "风格润色",
    pic: STYLE,
  },
  /** 标题专家 */
  TITLE: {
    name: "标题专家",
    pic: TITLE,
  },
  /** 音频上传专家 */
  UPLOAD: {
    name: "音频上传专家",
    pic: UPLOAD,
  },
  /** 音频上传专家 */
  VOICE1: {
    name: "音频展开专家",
    pic: VOICE1,
  },
  VOICE2: {
    name: "音频生成专家",
    pic: VOICE1,
  },
  VOICE3: {
    name: "音频作品专家",
    pic: VOICE1,
  },

  /** 视频专家 */
  VIDEO1: {
    name: "视频展开专家",
    pic: VIDEO1,
  },
  VIDEO2: {
    name: "视频等待专家",
    pic: VIDEO1,
  },
  VIDEO3: {
    name: "视频作品专家",
    pic: VIDEO1,
  },
  /** 洗稿专家 */
  COPYWRITER: {
    name: "洗稿专家",
    pic: COPYWRITER,
  },
  /** 数据分析专家 */
  SEARCH1: {
    name: "数据分析专家",
    pic: SEARCH1,
  },
  /** 数据清洗专家 */
  SEARCH2: {
    name: "数据清洗专家",
    pic: SEARCH1,
  },
  /** 数据搜索专家 */
  SEARCH3: {
    name: "数据搜索专家",
    pic: SEARCH1,
  },
  /** 爬虫专家 */
  CRAWLER: {
    name: "爬虫专家",
    pic: CRAWLER,
  },
  /** 开头专家 */
  OUTSET: {
    name: "开头专家",
    pic: OUTSET,
  },
  // /** 结尾专家 */
  // "":{
  //   name:"",
  //   pic:
  // },
  /** 观点专家 */
  VIEWPOINT: {
    name: "观点专家",
    pic: VIEWPOINT,
  },
  /** 钩子专家 */
  HOOK: {
    name: "钩子专家",
    pic: HOOK,
  },
  /** 知识库专家 */
  RAGDATA: {
    name: "知识库专家",
    pic: RAGDATA,
  },
  /** 商业洞察专家 */
  BI: {
    name: "商业洞察专家",
    pic: BI,
  },
  /** 业务专家 */
  BUSINESS: {
    name: "业务专家",
    pic: BUSINESS,
  },
  // 评论专家
  // 知识库专家
};

export default ImageFile;
