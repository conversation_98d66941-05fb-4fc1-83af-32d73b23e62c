import { formatDistance } from "date-fns";
import { zhCN } from "date-fns/locale";
import { LT } from "@/typing/types";
export const downloadAssets = (url: string) => {
  const link = document.createElement("a");
  link.style.display = "none";
  link.href = url;
  link.setAttribute("download", `assets_${Date.now()}`);
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
};
/** xml 请求下载 */
export const downloadByXML = (file_url: string, file_name: string) => {
  const xhr = new XMLHttpRequest();
  xhr.open("get", file_url, true);
  xhr.responseType = "blob";
  xhr.onload = function () {
    const url = window.URL.createObjectURL(xhr.response);
    const link = document.createElement("a");
    link.href = url;
    link.setAttribute("download", file_name);
    link.click();
  };
  xhr.send();
};
/** 下载图片 */
export const downloadImgByUrl = async (url: string) => {
  const response = await fetch(url);
  if (!response.ok) {
    throw new Error(`Failed to fetch image: ${url}`);
  }
  const blob = await response.blob();
  const link = document.createElement("a");
  link.href = URL.createObjectURL(blob);
  // 你可以从URL中提取文件名，或者使用自定义的文件名
  link.download = url.split("/").pop() || "img.jpg";
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  URL.revokeObjectURL(link.href);
};
/**
 * 获取音频的时长
 * @param url 音频地址
 * @param callback 回调函数
 */
export const getAudioDuration = (
  url: string,
  callback: (error: Error | null, duration?: string, total?: number) => void
) => {
  const audio = new Audio();

  audio.addEventListener("loadedmetadata", () => {
    const totalSeconds = audio.duration;
    const minutes = Math.floor(totalSeconds / 60);
    const seconds = Math.floor(totalSeconds % 60);
    const formattedDuration = `${minutes}:${seconds
      .toString()
      .padStart(2, "0")}`;
    callback(null, formattedDuration, totalSeconds);
  });

  audio.addEventListener("error", () => {
    callback(null);
  });

  audio.src = url;
};
/**
 * 秒 转 分
 * @param seconds 秒
 */
export const numberToTime = (seconds: number) => {
  return `${Math.floor(seconds / 60)}:${(seconds % 60).toFixed(0)}`;
};
/**
 * 获取内容(中国/单词)长度
 */
export const getTxtLength = ({
  txt,
  type,
}: {
  txt: string;
  type: LT;
}): number => {
  if (type === "zh") {
    return txt.length;
  } else {
    return txt.split(" ")?.length || 0;
  }
};
export const formatDate = (date: string | number | Date) => {
  return formatDistance(new Date(date), new Date(), {
    addSuffix: true,
    locale: zhCN,
  });
};

export const secondsToTime = (secs: number) => {
  var date = new Date(0);
  date.setSeconds(secs);
  return date.toISOString().substr(11, 8); // "HH:MM:SS"
};
