/** 首页 prompt 配置 */
import ImageFile from "@/assets/images";
import IconSvg from "@/assets/svg";
const { HomeZHCNSvg, HomeCNZHSvg } = IconSvg;
const {
  CRAWLER,
  PromptPic_7,
  COPYWRITER,
  HOOK,
  OUTSET,
  PromptPic_77,
  SEARCH1,
  CHOICE,
  WRITER,
  STYLE,
  VIDEO1,
  VOICE1,
  PERSON,
} = ImageFile;
export const promptConfig = [
  {
    key: "have_select_title",
    label: "我有选题",
    avatar: [STYLE.pic, WRITER.pic],
    formatterTxt: `根据以下内容为选题，生成一个爆款短视频：
[ ]`,
  },
  {
    key: "select_title",
    label: "提个需求",
    avatar: [SEARCH1.pic, CHOICE.pic],
    formatterTxt: `请根据以下需求生成爆款短视频：
[ ]`,
  },
  {
    key: "seeting_writing",
    label: "人设-成交文案",
    avatar: [PERSON.pic, HOOK.pic],
    formatterTxt: `结合我人设里提炼的 [ 商业目标 ] 的内容为主题，从我的人设里提炼一个 [ 推荐 ] 的观点，开头专家用 [ 冲突 ] 型开头，钩子专家要带上我人设里提炼的 [ 商业路径 ] 做为钩子内容，最后要让观众根据 [ 钩子的内容 ] 来评论区和我互动，生成一个爆款口播稿`,
  },
  {
    key: "seeting_xs",
    label: "人设-线索文案",
    avatar: [PERSON.pic, OUTSET.pic],
    formatterTxt: `结合我人设里提炼的 [ 商业观点 ] 的内容为主题，从我的人设里提炼一个 [ 推荐 ] 的观点，开头专家用 [ 冲突 ] 型开头，钩子专家要带上我人设里提炼的 [ 商业观点 ] 做为钩子内容，最后要让观众根据 [ 钩子的内容 ] 来评论区和我互动，生成一个爆款口播稿`,
  },
  {
    key: "data_write",
    label: "数据+文案专家",
    avatar: [CRAWLER.pic, WRITER.pic],
    formatterTxt: `请根据以下链接中内容生成文案:
[ 替换为粘贴的链接，支持微信以及绝大多数网站 ]`,
  },
  {
    key: "plagiarism",
    label: "洗稿工作流",
    avatar: [COPYWRITER.pic, STYLE.pic],
    formatterTxt: `文案写手，润色专家，帮我改写以下口播稿：
[ 替换文本 ]`,
  },
  {
    key: "audio",
    label: "音频专家",
    avatar: [VOICE1.pic],
    formatterTxt: `音频专家，请将以下文案生成音频：
[ 替换为粘贴的文案 ]`,
  },

  {
    key: "audio_synthesis",
    label: "音视频合成专家",
    avatar: [VOICE1.pic, VIDEO1.pic],
    formatterTxt: `音频专家、视频专家，我要上传音频合成视频`,
  },
];
/** 中文下拉时间 */
export const timeItems = [
  {
    key: 135,
    label: "30秒（约135字）",
    text: "30s",
  },
  {
    key: 270,
    label: "60秒（约270字）",
    text: "60s",
  },
  {
    key: 405,
    label: "90秒（约405字）",
    text: "90s",
  },
  {
    key: 540,
    label: "120秒（约540字）",
    text: "120s",
  },
  {
    key: 675,
    label: "120秒（约675字）",
    text: "150s",
  },
  {
    key: 810,
    label: "180秒（约810字）",
    text: "180s",
  },
];
/** 英文单词数量 */
export const enTimeItems = [
  {
    key: 68,
    label: "30秒（约68单词）",
    text: "30s",
  },
  {
    key: 136,
    label: "60秒（约136单词）",
    text: "60s",
  },
  {
    key: 204,
    label: "90秒（约204单词）",
    text: "90s",
  },
  {
    key: 272,
    label: "120秒（约272单词）",
    text: "120s",
  },
  {
    key: 340,
    label: "120秒（约340单词）",
    text: "150s",
  },
  {
    key: 408,
    label: "180秒（约408单词）",
    text: "180s",
  },
];
/**
 * 下拉语言
 */
export const languageItems = [
  {
    key: "zh",
    label: "中文",
    text: "中文",
  },
  {
    key: "en",
    label: "英文",
    text: "英文",
  },
  {
    key: "es",
    label: "西班牙语",
    text: "西班牙语",
  },
];
/**
 * 风格
 */
export const styleItems = [
  {
    key: "",
    label: "默认",
    text: "默认",
  },
  {
    key: "干货分享",
    label: "干货分享",
    text: "干货分享",
  },
  {
    key: "情绪共鸣",
    label: "情绪共鸣",
    text: "情绪共鸣",
  },
  {
    key: "幽默搞笑",
    label: "幽默搞笑",
    text: "幽默搞笑",
  },
  {
    key: "悬念引导",
    label: "悬念引导",
    text: "悬念引导",
  },
  {
    key: "喊话种草",
    label: "喊话种草",
    text: "喊话种草",
  },
  {
    key: "剧情沉浸",
    label: "剧情沉浸",
    text: "剧情沉浸",
  },
];
export const styleMap: { [key: string]: string } = {
  "": "",
  干货分享:
    "干货分享:专业、干练，带点权威感。如：“想提升效率？3个方法：第一，列清单；第二，定优先级；第三，专注半小时。试试看！",

  情绪共鸣:
    "情绪共鸣:亲切、煽情，偶尔带点戏剧化。如：“你是不是也觉得每天忙得晕头转向，却没成果？我也是，直到我发现这个方法……",

  幽默搞笑:
    "幽默搞笑:轻松、戏谑，偶尔带点“沙雕”感。如：“我减肥三天，瘦了0.1斤，照镜子一看，嗯，还是那么帅，算了，继续吃！",

  悬念引导:
    "悬念引导:吊胃口、紧凑，带点神秘感。如：“这个东西放冰箱能多用一个月，你猜是什么？答案在最后！",

  喊话种草:
    "喊话种草:热情、急切，带点“推销员”气势。如：“这款面膜真的绝了，敷一次皮肤水嫩到炸！赶紧试试，链接在下面！",

  剧情沉浸:
    "剧情沉浸:叙述性强，带点表演感。如：“早上起来，同事问我昨晚干嘛了，我说：敷了个面膜，熬夜追剧。现在皮肤还这么好，你信吗？",
};
/**
 * 易读性
 */
export const legibilityItems = [
  {
    key: 0,
    label: "默认",
    text: "默认",
  },
  {
    key: 1,
    label: "简单易懂",
    text: "简单易懂",
  },
  {
    key: 2,
    label: "日常对话",
    text: "日常对话",
  },
  {
    key: 3,
    label: "知识分享",
    text: "知识分享",
  },
  {
    key: 4,
    label: "专业内容",
    text: "专业内容",
  },
];
