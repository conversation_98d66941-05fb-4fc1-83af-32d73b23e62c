import type { ThemeConfig } from "antd";

export const darkThemeConfig: ThemeConfig = {
  token: {
    colorBgElevated: "#27272A", // 弹出层背景色
    colorText: "#FFFFFF", // 主文本色
    colorTextSecondary: "#A1A1AA", // 次要文本色
    controlItemBgHover: "#3F3F46", // hover 背景色
    colorBgContainer: "#18181B", // 容器背景色
    colorBorder: "#27272A", // 边框色
    borderRadius: 8, // 圆角
  },
  components: {
    Modal: {
      contentBg: "#27272A",
      headerBg: "#27272A",
      titleColor: "#FFFFFF",
      titleFontSize: 16,
      padding: 20,
    },
    Dropdown: {
      controlItemBgHover: "#3F3F46",
      controlItemBgActive: "#3F3F46",
      colorBgElevated: "transparent",
      boxShadowSecondary: "none",
    },
    DatePicker: {
      cellHoverBg: "#3F3F46",
      cellActiveWithRangeBg: "#3F3F46",
    },
  },
};
