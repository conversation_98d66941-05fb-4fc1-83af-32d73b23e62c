import { GetService, PostService } from "../base";
import { GetServiceUrl } from "../config";
import API_URL from "./api";
const { hot_list, file_upload, personal_knowledge_graph, precontract_create } =
  API_URL;

const getRandomItems = (array: [], num: number) => {
  const shuffled = array.slice(); // 复制数组
  for (let i = array.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
  }
  return shuffled.slice(0, num);
};
/**
 * 获取热门列表
 */
export async function getHotList(pid: number) {
  const res = await GetService({ url: `${hot_list}/${pid}` });
  if (res.code === 200) {
    if (res.data.length > 6) {
      const data = getRandomItems(res.data, 6);
      return { ...res, data };
    } else {
      return res;
    }
  }
  return Promise.reject(res);
}
/**
 * 获取首页文件上传的 url，token
 * return string
 */
export function getUploadInfo() {
  return GetServiceUrl() + file_upload;
}
/**
 * 知识图谱
 */
export async function getPersonalKnowledgeGraph(pid: number) {
  const res = await GetService({
    url: `${personal_knowledge_graph}/${pid}`,
  });
  return res;
}
interface PecontractCreateProps {
  mobile: string;
  business_name: string;
  city: string;
  verify_code: number;
}
/**
 * 联系我们
 */
export async function precontractCreate(body: PecontractCreateProps) {
  const res = await PostService({
    url: `${precontract_create}`,
    body: body,
  });
  return res;
}
