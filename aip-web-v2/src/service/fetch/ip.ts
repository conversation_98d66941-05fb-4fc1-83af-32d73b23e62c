import { GetService, PostService } from "../base";

/**
 * 更新ip信息
 */
export async function updateIpInfoApi(params: any) {
    const response = await PostService({
        url: "/admin/ip/update",
        body: { ...params },
    });
    // const response = await PostServiceStream({ tid, query });
    return response;
}

/**
 * 创建IP
 */
export async function createIpApi(params: any) {
    const response = await PostService({
        url: "/admin/ip/save",
        body: { ...params },
    });
    return response;
}