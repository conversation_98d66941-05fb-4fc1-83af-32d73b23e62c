const API_URL = {
  /** 登录接口 */
  user_login: "/admin/user/login",
  /** 获取用户信息接口 */
  user_info: "/admin/user/me",
  /** 获取IP列表接口 */
  user_ip_list: "/admin/ip/findAll",
  /** 获取IP详情接口 */
  user_ip_detail: "/admin/ip/detail/",
  send_verify_code: "/admin/user/send-verify-code",
  change_password_by_mobile: "/admin/user/change-password-by-mobile",

  /** slider-获取最新的前五条工作流 */
  task_latest: "/admin/task/latest",
  // 首页
  file_upload: "/common/v1/file/upload",
  hot_list: "/ai_search/v1/hot_list",
  precontract_create: "/admin/precontract/create",
  // workflow
  media_model_findAll: "/admin/media/model/findAll",
  voice_model_findAll: "/admin/voice/model/findAll",
  voice_model_create: "/admin/voice/model/create",
  voice_model_update: "/admin/voice/model/update",
  media_model_update: "/admin/media/model/update",
  audio_fish_clone: "/audio/fish/clone",
  voice_model_delete: "/admin/voice/model/delete/",
  media_model_delete: "/admin/media/model/delete/",
  media_model_transcode: "/admin/media/model/transcode/",
  media_model_save: "/admin/media/model/save",
  voice_model_save: "/admin/voice/model/save",
  common_file_upload: "/common/v1/file/upload",
  comment_findAll: "/admin/comment/findAll",
  taks_findAll: "/admin/task/findAll",
  agent_history_update: "/admin/agents-history/update",
  task_query: "/admin/voice/task/query",
  admin_image_save: "/admin/image-model/save",
  admin_image_find: "/admin/image-model/find",
  admin_virtual_find: "/admin/virtual-human/find",
  admin_virtual_save: "/admin/virtual-human/save",

  mcp_start: "/workflow/start",
  mcp_interrupt: "/workflow/interrupt",
  mcp_resume: "/workflow/resume",
  mcp_continue: "/workflow/continue",

  // agents_continue_creation: "/agents/v2.1/continue_creation",
  // agents_start_creation: "/agents/v2.1/start_creation",
  agents_start_creation: "/agents/v3/start_creation",
  agents_continue_creation: "/agents/v3/continue_creation",
  agents_stop_task: "/agents/v1/stop_task",
  task_agents_history: "/admin/agents-history/task/",
  check_add: "/agents/v3/check_add",

  // knowledge_base
  knowledge_search: "/api/personal-knowledge/search/",
  knowledge_delete: "/api/social-media/accounts/",
  knowledge_add: "/api/social-media/accounts",
  rpa_create_task: "/admin/rpa/create_task",
  knowledge_files: "/api/personal-knowledge/files",
  // knowledge_files_delete: "/api/personal-knowledge/files/",
  delete_embedding: "/rag/v1/delete_embedding",

  // works
  work_findAll: "/admin/work/findAll",
  work_update: "/admin/work/update", // 更新口播稿数据
  work_userUpdate: "/admin/work/userUpdate",
  work_delete: "/admin/work/delete/",
  findWorkWithTask: "/admin/work/findWorkWithTask", // 查询口播稿数据
  audio_speed: "/audio/speed",
  update_spend_voice: "/admin/voice/model/save_history",
  // mock workflow
  mock_start_creation: "/mock/agents/v2.1/start_creation",
  mock_continue_creation: "/mock/agents/v2.1/continue_creation",
  personal_knowledge_graph: "/api/personal-knowledge-graph", // 获取知识图谱状态
  personal_graph_data: "api/personal-graph-data", // 获取知识图谱数据

  admin_payment_init: "/admin/v2/payment/init", // 成功支付图片生产
  admin_payment_query: "/admin/payment/query", // 查询支付状态
  admin_payment_order_find: "/admin/payment/order/find", // 查询订单状态
};
export default API_URL;
