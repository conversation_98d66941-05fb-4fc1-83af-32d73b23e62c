import { GetService, PostService } from "../base";



/**
 * 保存ip_prompt信息
 */
export async function saveIpPromptApi(params: any) {
    const response = await PostService({
        url: "/admin/ip_prompt/save",
        body: { ...params },
    });
    return response;
}

/**
 * 更新ip_prompt信息
 */
export async function updateIpPromptApi(params: any) {
    const response = await PostService({
        url: "/admin/ip_prompt/update",
        body: { ...params },
    });
    return response;
}

/**
 * 获取ip_prompt信息
 */
export async function getIpPromptApi(pid: number) {
    const response = await GetService({
        url: `/admin/ip_prompt/detail/${pid}`,
    });
    return response;
}

