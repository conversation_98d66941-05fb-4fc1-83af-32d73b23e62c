import { GetService, PostService } from "../base";
import { SetToken } from "@/service/config";
import API_URL from "./api";

import { UserInformation, VerifyDataType } from "@/typing/types";
const {
  user_login,
  user_info,
  user_ip_list,
  user_ip_detail,
  send_verify_code,
  change_password_by_mobile,
} = API_URL;

export interface LoginParams {
  login_name: string;
  login_pwd: string;
}
export interface LoginResult {
  data: {
    access_token: string;
    token_type: string;
  };
}

type LoginFetchData = (params: LoginParams) => Promise<LoginResult>;

/**
 * 登录接口
 */
export const loginService: LoginFetchData = async (params: LoginParams) => {
  const result = await PostService({ url: user_login, body: params });
  if (result.code === 200) {
    SetToken(result.data.access_token);
    return result.data;
  }
  return Promise.reject(result);
};

type UserInfoFetchData = () => Promise<UserInformation>;

/**
 * 获取用户信息
 */
export const getUserInfoService: UserInfoFetchData = async () => {
  const result = await GetService({ url: user_info });
  if (result.code === 200) {
    return result.data;
  }
  return Promise.reject(result);
};

/**
 * 获取IP列表
 */
export const getIpListService = async (uid: string) => {
  const result = await PostService({ url: user_ip_list, body: { uid } });
  return result;
};

/**
 * 获取当前IP账户信息
 */
export const getCurrentIpService = async (ip: number) => {
  const result = await GetService({ url: `${user_ip_detail}${ip}` });
  return result;
};
/** [v1]发送手机验证码 */
export const getPhoneVerifyCode = async (mobile: string) => {
  const result = await PostService({
    url: `${send_verify_code}?mobile=${mobile}`,
    body: {},
  });
  return result;
};
/** 修改密码 */
export const updatePassword = async (params: VerifyDataType) => {
  const result = await PostService({
    url: change_password_by_mobile,
    body: params,
  });
  return result;
};
