import { GetService, PostService } from "../base";
import { GetServiceUrl, GetToken } from "../config";
import { LT } from "@/typing/types";

import API_URL from "./api";
const {
  agents_start_creation,
  agents_stop_task,
  agents_continue_creation,
  mock_continue_creation,
  mock_start_creation,
  comment_findAll,
  taks_findAll,
  task_agents_history,
  findWorkWithTask,
  audio_speed,
  update_spend_voice,
  check_add,
  task_query,
  personal_graph_data,
  mcp_start,
  mcp_continue,
  mcp_interrupt,
  mcp_resume,
} = API_URL;

export interface GetWorkflowDataProps {
  pid: number;
  task_id?: number;
  query?: string;
  status?: number;
  progress?: number;
  remark?: string;
  audio_url?: string;
  voice_is_upload?: boolean;
  voice_upload_url?: string;
  video_url?: string;
  audio_model_id?: string;
  video_model_id?: string;
  task_knowledge_ids?: string;
  is_pass?: number;
  agent_uuid?: string;
  doc_length?: number;
  video_model_pic_url?: string;

  is_knowledge?: number;
  is_person?: number;
  is_search?: number;
  is_rag?: number;
  language?: LT;
  read_score?: number;
  style?: string;
}
/**
 * 获取Chat 流数据
 */

export async function getWorkflowData(params: GetWorkflowDataProps) {
  const BASE_URL = GetServiceUrl();
  const controller = new AbortController();
  const signal = controller.signal;
  const token = GetToken();
  const responseData = await fetch(`${BASE_URL}${agents_start_creation}`, {
    body: JSON.stringify(params),
    method: "POST",
    signal,
    headers: {
      "Content-Type": "application/json",
      ...(token ? { Authorization: `Bearer ${token}` } : {}),
    },
  });
  // 请求错误处理
  if (!responseData.ok) {
    // 传入已读取的JSON数据
    return {
      response: Promise.reject("请求失败"),
      abort: () => controller.abort(),
      controller,
    };
  }
  return {
    response: responseData.body?.getReader(),
    abort: () => controller.abort(),
    controller,
  };

  // const response = await PostService({
  //   url: agents_start_creation,
  //   body: { ...params },
  // });
  // return response;
}

/**
 * Chat 工作流打断
 * params: { tid: number }
 */
export async function postWorkflowStop({ id }: { id: number }) {
  const response = await PostService({
    url: agents_stop_task,
    body: { task_id: id },
  });
  return response;
}

export async function testWorkflowContinue(params: GetWorkflowDataProps) {
  const BASE_URL = GetServiceUrl();
  const token = GetToken();
  const responseData = await fetch(`${BASE_URL}${agents_continue_creation}`, {
    body: JSON.stringify(params),
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      ...(token ? { Authorization: `Bearer ${token}` } : {}),
    },
  });
  // 请求错误处理
  if (!responseData.ok) {
    // 传入已读取的JSON数据
    console.log("请求失败");
  }
  // return {
  //   response: responseData.body?.getReader(),
  //   abort: () => controller.abort(),
  // };
  // const response = await PostService({
  //   url: agents_continue_creation,
  //   body: { ...params },
  // });
  // return response;
}
/**
 * chat 工作流继续
 */
export async function postWorkflowContinue(params: GetWorkflowDataProps) {
  const BASE_URL = GetServiceUrl();
  const controller = new AbortController();
  const signal = controller.signal;
  const token = GetToken();
  const responseData = await fetch(`${BASE_URL}${agents_continue_creation}`, {
    body: JSON.stringify(params),
    method: "POST",
    signal,
    headers: {
      "Content-Type": "application/json",
      ...(token ? { Authorization: `Bearer ${token}` } : {}),
    },
  });
  // 判断当前数据类型是否为JSON
  const type = responseData.headers
    .get("content-type")
    ?.includes("application/json");
  // 请求错误处理
  if (!responseData.ok) {
    // 传入已读取的JSON数据
    const data = await responseData.json();
    console.log("请求失败");
    if (type) {
      return {
        type: "json",
        response: Promise.reject(data),
        abort: () => controller.abort(),
        controller,
      };
    } else {
      return {
        type: "stream",
        response: Promise.reject(data),
        abort: () => controller.abort(),
        controller,
      };
    }
  }
  if (type) {
    const data = await responseData.json();
    return {
      type: "json",
      response: data,
      abort: () => controller.abort(),
      controller,
    };
  } else {
    return {
      type: "stream",
      response: responseData.body?.getReader(),
      abort: () => controller.abort(),
      controller,
    };
  }
  // const data = await responseData.json();
  // return {
  //   type: type ? "json" : "stream",
  //   response: type ? data : responseData.body?.getReader(),
  //   abort: () => controller.abort(),
  //   controller,
  // };
  // const response = await PostService({
  //   url: agents_continue_creation,
  //   body: { ...params },
  // });
  // return response;
}
/**
 * 获取语音稿件新增内容接口v3
 * @param task_id
 */
export async function checkAdd(task_id: number) {
  const BASE_URL = GetServiceUrl();
  const controller = new AbortController();
  const signal = controller.signal;
  const token = GetToken();
  const responseData = await fetch(`${BASE_URL}${check_add}/${task_id}`, {
    // body: JSON.stringify({ task_id }),
    method: "GET",
    signal,
    headers: {
      "Content-Type": "application/json",
      ...(token ? { Authorization: `Bearer ${token}` } : {}),
    },
  });
  // 请求错误处理
  if (!responseData.ok) {
    // 传入已读取的JSON数据
    return {
      response: Promise.reject("请求失败"),
      abort: () => controller.abort(),
      controller,
    };
  }
  return {
    response: responseData.body?.getReader(),
    abort: () => controller.abort(),
    controller,
  };
}

/**
 * 根据 task_id 获取所有评论
 * @param task_id
 */
export async function getComments(task_id: string) {
  const result = await PostService({ url: comment_findAll, body: { task_id } });
  // const data = await api.post(`/admin/comment/findAll`, { task_id });
  return result;
}

/**
 * 查询task
 * @params queryBody
 */
export async function getTaskList(queryBody: object) {
  const result = await PostService({
    url: taks_findAll,
    body: { ...queryBody },
  });
  // const data = await api.post(`/admin/task/findAll`, queryBody);
  return result;
}
/**
 * 查询生成视频状态
 */
export async function taskQuery(task_id: number) {
  const result = await PostService({ url: task_query, body: { task_id } });
  return result;
}
/**
 * 获取工作流历史
 * @params task_id
 */
export async function getWorkHistory({
  task_id,
  pid,
}: {
  task_id: number;
  pid: number;
}) {
  const result = await GetService({
    url: `${task_agents_history}${pid}/${task_id}`,
  });
  return result;
}

/**
 * 获取口播稿数据
 * @params task_id
 * @params pid
 */
export async function getOralData({
  task_id,
  pid,
}: {
  task_id: number;
  pid: number;
}) {
  const result = await PostService({
    url: findWorkWithTask,
    body: {
      task_id: task_id,
      pid: pid,
    },
  });
  return result;
}
/**
 * 获取变速音频
 * @param body
 */
export async function changeAudioSpeed(body: {
  input_url: string;
  speed: number;
  pid: number;
  task_id: number;
  uuid: string;
}) {
  const result = await PostService({
    url: audio_speed,
    body: body,
  });
  return result;
}
interface UpdateSpeedHistoryProps {
  voice_name?: string;
  voice_url: string;
  new_voice_url: string;
  uuid: string;
  task_id: number;
  pid: number;
}
/**
 * 获取知识图谱数据
 * @params taskId
 */
export async function personalGraphData(taskId: string) {
  const result = await GetService({
    url: `${personal_graph_data}${taskId}`,
  });
  return result;
}
/**
 * 变速之后更新历史结果
 * @params params
 */
export async function updateSpeedHistory(params: UpdateSpeedHistoryProps) {
  const result = await PostService({
    url: update_spend_voice,
    body: params,
  });
  return result;
}
/** cy 模拟工作流打断 */
export async function mockWorkflowStart(params: GetWorkflowDataProps) {
  const result = await PostService({
    url: mock_start_creation,
    body: { ...params },
  });
  return result;
}
/** cy 模拟继续工作流 */
export async function mockWorkflowContinue(params: GetWorkflowDataProps) {
  const result = await PostService({
    url: mock_continue_creation,
    body: { ...params },
  });
  return result;
}

interface MCPStartProps {
  language: string;
  pid: number;
  readability: number;
  script_length: number;
  style: string;
  user_input: string;
}
/**  MCP 接口  */
export async function MCPStart(params: MCPStartProps) {
  // const BASE_URL = GetServiceUrl();
  const controller = new AbortController();
  const signal = controller.signal;
  const token = GetToken();
  const responseData = await fetch(
    `https://uat-xinhou-aip-agents.aipgpt.cn${mcp_start}`,
    {
      body: JSON.stringify(params),
      method: "POST",
      signal,
      headers: {
        "Content-Type": "application/json",
        ...(token ? { Authorization: `Bearer ${token}` } : {}),
      },
    }
  );
  // 请求错误处理
  if (!responseData.ok) {
    // 传入已读取的JSON数据
    return {
      response: Promise.reject("请求失败"),
      abort: () => controller.abort(),
      controller,
    };
  }
  return {
    response: responseData.body?.getReader(),
    abort: () => controller.abort(),
    controller,
  };
}
/** cy 模拟工作流继续 */

// export async function mockWorkflowStop({ tid }: { tid: any }) {
//   const response = await PostServiceStreamStop(tid);
//   return response;
// }

/** 模拟工作流继续 */
// export async function mockWorkflowContinue({
//   tid,
//   query,
// }: {
//   tid: string;
//   query: string;
// }) {
//   const response = await PostServiceStreamContinue({ tid, query });
//   return response;
// }

/** 获取Chat 流数据 */
// export async function PostServiceStream({
//   query,
//   pid,
// }: {
//   query: string;
//   pid: string;
// }) {
//   const response = await PostService({ url: agents_start_creation });
// }
