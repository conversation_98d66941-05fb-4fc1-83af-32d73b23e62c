// import api from "@/lib/fetchUtils";
import { PostService, GetService } from "../base";
import API_URL from "./api";

const { work_findAll, work_update, work_userUpdate, work_delete } = API_URL;
//获取作品信息
export async function getWork(queryBody: object) {
  const result = await PostService({ url: work_findAll, body: queryBody });
  return result;
}

/**
 * 更新作品信息,实际是更新口播稿内容
 * 目前是连带工作流历史一起修改，协商后端决定
 * 未来单独修改作品信息，情况下，新增接口
 */
export async function upDateWork(queryBody: object) {
  const result = await PostService({ url: work_update, body: queryBody });
  return result;
}

//用户手动更新口播稿内容
export async function upDateOral(queryBody: object) {
  const result = await PostService({ url: work_userUpdate, body: queryBody });
  return result;
}

/** 删除作品 */
export async function deleteWork(id: number) {
  const result = await GetService({ url: `${work_delete}${id}` });
  return result;
}
