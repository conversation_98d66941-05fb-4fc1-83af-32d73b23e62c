import { PostService, GetService } from "../base";
import api from "./api";
const {
  media_model_findAll,
  voice_model_findAll,
  voice_model_create,
  voice_model_update,
  media_model_update,
  audio_fish_clone,
  voice_model_delete,
  media_model_delete,
  media_model_transcode,
  media_model_save,
  voice_model_save,
  common_file_upload,
  admin_image_save,
  admin_image_find,
  admin_virtual_save,
  admin_virtual_find,
  admin_payment_init,
  admin_payment_query,
  admin_payment_order_find,
} = api;
/** [v1]查询数字人多媒体信息表信息接口 */
export async function getVideoList(body: any) {
  const result = await PostService({ url: media_model_findAll, body: body });
  return result;
}
/** [v1]查询声音模型信息表信息接口 */
export async function getAudioList(body: any) {
  const result = await PostService({ url: voice_model_findAll, body: body });
  return result;
}
/** [v1]新增声音模型信息表信息接口 */
export async function voiceModelCreate(body: any) {
  const result = await PostService({ url: voice_model_create, body: body });
  return result;
}
/** [v1]更新声音模型信息表信息接口 */
export async function voiceModelUpdate(body: any) {
  const result = await PostService({ url: voice_model_update, body: body });
  return result;
}

/** [v1]更新数字人多媒体信息表信息接口 */
export async function mediaModelUpdate(body: any) {
  const result = await PostService({ url: media_model_update, body: body });
  return result;
}
/** [v1]Fish复刻声音 */
export async function audioClone(body: any) {
  const result = await PostService({ url: audio_fish_clone, body: body });
  return result;
}
/** [v1]删除声音模型信息表信息接口 */
export async function audioDelete(id: number) {
  const result = await GetService({
    url: `${voice_model_delete}${id}`,
    customMessage: { error: "删除声音模型失败", success: "删除声音模型成功！" },
  });
  return result;
}
/** [v1]删除数字人多媒体信息表信息接口 */
export async function videoDelete(id: number) {
  const result = await GetService({
    url: `${media_model_delete}${id}`,
    customMessage: { error: "删除视频模型失败", success: "删除视频模型成功！" },
  });
  return result;
}
/** [v1]视频转码接口 */
export async function videoTranscode(id: any) {
  const result = await PostService({ url: `${media_model_transcode}${id}` });
  return result;
}

/** [v1]保存数字人多媒体信息表信息接口 */
export async function saveMediaModel(body: any) {
  const result = await PostService({ url: media_model_save, body: body });
  return result;
}

/** [v1]保存声音模型信息表信息接口 */
export async function saveVoiceModel(body: any) {
  const result = await PostService({ url: voice_model_save, body: body });
  return result;
}

/** 上传通用待训练文件接口(支持批量上传) */
export async function commonUploadFile(body: any) {
  const result = await PostService({
    url: common_file_upload,
    body: body,
    headers: {
      "Content-Type": `multipart/form-data;chartset=utf-8;boundary=${Math.random().toString()}`,
    },
  });
  return result;
}
// interface saveImageToModalProps {
//   image_url: string;
//   model_type: string;
//   speech_style?: string;
//   interview_style?: string;
//   duration: string;
//   appearance_note?: string;
//   status?: number;
// }
/** 保存图片制作专属模型信息接口 */
// export async function saveImageToModal(body: saveImageToModalProps) {
//   const result = await PostService({
//     url: admin_image_save,
//     body: body,
//   });
//   return result;
// }
interface saveVirtualProps {
  model_type: string;
  speech_style?: string;
  interview_style?: string;
  duration: string;
  appearance_note: string;
}
/** 保存超虚拟人模型制作信息接口 */
export async function saveVirtual(body: saveVirtualProps) {
  const result = await PostService({
    url: admin_virtual_save,
    body: body,
  });
  return result;
}
interface findImageMoalProps {
  query: null | {
    pid?: number;
    model_type?: string;
    speech_style?: string;
    interview_style?: string;
    status?: number;
    del_flag?: number;
  };
  pager: {
    page_num: number;
    page_size: number;
    total_page?: number;
    total_record?: number;
  };
  sorter?: {
    orders: { direction?: "desc" | "asc"; property?: string }[];
  };
}
/** 查询图片制作专属模型信息带分页接口 */
export async function findImageMoal(body: findImageMoalProps) {
  const result = await PostService({
    url: admin_image_find,
    body: body,
  });
  return result;
}
interface findVirtualProps {
  query: null | {
    pid?: number;
    model_type?: string;
    speech_style?: string;
    interview_style?: string;
    status?: number;
    del_flag?: number;
  };
  pager: {
    page_num: number;
    page_size: number;
    total_page?: number;
    total_record?: number;
  };
  sorter?: {
    orders: { direction?: "desc" | "asc"; property?: string }[];
  };
}
/** 查询超虚拟人模型制作信息带分页接口  delete*/
export async function findVirtual(body: findVirtualProps) {
  const result = await PostService({
    url: admin_virtual_find,
    body: body,
  });
  return result;
}

interface PaymentModalProps {
  pay_type: string;
  product_id: number;
  param: {
    image_url: string;
    model_type: string;
    speech_style?: string;
    interview_style?: string;
    duration: string;
    appearance_note?: string;
    status?: number;
  };
}

/** 成功支付图片生产 */
export async function paymentInit(body: PaymentModalProps) {
  const result = await PostService({
    url: admin_payment_init,
    body: body,
  });
  return result;
}
/** 查询支付状态 */
export async function paymentQuery(out_trade_no: string) {
  const result = await GetService({
    url: `${admin_payment_query}/${out_trade_no}`,
  });
  return result;
}
interface findOrderProps {
  pager: {
    page_num: number;
    page_size: number;
  };
  query: {
    customer_id: number;
  };
}
/** [v1][payment][order][find]分页查询支付订单表 */
export async function adminPaymentOrderFind(body: findOrderProps) {
  const result = await PostService({
    url: admin_payment_order_find,
    body: body,
  });
  return result;
}
