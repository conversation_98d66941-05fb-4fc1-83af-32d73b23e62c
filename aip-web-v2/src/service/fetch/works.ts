import { PostService, GetService } from "../base";
import API_URL from "./api";
const { work_findAll, work_update, work_userUpdate, work_delete } = API_URL;
/**
 * 获取作品信息
 */
export async function getWork(queryBody: object) {
  const result = await PostService({ url: work_findAll, body: queryBody });
  return result;
}

/**
 * 更新作品信息
 */
export async function upDateWork(queryBody: object) {
  const result = await PostService({ url: work_update, body: queryBody });
  return result;
}

/**
 * 用户手动更新口播稿内容
 * delete
 */
export async function upDateOral(queryBody: object) {
  const result = await PostService({ url: work_userUpdate, body: queryBody });
  return result;
}

/**
 * 删除作品信息
 */
export async function deleteWork(id: number) {
  const result = await GetService({ url: `${work_delete}${id}` });
  return result;
}
