import { GetService, DeleteService, PostService } from "../base";
import API_URL from "./api";
import { KnowledgeBaseResponse } from "@/types/knowledgeBase";
const {
  knowledge_search,
  knowledge_delete,
  rpa_create_task,
  knowledge_add,
  knowledge_files,
  // knowledge_files_delete,
  delete_embedding,
} = API_URL;
/**
 * 获取个人知识库列表
 * @param {ipId} 账号id string
 */
export async function getPersonalKnowledgeClassify(
  ipId: string
): Promise<KnowledgeBaseResponse> {
  const timestamp = Date.now();
  const url = `${knowledge_search}${ipId}${
    ipId.includes("?") ? "&" : "?"
  }t=${timestamp}`;
  const result = await GetService({ url });
  return result;
}
/**
 * 解绑链接
 * @param {account_id} 账号id
 */
export async function unLinkData(account_id: string) {
  const result = await DeleteService({
    url: `${knowledge_delete}${account_id}`,
    customMessage: {
      error: "解绑失败",
      success: "解绑成功",
    },
  });
  return result;
}

type linkParams = {
  zhihu_url: string;
  num: number;
  pid: number;
  knowledge_id: number;
};
/**
 * 添加内容
 * @param {queryBody} 查询参数
 * {zhihu_url: string;num: number;pid: number;knowledge_id: number;}
 */
export async function linkPersonalData(queryBody: linkParams) {
  const result = PostService({
    url: `${rpa_create_task}`,
    body: {
      robot_name: "zhihu_qa_bot",
      robot_params: JSON.stringify(queryBody),
    },
    customMessage: {
      success: "添加内容成功",
      error: "添加内容失败！",
    },
  });
  return result;
}
/**
 * 添加社交媒体账号
 * @param {body} 查询参数
 */
export async function postAddMedia(body: object) {
  const result = await PostService({
    url: `${knowledge_add}`,
    body,
    customMessage: {
      error: "解析失败",
      success: "添加成功！后台将自动学习账号作品内容",
    },
  });
  // const data = await api.post(
  //   `/api/social-media/accounts`,
  //   body,
  //   {},
  //   { error: "解析失败", success: "添加成功！后台将自动学习账号作品内容" }
  // );
  return result;
}
/**
 * 获取个人知识库文件列表
 * @param {queryBody} 查询参数
 */
export async function getPersonalData(queryBody: object) {
  const result = await PostService({
    url: `${knowledge_files}`,
    body: queryBody,
  });
  // const data = await api.post(`/api/personal-knowledge/files`, queryBody);
  return result;
}
interface DeleteEventProps {
  pid: number;
  file_id: number;
}
/**
 * 删除个人知识库文件
 * @param params 删除参数
 */
export async function deletePersonalData(params: DeleteEventProps) {
  const result = await PostService({
    url: `${delete_embedding}`,
    body: params,
  });
  return result;
}
