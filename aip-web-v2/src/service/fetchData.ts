import {
  loginService,
  LoginParams,
  getUserInfoService,
  getIpListService,
  getCurrentIpService,
  getPhoneVerifyCode,
  updatePassword,
} from "./fetch/login";
import {
  getHotList,
  getUploadInfo,
  getPersonalKnowledgeGraph,
  precontractCreate,
} from "./fetch/home";
import { getLatesTask } from "./fetch/slider";
import {
  getVideoList,
  getAudioList,
  voiceModelCreate,
  voiceModelUpdate,
  mediaModelUpdate,
  audioClone,
  audioDelete,
  videoDelete,
  videoTranscode,
  saveVoiceModel,
  saveMediaModel,
  commonUploadFile,
  paymentInit,
  paymentQuery,
  saveVirtual,
  findImageMoal,
  findVirtual,
  adminPaymentOrderFind,
} from "./fetch/privateMode";
import {
  getPersonalKnowledgeClassify,
  unLinkData,
  linkPersonalData,
  postAddMedia,
  getPersonalData,
  deletePersonalData,
} from "./fetch/knowledgeBase";

import { getWork, upDateWork, upDateOral, deleteWork } from "./fetch/works";
import {
  mockWorkflowStart,
  mockWorkflowContinue,
  getWorkflowData,
  GetWorkflowDataProps,
  postWorkflowContinue,
  getComments,
  getTaskList,
  getWorkHistory,
  getOralData,
  changeAudioSpeed,
  updateSpeedHistory,
  checkAdd,
  taskQuery,
  MCPStart,
} from "./fetch/workflow";

export {
  loginService,
  getPhoneVerifyCode,
  updatePassword,
  getUserInfoService,
  getIpListService,
  getCurrentIpService,
  getHotList,
  getUploadInfo,
  getLatesTask,
  getVideoList,
  getAudioList,
  voiceModelCreate,
  voiceModelUpdate,
  mediaModelUpdate,
  audioClone,
  audioDelete,
  videoDelete,
  videoTranscode,
  saveVoiceModel,
  saveMediaModel,
  commonUploadFile,
  getPersonalKnowledgeClassify,
  unLinkData,
  linkPersonalData,
  postAddMedia,
  getPersonalData,
  deletePersonalData,
  changeAudioSpeed,
  updateSpeedHistory,
  checkAdd,
  taskQuery,
  findImageMoal,
  findVirtual,
  saveVirtual,
  MCPStart,
  // work
  getWork,
  upDateWork,
  upDateOral,
  deleteWork,
  getWorkflowData,
  getComments,
  getTaskList,
  getWorkHistory,
  getOralData,
  postWorkflowContinue,
  mockWorkflowStart,
  mockWorkflowContinue,
  paymentInit,
  paymentQuery,
  adminPaymentOrderFind,
  getPersonalKnowledgeGraph,
  precontractCreate,
};
export type { LoginParams, GetWorkflowDataProps };
