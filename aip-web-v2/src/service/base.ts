import { GetServiceUrl, GetToken } from "./config";
import { CustomMessage, RequestConfig, FetchRequest } from "@/typing/fetchType";
const ERRORCODE = {
  401: "未授权",
  403: "禁止访问",
  404: "请求资源不存在",
  500: "服务器内部错误",
  501: "服务器未实现",
  502: "网关错误",
  503: "服务不可用",
  504: "网关超时",
  505: "http版本不支持该请求",
};
const BASE_URL = GetServiceUrl();
class FetchWrapperEvent {
  private cacheCustomMessage: CustomMessage | undefined = undefined;
  constructor() {}
  /** 请求之前拦截 */
  private requestInterceptors({
    url,
    option,
  }: {
    url: string;
    option: RequestInit;
  }): Request {
    const token = GetToken();
    const options = {
      ...option,
      headers: {
        "Content-Type": "application/json",
        ...(token ? { Authorization: `Bearer ${token}` } : {}),
        ...option.headers,
      },
    };
    return new Request(url, options);
  }
  /** 拦截数据处理 */
  private async responseInterceptors(response: Response) {
    let jsonData;
    try {
      const contentType = response.headers.get("Content-Type");
      console.log("contentType", contentType);

      // 流式数据请求
      if (contentType && contentType.includes("text/event-stream")) {
        return response.body?.getReader();
      } else {
        // 保存json数据，这样只需要读取一次
        jsonData = await response.json();
      }

      // 请求错误处理
      if (!response.ok) {
        // 传入已读取的JSON数据
        this.failHandle(response, jsonData);
      }

      return jsonData;
    } catch (error) {
      console.log(error);
      throw error;
    }
  }
  /** 请求失败拦截 */
  private failHandle(response: Response, jsonData: any) {
    try {
      let message = ERRORCODE[response.status as keyof typeof ERRORCODE];
      if (!message) {
        message = "未知错误";
      }
      // 全局提示报错
      // 重新登录
      if (response.status === 401) {
        // localStorage.clear();
        localStorage.removeItem("AIPGPT_TOKEN");
        location.href = "/app/home";
        return;
      }

      // 使用已经读取的JSON数据
      console.log("error:", jsonData);
      throw new Error(jsonData?.detail || message || "Something went wrong");
    } catch (error) {
      console.log(error);
      throw error;
    }
  }
  /** 代码报错拦截 */
  private errorHandle(error: any) {
    console.log("Fetch error:", error);
    throw error;
  }
  /** fetch 请求 */
  async fetchMain(options: FetchRequest) {
    const { url, option, customMessage } = options;
    // 错误自定义提示信息
    if (customMessage) {
      this.cacheCustomMessage = customMessage;
    }
    const request = this.requestInterceptors({
      url: `${BASE_URL}${url}`,
      option,
    });

    const response = await fetch(request);
    const responseResult = await this.responseInterceptors(response);
    return responseResult;
  }
}
/** 实例化 */
const fetchWrapperEvent = new FetchWrapperEvent();
/**
 * Get 请求
 * params: url 请求地址 option 请求参数RequestInit类型
 */
export const GetService = async ({
  url,
  headers = {},
  others = {},
  customMessage,
}: RequestConfig) => {
  const options = {
    headers,
    method: "GET",
    ...others,
  };
  return await fetchWrapperEvent.fetchMain({
    url,
    option: { ...options } as unknown as RequestInit,
    customMessage,
  });
};

/**
 * Delete 请求
 * params: url 请求地址 option 请求参数RequestInit类型
 */
export const DeleteService = async ({
  url,
  headers = {},
  others = {},
  customMessage,
}: RequestConfig) => {
  const options = {
    headers,
    method: "DELETE",
    ...others,
  };
  return await fetchWrapperEvent.fetchMain({
    url,
    option: { ...options } as unknown as RequestInit,
    customMessage,
  });
};

/**
 * Post 请求
 * params:
 *  url 请求地址
 *  others 其他数据信息
 *  body 请求参数
 *  customMessage：{success: string; error: string;} 自定义错误信息
 */
export const PostService = async ({
  url,
  headers = {},
  others = {},
  body = {},
  customMessage,
}: RequestConfig) => {
  const options = {
    method: "post",
    body: JSON.stringify(body ?? {}, null, 2),
    headers,
    ...others,
  };
  console.log(options);
  return await fetchWrapperEvent.fetchMain({
    url,
    option: { ...options } as RequestInit,
    customMessage,
  });
};

/**
 * Post 请求  mock
 * 获取流数据
 */
export const PostServiceStream = async ({
  query,
}: {
  tid: string;
  query: string;
}) => {
  const response = await fetch(
    `http://*************:8000/agents/v2/start_creation`,
    {
      method: "post",
      headers: {
        "Content-Type": "application/json",
        Authorization:
          "bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJqYWNsbGEiLCJleHAiOjE3MzU5ODE5NjV9.GflwymgL_34G3ttcKh3VfMRWNrnZhE_6A-Q7iKvZNuY",
      },
      body: JSON.stringify(
        {
          query,
          pid: "18",
        },
        null,
        2
      ),
      // signal: signal,
    }
  );
  console.log("response:", response);
  return response.body?.getReader();
};

/**
 * Post 请求 mock
 * 获取流数据打断
 */
// "http://test-xinhou-aip-admin.aipgpt.cn/agents/v1/stop_task",
// export const PostServiceStreamStop = async (tid: any) => {
//   console.log("=====");
//   const response = await fetch(
//     "http://*************:8000/agents/v1/stop_task",
//     {
//       method: "post",
//       headers: {
//         "Content-Type": "application/json",
//         Authorization:
//           "bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJqYWNsbGEiLCJleHAiOjE3MzU5ODE5NjV9.GflwymgL_34G3ttcKh3VfMRWNrnZhE_6A-Q7iKvZNuY",
//       },
//       body: JSON.stringify({ id: `${tid}` }),
//     }
//   );
//   console.log("response:", response);
//   return response;
// };

/**
 * Post 请求 mock
 * 获取流数据打断
 */
// export const PostServiceStreamContinue = async ({
//   tid,
//   query,
// }: {
//   tid: string;
//   query: any;
// }) => {
//   const response = await fetch(
//     "http://*************:8000/agents/v2/continue_creation",
//     {
//       method: "post",
//       headers: {
//         "Content-Type": "application/json",
//         Authorization:
//           "bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJqYWNsbGEiLCJleHAiOjE3MzUyOTMyMjJ9.OrhwpxQDRtLQVaTMj_WRgQ0FXSQ_Dy_pRKF2_xI6-ZE",
//       },
//       body: JSON.stringify({
//         task_id: Number(tid),
//         pid: "18",
//         query,
//       }),
//     }
//   );
//   console.log("response:", response);
//   return response.body?.getReader();
// };
