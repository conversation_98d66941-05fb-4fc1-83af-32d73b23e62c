function isBrowserEnvironment() {
  return (
    typeof window !== "undefined" && typeof window.document !== "undefined"
  );
}
/** 获取当前请求域名前缀 */
export const GetServiceUrl = () => {
  // console.log("process.env.SERVER_BASE_URL:", process.env.SERVER_BASE_URL);
  /**
   * 主域名 包含 aipgpt.cn 和 aipgpt.com
   * 环境包括 uat、test、development
   * 则使用我们的自己服务器，
   * 只有生产环境才根据域名来判断
   * 其他环境使用其他人的服务器域名
   */
  const env = process.env.NODE_ENV;
  if (env === "production") {
    if (isBrowserEnvironment()) {
      const locationURL = location.host;
      if (
        locationURL.includes("aipgpt.cn") ||
        locationURL.includes("aipgpt.com")
      ) {
        return process.env.SERVER_BASE_URL;
      } else {
        return `https://admin.${location.host}`;
      }
    } else {
      return process.env.SERVER_BASE_URL;
    }
  } else {
    return process.env.SERVER_BASE_URL;
  }
};

/** 获取token */
export const GetToken = () => {
  return localStorage.getItem("AIPGPT_TOKEN");
};
/** 设置token */
export const SetToken = (token: string) => {
  localStorage.setItem("AIPGPT_TOKEN", token);
};
/* 清楚token**/
export const ClearToken = () => {
  localStorage.removeItem("AIPGPT_TOKEN");
};
