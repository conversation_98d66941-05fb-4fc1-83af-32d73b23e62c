/* 深色主题弹出层样式 */
.dark-theme-modal .ant-modal-content {
  @apply bg-[#27272A] border border-white/5;
}

.dark-theme-popover .ant-popover-inner {
  @apply bg-[#27272A] border border-white/5 rounded-lg shadow-lg;
}

.dark-theme-picker {
  @apply bg-[#27272A] border-white/5 text-white/90;
}

.dark-theme-picker .ant-picker-panel {
  @apply bg-[#27272A] border-white/5;
}

.dark-theme-picker .ant-picker-cell {
  @apply text-white/60;
}

.dark-theme-picker .ant-picker-cell-in-view {
  @apply text-white/90;
}

.dark-theme-picker .ant-picker-cell:hover .ant-picker-cell-inner {
  @apply bg-[#3F3F46];
}

.ant-dropdown .ant-dropdown-menu {
  @apply bg-transparent border-none;
}

.ant-dropdown .ant-dropdown-menu-item {
  @apply transition-colors duration-150 ease-in-out;
}

.ant-dropdown .ant-dropdown-menu-item:hover {
  @apply bg-[#3F3F46];
}
