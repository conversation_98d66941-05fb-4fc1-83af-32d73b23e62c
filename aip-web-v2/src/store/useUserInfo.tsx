import { create } from "zustand";
import { persist, createJSONStorage } from "zustand/middleware";
import { UserInformation, CurrentIpInformation } from "@/typing/types";

interface UserInfoStore {
  token: string;
  userInfo: UserInformation;
  currentIpUser: CurrentIpInformation;
  currentIpList: CurrentIpInformation[];
  updateUserInfo: (userInfo: UserInformation) => void;
  updateCurrentIpUser: (currentIpUser: CurrentIpInformation) => void;
  updateCurrentIpList: (currentIpList: CurrentIpInformation[]) => void;
  updateToken: (token: string) => void;
}

export const useUserInfoStore = create(
  persist<UserInfoStore>(
    (set) => ({
      token: "",
      userInfo: {} as UserInformation,
      currentIpUser: {} as CurrentIpInformation,
      currentIpList: [] as CurrentIpInformation[],
      updateUserInfo: (userInfo: UserInformation) => set(() => ({ userInfo })),
      updateCurrentIpUser: (currentIpUser: CurrentIpInformation) =>
        set(() => ({ currentIpUser })),
      updateCurrentIpList: (currentIpList: CurrentIpInformation[]) =>
        set(() => ({ currentIpList })),
      updateToken: (token: string) => set(() => ({ token })),
    }),
    {
      name: "AIPGPT_USER_INFO",
      storage: createJSONStorage(() => localStorage),
    }
  )
);
