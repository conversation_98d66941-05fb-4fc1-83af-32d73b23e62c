import { create } from "zustand";
import { IInformation } from "@/typing/Workflow";
import { LT } from "@/typing/types";
export interface startParamsProps {
  pid: number;
  task_id?: number;
  query?: string;
  status?: number;
  progress?: number;
  remark?: string;
  audio_model_id?: string;
  video_model_id?: string;
  task_knowledge_ids?: string;
  doc_length?: number; // 字数
  language?: LT; // 语言类型
  style?: string; // 风格
  read_score?: number; // 易读性
  is_person: number; // 启动人设
  is_search: number; // 启动搜索
  agent_uuid?: string;
  is_knowledge?: number; //知识库搜索
}

interface Oral {
  label: string;
  think_txt: string;
  think_other_txt: string;
  id: number;
  uuid: string;
}
interface ChatLoadingProps {
  chatLoading: boolean;
  updateCahtLoading: (chatLoading: boolean) => void;
}

interface workflowSliderProps {
  open: boolean;
  tabType: "oral" | "video" | "comment" | "knowledge" | "map";
}

interface useWorkflowStore extends ChatLoadingProps {
  taskId: number; // 任务id
  taskName: string; // 任务名称
  docLength: number; // 口播稿限制字数
  languageType: LT; // 语言类型
  startParams: startParamsProps;
  oralInformation: Oral;
  knowledgeGraph: { [key: string]: any };
  aiSearchData: {
    article: any[];
    image_urls: any[];
    video: any[];
    douyin?: any[];
  };
  cacheIntervalVideoTask: Map<string, string>;
  streamInterface: AbortController[];
  WResult: { [key: string]: IInformation }; // 渲染的数据
  workflowSlider: workflowSliderProps; // 控制工作流侧边栏
  knowledgeExpand: any[]; // 知识库数据
  updateKnowledgeExpand: (val: any[]) => void; // 更新知识库数据
  updateKnowledgeGraph: (val: { [key: string]: any }) => void;
  updateCacheIntervalVideoTask: (val: string) => void;
  clearCacheIntervalVideoTask: (val: string) => void;
  addStreamInterface: (item: AbortController) => void;
  clearStreamInterfance: () => void;
  updateTaskId: (taskId: number) => void;
  updateDocLength: (l: number) => void;
  updateLanguageType: (l: LT) => void;
  updateTaskName: (taskName: string) => void;
  updateOralText: (oral: Oral) => void;
  updateWResult: (WResult: { [key: string]: IInformation }) => void;
  updateAiSearchData: (aiSearchData: {
    article: any[];
    image_urls: any[];
    video: any[];
    douyin?: any[];
  }) => void;
  setTaskKnowledgeParams: (startParams: startParamsProps) => void;
  initTaskKnowledgeParams: () => void;
  updateWorkflowSlider: (data: workflowSliderProps) => void;
}

const charLoading = {};
const useWorkflowStore = create<useWorkflowStore>((set, get) => ({
  ...charLoading,
  taskId: 0,
  taskName: "",
  docLength: 1500,
  languageType: "zh",
  startParams: {
    pid: 0,
    doc_length: 270,
    language: "zh",
    style: "",
    read_score: 0,
    is_person: 1,
    is_search: 0,
    is_knowledge: 0, //知识库搜索
  },
  oralInformation: {
    think_txt: "",
    think_other_txt: "",
    label: "",
    id: 0,
    uuid: "",
  },
  knowledgeGraph: {},
  aiSearchData: {
    article: [],
    image_urls: [],
    video: [],
  },
  chatLoading: false,
  WResult: {},
  workflowSlider: {
    open: false,
    tabType: "oral",
  },
  streamInterface: [],
  cacheIntervalVideoTask: new Map(),
  knowledgeExpand: [],
  updateKnowledgeExpand: (val) =>
    set(() => {
      return {
        knowledgeExpand: val,
      };
    }),
  updateKnowledgeGraph: (val) =>
    set((state) => {
      return {
        knowledgeGraph: val,
      };
    }),
  updateCacheIntervalVideoTask: (val) =>
    set((state) => {
      const data = get().cacheIntervalVideoTask;
      data.set(val, "true");
      return {
        cacheIntervalVideoTask: data,
      };
    }),
  clearCacheIntervalVideoTask: (val) =>
    set((state) => {
      const data = get().cacheIntervalVideoTask;
      data.delete(val);
      return {
        cacheIntervalVideoTask: data,
      };
    }),
  addStreamInterface: (item) =>
    set((state) => {
      const streams = get().streamInterface;
      streams.push(item);
      return {
        streamInterface: streams,
      };
    }),
  clearStreamInterfance: () =>
    set((state) => {
      const streams = get().streamInterface;
      streams.forEach((item) => {
        if (!item.signal.aborted) {
          try {
            item.abort();
          } catch (error) {
            console.log(error);
          }
        }
      });
      return {
        streamInterface: [],
      };
    }),
  updateTaskId: (id) =>
    set((state) => {
      return { taskId: id };
    }),
  updateDocLength: (l) => set((state) => ({ docLength: l })),
  updateLanguageType: (lt) => set((state) => ({ languageType: lt })),
  updateTaskName: (taskName) =>
    set((state) => {
      return { taskName: taskName };
    }),
  updateOralText: (oral: Oral) => set((state) => ({ oralInformation: oral })),
  updateWResult: (WResult) => set((state) => ({ WResult: WResult })),
  updateAiSearchData: (aiSearchData) =>
    set((state) => ({ aiSearchData: aiSearchData })),
  setTaskKnowledgeParams: (params: startParamsProps) =>
    set((state) => ({ startParams: { ...get().startParams, ...params } })),
  initTaskKnowledgeParams: () =>
    set((state) => ({
      startParams: {
        pid: 0,
        doc_length: 270,
        language: "zh",
        style: "",
        read_score: 0,
        is_person: 1,
        is_search: 0,
        is_knowledge: 0, //知识库搜索
      },
    })),
  updateCahtLoading: (chatLoading: boolean) =>
    set((state) => ({ chatLoading: chatLoading })),
  updateWorkflowSlider: (data: workflowSliderProps) =>
    set((state) => ({
      workflowSlider: data,
    })),
}));

export default useWorkflowStore;
