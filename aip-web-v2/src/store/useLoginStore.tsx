import { create } from "zustand";
import { persist, createJSONStorage } from "zustand/middleware";
interface loginStore {
  isLogin: boolean;
  isLoginOpen: boolean;
  updateIsLogin: (isLogin: boolean) => void;
  updateIsLoginOpen: (isLoginOpen: boolean) => void;
}

const useLoginStore = create<loginStore>()(
  persist(
    (set) => ({
      isLogin: false,
      isLoginOpen: false,
      updateIsLogin: (isLogin: boolean) => set({ isLogin }),
      updateIsLoginOpen: (isLoginOpen: boolean) => set({ isLoginOpen }),
    }),
    {
      name: "aipgpt_loged", // localStorage中的key
      storage: createJSONStorage(() => localStorage), // 使用localStorage
    }
  )
);

export default useLoginStore;
