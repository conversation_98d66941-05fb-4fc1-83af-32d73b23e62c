import { updateIpPromptA<PERSON> } from "@/service/fetch/ip_prompt";
import { create } from "zustand";

interface IPPersonalStore {
  isIPPersonalSave: boolean;
  jsonInfo: any;
  handlePageLeave?: (targetPath?: string) => Promise<boolean>;
  setIsIPPersonalSave: (isIPPersonalSave: boolean) => void;
  setJsonInfo: (jsonInfo: any) => void;
  saveJsonInfo: (userId: string, jsonInfo: any) => Promise<void>;
}

const useIPPersonalStore = create<IPPersonalStore>()((set, get) => ({
  isIPPersonalSave: true,
  jsonInfo: {},
  handlePageLeave: undefined,
  setIsIPPersonalSave: (isIPPersonalSave: boolean) => set({ isIPPersonalSave }),
  setJsonInfo: (jsonInfo: any) => set({ jsonInfo }),
  saveJsonInfo: async (userId: string, jsonInfo: any) => {
    try {
      await updateIpPromptApi({
        id: userId,
        json: jsonInfo,
      });
      set({ isIPPersonalSave: false });
    } catch (error) {
      console.error("保存失败:", error);
      set({ isIPPersonalSave: false });
    }
  },
}));

export default useIPPersonalStore;
