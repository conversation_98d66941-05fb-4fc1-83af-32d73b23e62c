import { boolean } from "zod";
import { create } from "zustand";
interface ModalStoreProps {
  demoOpen: {
    picToVideo: boolean;
    virtual: boolean;
    // history: boolean;
  };
  updateDemoOpen: (picToVideo: boolean) => void;
  updateVirtalOpen: (virtual: boolean) => void;
  // updateHistoryOpen: (history: boolean) => void;
}
const useModalStore = create<ModalStoreProps>()((set, get) => ({
  demoOpen: {
    picToVideo: true,
    virtual: true,
    // history: false,
  },
  updateVirtalOpen: (virtual) =>
    set(() => ({
      demoOpen: {
        picToVideo: get().demoOpen.picToVideo,
        // history: get().demoOpen.history,
        virtual: virtual,
      },
    })),
  updateDemoOpen: (picToVideo) =>
    set(() => ({
      demoOpen: {
        picToVideo: picToVideo,
        // history: get().demoOpen.history,
        virtual: get().demoOpen.virtual,
      },
    })),
  // updateHistoryOpen: (history) =>
  //   set(() => ({
  //     demoOpen: {
  //       picToVideo: get().demoOpen.picToVideo,
  //       history: history,
  //       virtual: get().demoOpen.virtual,
  //     },
  //   })),
}));

export default useModalStore;
