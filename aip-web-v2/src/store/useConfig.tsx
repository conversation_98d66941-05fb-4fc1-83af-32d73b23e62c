import { create } from "zustand";
import { persist, createJSONStorage } from "zustand/middleware";
import { UserInformation, CurrentIpInformation, LT } from "@/typing/types";

interface NotificationProps {
  type: "success" | "info" | "warning" | "error";
  message: string;
  description: string;
  key: number;
}
interface ConfigStore {
  notificationInformation: NotificationProps;
  updateNotificationInformation: (
    notificationInformation: NotificationProps
  ) => void;
}

export const useConfigStore = create<ConfigStore>((set) => ({
  notificationInformation: {
    type: "info",
    message: "",
    description: "",
    key: 0,
  },
  updateNotificationInformation: (notificationInformation: NotificationProps) =>
    set({ notificationInformation }),
}));

interface SettingProps {
  language?: { key: LT; label: string; text: string };
  doc_length?: { key: number; label: string; text: string };
  style?: { key: string; label: string; text: string };
  read_score?: { key: number; label: string; text: string };
  is_person?: number;
  is_search?: number;
  is_knowledge?: number;
  is_rag?: number;
}
interface PersistenceConfigProps {
  homeSetting: { [key: string]: SettingProps };
  updateHomeByPid: ({
    pid,
    value,
  }: {
    pid: string;
    value: SettingProps;
  }) => void;
  updateHomeSetting: ({
    pid,
    key,
    value,
  }: {
    pid: string;
    key:
      | "language"
      | "doc_length"
      | "style"
      | "read_score"
      | "is_person"
      | "is_search"
      | "is_knowledge"
      | "is_rag";
    value: any;
  }) => void;
}
export const useGlobalConfigStore = create<PersistenceConfigProps>()(
  persist(
    (set, get) => ({
      homeSetting: {},
      /** 更新配置数据 */
      updateHomeSetting: ({ pid, key, value }) =>
        set((state) => {
          let data = get().homeSetting[pid];
          // 判断 更新创建信息
          if (data) {
            data = {
              ...data,
              [key]: value,
            };
          } else {
            data = {
              language: { key: "zh", label: "中文", text: "中文" },
              doc_length: { key: 270, label: "60s", text: "60s" },
              style: { key: "", label: "默认", text: "默认" },
              read_score: { key: 0, label: "默认", text: "默认" },
              is_person: 1,
              is_search: 0,
              is_knowledge: 0,
              is_rag: 0,
              [key]: value,
            };
          }
          const d = get().homeSetting;
          d[pid] = data;
          return {
            homeSetting: d,
          };
        }),
      updateHomeByPid: ({ pid, value }) =>
        set((state) => {
          let data = get().homeSetting;
          // 判断 更新创建信息
          if (data[pid]) {
            let pidData = data[pid];
            pidData = {
              ...pidData,
              ...value,
            };
          } else {
            let pidData = {
              language: { key: "zh" as LT, label: "中文", text: "中文" },
              doc_length: { key: 270, label: "60s", text: "60s" },
              style: { key: "", label: "默认", text: "默认" },
              read_score: { key: 0, label: "默认", text: "默认" },
              is_person: 1,
              is_search: 0,
              is_knowledge: 0,
              is_rag: 0,
              ...value,
            };
            data[pid] = pidData;
          }
          return {
            homeSetting: data,
          };
        }),
    }),
    {
      name: "PersistenceConfig", // localStorage中的key
      storage: createJSONStorage(() => localStorage), // 使用localStorage
    }
  )
);
