export interface UserInformation {
  login_pwd: string;
  user_type?: number;
  login_name?: string;
  phone?: string;
  mobile?: string;
  avatar?: string;
  del_flag?: number;
  id?: number;
  created_at?: string;
  updated_at?: string;
  email?: string | null;
  user_name?: string;
  sex?: string;
  status?: string;
  remark?: any;
  create_by?: string | null;
  update_by?: string | null;
}
export interface CurrentIpInformation {
  ip_name: string;
  avatar: string;
  del_flag: number;
  group_id: number | null;
  remain_point: number;
  id: number;
  created_at: string;
  updated_at: string;
  status: number;
  uid: number;
  remark: string;
  group_name: string | null;
  expire_time: string;
  create_by: string | null;
  update_by: string | null;
}
export type PlanType = "LOGIN" | "VERIFY" | "UPDATE" | "ContactUs" | "SUCCESS";
export interface VerifyDataType {
  mobile?: string;
  verify_code?: string;
  new_password?: string;
}
