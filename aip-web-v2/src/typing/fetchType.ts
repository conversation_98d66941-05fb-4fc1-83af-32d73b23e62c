export type HttpMethod = "GET" | "POST" | "PUT" | "DELETE";
export interface CustomMessage {
  success: string;
  error: string;
}
export interface RequestConfig {
  url: string;
  headers?: { [key: string]: any };
  others?: { [key: string]: any };
  body?: { [key: string]: any };
  customMessage?: CustomMessage;
}
export interface FetchRequest {
  url: string;
  option: RequestInit;
  customMessage?: CustomMessage;
}
