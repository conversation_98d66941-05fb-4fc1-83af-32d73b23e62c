export type LT =
  | "zh"
  | "zh-Hant"
  | "en"
  | "es"
  | "fr"
  | "de"
  | "ja"
  | "ru"
  | "it"
  | "pl"
  | "th"
  | "hi"
  | "id"
  | "pt"
  | "ko"
  | "vi";
export interface IInformation {
  id?: any;
  agent_name_cn: string;
  agent_style: string;
  agent_id: number;
  content: any;
  code?: "done" | null;
  last?: boolean;
  agent_uuid?: string;
  work_id?: number;
  uuid: string;
  task_id?: number;
  agent_action?: string;
  think_txt?: string;
  think_other_txt?: string;
  language?: LT;
}
// 评论接口
export interface ICommentData {
  id: number;
  task_id: number;
  comment_type: ECommentType;
  content: string;
  status: ECommentStatus;
  remark: string;
  create_by: string;
  created_at: string;
  update_by: string;
  updated_at: string;
}
// 评论类型 评论类型:1=作者自评,2=观众-搞笑,3=观众-讽刺,3=观众-鼓励,4=朋友圈文案,5=群聊转发
export enum ECommentType {
  AUTHOR = 1,
  FUNNY = 2,
  SATIRE = 3,
  ENCOURAGE = 4,
  FRIENDS = 5,
  GROUP = 6,
}
// 评论状态:1=正常,2=待审核,3=已删除
export enum ECommentStatus {
  NORMAL = 1,
  PENDING = 2,
  DELETED = 3,
}
export interface TaskFindAll {
  pid: number;
  status: number;
  del_flag: number;
  progress: number;
  title: string;
  remark: string;
  id: number;
  create_by: string;
  created_at: string;
  update_by: string;
  updated_at: string;
}
