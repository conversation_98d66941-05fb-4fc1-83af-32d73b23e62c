export type MediaType = "video" | "audio";
export interface AudioItem {
  id: number;
  pid: number;
  playStatus: boolean;
  status: number;
  created_at: string;
  voice_name: string;
  voice_url: string;
  index: number;
  [key: string]: any;
}
export interface VideoItem {
  id: number;
  pid: number;
  title: string;
  pic_url: string;
  media_url: string;
  created_at: string;
  index: number;
  playStatus: boolean;
  status: number;
  resolution: {
    width: number;
    height: number;
  };
  [key: string]: any;
}
