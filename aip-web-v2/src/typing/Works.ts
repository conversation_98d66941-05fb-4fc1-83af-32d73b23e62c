enum EWorkType {
  TEXT = 1,
  AUDIO = 2,
  VIDEO = 3,
}
enum EWorkStatus {
  NORMAL = 1,
  PENDING = 2,
  DELETED = 3,
}
// 作品接口
export interface IWorkData {
  id: number;
  // 作品类型 1=文本, 2=音频, 3=视频
  work_type: EWorkType;
  title: string;
  // 作品状态: 1=正常, 2=待审核, 3=已删除
  status: EWorkStatus;
  pid: number;
  create_by: string;
  update_by: string;
  file_id: string;
  task_id: number;
  content: string;
  remark: string;
  created_at: string;
  updated_at: string;
  file_url: string;
  file_review_pic?: string;
}
