import { MCPStart } from "@/service/fetchData";

export function useMCP() {
  const MCPRunInitEvent = () => {
    const params = {
      language: "中文",
      pid: 123456,
      readability: 5,
      script_length: 270,
      style: "正式",
      user_input: "我想写一个关于人工智能的脚本",
    };
    MCPStart(params).then(
      ({
        response,
        abort,
        controller,
      }: {
        response: any;
        abort: any;
        controller: AbortController;
      }) => {
        const decoder = new TextDecoder();
        response
          .read()
          .then(({ value, done }: { value: any; done: boolean }) => {
            console.log("value:", value);
          });
      }
    );
    //   {
    //     response,
    //     abort,
    //     controller,
    //   }: {
    //     response: any;
    //     abort: any;
    //     controller: AbortController;
    //   }) =>
  };
  return {
    MCPRunInitEvent,
  };
}
