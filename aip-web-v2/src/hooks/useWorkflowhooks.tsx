"use client";
import { useEffect, useRef, useState } from "react";
import { IInformation, LT } from "@/typing/types";
import { notification } from "antd";
import { v4 as uuidv4 } from "uuid";
// import { getWorkHistory } from "@/service/fetchData";
import {
  // mockWorkflowData,
  getWorkflowData,
  GetWorkflowDataProps,
  postWorkflowContinue,
  checkAdd,
  taskQuery,
  upDateWork,
  upDateOral,
} from "@/service/fetchData";
import {
  useWorkflowStore,
  useUserInfoStore,
  useConfigStore,
} from "@/store/store";
let prev_agent_key = ``;
export function useWorkflowInformation() {
  const StartWorkflowRef = useRef<() => void>(null);
  const ContinueWorkflowRef = useRef<() => void>(null);
  const HistoryWorkflowRef = useRef<() => void>(null);
  const taskQueryRef = useRef<any>(1);
  const cacheLastAgentRef = useRef<IInformation | number>(1);
  // const intervalRef = useRef<any>(null);
  const WResultRef = useRef<{ [key: string]: IInformation }>(
    {} as { [key: string]: IInformation }
  );

  // 控制
  const [task_id, setTid] = useState<number>();
  const { currentIpUser } = useUserInfoStore((state) => state);
  const {
    updateWResult,
    updateOralText,
    updateAiSearchData,
    updateCahtLoading,
    initTaskKnowledgeParams,
    updateTaskName,
    updateTaskId,
    updateDocLength,
    updateLanguageType,
    addStreamInterface,
    updateCacheIntervalVideoTask,
    updateKnowledgeGraph,
    updateKnowledgeExpand,
  } = useWorkflowStore((state) => state);
  const updateNotificationInformation = useConfigStore(
    (state) => state.updateNotificationInformation
  );
  /**
   * 设置知识图谱数据
   */
  const setKnowledgeData = (agent: IInformation) => {
    try {
      const data = JSON.parse(agent.content);
      if (typeof data === "object") {
        updateKnowledgeGraph(data);
      } else {
        updateKnowledgeGraph({});
      }
    } catch (error) {
      updateKnowledgeGraph({});
    }
  };
  /**
   * 更新口播搞数据
   */
  const getNewOralTxt = (agent: IInformation) => {
    updateOralText({
      label: agent.content,
      think_txt: agent.think_txt || "",
      think_other_txt: agent.think_other_txt || "",
      id: agent.work_id as number,
      uuid: agent.uuid,
    });
  };
  /**
   * 初始化口播搞数据
   */
  const initNewOralTxt = () => {
    updateOralText({
      label: "",
      think_other_txt: "",
      think_txt: "",
      id: 0,
      uuid: "",
    });
  };
  /**
   * 解析知识库专家json数据
   */
  const parseKnowledgeData = (agent: IInformation) => {
    try {
      const data = JSON.parse(agent.content);
      updateKnowledgeExpand(data.person_knowledge ?? []);
      return data?.person_knowledge?.length ?? 0;
    } catch (error) {
      updateKnowledgeExpand([]);
      return 0;
    }
  };
  interface SwitchAgentOralProps {
    agent: IInformation;
    content: string;
  }
  /**
   * 润色数据分割
   */
  const SwitchAgentOral = ({
    agent,
    content,
  }: SwitchAgentOralProps): IInformation => {
    let c = `${agent?.content ?? ""}${content}`;
    agent.content = c;

    if (c.includes("<think>")) {
      const startIndex = c.includes("<think>") ? c.indexOf("<think>") : 0;
      const endIndex = c.includes("</think>")
        ? c.indexOf("</think>", startIndex) + 8
        : -1;
      agent.think_txt =
        endIndex === -1
          ? c.substring(startIndex)
          : c.substring(startIndex, endIndex);
      if (c.includes("</think>")) {
        agent.think_other_txt = c.split("</think>")[1];
      } else {
        agent.think_other_txt = "";
      }
    } else {
      agent.think_other_txt = c;
    }

    return agent;
  };
  // 更新口播稿到历史数据
  // const updateOralToHistory = (agent: IInformation) => {
  //   upDateOral({
  //     pid: currentIpUser.id,
  //     task_id: localStorage.getItem("workflow_task_id"),
  //     content: agent.content,
  //     uuid: agent.uuid,
  //   });
  // };
  /**
   * 监听上一个agent
   * 并打上code 状态
   */
  const addEndTag = () => {
    if (
      prev_agent_key &&
      WResultRef.current &&
      WResultRef.current[prev_agent_key]
    ) {
      // 判断是否是图谱Agent
      // if (WResultRef.current[prev_agent_key].agent_action === "STYLE") {
      //   setKnowledgeData(WResultRef.current[prev_agent_key]);
      // }
      // 判断是否是知识库Agent，解析知识库数据
      if (WResultRef.current[prev_agent_key].agent_action === "RAGDATA") {
        const knowledgeData = WResultRef.current[prev_agent_key];
        const length = parseKnowledgeData(knowledgeData);
        WResultRef.current[prev_agent_key].think_txt = `${length}`;
      }
      // 判断是否是润色文案Agent，用来设置口播稿
      if (WResultRef.current[prev_agent_key].agent_action === "STYLE") {
        console.log("更新口播稿", WResultRef.current[prev_agent_key]);
        getNewOralTxt(WResultRef.current[prev_agent_key]);
      }
      let WResultArr = Object.entries(WResultRef.current);
      // 判断选题是否是最后一个Agent
      for (const [key, value] of WResultArr) {
        WResultRef.current[key] = {
          ...value,
          last: value.agent_action === "CHOICE" ? true : false,
        };
      }
      // 是否是数据专家 获取素材数据
      if (WResultRef.current[prev_agent_key].agent_action === "SEARCH3") {
        initMaterialData(WResultRef.current[prev_agent_key]);
      }

      // 获取工作流语言类型 并 设置语言类型和字数
      if (WResultRef.current[prev_agent_key]?.language === "zh") {
        updateDocLength(1500);
        updateLanguageType("zh");
      } else if (
        WResultRef.current[prev_agent_key]?.language === "en" ||
        WResultRef.current[prev_agent_key]?.language === "es"
      ) {
        updateDocLength(700);
        updateLanguageType(WResultRef.current[prev_agent_key].language as LT);
      }
      // 最后一个设置结束状态
      WResultRef.current[prev_agent_key].code = "done";
    }
  };

  /** 解析素材数据 */
  const initMaterialData = (agent: IInformation) => {
    let aiSearchData = { article: [], image_urls: [], video: [] };
    if (agent) {
      try {
        const content = JSON.parse(agent.content);
        aiSearchData = {
          article: content?.link_info ?? [],
          image_urls: content?.image_urls ?? [],
          video: [],
        };
      } catch (error) {
        console.error("素材数据解析报错");
        aiSearchData = {
          article: [],
          image_urls: [],
          video: [],
        };
      }
    }
    updateAiSearchData(aiSearchData);
  };

  /**  处理解析流的数据 */
  const initWorkflowData = (json: any) => {
    console.log("mmmm", json);
    let key = json.uuid;
    setTid(json.task_id);
    updateTaskId(json.task_id);
    const WResultData = WResultRef.current;
    let thisResult: IInformation | undefined;
    // 判断当前key是否存在
    if (WResultRef.current) {
      thisResult = WResultRef.current[key];
    }
    // 判断当前有这个agent
    if (thisResult) {
      if (json.agent_action === "CHOICE") {
        thisResult.content = `${thisResult.content}_CHOICE_${json.content}`;
      } else if (json.agent_action === "STYLE") {
        const a = SwitchAgentOral({ agent: thisResult, content: json.content });
        thisResult.think_txt = a.think_txt;
        thisResult.think_other_txt = a.think_other_txt?.trim();
      } else {
        thisResult.content = `${thisResult?.content ?? ""}${json.content}`;
      }
      thisResult.work_id = json.work_id;
      WResultData[key] = thisResult;
      WResultRef.current = WResultData;
    } else {
      addEndTag();
      prev_agent_key = key;
      key = json.uuid;
      thisResult = {} as IInformation;
      thisResult = {
        agent_name_cn: json.agent_name_cn,
        agent_style: json.agent_style,
        agent_id: json.agent_id,
        content: json.content,
        id: key,
        uuid: json.uuid,
        think_txt: "",
        think_other_txt: "",
        agent_uuid: json.agent_uuid,
        work_id: json.work_id,
        task_id: json.task_id,
        agent_action: json.agent_action,
        code: null,
        language: json.language,
      };
      WResultData[key] = thisResult;
      WResultRef.current = WResultData;
      // 音频阶段、视频阶段、搜索专家阶段
      if (
        ["VOICE2", "VOICE3", "VIDEO2", "VIDEO3", "SEARCH2", "SEARCH3"].includes(
          json.agent_action
        )
      ) {
        filterAudioExpert(json.uuid);
      }
    }
    if (WResultRef.current) {
      updateWResult({ ...WResultRef.current });
    }
  };
  /** 处理解析流的数据 */
  const continueWorkflowData = (json: any) => {
    let key = json.uuid;
    setTid(json.task_id);
    const WResultData = WResultRef.current;
    let thisResult: IInformation | undefined;
    // 判断当前有这个agent
    if (WResultRef.current) {
      thisResult = WResultRef.current[key];
    }
    if (thisResult) {
      if (json.agent_action === "CHOICE") {
        thisResult.content = `${thisResult.content}_CHOICE_${json.content}`;
      } else if (json.agent_action === "STYLE") {
        const a = SwitchAgentOral({ agent: thisResult, content: json.content });
        thisResult.think_txt = a.think_txt;
        thisResult.think_other_txt = a.think_other_txt?.trim();
      } else {
        thisResult.content = `${thisResult?.content ?? ""}${json.content}`;
      }
      thisResult.work_id = json.work_id;
      WResultData[key] = thisResult;
      WResultRef.current = WResultData;
    } else {
      addEndTag();
      prev_agent_key = key;
      key = json.uuid;
      thisResult = {} as IInformation;
      thisResult = {
        uuid: json.uuid,
        agent_name_cn: json.agent_name_cn,
        agent_style: json.agent_style,
        agent_id: Number(json.agent_id),
        content: json.content,
        id: key,
        think_txt: "",
        think_other_txt: "",
        last: json.agent_action === "CHOICE" ? true : false,
        agent_uuid: json.agent_uuid,
        work_id: json.work_id,
        agent_action: json.agent_action,
        code: null,
        language: json.language,
      };
      WResultData[key] = thisResult;
      WResultRef.current = WResultData;
      // 音频阶段、视频阶段、搜索专家阶段
      if (
        ["VOICE2", "VOICE3", "VIDEO2", "VIDEO3", "SEARCH2", "SEARCH3"].includes(
          json.agent_action
        )
      ) {
        filterAudioExpert(json.uuid);
      }
    }

    if (WResultRef.current) {
      updateWResult({ ...WResultRef.current });
    }
  };
  /**
   * 音频三阶段过滤，
   * 如果出现 VOICE_2，则过滤掉 VOICE_1
   * 如果出现 VOICE_3，则过滤掉 VOICE_1,VOICE_2
   * 视频三阶段过滤
   * 如果出现 VIDEO_2，则过滤掉 VIDEO_1
   * 如果出现 VIDEO_3，则过滤掉 VIDEO_1,VIDEO_2
   * 数据专家三阶段过滤
   * 如果出现 DATA_LOADING_2，则过滤掉 DATA_LOADING_1
   * 如果出现 DATA，则过滤掉 DATA_LOADING_1,DATA_LOADING_2
   */
  const filterAudioExpert = (uuid: string) => {
    const item = WResultRef.current[uuid];
    if (item.agent_style === "VOICE_2") {
      for (const [key, value] of Object.entries(WResultRef.current)) {
        if (
          value.agent_uuid === item.agent_uuid &&
          value.agent_style === "VOICE_1"
        ) {
          delete WResultRef.current[key];
        }
      }
    }
    if (item.agent_style === "VOICE_3") {
      for (const [key, value] of Object.entries(WResultRef.current)) {
        if (
          value.agent_uuid === item.agent_uuid &&
          ["VOICE_1", "VOICE_2", "VOICE_UPLOAD"].includes(value.agent_style)
        ) {
          delete WResultRef.current[key];
        }
      }
    }
    if (item.agent_style === "VIDEO_2") {
      for (const [key, value] of Object.entries(WResultRef.current)) {
        if (
          value.agent_uuid === item.agent_uuid &&
          value.agent_style === "VIDEO_1"
        ) {
          delete WResultRef.current[key];
        }
      }
    }
    if (item.agent_style === "VIDEO_3") {
      for (const [key, value] of Object.entries(WResultRef.current)) {
        if (
          value.agent_uuid === item.agent_uuid &&
          ["VIDEO_1", "VIDEO_2"].includes(value.agent_style)
        ) {
          delete WResultRef.current[key];
        }
      }
    }
    if (item.agent_style === "DATA_LOADING_2") {
      for (const [key, value] of Object.entries(WResultRef.current)) {
        if (
          value.agent_uuid === item.agent_uuid &&
          value.agent_style === "DATA_LOADING_1"
        ) {
          delete WResultRef.current[key];
        }
      }
    }
    if (item.agent_style === "DATA") {
      for (const [key, value] of Object.entries(WResultRef.current)) {
        if (
          value.agent_uuid === item.agent_uuid &&
          ["DATA_LOADING_1", "DATA_LOADING_2"].includes(value.agent_style)
        ) {
          delete WResultRef.current[key];
        }
      }
    }
  };
  /** 解析流数据 */
  const workflowRender = async (
    reader: any,
    callback: (result: any) => void,
    abort?: () => void
  ) => {
    try {
      console.log("解析数据");
      updateCahtLoading(true);
      const decoder = new TextDecoder();
      let buffer = "";
      const eve = () => {
        reader.read().then(({ value, done }: { value: any; done: boolean }) => {
          if (done) {
            addEndTag();
            // 设置选题是最后一个Agent
            if (WResultRef.current[prev_agent_key]?.agent_action === "CHOICE") {
              WResultRef.current[prev_agent_key].last = false;
            }

            // 清除口播稿的thinking
            let values = Object.values(WResultRef.current);
            // if (values?.length > 0) {
            //   values.forEach((item) => {
            //     if (
            //       item.agent_action === "STYLE" &&
            //       item.content.includes("</think>")
            //     ) {
            //       WResultRef.current[item.uuid].content = item.content
            //         .split("</think>")[1]
            //         ?.trim();
            //       updateOralToHistory(item);
            //     }
            //   });
            // }
            // 更新最后一个agent 结束状态
            const v: any = values.at(-1);
            v["code"] = "done";
            let uuid = v?.uuid;
            if (uuid) {
              WResultRef.current[uuid] = v;
            }
            updateWResult({ ...WResultRef.current });
            prev_agent_key = "";
            // workflowDoneInterval();
            console.log("处理结束：：：", Object.values(WResultRef.current));

            if (v && v.agent_action === "VIDEO3") {
              updateCahtLoading(true);
              // 每次轮训创建一个唯一标识 和 任务id 的本次存储
              // 当切换的时候 根据 任务id 找到 唯一标识，并清除掉唯一标识
              // 轮训的时候 判断唯一标识是否存在，存在就轮训，不存在就停止轮训
              const p = uuidv4();
              localStorage.setItem(
                `QueryTask_${localStorage.getItem("workflow_task_id")}`,
                p
              );
              localStorage.setItem(`${p}`, "true");
              intervalVideoResult({
                content: v.content,
                task_id: Number(localStorage.getItem("workflow_task_id") ?? 0),
                uuid: v.uuid,
                uuidv4: p,
              });
            }
            updateCahtLoading(false);
            return;
          }
          // 解析数据流
          buffer += decoder.decode(value, { stream: true });
          const lines = buffer.split("\n");
          buffer = lines.pop() || "";
          // 循环取数据
          for (const line of lines) {
            // 判断数据
            if (line.startsWith("data: ")) {
              const jsonString = line.slice(6); // 去掉"data: "前缀
              const json = JSON.parse(jsonString);
              console.log("-------------------------------99999", json);
              callback(json);
            }
          }
          eve();
        });
      };
      eve();
    } catch (error: unknown) {
      console.log("失败");
      updateCahtLoading(false);
      console.error("Error parsing data:", error);
    }
  };
  /**
   * 首次进入请求流数据
   * @params param
   */
  const runInitEvent = (param: GetWorkflowDataProps, question: string) => {
    console.log("初始化流程数据");
    updateWResult({});
    initNewOralTxt();
    getWorkflowData(param).then(
      ({
        response,
        abort,
        controller,
      }: {
        response: any;
        abort: any;
        controller: AbortController;
      }) => {
        StartWorkflowRef.current = abort;
        // 收集当前工作流链接
        addStreamInterface(controller);
        workflowRender(response, initWorkflowData);
      }
    );
  };
  interface intervalVideoResultProps {
    content: string;
    task_id: number;
    uuid: string;
    uuidv4?: string;
  }
  /**
   * 循环任务接口
   */
  const intervalVideoResult = ({
    content,
    task_id,
    uuid,
    uuidv4,
  }: intervalVideoResultProps) => {
    try {
      const jsonMD = JSON.parse(content);
      if (jsonMD?.video_url === "") {
        // 当创建轮训之后根据任务 ID 创建停止任务状态
        // 切换之后任务状态更新
        // 然后轮训判断状态，非当前轮训的uuid停止
        // 中间可能存在3s时间间隔问题
        if (localStorage.getItem(`${uuidv4}`) !== "true") {
          clearTimeout(taskQueryRef.current);
          taskQueryRef.current = null;
          updateCahtLoading(false);
          return;
        }
        taskQueryRef.current = setTimeout(() => {
          updateCahtLoading(true);
          taskQuery(task_id).then((res) => {
            if (res.code === 200) {
              const url = res?.data?.video_url;
              if (!url) {
                // 没有地址情况下重新请求
                intervalVideoResult({
                  content,
                  task_id,
                  uuid,
                  uuidv4,
                });
              } else {
                clearTimeout(taskQueryRef.current);
                jsonMD.video_url = url;
                WResultRef.current[uuid].content = JSON.stringify(jsonMD);
                updateWResult({ ...WResultRef.current });
                updateCahtLoading(false);
              }
            }
          });
        }, 3000);
      }
    } catch (error) {}
  };
  interface runContinueEvevtProps {
    params: GetWorkflowDataProps;
    lastWResult: { [key: string]: any };
    callback?: () => void;
  }

  /**
   * 持续对话/继续操作
   * @params params
   * @params lastWResult
   */
  const runContinueEvevt = ({
    params,
    lastWResult,
    callback,
  }: runContinueEvevtProps) => {
    WResultRef.current = { ...lastWResult };
    console.log("继续");
    postWorkflowContinue(params)
      .then(({ type, response, abort, controller }) => {
        if (type === "json") {
          if (response.code !== 200) {
            updateNotificationInformation({
              key: Math.random(),
              type: "error",
              message: `${response.code}:${response.msg}`,
              description: response.data,
            });
          }
        }
        if (type === "stream") {
          // 流式返回
          ContinueWorkflowRef.current = abort;
          // 收集当前工作流链接
          addStreamInterface(controller);
          workflowRender(response, continueWorkflowData, abort);
        }
      })
      .finally(() => {
        if (callback) {
          callback();
        }
      });
  };
  interface updateAudioSpeedProps {
    uuid: string;
    speed: number;
    new_audio_url: string;
    lastWResult: { [key: string]: any };
  }
  /**
   * 更新音频速度
   * @params uuid
   */
  const updateAudioSpeed = ({
    uuid,
    speed,
    new_audio_url,
    lastWResult,
  }: updateAudioSpeedProps) => {
    const content = lastWResult[uuid]?.content;
    try {
      const data = JSON.parse(content);
      data.speed = speed;
      data.new_audio_url = new_audio_url;
      lastWResult[uuid].content = JSON.stringify(data);
      updateWResult({ ...lastWResult });
    } catch (error) {}
  };

  /**
   * 获取流程历史
   * @params task_id
   */
  const getHistoreyEvent = (task_id: number) => {
    updateWResult({});
    initNewOralTxt();
    checkAdd(task_id).then(({ response, abort, controller }) => {
      HistoryWorkflowRef.current = abort;
      // 收集当前工作流链接
      addStreamInterface(controller);
      workflowRender(response, initWorkflowData);
    });
  };
  interface senderQueryProps {
    question: string;
    params: GetWorkflowDataProps;
    lastWResult: { [key: string]: any };
    callback?: () => void;
  }
  /**
   * 获取专家回复
   * @param question
   * @param params
   */
  const senderQuery = ({
    question,
    params,
    lastWResult,
    callback,
  }: senderQueryProps) => {
    WResultRef.current = { ...lastWResult };
    postWorkflowContinue(params)
      .then(({ type, response, abort, controller }) => {
        if (type === "json") {
          if (response.code !== 200) {
            updateNotificationInformation({
              key: Math.random(),
              type: "error",
              message: `${response.code}:${response.msg}`,
              description: response.data,
            });
          }
        }
        if (type === "stream") {
          ContinueWorkflowRef.current = abort;
          // 收集当前工作流链接
          addStreamInterface(controller);
          workflowRender(response, continueWorkflowData);
        }
      })
      .finally(() => {
        if (callback) {
          callback();
        }
      });
  };

  interface synchronousOralTxtProps {
    think_other_txt: string;
    content: string;
    uuid: string;
    lastWResult: { [key: string]: any };
  }
  /**
   * 同步润色文案
   * @param
   */
  const synchronousOralTxt = ({
    think_other_txt,
    content,
    uuid,
    lastWResult,
  }: synchronousOralTxtProps) => {
    if (uuid && lastWResult[uuid]) {
      const c = lastWResult[uuid].content;
      lastWResult[uuid].content = `${think_other_txt}${content}`;
      lastWResult[uuid].think_other_txt = think_other_txt;
      getNewOralTxt(lastWResult[uuid]);
      updateWResult({ ...lastWResult });
    }
  };
  const overWorkflowTask = () => {
    // 清空当前数据
    updateWResult({});
    // 清空初始化数据
    initTaskKnowledgeParams();
  };
  useEffect(() => {
    if (task_id) {
      localStorage.setItem("workflow_task_id", task_id.toString());
    }
  }, [task_id]);
  useEffect(() => {
    return () => {
      // clearAbort();
      console.log("卸载");
    };
  }, []);
  return {
    runInitEvent,
    runContinueEvevt,
    senderQuery,
    getHistoreyEvent,
    synchronousOralTxt,
    overWorkflowTask,
    updateAudioSpeed,
  };
}
