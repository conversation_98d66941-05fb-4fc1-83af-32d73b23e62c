import { useRef, useState } from "react";
import { message, App } from "antd";
import {
  saveMediaModel,
  saveVoiceModel,
  audioClone,
  videoTranscode,
} from "@/service/fetchData";
import { GetToken } from "@/service/config";

/** 文件上传 */
export interface UploadEventType {
  files: any[];
  ip_name: string;
  fileName: string;
  url: string;
  id: number;
  cloneUrl?: string;
}

export const useVideoModeUpload = (callback: () => void) => {
  const [progress, setProgress] = useState(0);
  const [loading, setLoading] = useState(false);
  
  // 使用 App.useApp() 替代 useMessage
  const { message: messageApi } = App.useApp();
  
  const showMessage = {
    success: (content: string) => messageApi.success(content),
    error: (content: string) => messageApi.error(content),
    info: (content: string) => messageApi.info(content)
  };
  
  const [uploadParams, setParams] = useState<UploadEventType>({
    files: [],
    ip_name: "",
    fileName: "",
    url: "",
    id: 0,
  });
  const paramsRef = useRef({
    id: 0,
    fileName: "",
  });
  /** 入库 */
  const uploadSucess = (info: any, transcode: boolean) => {
    saveMediaModel({
      pid: paramsRef.current.id,
      media_url: info[0].file_url,
      title: paramsRef.current.fileName,
      transcode: transcode,
    })
      .then((res) => {
        if (res.code === 200) {
          showMessage.success("上传成功！");
          transcodeEvent(info);
        }
      })
      .finally(() => {
        setProgress(0);
        setLoading(false);
        callback();
        setProgress(0);
        setLoading(false);
        setParams({
          files: [],
          ip_name: "",
          fileName: "",
          url: "",
          id: 0,
        });
      });
  };
  /** 视频转码 */
  const transcodeEvent = (id: any) => {
    showMessage.info("视频转码中请耐心等待30秒~5分钟，再刷新");
    videoTranscode(id)
      .then((res) => {
        if (res.code) {
          showMessage.success("视频转码成功");
        } else {
          showMessage.error("视频转码失败");
        }
      })
      .finally(() => {
        setProgress(0);
        setLoading(false);
        callback();
        setProgress(0);
        setLoading(false);
        setParams({
          files: [],
          ip_name: "",
          fileName: "",
          url: "",
          id: 0,
        });
      });
  };
  const formDataObj = ({
    file,
    ip_name,
    pid,
  }: {
    file: any;
    ip_name: string;
    pid: number;
  }) => {
    const formData = new FormData();
    formData.append("file", file);
    formData.append("title", ip_name);
    formData.append("pid", `${pid}`);
    return formData;
  };
  const handleEvent = function (e: any) {
    if (e.type === "loadstart") {
      setProgress(0);
      setLoading(true);
    }
    if (e.type === "progress") {
      const rate = Number(((e.loaded / e.total) * 100).toFixed(4));
      setProgress(rate);
    }
    if (e.type === "error") {
      setProgress(0);
      setLoading(false);
    }
  };
  const uploadEve = ({
    files,
    ip_name,
    id,
    fileName,
    url,
  }: UploadEventType) => {
    return new Promise(() => {
      paramsRef.current.id = id;
      paramsRef.current.fileName = fileName;
      setParams({
        files,
        ip_name,
        id,
        fileName,
        url,
      });

      const xhr = new XMLHttpRequest();

      xhr.upload.addEventListener("loadstart", handleEvent);
      xhr.upload.addEventListener("progress", handleEvent);
      xhr.upload.addEventListener("error", handleEvent);
      xhr.responseType = "json";

      xhr.open("post", url);
      xhr.setRequestHeader("Authorization", `Bearer ${GetToken()}`);
      xhr.send(formDataObj({ file: files[0], ip_name: fileName, pid: id }));
      xhr.onreadystatechange = () => {
        if (xhr.readyState === 4) {
          if (xhr.response?.code === 200) {
            showMessage.success("上传成功！");
            transcodeEvent(xhr.response.data?.id);
          } else {
            showMessage.error("上传失败！");
          }
        }
      };
    });
  };
  return { uploadEve, progress, loading, uploadParams };
};

export const useAudioModeUpload = (callback: () => void) => {
  const [progress, setProgress] = useState(0);
  const [loading, setLoading] = useState(false);
  const { message: messageApi } = App.useApp();
  
  const showMessage = {
    success: (content: string) => messageApi.success(content),
    error: (content: string) => messageApi.error(content),
    info: (content: string) => messageApi.info(content)
  };

  const [uploadParams, setParams] = useState<UploadEventType>({
    files: [],
    ip_name: "",
    fileName: "",
    url: "",
    id: 0,
  });
  const paramsRef = useRef({
    id: 0,
    fileName: "",
    cloneUrl: "",
    files: [] as any[],
  });
  const handleEvent = function (e: any) {
    if (e.type === "loadstart") {
      setProgress(0);
      setLoading(true);
    }
    if (e.type === "progress") {
      const rate = Number(((e.loaded / e.total) * 100).toFixed(4));
      setProgress(rate);
    }
    if (e.type === "error") {
      setProgress(0);
      setLoading(false);
    }
  };
  // TODO
  // const formDataObj = ({ file, ip_name }: { file: any; ip_name: string }) => {
  const formDataObj = ({
    voice_file,
    pid,
    title,
  }: {
    voice_file: any;
    pid: number;
    title: string;
  }) => {
    const formData = new FormData();
    formData.append("title", title);
    formData.append("voice_file", voice_file);
    formData.append("pid", `${pid}`);
    // formData.append("files", file);
    // formData.append("ip_name", ip_name);
    // formData.append("emb_type", `5`);
    return formData;
  };
  /** 初始化状态 */
  const init = () => {
    setLoading(false);
    setProgress(0);
    setParams({
      files: [],
      ip_name: "",
      fileName: "",
      url: "",
      id: 0,
      cloneUrl: "",
    });
    callback();
  };
  /** 克隆音频 */
  const audioCloneEve = ({
    title,
    pid,
    voice_file,
  }: {
    title: string;
    pid: number;
    voice_file: string;
  }) => {
    const formData = new FormData();
    formData.append("voice_file", paramsRef.current.files[0]);
    formData.append("pid", `${pid}`);
    formData.append("title", title);

    if (paramsRef.current.cloneUrl) {
      audioClone(formData)
        .then((res) => {
          if (res.code) {
            showMessage.success("上传成功！");
          } else {
            showMessage.error("音频克隆失败！");
          }
        })
        .finally(() => {
          init();
        });
    }
  };
  /** 上传音频sso入库 */
  const uploadSucess = (info: any) => {
    saveVoiceModel({
      pid: paramsRef.current.id,
      voice_url: info[0].file_url,
      voice_name: paramsRef.current.fileName,
      status: 1,
      del_flag: 1,
    })
      .then((res) => {
        if (res.code === 200) {
          audioCloneEve({
            pid: paramsRef.current.id,
            voice_file: info[0].file_url,
            title: paramsRef.current.fileName,
          });
        }
      })
      .catch(() => {
        showMessage.error("入库失败");
        init();
      });
  };
  /** 上传音频 */
  const uploadEve = ({
    files,
    ip_name,
    id,
    fileName,
    url,
    cloneUrl,
  }: UploadEventType) => {
    setParams({
      files,
      ip_name,
      fileName,
      url,
      id,
      cloneUrl,
    });

    paramsRef.current.id = id;
    paramsRef.current.fileName = fileName;
    paramsRef.current.cloneUrl = cloneUrl as string;
    paramsRef.current.files = files;

    const xhr = new XMLHttpRequest();
    xhr.upload.addEventListener("loadstart", handleEvent);
    xhr.upload.addEventListener("load", handleEvent);
    xhr.upload.addEventListener("loadend", handleEvent);
    xhr.upload.addEventListener("progress", handleEvent);
    xhr.upload.addEventListener("error", handleEvent);
    xhr.upload.addEventListener("abort", handleEvent);
    xhr.responseType = "json";
    xhr.open("post", `${cloneUrl}`);
    xhr.setRequestHeader("Authorization", `Bearer ${GetToken()}`);
    xhr.send(formDataObj({ voice_file: files[0], pid: id, title: fileName }));
    xhr.onreadystatechange = () => {
      if (xhr.readyState === 4) {
        if (xhr.response?.code === 200) {
          showMessage.success("上传成功");
          // 上传成功后的处理
          setProgress(100);
          setTimeout(() => {
            init();
          }, 1000);
        } else {
          showMessage.error("上传失败！");
          setProgress(0);
          setLoading(false);
        }
      }
    };
  };
  return { uploadEve, uploadParams, loading, progress };
};
