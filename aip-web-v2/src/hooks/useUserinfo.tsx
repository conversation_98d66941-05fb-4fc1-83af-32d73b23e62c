import { useRouter } from "next/navigation";
import {
  getUserInfoService,
  getIpListService,
  getCurrentIpService,
} from "@/service/fetchData";
import { useUserInfoStore, useLoginStore } from "@/store/store";
import { ClearToken } from "@/service/config";

const useUser = () => {
  const router = useRouter();
  const updateIsLogin = useLoginStore((state) => state.updateIsLogin);
  const updateIsLoginOpen = useLoginStore((state) => state.updateIsLoginOpen);
  const updateCurrentIpUser = useUserInfoStore(
    (state) => state.updateCurrentIpUser
  );
  const updateUserInfo = useUserInfoStore((state) => state.updateUserInfo);
  const updateCurrentIpList = useUserInfoStore(
    (state) => state.updateCurrentIpList
  );
  const currentIpUser = useUserInfoStore((state) => state.currentIpUser);
  /**
   * 获取当前用户信息的IP信息
   */
  const GetIpDetail = async (ip: number) => {
    getCurrentIpService(ip).then((res) => {
      if (res.code === 200) {
        updateCurrentIpUser(res.data);
      }
    });
  };

  /**
   * 请求用户信息
   */
  const GetUserInfo = async () => {
    const result = await getUserInfoService();
    updateUserInfo(result);
    if (!result.id) return;
    const ipList = await getIpListService(`${result.id}`);
    if (ipList.code === 200 && ipList?.data?.length > 0) {
      updateCurrentIpList(ipList.data);
      if (currentIpUser.id) {
        GetIpDetail(currentIpUser.id);
      } else {
        GetIpDetail(ipList.data[0].id);
      }
    } else {
      updateCurrentIpList([]);
      updateCurrentIpUser({} as any);
    }
  };

  /**
   * 退出登录
   */
  const LogoutEve = async () => {
    ClearToken();
    updateUserInfo({} as any);
    updateCurrentIpUser({} as any);
    updateCurrentIpList([]);
    updateIsLogin(false);
    updateIsLoginOpen(true);
    router.push("/home");
  };
  return {
    GetUserInfo,
    GetIpDetail,
    LogoutEve,
  };
};
export default useUser;
