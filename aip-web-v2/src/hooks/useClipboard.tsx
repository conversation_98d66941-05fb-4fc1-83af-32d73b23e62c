import { ReactNode, useCallback } from "react";
import { toast } from "sonner";

export const useClipboard = (value: string = "", icon?: ReactNode) => {
  const onCopy = useCallback(() => {
    navigator.clipboard
      .writeText(value)
      .then(() => {
        toast.success("复制成功", {
          position: "top-right",
          richColors: true,
          duration: 800,
          icon,
          style: {
            // width: 'auto',
            // left: '50%',
            // transform: 'translateX(-50%)',
          },
        });
      })
      .catch((error: any) => {
        toast.error("复制失败", {
          position: "top-right",
          richColors: true,
          duration: 800,
        });
      });
  }, [value]);
  const onCopyToClipboard = (val: string = "") => {
    navigator.clipboard
      .writeText(val)
      .then(() => {
        toast.success("复制成功", {
          position: "top-right",
          richColors: true,
          duration: 800,
          icon,
          style: {
            // width: 'auto',
            // left: '50%',
            // transform: 'translateX(-50%)',
          },
        });
      })
      .catch((error: any) => {
        toast.error("复制失败", {
          position: "top-right",
          richColors: true,
          duration: 800,
        });
      });
  };
  return { onCopy, onCopyToClipboard };
};
