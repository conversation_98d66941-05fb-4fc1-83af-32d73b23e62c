import "@ant-design/v5-patch-for-react-19";
import type { Metadata } from "next";
import Favicon from "./favicon.ico";
import { Geist, Geist_Mono } from "next/font/google";
import { Providers } from "./providers";

import "./globals.css";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "AIP短视频智能体一站式创作平台",
  description:
    "AIPGPT使用Multi-Agent智能体在IP策划、短视频脚本创作等多场景等进行协作和自主优化的混合专家模型，AIPGPT平台服务于IP短视频策划、口播稿、口播短视频制作和分发、粉丝互动管理等场景。",
  icons: [{ rel: "icon", url: Favicon.src }],
  referrer: "no-referrer",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        <Providers>{children}</Providers>
      </body>
    </html>
  );
}
