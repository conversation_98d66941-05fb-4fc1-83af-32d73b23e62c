"use client";

import React, {
  createContext,
  useContext,
  useState,
  ReactNode,
  useRef,
} from "react";
import Image from "next/image";
import { tooltip_bg } from "@/assets/tooltip/index";
import { StaticImport } from "next/dist/shared/lib/get-img-props";
// import { useTranslations } from "next-intl";

interface TooltipLabel {
  title: string;
  jd: string;
  duty: string[];
  avatar?: StaticImport; // 可选头像路径
  name?: string; // 可选人物名称
  principle: string; // 可选原理
}

interface TooltipState {
  content: TooltipLabel | null;
  position: { top: number; left: number };
  visible: boolean;
  id: number;
}

interface TooltipContextType {
  showTooltip: (
    content: TooltipLabel,
    position: { top: number; left: number },
    id: number
  ) => void;
  hideTooltip: () => void;
  tooltip: TooltipState;
}

const TooltipContext = createContext<TooltipContextType | null>(null);

export const TooltipProvider = ({ children }: { children: ReactNode }) => {
  // const t = useTranslations("group");
  const divRef = useRef<HTMLDivElement>(null);
  const [tooltip, setTooltip] = useState<TooltipState>({
    content: null,
    position: { top: 0, left: 0 },
    visible: false,
    id: 0,
  });

  const showTooltip = (
    content: TooltipLabel,
    position: { top: number; left: number },
    id: number
  ) => {
    // const positionOption = {
    //   ...position,
    // };
    setTooltip({ content, id, position, visible: true });
  };

  const hideTooltip = () => {
    setTooltip((prev) => ({ ...prev, visible: false }));
  };

  return (
    <TooltipContext.Provider value={{ showTooltip, hideTooltip, tooltip }}>
      {children}
      {
        <div
          ref={divRef}
          className="z-50 w-[280px] px-[24px] pt-[60px] pb-[20px] text-white  rounded-lg transition-opacity duration-200 absolute"
          style={{
            visibility:
              tooltip.visible && tooltip.content ? "visible" : "hidden",
            top: tooltip.position.top,
            left: tooltip.position.left,
            transition: "opacity 0.3s ease, visibility 0s linear 0.3s", // 设置平滑过渡效果
            boxShadow: "3px 4px 40px rgb(8, 3, 42,.2)", // 半透明白色阴影
            backgroundImage: `url(${tooltip_bg.src})`,
            backgroundSize: "cover",
            backgroundPosition: "center",
            backgroundRepeat: "no-repeat",
            border: "1.5px solid rgba(76, 51, 255, 0.65)",
            // boxShadow: "0px 8px 20px -10px #bbbbbb",
          }}
        >
          <div className="w-full bg-[#09173C] border border-[#4C33FF] text-[#22DEFE] text-xs px-[24px] pt-[48px] pb-[24px] border-opacity-65 rounded-lg shadow-[0px_0px_4px_1px_rgba(95,218,255,0.15),0px_0px_3px_3px_rgba(50,64,255,0.35)] relative">
            {/* 头像 */}
            <div className="w-[64px] h-[64px] rounded-full bg-gradient-to-r from-[#4C33FF] via-[#379BFF] to-[#040F2B] p-[2px] absolute top-[-32px] left-1/2 transform -translate-x-1/2">
              <div className="w-full h-full rounded-full bg-white overflow-hidden">
                {tooltip.content && tooltip.content.avatar && (
                  <Image
                    src={tooltip.content.avatar}
                    alt=""
                    className="w-full h-full object-contain"
                  />
                )}
              </div>
            </div>

            {/* 头部 */}
            {tooltip.content && tooltip.content.title && (
              <div className="text-xl text-center mt-[-10px] font-semibold">
                {/* {t(`trip_data.${tooltip.id}.label.title`)} */}
                {tooltip.content.title}
              </div>
            )}

            {/* title + JD */}
            {tooltip.content && tooltip.content.jd && (
              <div className="text-xs text-center text-[#8D31FF] mt-1">
                {/* {t(`trip_data.${tooltip.id}.label.jd`)} */}
                {tooltip.content.jd}
              </div>
            )}

            {/* 职责 duty */}
            <div className="text-xslist-disc list-inside space-y-1">
              <div className="flex items-center text-base font-semibold pt-[24px] pb-[9px]">
                岗位职责
              </div>
              {tooltip.content &&
                tooltip.content.duty.map((item, idx) => (
                  <div className="py-[2px]" key={idx}>
                    {idx + 1 + "."}
                    <span className="pl-2">
                      {/* {t(`trip_data.${tooltip.id}.label.duty.${idx}`)} */}
                      {item}
                    </span>
                  </div>
                ))}
            </div>
          </div>

          {tooltip.content && tooltip.content.principle && (
            <div className="flex items-center justify-center mt-5  text-white text-xs">
              {tooltip.content && tooltip.content.principle}
            </div>
          )}
        </div>
      }
    </TooltipContext.Provider>
  );
};

export const useTooltip = () => {
  const context = useContext(TooltipContext);
  if (!context)
    throw new Error("useTooltip must be used within TooltipProvider");
  return context;
};
