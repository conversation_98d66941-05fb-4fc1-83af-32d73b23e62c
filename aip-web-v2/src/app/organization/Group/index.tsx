// import { useTranslations } from "next-intl";
import ScrollReveal from "../ScrollReveal";
import { TooltipProvider } from "../TooltipProvider";
import HoverAnchor from "../HoverAnchor";
import { cn } from "@/lib/utils";
import Image from "next/image";
import styles from "./style.module.css";
import {
  data1,
  data2,
  data3,
  data4,
  aip_logo,
  content1,
  content2,
  content3,
  content4,
  content5,
  content6,
  content7,
  content8,
  content9,
  content10,
  content11,
  content12,
  content13,
  content14,
  content15,
  knowledge_one,
  knowledge_two,
  persona_one,
  persona_there,
  persona_two,
  tooltip_bg,
  GroupPng,
} from "../../../assets/tooltip/index";

const GroupBlock = () => {
  const hoverPoints = [
    {
      label: {
        title: "AIP掌柜",
        jd: "IP孵化总控掌柜 | 50万-100万/年",
        duty: [
          "动态路由智能识别操盘手意图",
          "全流程智能化任务调度",
          "协调跨组资源冲突",
          "生成全流程数据信息流",
        ],
        avatar: aip_logo,
        principle: "Model Pruning、微调、RAG、Graph",
      },
      left: "44.6%",
      top: "-2%",
      frist: true,
    },
    {
      label: {
        title: "商业洞察专家",
        jd: "创始人价值解码 | 40万-60万/年",
        duty: [
          "分析客户企业/战略内容",
          "提炼人设记忆点",
          "建立人设与业务关联模型",
          "制作人设收束模型",
        ],
        avatar: persona_one,
        principle: "蒸馏、RAG、Graph",
      },
      left: "1.6%",
      top: "31%",
    },
    {
      label: {
        title: "通用人设专家",
        jd: "短视频人设建模 | 30-50万/年",
        duty: [
          "设计专业度-亲和力平衡模型",
          "分析人设测试问卷",
          "创建人设思维库",
          "监测人设一致性偏离情况",
          "MOE to 操盘手助理",
        ],
        avatar: persona_two,
        principle: "蒸馏、RAG、Graph",
      },
      left: "1.6%",
      top: "45%",
    },
    {
      label: {
        title: "通用业务专家",
        jd: "爆款IP翻译官 | 20万-30万/年",
        duty: [
          "学习爆款 IP 人设、分析并设计模版",
          "爆款人设通信至人设组",
          "MOE to 操盘手助理",
        ],
        avatar: persona_there,
        principle: "蒸馏、RAG、Graph",
      },
      left: "1.6%",
      top: "59.5%",
    },
    {
      label: {
        title: "对标账号专家",
        jd: "竞品情报分析师 | 25万-35万/年",
        duty: [
          "采集制定对标账号信息包括前 100 条作品",
          "拆解对标账号要素矩阵",
          "拆解对标账号视频要素",
          "建立对标账号监测库",
        ],
        avatar: knowledge_one,
        principle: "多模态、微调、RAG、Graph",
      },
      left: "18.6%",
      top: "31%",
    },
    {
      label: {
        title: "专业知识专家",
        jd: "知识图谱构建 | 30万-50万/年",
        duty: [
          "对客户上传的数据进行清洗、切割、标注",
          "构建知识图谱",
          "制作知识卡片",
          "监控知识库收束情况，预警AI幻觉情况",
          "维护动态收束机制",
        ],
        avatar: knowledge_two,
        principle: "Graph RAG",
      },
      left: "18.6%",
      top: "45%",
    },
    {
      label: {
        title: "选题专家",
        jd: "热点爆破手 | 30万-50万/年",
        duty: [
          "监控50+信息源热点 ",
          "生成选题可行性评分 ",
          "建立选题热度预测模型 ",
          "制作选题组合策略 ",
          "制作选题推荐理由",
        ],
        avatar: content1,
        principle: "蒸馏、RAG",
      },
      left: "34.3%",
      top: "30.5%",
    },
    {
      label: {
        title: "结构专家",
        jd: "脚本设计师 | 30万-50万/年",
        duty: [
          "设计0 - 8秒注意力锚点",
          "开发故事冲突模板",
          "优化信息密度曲线",
          "制定AB版测试方案",
        ],
        avatar: content2,
        principle: "蒸馏、RAG",
      },
      left: "34.3%",
      top: "45%",
    },
    {
      label: {
        title: "观点专家",
        jd: "认知包装师 | 30万-50万/年",
        duty: [
          "提炼IP价值观金句",
          "开发认知冲突模型",
          "制作观点传播力指数 ",
          "设计用户认知阶梯",
        ],
        avatar: content3,
        principle: "蒸馏、RAG",
      },
      left: "34.3%",
      top: "59.6%",
    },
    {
      label: {
        title: "钩子专家",
        jd: "流量捕手 | 30万-50万/年",
        duty: [
          "分析10000+爆款开场 ",
          "开发95类钩子模板 ",
          "制作钩子效果预测模型 ",
          "优化钩子迭代机制",
        ],
        avatar: content4,
        principle: "微调",
      },
      left: "47.8%",
      top: "30.5%",
    },
    {
      label: {
        title: "开头专家",
        jd: "黄金三秒设计师 | 25万-35万/年",
        duty: [
          " 建立开场话术库",
          "设计场景化开场",
          "开发情绪激励反馈模板",
          "评估开场文案跳出可能性",
        ],
        avatar: content5,
        principle: "蒸馏、RAG",
      },
      left: "47.8%",
      top: "45%",
    },
    {
      label: {
        title: "文案专家",
        jd: "专业文案转译器 | 30万-50万/年",
        duty: [
          "执行Flesch易读性优化（Flesch≥82）",
          "开发行业术语词典",
          "制作口语化表达模板 ",
          "控制文案信息损耗率≤15%",
        ],
        avatar: content6,
        principle: "微调、RAG、Graph",
      },
      left: "47.8%",
      top: "59.7%",
    },
    {
      label: {
        title: "润色专家",
        jd: "高级文案优化师 | 30万-45万/年",
        duty: [
          "负责润色优化文案内容，提升文案质量",
          "调整文案风格与语调以提高共鸣度",
          "优化文案结构与节奏，确保信息清晰传递",
          "精炼文字表达，提高文案简洁度与吸引力",
        ],
        avatar: content7,
        principle: "微调",
      },
      left: "47.8%",
      top: "74%",
    },
    {
      label: {
        title: "风格专家",
        jd: "文案适配专家 | 25万-35万/年",
        duty: [
          "制定多平台风格库",
          "开发节奏感调节工具",
          "设计情绪感染指数",
          "优化跨平台适配方案",
        ],
        avatar: content8,
        principle: "微调",
      },
      left: "62.1%",
      top: "30.4%",
    },
    {
      label: {
        title: "标题专家",
        jd: "CTR优化师 | 30万-50万/年",
        duty: [
          "分析100w+标题数据 ",
          "开发标题公式生成器 ",
          "制作标题热度预测模型 ",
          "执行AB测试优化",
        ],
        avatar: content9,
        principle: "蒸馏、RAG",
      },
      left: "62.1%",
      top: "45%",
    },
    {
      label: {
        title: "洗稿专家",
        jd: "内容重构师 | 25万-35万/年",
        duty: [
          "检测内容重复率",
          "开发同义替换算法 ",
          "设计故事改编模板",
          "维护洗稿质量评估体系",
        ],
        avatar: content10,
        principle: "微调、RAG、Graph",
      },
      left: "62.1%",
      top: "59.7%",
    },
    {
      label: {
        title: "音频专家",
        jd: "声音雕刻家 | 30万-50万/年",
        duty: [
          "审核上传音频干声素材",
          "开发 IP 专属的情绪复刻模型",
          "设计重点词强化方案",
          "优化语速节奏",
          "降噪、音质美化及修复",
        ],
        avatar: content11,
        principle: "多模态、RAG",
      },
      left: "76.8%",
      top: "30.6%",
    },
    {
      label: {
        title: "音色专家",
        jd: "声音雕刻家 | 40万-50万/年",
        duty: [
          "开发音色分析与复刻模型，实现声音的高保真还原",
          "设计个性化音色调整方案，满足不同IP的定制需求",
          "优化音色与情感的匹配度，提升声音表现力",
          "降噪、音质美化及修复，确保音频质量",
          "维护音色库，更新和扩充音色样本",
        ],
        avatar: content12,
        principle: "多模态、RAG",
      },
      left: "76.8%",
      top: "44.9%",
    },
    {
      label: {
        title: "视频专家",
        jd: "视觉工程师 | 40万-50万/年",
        duty: [
          "设计并执行数字人嘴型匹配组合方案 ",
          "开发重点标注嘴型库、微表情库 ",
          "优化画面信息密度 ",
          " 制作多版本音频和肢体动作匹配模板 ",
          "执行音画匹配方案",
        ],
        avatar: content13,
        principle: "多模态、RAG",
      },
      left: "76.8%",
      top: "59.7%",
    },
    {
      label: {
        title: "照片推理专家",
        jd: "视觉解析师 | 35万-45万/年",
        duty: [
          "开发图像分析与推理算法，提取照片中的关键信息 ",
          "设计照片风格迁移方案，实现视觉效果的多样化 ",
          "优化图像清晰度与色彩还原技术",
          "建立照片数据库，分类管理样本 ",
          "研究并应用最新的图像处理技术",
        ],
        avatar: content14,
        principle: "多模态、RAG",
      },
      left: "76.8%",
      top: "74%",
    },
    {
      label: {
        title: "人物复刻专家",
        jd: "数字克隆师 | 40万-50万/年",
        duty: [
          "开发人物形象复刻模型，实现外貌、神态的高度还原",
          "设计多维度人物特征提取方案，包括面部、肢体动作等",
          "优化数字人与真实人物匹配度",
          "建立人物特征库，支持快速调用与调整 ",
          "研究并应用最新的AI生成技术，提升复刻效率与质量",
        ],
        avatar: content15,
        principle: "多模态、RAG",
      },
      left: "76.8%",
      top: "88.3%",
    },
    {
      label: {
        title: "数据搜索专家",
        jd: "情报侦察兵 | 30万-50万/年",
        duty: [
          "监控100+热点数据源 ",
          "建立热词、爆款追踪系统 ",
          "开发数据预警模型 ",
          "生成并推送针对 IP 的数据采集报告",
        ],
        avatar: data1,
        principle: "RAG+AI检索",
      },
      left: "91.8%",
      top: "30.5%",
    },
    {
      label: {
        title: "数据清洗专家",
        jd: "数据炼金师 | 30万-50万/年",
        duty: [
          "处理非结构化数据",
          "建立语义分类标准",
          "开发脏数据过滤算法 ",
          "维护数据质量评估体系",
        ],
        avatar: data2,
        principle: "RAG+AI检索",
      },
      left: "91.8%",
      top: "45.1%",
    },
    {
      label: {
        title: "数据分析专家",
        jd: "决策智囊团 | 30万-50万/年",
        duty: [
          "构建内容效果归因模型",
          "开发爆款预测算法",
          "制作数据洞察模型",
          "优化迭代策略",
        ],
        avatar: data3,
        principle: "RAG+AI检索",
      },
      left: "91.9%",
      top: "59.3%",
    },
    {
      label: {
        title: "爬虫专家",
        jd: "信息捕网人 | 30万-50万/年",
        duty: [
          "设计反爬绕过方案",
          "定向采集指定数据源",
          "构建用户画像数据库",
          "维护数据采集合规性",
        ],
        avatar: data4,
        principle: "RAG+AI检索",
      },
      left: "91.9%",
      top: "74%",
    },
  ];
  // const t = useTranslations("group");
  return (
    <TooltipProvider>
      <section className={styles.container}>
        <ScrollReveal y={-36}>
          <div className="text-white text-center text-3xl font-semibold md:text-5xl mb-[36px] md:mb-[80px]">
            智能体组织架构
          </div>
        </ScrollReveal>
        <ScrollReveal y={-36}>
          <div className="w-full relative">
            <Image
              src={GroupPng}
              alt=""
              className="w-full aspect-[1060 / 1030]"
            />
            {/* <div className="left-[1%] top-[30%] absolute w-[8%] h-[8%] bg-slate-50"></div> */}
            {hoverPoints.map((point, idx) => (
              <HoverAnchor
                key={idx}
                id={idx}
                content={point.label}
                className={cn(
                  "hidden md:block  absolute w-[7%] h-[7%]  rounded-[100%] hover:bg-black/60 transition-colors duration-300 cursor-pointer",
                  point.frist ? "w-[11%] h-[11%]" : ""
                )}
                style={{
                  left: point.left,
                  top: point.top,
                }}
              />
            ))}
          </div>
        </ScrollReveal>
      </section>
    </TooltipProvider>
  );
};
export default GroupBlock;
