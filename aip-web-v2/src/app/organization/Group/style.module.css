.container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  max-width: 64rem; /* max-w-5xl = 64rem */
  margin-left: auto;
  margin-right: auto;
  width: 100%;
  padding-left: 1rem; /* px-4 = 1rem */
  padding-right: 1rem; /* px-4 = 1rem */
  margin-top: 10rem; /* mt-40 = 10rem */
  padding: 10rem 0; /* pd-40 可能是笔误，应为 pb-40 = 10rem */
}

/* 响应式设计 - 移动端优先 */
@media (min-width: 768px) {
  .container {
    min-height: 100vh; /* mb:min-h-screen 可能是 md:min-h-screen */
    padding-left: 0; /* md:px-0 */
    padding-right: 0; /* md:px-0 */
    margin-top: 0.25rem; /* md:mt-1xl - 注意: Tailwind 中没有 1xl，可能是 1rem 或 0.25rem */
  }
}
