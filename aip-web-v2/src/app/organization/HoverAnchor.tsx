"use client";

import React, { useRef } from "react";
import { useTooltip } from "./TooltipProvider";

interface TooltipLabel {
  title: string;
  jd: string;
  duty: string[];
  avatar?: any;
  id?: number;
  principle: string;
}

interface HoverAnchorProps {
  content: TooltipLabel;
  className?: string;
  style?: React.CSSProperties;
  id: number;
}

const HoverAnchor: React.FC<HoverAnchorProps> = ({
  content,
  className,
  style,
  id,
}) => {
  const ref = useRef<HTMLDivElement>(null);
  const { showTooltip, hideTooltip } = useTooltip();

  const handleMouseEnter = () => {
    const rect = ref.current?.getBoundingClientRect();
    console.log(rect, "asfaf");
    const tooltipWidth = 280; // 提示框宽度
    const tooltipHeight = 300; // 提示框高度（大致高度，需要根据实际高度调整）
    const margin = 15; // 额外的间距

    if (!rect) return;

    let left = rect.right + margin + window.scrollX;
    let top = rect.top + window.scrollY;

    // 判断右边是否超出屏幕
    if (rect.right + tooltipWidth > window.innerWidth) {
      left = rect.left - tooltipWidth - margin + window.scrollX;
    }

    // 判断下边是否超出屏幕，如果超出则调整到上面显示
    if (rect.bottom + tooltipHeight > window.innerHeight) {
      top = rect.top - tooltipHeight - margin + window.scrollY;
    }

    // 如果位置仍然有问题，再根据需要调整
    if (top < 0) {
      top = margin + window.scrollY; // 向下微调
    }

    showTooltip(content, { top, left }, id);
  };

  return (
    <div
      ref={ref}
      className={className}
      style={style}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={hideTooltip}
    />
  );
};

export default HoverAnchor;
