import { createOpenAI } from '@ai-sdk/openai';
import { streamText, tool } from 'ai';
import { z } from 'zod';
const openai = createOpenAI({
    // custom settings, e.g.
    // baseURL: 'https://api.deepseek.com/v1',
    // baseURL: 'https://aws.storypower.ai/v1',
    baseURL: 'https://api.gpt2share.com/v1',
    // apiKey: 'sk-d7b26ed43d54404f967ced8602090adc',
    // apiKey: 'sk-rELshXQRllC8tS2945152937BaCd410998F7751e98D8Ab1a'
    apiKey: 'sk-vLtIldY39OPhq8IZkTFCUhrmzZEvXITwC9EQV51LYGp5feAG'
    // strict mode, enable when using the OpenAI API
});
export const maxDuration = 30;

export async function POST(req: Request) {
    const { messages } = await req.json();

    const result = streamText({
        model: openai('gpt-4o'),
        // model: openai('deepseek-chat'),
        // model: openai('claude-3-5-sonnet-20241022'),
        messages,
        maxSteps: 5,
        tools: {
            weather: tool({
                description: 'Get the weather in a location (fahrenheit)',
                parameters: z.object({
                    location: z.string().describe('The location to get the weather for'),
                }),
                execute: async ({ location }) => {
                    const temperature = Math.round(Math.random() * (90 - 32) + 32);
                    return {
                        location,
                        temperature,
                    };
                },
            }),
            convertFahrenheitToCelsius: tool({
                description: 'Convert a temperature in fahrenheit to celsius',
                parameters: z.object({
                    temperature: z
                        .number()
                        .describe('The temperature in fahrenheit to convert'),
                }),
                execute: async ({ temperature }) => {
                    const celsius = Math.round((temperature - 32) * (5 / 9));
                    return {
                        celsius,
                    };
                },
            }),
        },
    });

    return result.toDataStreamResponse();
}