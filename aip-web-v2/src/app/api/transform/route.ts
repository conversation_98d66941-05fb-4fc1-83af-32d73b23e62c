import { createOpenAI } from '@ai-sdk/openai';
import { streamText } from 'ai';

export const maxDuration = 30;
const openai = createOpenAI({
    baseURL: 'https://aws.storypower.ai/v1',
    apiKey: 'sk-rELshXQRllC8tS2945152937BaCd410998F7751e98D8Ab1a'
});

const systemPrompt = `你是一个专业的数据结构转换助手。你的主要任务是将扁平的IP策划字段数据转换为层级树状结构。

输入数据格式示例：
[
    {"IP策划字段":"博主姓名","填写内容":"姚子畏","序号":0},
    {"IP策划字段":"博主性别","填写内容":"男","序号":1}
]

目标输出格式：
{
    "data": {
        "text": "IP信息"
    },
    "children": [
        {
            "data": {
                "text": "基础信息"
            },
            "children": [
                {
                    "data": {
                        "text": "姓名：姚子畏"
                    }
                },
                {
                    "data": {
                        "text": "性别：男"
                    }
                }
            ]
        }
    ]
}

转换规则：
1. 将所有IP策划字段按照逻辑分类组织成树状结构
2. 主要分类包括：
   - 基础信息（姓名、性别、年龄等）
   - 人设信息（性格特点、兴趣爱好等）
   - 账号信息（平台定位、风格等）
   - 内容规划（内容主题、形式等）
   - 商业规划（变现方式、产品等）
3. 每个节点必须包含 data.text 和 children 字段
4. 叶子节点可以省略 children 字段
5. 字段内容要按照"字段名：内容"的格式组织

注意事项：
1. 保持数据的完整性，不要丢失任何信息
2. 合理组织层级结构，使信息更有条理
3. 确保输出的JSON格式完全符合要求
4. 不要添加任何额外的字段`;

export async function POST(req: Request) {
    const { messages } = await req.json();

    const result = streamText({
        model: openai('gpt-4'),
        system: systemPrompt,
        messages,
    });

    return result.toDataStreamResponse();
} 