from sqlalchemy import create_engine, Column, Integer, String, select
from sqlalchemy.orm import declarative_base, Session
import requests
import json
from typing import List, Dict

Base = declarative_base()

class IPPlanningField(Base):
    """IP策划字段表模型"""
    __tablename__ = 'ip_planning_fields'

    id = Column(Integer, primary_key=True)
    field_name = Column(String(255), nullable=False)
    field_content = Column(String(1000))
    field_order = Column(Integer)

    def to_dict(self) -> Dict:
        return {
            "IP策划字段": self.field_name,
            "填写内容": self.field_content,
            "序号": self.field_order
        }

# 使用与route.ts相同的系统提示词
SYSTEM_PROMPT = """你是一个专业的数据结构转换助手。你的主要任务是将扁平的IP策划字段数据转换为层级树状结构。

输入数据格式示例：
[
    {"IP策划字段":"博主姓名","填写内容":"姚子畏","序号":0},
    {"IP策划字段":"博主性别","填写内容":"男","序号":1}
]

目标输出格式：
{
    "data": {
        "text": "IP信息"
    },
    "children": [
        {
            "data": {
                "text": "基础信息"
            },
            "children": [
                {
                    "data": {
                        "text": "姓名：姚子畏"
                    }
                },
                {
                    "data": {
                        "text": "性别：男"
                    }
                }
            ]
        }
    ]
}

转换规则：
1. 将所有IP策划字段按照逻辑分类组织成树状结构
2. 主要分类包括：
   - 基础信息（姓名、性别、年龄等）
   - 人设信息（性格特点、兴趣爱好等）
   - 账号信息（平台定位、风格等）
   - 内容规划（内容主题、形式等）
   - 商业规划（变现方式、产品等）
3. 每个节点必须包含 data.text 和 children 字段
4. 叶子节点可以省略 children 字段
5. 字段内容要按照"字段名：内容"的格式组织

注意事项：
1. 保持数据的完整性，不要丢失任何信息
2. 合理组织层级结构，使信息更有条理
3. 确保输出的JSON格式完全符合要求
4. 不要添加任何额外的字段"""

class IPDataTransformer:
    def __init__(self, db_url: str):
        """Initialize database connection using SQLAlchemy"""
        self.engine = create_engine(db_url)
        Base.metadata.create_all(self.engine)
        self.transform_api_url = "http://localhost:3000/api/transform"

    def fetch_ip_data(self) -> List[Dict]:
        """使用SQLAlchemy ORM获取IP策划字段数据"""
        try:
            with Session(self.engine) as session:
                stmt = select(IPPlanningField).order_by(IPPlanningField.field_order)
                results = session.execute(stmt).scalars().all()
                return [result.to_dict() for result in results]
        except Exception as e:
            print(f"Error fetching data: {e}")
            return []

    def transform_data(self, data: List[Dict]) -> Dict:
        """调用transform API转换数据结构"""
        try:
            # 使用与route.ts相同的消息格式
            payload = {
                "messages": [
                    {
                        "role": "system",
                        "content": SYSTEM_PROMPT
                    },
                    {
                        "role": "user",
                        "content": f"请将以下数据转换为指定的树状结构：{json.dumps(data, ensure_ascii=False)}"
                    }
                ]
            }
            
            response = requests.post(
                self.transform_api_url,
                json=payload,
                headers={"Content-Type": "application/json"}
            )
            
            if response.status_code == 200:
                transformed_data = ""
                for line in response.iter_lines():
                    if line:
                        decoded_line = line.decode('utf-8')
                        if decoded_line.startswith('data: '):
                            transformed_data += decoded_line[6:]
                return json.loads(transformed_data)
            else:
                print(f"API request failed with status code: {response.status_code}")
                return None
        except Exception as e:
            print(f"Error transforming data: {e}")
            return None

    def save_transformed_data(self, transformed_data: Dict, output_file: str):
        """保存转换后的数据到文件"""
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(transformed_data, f, ensure_ascii=False, indent=2)
            print(f"Transformed data saved to {output_file}")
        except Exception as e:
            print(f"Error saving data: {e}")

    def process(self, output_file: str = 'transformed_ip_data.json'):
        """执行完整的数据处理流程"""
        # 1. 获取数据
        raw_data = self.fetch_ip_data()
        if not raw_data:
            print("No data found to process")
            return

        # 2. 转换数据
        transformed_data = self.transform_data(raw_data)
        if not transformed_data:
            print("Data transformation failed")
            return

        # 3. 保存结果
        self.save_transformed_data(transformed_data, output_file)

if __name__ == "__main__":
    # 配置数据库连接URL
    DB_URL = "mysql+pymysql://your_username:your_password@localhost/your_database?charset=utf8mb4"
    
    # 创建转换器实例并执行处理
    transformer = IPDataTransformer(DB_URL)
    transformer.process() 