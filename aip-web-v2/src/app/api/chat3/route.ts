import { createOpenAI } from '@ai-sdk/openai';
import { streamText, tool } from 'ai';
import { z } from 'zod';
export const maxDuration = 30;
const openai = createOpenAI({
    // custom settings, e.g.
    // baseURL: 'https://api.deepseek.com/v1',
    baseURL: 'https://aws.storypower.ai/v1',
    // apiKey: 'sk-76f36adf178e40478e26c328dadc0bd7',
    apiKey: 'sk-rELshXQRllC8tS2945152937BaCd410998F7751e98D8Ab1a'
    // strict mode, enable when using the OpenAI API
});

const systemPrompt = `生成一个列表是关于短视频选题的，选题内容伪造即可
few_shot_example：

### 什么？xxx要杀死aaa？<CUT>  

### 全球首例猪肾移植人体手术成功<CUT>  

### 欧洲能源危机加剧，天然气价格飙升<CUT>  



## 始终记住
1. 每个新闻结束用<CUT>来标识
2. 除了新闻列表也要给一些其他内容，例如问候，或者一些其他内容`



export async function POST(req: Request) {
    const { prompt }: { prompt: string } = await req.json();

    const result = streamText({
        model: openai('gpt-4o'),
        system: systemPrompt,
        prompt,
    });

    return result.toDataStreamResponse();
}