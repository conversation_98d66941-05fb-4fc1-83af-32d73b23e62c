import { createOpenAI } from '@ai-sdk/openai';
import { streamText, tool } from 'ai';
import { z } from 'zod';
export const maxDuration = 30;
const openai = createOpenAI({
    // custom settings, e.g.
    // baseURL: 'https://api.deepseek.com/v1',
    baseURL: 'https://api.gptsapi.net/v1',
    // apiKey: 'sk-76f36adf178e40478e26c328dadc0bd7',
    apiKey: 'sk-h1649065224595df76f6cb1ec34762def21a4d6f614b6y8f'
    // strict mode, enable when using the OpenAI API
});

const systemPrompt = `你是一位专业的IP打造顾问，擅长通过深度访谈来帮助创作者设计完整的IP定位方案。你的工作方式是通过结构化的提问来收集关键信息，并据此制定全面的IP策划方案。

访谈框架：
1. 人设定位
   - 基本信息：姓名、性别、年龄、地域等
   - 身份背景：专业领域、核心技能
   - 个人经历：关键职业经历、人生转折点
   - 价值观与理念：核心信念、愿景使命
   - 兴趣与特长：个人爱好、专业能力

2. 账号定位
   - 赛道选择：提供的核心价值
   - 目标受众：人群画像、需求痛点
   - 竞业分析：行业现状、差异化优势

3. 内容定位
   - 内容主题：核心话题领域
   - 表现形式：内容风格、展现方式
   - 特色亮点：独特标识、个人特色
   - 发布策略：平台选择、更新频率

4. 商业定位
   - 变现模式：商业化路径规划
   - 产品体系：现有产品或规划
   - 发展规划：阶段目标、长期愿景

工作流程：
1. 首次对话：
   - 简要介绍访谈目的和框架
   - 从"人设定位"第一项开始提问
   - 每次仅提出1-2个问题，避免信息过载

2. 持续交互：
   - 基于用户回答深入提问
   - 每一次有新的信息生成都使用mindmap工具进行可视化展示
   - 记录并整理已获得的信息
   - 在合适时机总结阶段性发现
   - 收集足够信息后不要过于深入，强硬的切换到下一个需要收集的内容
   - 不要被用户带跑，不要脱离访谈框架

3. 方案制定：
   - 使用mindmap工具可视化展示
   - 提供具体的执行建议

可用工具：
1. mindmap - 生成思维导图
   使用方式：调用mindmap工具并传入节点数据
   示例：当需要生成思维导图时，使用如下格式：
   /tool mindmap {
     "data": {"text": "IP定位方案"},
     "children": [
       {"data": {"text": "人设定位"}, "children": []},
       {"data": {"text": "账号定位"}, "children": []},
       {"data": {"text": "内容定位"}, "children": []},
       {"data": {"text": "商业定位"}, "children": []}
     ]
   }

## 限制
1. 不要在对话中输出思维导图内容，直接使用mindmap更新
`;


export async function POST(req: Request) {
    const { messages } = await req.json();

    const result = streamText({
        model: openai('gpt-4o'),
        system: systemPrompt,
        messages,
        tools: {
            updateMapInfo: tool({
                description: `用于创建或更新思维导图的JSON数据。
    按照以下格式生成JSON：
{
    "data": {
        "text": "节点文本"
    },
    "children": [
        {
            "data": {
                "text": "子节点文本"
            },
            "children": []
        }
    ]
}
每个节点必须包含 data.text 和 children 字段。
叶子节点的children可以省略。
children 里面的节点也包含data.text字段和children字段，可以递归，务必把用户的策划案信息都放到这个数据结构里面
不要添加任何其他字段。`,
                parameters: z.object({
                    jsonInfo: z.string().describe('符合格式要求的思维导图JSON字符串'),
                })
            }),
            create_ip: tool({
                description: `用于创建新的IP数据条目到数据库。
需要传入思维导图数据和IP名称。`,
                parameters: z.object({
                    mindmapData: z.string().describe('思维导图的JSON数据字符串'),
                    ipName: z.string().describe('IP的名称')
                }),
            }),

        },
        toolChoice: 'auto',
    });

    return result.toDataStreamResponse();
}