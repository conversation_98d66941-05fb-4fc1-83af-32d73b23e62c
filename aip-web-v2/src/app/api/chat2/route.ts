import { createOpenAI } from "@ai-sdk/openai";
import { streamText, tool } from "ai";
import { z } from "zod";
export const maxDuration = 30;
const openai = createOpenAI({
  // custom settings, e.g.
  // baseURL: 'https://api.deepseek.com/v1',
  //   baseURL: "https://api.gptsapi.net/v1",
  baseURL: "https://api.gpt2share.com/v1",
  // apiKey: 'sk-76f36adf178e40478e26c328dadc0bd7',
  //   apiKey: "sk-h1649065224595df76f6cb1ec34762def21a4d6f614b6y8f",
  apiKey: "sk-vLtIldY39OPhq8IZkTFCUhrmzZEvXITwC9EQV51LYGp5feAG",

  // strict mode, enable when using the OpenAI API
});

const systemPrompt = `你是一位专业的IP打造顾问，擅长基于现有IP数据提供专业的优化建议，以及帮助用户生成ip信息。你的工作流程是：

1. 首次对话时：
   - 使用 get_mindmap 工具获取用户现有的IP思维导图数据
   - 分析数据中的关键信息
   - 识别潜在的改进空间

2. 分析维度：
   - 人设完整度：个人信息、背景故事、价值主张是否清晰
   - 定位准确性：目标受众、市场定位是否精准
   - 内容策略：主题规划、内容形式是否合理
   - 商业模式：变现路径、产品体系是否可行

3. 建议提供：
   - 基于数据分析提供具体的优化建议
   - 针对不同维度给出可执行的改进方案
   - 结合行业最佳实践提供参考案例

4. 持续优化：
   - 记录用户反馈和新增信息
   - 使用 updateMapInfo 工具更新思维导图
   - 跟踪改进效果并适时调整建议

注意事项：
1. 始终先获取现有数据再给出建议
2. 保持专业、建设性的建议语气
3. 提供具体、可执行的优化方案
4. 注意建议的优先级排序
5. 适时总结已达成的改进效果
6. 如果用户需要重新整理ip信息，尽可能的按照人设、账号、内容、商业四个维度提供信息.
7. 当用户需要整理思维导图结构的时候，除了按照第六点外，记住尽可能保持信息的细致和完整
8. 当用户需要调整思维导图的结果或者内容的时候，不要寻用用户是否要修改，直接应用

如果用户是首次使用或暂无数据：
1. 说明需要先完成基础信息收集
2. 引导用户按照人设、账号、内容、商业四个维度提供信息
3. 使用 updateMapInfo 工具记录新增信息

可用工具：
1. get_mindmap - 获取现有思维导图数据
2. updateMapInfo - 更新思维导图数据

始终记住: 
1. 对话开始的时候先问用户对什么地方有疑问，除此之外不要输出别的内容。
2. 当用户回答之后马上调用get_mindmap工具获取数据，再基于数据提供建议。
3. 当你输出完建议之后，请寻用用户是否要应用这些信息，若用户同意请使用updateMapInfo工具更新思维导图。
4. 更新的工具可以包含更多的树结构，比如人设、账号、内容、商业等`;

export async function POST(req: Request) {
  const { messages } = await req.json();
  console.log("发送的消息", messages);

  const result = streamText({
    model: openai("gpt-4o"),
    // model: openai('claude-3-5-sonnet-20241022'),
    system: systemPrompt,
    messages,
    tools: {
      updateMapInfo: tool({
        description: `用于创建或更新思维导图的JSON数据。
    按照以下格式生成JSON：
{
    "data": {
        "text": "节点文本"
    },
    "children": [
        {
            "data": {
                "text": "子节点文本"
            },
            "children": []
        }
    ]
}
每个节点必须包含 data.text 和 children 字段。
叶子节点的children可以省略。
children 里面的节点也包含data.text字段和children字段，可以递归，务必把用户的策划案信息都放到这个数据结构里面
不要添加任何其他字段。`,
        parameters: z.object({
          jsonInfo: z.string().describe("符合格式要求的思维导图JSON字符串"),
        }),
        // execute: async ({ jsonInfo }) => {
        //   console.log("🛠️ updateMapInfo 被调用:", jsonInfo);
        //   // 你可以把 jsonInfo 存储到数据库或返回确认信息
        //   return { success: true };
        // },
      }),
      get_mindmap: tool({
        description: `用于获取现有的思维导图数据`,
        parameters: z.object({}),
        // execute: async () => {
        //   console.log("🛠️ get_mindmap 被调用");
        //   // 假设初始返回一个空导图
        //   return {
        //     data: { text: "人设" },
        //     children: [],
        //   };
        // },
      }),
    },
    // toolChoice: 'auto',
  });

  return result.toDataStreamResponse();
}
