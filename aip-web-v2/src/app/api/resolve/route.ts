// app/api/resolve/route.ts
import { NextResponse } from "next/server";

export async function GET(request: Request) {
  const { searchParams } = new URL(request.url);
  const url = searchParams.get("url");

  if (!url || !url.startsWith("https://v.douyin.com/")) {
    return NextResponse.json({ error: "无效链接" }, { status: 400 });
  }

  try {
    const response = await fetch(url, {
      method: "GET",
      redirect: "follow",
    });

    const finalUrl = response.url;

    let type: "video" | "user" | "unknown" = "unknown";
    if (finalUrl.includes("/video/")) {
      type = "video";
    } else if (finalUrl.includes("/user/")) {
      type = "user";
    }

    return NextResponse.json({ finalUrl, type });
  } catch (e: any) {
    return NextResponse.json(
      { error: "解析失败", detail: e.message },
      { status: 500 }
    );
  }
}
