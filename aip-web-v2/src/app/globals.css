@tailwind base;
@tailwind components;
@tailwind utilities;

/* Markdown Styles */
.prose-invert {
  --tw-prose-body: theme("colors.gray.300");
  --tw-prose-headings: theme("colors.white");
  --tw-prose-links: theme("colors.blue.400");
  --tw-prose-links-hover: theme("colors.blue.300");
  --tw-prose-underline: theme("colors.blue.400/0.3");
  --tw-prose-underline-hover: theme("colors.blue.400");
  --tw-prose-bold: theme("colors.white");
  --tw-prose-counters: theme("colors.gray.400");
  --tw-prose-bullets: theme("colors.gray.400");
  --tw-prose-hr: theme("colors.gray.700");
  --tw-prose-quote-borders: theme("colors.gray.700");
  --tw-prose-captions: theme("colors.gray.400");
  --tw-prose-code: theme("colors.white");
  --tw-prose-code-bg: theme("colors.gray.800");
  --tw-prose-pre-code: theme("colors.gray.200");
  --tw-prose-pre-bg: theme("colors.gray.800");
  --tw-prose-pre-border: theme("colors.gray.700");
  --tw-prose-th-borders: theme("colors.gray.700");
  --tw-prose-td-borders: theme("colors.gray.700");
}

.prose pre {
  background-color: theme("colors.gray.800");
  border: 1px solid theme("colors.gray.700");
  border-radius: 0.375rem;
  padding: 0.75rem 1rem;
  margin: 1rem 0;
}

.prose code {
  background-color: theme("colors.gray.800");
  border-radius: 0.25rem;
  padding: 0.125rem 0.25rem;
  font-size: 0.875em;
}

.prose a {
  text-decoration: none;
  border-bottom: 1px solid var(--tw-prose-underline);
  transition: all 0.2s;
}

.prose a:hover {
  border-bottom-color: var(--tw-prose-underline-hover);
}

html,
body {
  height: 100%;
  /* 
    优化字体栈，确保数字显示一致性:
    1. 系统UI字体优先
    2. 确保数字等宽显示
    3. 优质中文字体支持
  */
  font-family: system-ui, "SF Pro Display", "SF Pro SC", "PingFang SC",
    "Microsoft YaHei", "思源黑体", "Helvetica Neue", sans-serif;

  /* 字体渲染优化 */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;

  /* 防止字体缩放问题 */
  -webkit-text-size-adjust: 100%;
  text-size-adjust: 100%;

  /* 优化字体特性，特别是数字显示 */
  font-feature-settings: "tnum" on, /* 等宽数字 */ "kern" on,
    /* 字距调整 */ "liga" on, /* 连字 */ "calt" on, /* 上下文替换 */ "ss01" on; /* 样式集合 */
}

/* 专门针对输入框的数字显示优化 */
textarea,
input {
  font-feature-settings: "tnum" on;
  font-variant-numeric: tabular-nums;
}

/* 确保中文标点符号表现正确 */
:lang(zh-CN) {
  text-align: justify;
  text-justify: inter-ideograph;
}

::-webkit-scrollbar {
  width: 4px;
}

::-webkit-scrollbar-track {
  border-radius: 0px;
  background-color: rgba(255, 255, 255, 0.1);
  box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
}

::-webkit-scrollbar-thumb {
  border-radius: 4px;
  background-color: #363636;
}

.scroll_transparent::-webkit-scrollbar {
  width: 4px;
}

.scroll_transparent::-webkit-scrollbar-track {
  border-radius: 0px;
  background-color: transparent;
  /* box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3); */
}

.scroll_transparent::-webkit-scrollbar-thumb {
  border-radius: 4px;
  background-color: transparent;
}

.ant-bubble.ant-bubble-end .ant-bubble-content-wrapper {
  align-items: end;
}

.ant-bubble-avatar {
  flex-shrink: 0;
  flex-grow: 0;
}

#wfTabs .ant-slider-handle::after,
#agent_slider .ant-slider-handle::after {
  background-color: #fff;
  box-shadow: 0 0 0 2px #fff;
}

#wfTabs .ant-tabs-content,
#wfTabs .ant-tabs-tabpane {
  height: 100%;
}
.delete-confirm-modal .ant-modal-content {
  padding: 24px;
  border-radius: 12px;
}

.delete-confirm-modal .ant-modal-footer {
  margin-top: 24px;
  border-top: none;
  padding: 0;
}

.delete-confirm-modal .ant-modal-footer .ant-btn {
  height: 40px;
  padding: 0 24px;
  font-size: 14px;
  border-radius: 8px;
}

.delete-confirm-modal .ant-modal-footer .ant-btn-primary {
  background: #ef4444;
  border: none;
  box-shadow: none;
}

.delete-confirm-modal .ant-modal-footer .ant-btn-primary:hover {
  background: #dc2626;
}

.delete-confirm-modal .ant-modal-close {
  top: 16px;
  right: 16px;
}

.delete-confirm-modal .ant-modal-close:hover {
  background: rgba(0, 0, 0, 0.04);
}

.ant-tabs-nav::before {
  border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;
}

.ant-tabs-tab {
  color: rgba(255, 255, 255, 0.6) !important;
}

.ant-tabs-tab-active {
  color: white !important;
}

.ant-tabs-ink-bar {
  background: #3b82f6 !important;
}
think {
  color: rgba(255, 255, 255, 0.5);
  font-family: "PingFang SC";
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: 24px; /* 200% */
  padding-bottom: 20px;
}

.rc-virtual-list-scrollbar-vertical {
  display: none;
}
.rc-virtual-list-holder-inner {
  padding-top: 10px;
  max-width: 650px;
  margin: 0 auto !important;
}

/* 登陆输入框颜色控制 */
#loginModal .anticon.ant-input-password-icon:hover {
  color: #363636;
  opacity: 1;
}
#loginModal .ant-input::placeholder {
  color: #e1e1e1;
}
#loginModal .ant-btn-variant-solid:disabled {
  cursor: not-allowed;
  border-color: rgb(217, 217, 217);
  color: rgba(0, 0, 0, 0.25);
  background: rgba(0, 0, 0, 0.04);
  box-shadow: none;
  line-height: 25.1429px;
}
