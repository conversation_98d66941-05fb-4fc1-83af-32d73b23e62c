"use client";
import { useChat } from "ai/react";
import { useState, useEffect, useRef, useCallback } from "react";
import { cn } from "@/utils/utils";
import { LoadingDots } from "@/components/commonUI/loading-dots";
import { useUserInfoStore } from "@/store/useUserInfo";
import {
  getIpPromptApi,
  saveIpPromptApi,
  updateIpPromptApi,
} from "@/service/fetch/ip_prompt";
import { Avatar } from "@nextui-org/react";
import Image from "next/image";
import AvatarList from "@/components/AvatarList";
import { useFullscreen } from "ahooks";
import { App } from "antd";
import { motion, AnimatePresence } from "framer-motion";
import ImageFile from "@/assets/images";
import useIPPersonalStore from "@/store/useIPPersonalStore";
import ReactMarkdown from "react-markdown";
import remarkGfm from "remark-gfm";
import rehypeRaw from "rehype-raw";
import rehypeSanitize from "rehype-sanitize";
import styles from "../@slider/style.module.css";
import { useRouter } from "next/navigation";

const IpPersonaPage = () => {
  const mindMapRef = useRef<any>(null);
  const isInitializedRef = useRef(false);
  const { jsonInfo, setJsonInfo } = useIPPersonalStore();
  const { currentIpUser } = useUserInfoStore();
  const jsonInfoRef = useRef("");
  // const [messageApi, contextHolder] = message.useMessage();
  const { message, notification, modal } = App.useApp();
  const { AgentPic_5, PERSON, BUSINESS, BI } = ImageFile;
  const mindMapContainerRef = useRef<HTMLDivElement>(null);
  const [isFullscreen, { toggleFullscreen }] =
    useFullscreen(mindMapContainerRef);
  const [isSaving, setIsSaving] = useState(false);
  const [isChatVisible, setIsChatVisible] = useState(false);
  const { isIPPersonalSave, setIsIPPersonalSave } = useIPPersonalStore();
  const isIPPersonalSaveRef = useRef(true);
  const initialDataRef = useRef<string>("");
  const router = useRouter();

  useEffect(() => {
    isIPPersonalSaveRef.current = isIPPersonalSave;
  }, [isIPPersonalSave]);
  const {
    messages,
    input,
    handleInputChange,
    append,
    handleSubmit,
    isLoading,
  } = useChat({
    api: "/app/api/chat2",
    maxSteps: 5,
    async onToolCall({ toolCall }) {
      console.log("调用工具", toolCall);
      console.log("工具参数", toolCall.args);
      console.log(toolCall.toolName, "工具名");
      if (toolCall.toolName === "updateMapInfo") {
        const args = toolCall.args as { jsonInfo?: string }; // 添加类型断言
        const parsedInfo = JSON.parse(args.jsonInfo || "{}");
        console.log("已调用", parsedInfo);
        setJsonInfo(args.jsonInfo || "");
        return "更新思维导图成功";
      }
      if (toolCall.toolName === "get_mindmap") {
        return jsonInfoRef.current;
      }
    },
  });

  useEffect(() => {
    console.log("Loading state:", isLoading);
  }, [isLoading]);

  // 修改获取左侧菜单宽度的函数
  const getLeftMenuWidth = () => {
    const sliderbar = document.querySelector(`.${styles.sliderbar}`);
    if (!sliderbar) return 0;

    // 判断是否是折叠状态
    const isCollapsed = sliderbar.classList.contains(styles.sliderbarCollapse);
    if (isCollapsed) {
      return 60; // 折叠状态固定宽度
    }

    // 展开状态获取实际宽度
    return sliderbar.getBoundingClientRect().width;
  };

  // 添加监听左侧菜单宽度变化的逻辑
  useEffect(() => {
    if (!isFullscreen) return;

    const resizeObserver = new ResizeObserver(() => {
      if (mindMapContainerRef.current) {
        const leftMenuWidth = getLeftMenuWidth();
        mindMapContainerRef.current.style.width = `calc(100vw - ${leftMenuWidth}px)`;
        // 重新调整思维导图大小
        if (mindMapRef.current) {
          mindMapRef.current.resize();
          mindMapRef.current.view.fit();
        }
      }
    });

    const sliderbar = document.querySelector(`.${styles.sliderbar}`);
    if (sliderbar) {
      resizeObserver.observe(sliderbar);
    }

    return () => {
      resizeObserver.disconnect();
    };
  }, [isFullscreen]);

  // 修改全屏切换的效果
  useEffect(() => {
    if (mindMapRef.current) {
      setTimeout(() => {
        if (mindMapContainerRef.current) {
          // 退出全屏时，先重置容器宽度为100%
          mindMapContainerRef.current.style.width = "100%";

          if (isFullscreen) {
            const leftMenuWidth = getLeftMenuWidth();
            mindMapContainerRef.current.style.width = `calc(100vw - ${leftMenuWidth}px)`;
          }

          // 强制重新计算容器尺寸
          const container = document.getElementById("mindMapContainer");
          if (container) {
            const svgElement = container.querySelector("svg");
            if (svgElement) {
              svgElement.style.width = "100%";
              svgElement.style.height = "100%";
            }
          }
        }

        // 延迟执行resize和fit，确保DOM已更新
        setTimeout(() => {
          mindMapRef.current.resize();
          mindMapRef.current.view.fit();
        }, 50);
      }, 100);
    }
  }, [isFullscreen]);

  /**
   * 初始化思维导图
   */
  useEffect(() => {
    if (!mindMapRef.current && !isInitializedRef.current && currentIpUser.id) {
      (async () => {
        const MindMapImport = await import("simple-mind-map");
        const ThemeImport = await import("simple-mind-map-plugin-themes");
        const RainbowLinesImport = await import(
          // @ts-ignore
          "simple-mind-map/src/plugins/RainbowLines.js"
        );
        const WatermarkImport = await import(
          // @ts-ignore
          "simple-mind-map/src/plugins/Watermark.js"
        );
        const ExportImport = await import(
          // @ts-ignore
          "simple-mind-map/src/plugins/Export.js"
        );
        const MindMap = MindMapImport.default;
        const Watermark = WatermarkImport.default;
        MindMap.usePlugin(Watermark);
        const RainbowLines = RainbowLinesImport.default;
        MindMap.usePlugin(RainbowLines);
        const Export = ExportImport.default;
        MindMap.usePlugin(Export);
        const Theme = ThemeImport.default;
        Theme.init(MindMap);
        const userData = await getIpPromptApi(currentIpUser.id);
        let initialData = {
          data: {
            text: "等待分析中",
          },
          children: [],
        };

        // 如果已经有数据，覆盖到初始化数据里面
        if (userData?.data?.json) {
          jsonInfoRef.current = JSON.parse(userData.data.json);
          initialData = JSON.parse(userData.data.json);
        }

        // 初始化思维导图实例
        mindMapRef.current = new MindMap({
          el: document.getElementById("mindMapContainer"),
          data: initialData,
          mousewheelAction: "zoom",
          theme: "dark3",
          themeConfig: {
            backgroundColor: "#222",
          },
          //水印插件
          watermarkConfig: {
            text: "AIPGPT",
            lineSpacing: 150,
            textSpacing: 150,
            angle: 30,
            textStyle: {
              color: "#999",
              opacity: 0.02,
              fontSize: 48,
              // @ts-ignore
              fontWeight: 1000,
            },
          },
          //彩虹线条插件
          rainbowLinesConfig: {
            open: true,
            colorsList: [],
          },
        });

        // 思维导图渲染完成
        mindMapRef.current.on("node_tree_render_end", () => {
          mindMapRef.current.view.fit();
          mindMapRef.current.off("node_tree_render_end");

          // 保存初始数据
          initialDataRef.current = JSON.stringify(mindMapRef.current.getData());

          // 只监听实际的数据修改事件
          mindMapRef.current.on("data_change", () => {
            const currentData = JSON.stringify(mindMapRef.current.getData());
            const hasChanged = initialDataRef.current !== currentData;
            setIsIPPersonalSave(!hasChanged);
          });
        });

        // 标记为已初始化
        isInitializedRef.current = true;
      })();
    }
  }, [currentIpUser.id]);

  // 更新思维导图数据
  useEffect(() => {
    if (mindMapRef.current && jsonInfo) {
      const newData = JSON.parse(jsonInfo);
      mindMapRef.current.updateData(newData);
      setIsIPPersonalSave(false);
    }
    jsonInfoRef.current = jsonInfo;
  }, [jsonInfo]);

  useEffect(() => {
    if (isChatVisible && messages.length === 0) {
      append({
        id: Date.now().toString(), // 添加唯一id
        content: "<START>你好",
        role: "user",
      });
    }
  }, [isChatVisible]);

  // Add save handler function
  const handleSave = useCallback(async () => {
    if (!mindMapRef.current) {
      message.error("数据未准备就绪，无法保存");
      return false;
    }

    const currentData = JSON.stringify(mindMapRef.current.getData());
    if (currentData === initialDataRef.current) {
      message.info("没有需要保存的更改");
      return true;
    }

    setIsSaving(true);
    try {
      const res = await getIpPromptApi(Number(currentIpUser.id));
      if (res?.data?.id) {
        await updateIpPromptApi({
          id: res.data.id,
          json: currentData,
        });
      } else {
        await saveIpPromptApi({
          pid: currentIpUser.id,
          json: currentData,
          ip_name: currentIpUser.ip_name,
        });
      }

      initialDataRef.current = currentData;
      setIsIPPersonalSave(true);
      message.success("保存成功");
      return true;
    } catch (error) {
      console.error("Failed to save mindmap:", error);
      message.error("保存失败");
      return false;
    } finally {
      setIsSaving(false);
    }
  }, [currentIpUser, mindMapRef, initialDataRef, message, setIsIPPersonalSave]);

  // 使用 useCallback 包装 handlePageLeave
  const handlePageLeave = useCallback(
    async (targetPath?: string): Promise<boolean> => {
      if (!mindMapRef.current) return true;

      const currentData = JSON.stringify(mindMapRef.current.getData());
      if (currentData == initialDataRef.current) return true;

      return new Promise<boolean>((resolve) => {
        modal.confirm({
          title: "您有未保存的更改，请问是否要帮您保存?",
          okText: "保存",
          cancelText: "不保存",
          async onOk() {
            const saved = await handleSave();
            if (saved && targetPath) {
              router.push(targetPath);
            }
            resolve(saved);
          },
          onCancel() {
            if (targetPath) {
              router.push(targetPath);
            }
            resolve(true);
          },
        });
      });
    },
    [mindMapRef, initialDataRef, modal, handleSave, router]
  );

  // 使用 useRef 存储最新的 handlePageLeave
  const handlePageLeaveRef = useRef(handlePageLeave);

  // 更新 ref 的值
  useEffect(() => {
    handlePageLeaveRef.current = handlePageLeave;
  }, [handlePageLeave]);

  // 修改 store 更新逻辑
  useEffect(() => {
    useIPPersonalStore.setState({
      handlePageLeave: (...args) => handlePageLeaveRef.current(...args),
    });

    return () => {
      useIPPersonalStore.setState({ handlePageLeave: undefined });
    };
  }, []); // 空依赖数组

  // 添加浏览器刷新/关闭拦截
  useEffect(() => {
    const handleBeforeUnload = (e: BeforeUnloadEvent) => {
      const currentData = mindMapRef.current
        ? JSON.stringify(mindMapRef.current.getData())
        : "";
      if (currentData !== initialDataRef.current) {
        e.preventDefault();
        e.returnValue = "您有未保存的更改，确定要离开吗？";
        return e.returnValue;
      }
    };

    window.addEventListener("beforeunload", handleBeforeUnload);
    return () => {
      window.removeEventListener("beforeunload", handleBeforeUnload);
    };
  }, []);

  return (
    <div className="flex flex-col h-full">
      {/* {contextHolder} */}

      <header className="flex bg-white/5 border-b border-white/10 justify-between items-center px-[24px] py-[20px] h-[68px]">
        <div className="flex items-center gap-3">
          <h1 className="text-[rgba(255,255,255,0.95)] !font-pingfang !text-lg !font-semibold !leading-6">
            IP人设
          </h1>
          <AvatarList avatar={[PERSON.pic, BUSINESS.pic, BI.pic]} />
          <div className="flex items-center gap-2">
            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-400/10 text-blue-400 border border-blue-500/20">
              <svg
                className="w-3 h-3 mr-1"
                fill="currentColor"
                viewBox="0 0 20 20"
              >
                <path
                  fillRule="evenodd"
                  d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                  clipRule="evenodd"
                />
              </svg>
              人设专家
            </span>
          </div>
          <div className="flex items-center gap-2">
            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-400/10 text-blue-400 border border-blue-500/20">
              <svg
                className="w-3 h-3 mr-1"
                fill="currentColor"
                viewBox="0 0 20 20"
              >
                <path
                  fillRule="evenodd"
                  d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                  clipRule="evenodd"
                />
              </svg>
              商业洞察专家
            </span>
          </div>
          <div className="flex items-center gap-2">
            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-400/10 text-blue-400 border border-blue-500/20">
              <svg
                className="w-3 h-3 mr-1"
                fill="currentColor"
                viewBox="0 0 20 20"
              >
                <path
                  fillRule="evenodd"
                  d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                  clipRule="evenodd"
                />
              </svg>
              业务专家
            </span>
          </div>
        </div>
        <div className="flex gap-3 items-center">
          <button
            onClick={() => setIsChatVisible(!isChatVisible)}
            className={cn(
              "flex items-center gap-2 px-4 py-2 rounded-md transition-all",
              "bg-gradient-to-r from-blue-500/20 to-purple-500/20 hover:from-blue-500/30 hover:to-purple-500/30",
              "border border-white/10 text-white/90",
              "backdrop-blur-sm shadow-lg hover:shadow-blue-500/10",
              "transform hover:scale-105 active:scale-95",
              "duration-200 ease-in-out"
            )}
          >
            {isChatVisible ? (
              <>
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                >
                  <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z" />
                  <line x1="3" y1="21" x2="21" y2="3" />
                </svg>
                <span className="font-medium">关闭对话</span>
              </>
            ) : (
              <>
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="text-blue-400"
                >
                  <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z" />
                </svg>
                <span className="font-medium">操盘手助理</span>
              </>
            )}
          </button>
          <button
            onClick={handleSave}
            disabled={isSaving}
            className={cn(
              "flex items-center gap-2 px-4 py-2 rounded-md transition-all",
              "bg-gradient-to-r from-emerald-500/20 to-teal-500/20 hover:from-emerald-500/30 hover:to-teal-500/30",
              "border border-white/10 text-white/90",
              "backdrop-blur-sm shadow-lg hover:shadow-emerald-500/10",
              "transform hover:scale-105 active:scale-95",
              "duration-200 ease-in-out",
              isSaving && "opacity-70 cursor-not-allowed hover:scale-100"
            )}
          >
            {isSaving ? (
              <>
                <svg
                  className="animate-spin"
                  xmlns="http://www.w3.org/2000/svg"
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                >
                  <line x1="12" y1="2" x2="12" y2="6" />
                  <line x1="12" y1="18" x2="12" y2="22" />
                  <line x1="4.93" y1="4.93" x2="7.76" y2="7.76" />
                  <line x1="16.24" y1="16.24" x2="19.07" y2="19.07" />
                  <line x1="2" y1="12" x2="6" y2="12" />
                  <line x1="18" y1="12" x2="22" y2="12" />
                  <line x1="4.93" y1="19.07" x2="7.76" y2="16.24" />
                  <line x1="16.24" y1="7.76" x2="19.07" y2="4.93" />
                </svg>
                <span className="font-medium">保存中...</span>
              </>
            ) : (
              <>
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                >
                  <path d="M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z" />
                  <polyline points="17 21 17 13 7 13 7 21" />
                  <polyline points="7 3 7 8 15 8" />
                </svg>
                <span className="font-medium">保存</span>
              </>
            )}
          </button>
        </div>
      </header>
      <section className="margin-1 h-[calc(100vh-64px)] flex overflow-hidden relative">
        {/* 思维导图区域 - 更深的背景色 */}
        <article className="bg-[#1a1a1a] overflow-hidden h-full min-h-0 border border-white/10 relative flex-grow flex flex-col">
          <div
            id="mindMapContainer"
            ref={mindMapContainerRef}
            className="flex-1 w-full relative"
            style={{
              height: isFullscreen ? "100vh" : "100%",
              width: "100%", // 默认宽度始终为100%
            }}
          ></div>

          {/* 控制按钮容器 */}
          <div className="absolute bottom-4 left-4 flex gap-3 z-10">
            <button
              onClick={() => {
                toggleFullscreen();
              }}
              className="hover:bg-white/10 text-white/20 hover:text-white p-2 rounded-md transition-all"
            >
              {isFullscreen ? (
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="20"
                  height="20"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                >
                  <path d="M15 3h6v6M9 3H3v6M3 15v6h6M21 15v6h-6" />
                </svg>
              ) : (
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="20"
                  height="20"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                >
                  <path d="M3 9V3h6M21 9V3h-6M3 15v6h6M21 15v6h-6" />
                </svg>
              )}
            </button>
            <button
              onClick={async () => {
                if (mindMapRef.current) {
                  const data = await mindMapRef.current.doExport.png(
                    "思维导图",
                    false
                  );
                  const a = document.createElement("a");
                  a.href = data;
                  a.download = "思维导图.png";
                  a.click();
                }
              }}
              className="hover:bg-white/10 text-white/20 hover:text-white p-2 rounded-md transition-all"
              title="下载思维导图"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="20"
                height="20"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              >
                <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4" />
                <polyline points="7 10 12 15 17 10" />
                <line x1="12" y1="15" x2="12" y2="3" />
              </svg>
            </button>
          </div>
        </article>

        {/* 聊天区域 - 调整背景色以匹配整体设计 */}
        <AnimatePresence mode="wait">
          {isChatVisible && (
            <motion.article
              initial={{ x: "100%", opacity: 0 }}
              animate={{ x: 0, opacity: 1 }}
              exit={{ x: "100%", opacity: 0 }}
              transition={{
                type: "spring",
                stiffness: 300,
                damping: 30,
                opacity: { duration: 0.2 },
              }}
              className="absolute right-0 top-0 bottom-0 grid grid-rows-[1fr_auto] min-h-0 h-full border-l border-white/10 bg-[#1f1f1f]"
              style={{
                width: "30%",
                minWidth: "300px",
                maxWidth: "500px",
              }}
            >
              <div className="min-h-0 overflow-auto p-6 space-y-4">
                {messages.map((m, index) => {
                  // 如果没有内容或以 <START> 开头，直接跳过
                  if (!m.content || m.content.startsWith("<START>")) {
                    return null;
                  }
                  return (
                    <div
                      key={m.id || `message-${index}`} // 使用 m.id 或回退到索引
                      className={`flex items-start gap-3 ${
                        m.role === "user" ? "flex-row-reverse" : ""
                      }`}
                    >
                      {m.role === "user" ? (
                        <Avatar
                          key={`avatar-${m.id || index}`}
                          size="md"
                          className="shrink-0 grow-0"
                        />
                      ) : (
                        <Image
                          key={`image-${m.id || index}`}
                          src={AgentPic_5}
                          alt="avatar"
                          className="w-8 h-8 rounded-full flex-shrink-0"
                        />
                      )}
                      <div className="flex flex-col">
                        {m.role != "user" && (
                          <div className="text-sm text-white/50 pl-1">
                            操盘手助理
                          </div>
                        )}
                        <div
                          className={`p-4 rounded-xl ${
                            m.role === "user"
                              ? "bg-white/5 border border-white/10"
                              : "bg-black/20 border border-white/10"
                          } max-w-[85%]`}
                        >
                          <div className="text-white/90 prose prose-invert prose-sm max-w-none">
                            <ReactMarkdown
                              remarkPlugins={[remarkGfm]}
                              rehypePlugins={[rehypeRaw, rehypeSanitize]}
                              className="break-words"
                            >
                              {m.content}
                            </ReactMarkdown>
                          </div>
                        </div>
                      </div>
                    </div>
                  );
                })}

                {isLoading && (
                  <div className="flex items-start gap-3">
                    <Image
                      src={AgentPic_5}
                      alt="AI Avatar"
                      className="w-8 h-8 rounded-full flex-shrink-0"
                    />
                    <div className="bg-black/20 border border-white/10 p-4 rounded-xl max-w-[85%]">
                      <div className="flex items-center space-x-2 text-white/90">
                        <span className="text-sm">思考中</span>
                        <LoadingDots />
                      </div>
                    </div>
                  </div>
                )}
              </div>

              <form
                onSubmit={handleSubmit}
                className="p-4 border-t border-white/10"
              >
                <div className="relative">
                  <input
                    value={input}
                    onChange={handleInputChange}
                    disabled={isLoading}
                    className={cn(
                      "w-full p-4 rounded-xl bg-black/20",
                      "border border-white/10",
                      "text-sm text-white placeholder-white/30",
                      "focus:ring-2 focus:ring-white/20 focus:border-white/20",
                      "transition-all duration-200",
                      isLoading && "opacity-50 cursor-not-allowed"
                    )}
                    placeholder={
                      isLoading ? "AI 正在思考中..." : "输入您的问题..."
                    }
                  />
                  {isLoading && (
                    <div className="absolute right-4 top-1/2 -translate-y-1/2">
                      <LoadingDots />
                    </div>
                  )}
                </div>
              </form>
            </motion.article>
          )}
        </AnimatePresence>
      </section>
    </div>
  );
};

export default IpPersonaPage;
