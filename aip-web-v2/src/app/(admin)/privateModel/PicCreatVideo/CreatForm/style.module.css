.main {
  display: flex;
  justify-content: center;
  flex-grow: 1;
  width: 100%;
  height: 100%;
  padding-top: 40px;
  overflow-y: auto;
}
.formHeader {
  display: flex;
  width: 100%;
  display: flex;
  justify-content: space-between;
}
.noteBlock {
  width: 100%;
  border-radius: 4px;
  background: #27272a;
  padding: 16px 26px;
}
.noteTitle {
  color: rgba(255, 255, 255, 0.85);
  font-family: "PingFang SC";
  font-size: 16px;
  font-style: normal;
  font-weight: 500;
  line-height: 24px; /* 150% */
}
.noteSubTitle {
  color: rgba(255, 255, 255, 0.65);
  font-family: "PingFang SC";
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px; /* 183.333% */
}
.priceTxt {
  color: rgba(255, 255, 255, 0.65);
  font-family: "PingFang SC";
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  letter-spacing: 1px;
}
.payBtn {
  display: flex;
  align-items: center;
  gap: 6px;
}
.priceNumber {
  font-size: 16px;
  font-style: normal;
  font-weight: 500;
  color: #fff;
  opacity: 1;
  margin: 0 2px;
}
.uploadNode {
  color: rgba(255, 255, 255, 0.65);
  font-family: "PingFang SC";
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  margin-top: 10px;
}
.priceNote {
  color: rgba(255, 255, 255, 0.85);
  font-size: 14px;
  line-height: 22px;
  margin-top: 8px;
}
