import { Dispatch, FC, SetStateAction, useState } from "react";
import {
  Button,
  Form,
  Input,
  Radio,
  Space,
  Upload,
  message,
  Image,
} from "antd";
import type { GetProp, UploadFile, UploadProps } from "antd";
import IconSvg from "@/assets/svg";
import {
  ArrowRightOutlined,
  CheckCircleFilled,
  InfoCircleOutlined,
  LoadingOutlined,
  PlusOutlined,
  UnorderedListOutlined,
} from "@ant-design/icons";
import { GetServiceUrl } from "@/service/config";
import { paymentInit } from "@/service/fetchData";
import { useUserInfoStore } from "@/store/store";
import styles from "./style.module.css";
import { Lightbulb } from "lucide-react";
import { useModalStore } from "@/store/store";

const { TextArea } = Input;
const { CommonWx, CommonZfb } = IconSvg;
type FileType = Parameters<GetProp<UploadProps, "beforeUpload">>[0];

const layout = {
  labelCol: { span: 6 },
  wrapperCol: { span: 18 },
};
const tailLayout = {
  wrapperCol: { offset: 6, span: 18 },
};
interface CreateFormProps {
  createCallback: () => void;
  // openList: () => void;
  setPayType: Dispatch<SetStateAction<string>>;
  setQrcode_url: Dispatch<SetStateAction<string>>;
  setTrade_no: Dispatch<SetStateAction<string>>;
}
const fileTypes = ["image/jpeg", "image/png", "image/jpg", "image/webp"];
const MIMEType = "image/jpeg, image/png, image/jpg, image/webp";
const getBase64 = (file: any) =>
  new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => resolve(reader.result);
    reader.onerror = (error) => reject(error);
  });
const CreatForm: FC<CreateFormProps> = ({
  createCallback,
  // openList,
  setPayType,
  setQrcode_url,
  setTrade_no,
}) => {
  const currentIpUser = useUserInfoStore((state) => state.currentIpUser);
  const [previewOpen, setPreviewOpen] = useState(false);
  const [previewImage, setPreviewImage] = useState("");
  const [modalType, setModalType] = useState<string>("口播 (面对镜头)");

  const [messageApi, contextHolder] = message.useMessage();
  const [successState, setSucessState] = useState(false);
  const [saveLoading, setSaveLoading] = useState(false);
  const [fileList, setFileList] = useState<any[]>([]);
  const [uploadImageUrl, setUploadImageUrl] = useState("");
  const [loading, setLoading] = useState(false);
  const [form] = Form.useForm();
  const { updateDemoOpen, demoOpen } = useModalStore((state) => state);
  // 重置数据
  const onReset = () => {
    setModalType("口播 (面对镜头)");
    form.resetFields();
  };
  const beforeUpload = (file: FileType) => {
    const isJpgOrPng = fileTypes.includes(file.type);
    if (!isJpgOrPng) {
      messageApi.error("请上传 jpeg、png、jpg、webp 图片");
      return false;
    }
    const isLt2M = file.size / 1024 / 1024 < 50;
    if (!isLt2M) {
      messageApi.error("图片最大支持 50MB 大小");
      return false;
    }
    return isJpgOrPng && isLt2M;
  };
  const handleChange: UploadProps["onChange"] = (info) => {
    const { fileList: newFileList } = info;
    // 过滤掉限制上传图片类型
    setFileList(
      newFileList.filter((item: UploadFile<any>) =>
        fileTypes.includes(item.type || "")
      )
    );
    if (info.file.status === "uploading") {
      setLoading(true);
      return;
    }
    if (info.file.status === "done") {
      const {
        file: { response },
      } = info;
      if (response?.data?.length > 0) {
        setUploadImageUrl(response?.data[0]?.file_url);
      } else {
        setUploadImageUrl("");
      }
      setLoading(false);
    }
  };
  // 提交数据
  const onFinish = (values: any) => {
    console.log("values：", values, fileList);
    if (!uploadImageUrl) {
      messageApi.error("请选择上传图片！");
      return;
    }
    values.image_url = uploadImageUrl;
    values.pid = currentIpUser.id;
    const pay_type = values.pay_type;
    delete values.pay_type;
    form
      .validateFields()
      .then(() => {
        const params = {
          pay_type: pay_type,
          product_id: 4,
          param: values,
        };
        setSaveLoading(true);
        paymentInit(params)
          .then((res) => {
            if (res?.code === 200) {
              // 判断创建二维码状态
              if (res?.data?.code === 1) {
                console.log("创建支付图片:", res);
                setPayType(pay_type);
                setQrcode_url(res?.data?.img);
                setTrade_no(res?.data?.trade_no);
                // messageApi.success("保存成功！");
                createCallback();
              } else {
                messageApi.error(`${res.msg}`);
              }
            } else {
              messageApi.error(`保存失败:${res.msg}`);
            }
          })
          .finally(() => {
            setSaveLoading(false);
          });
      })
      .finally(() => {
        setSaveLoading(false);
      });
  };
  const handlePreview = async (file: any) => {
    if (!file.url && !file.preview) {
      file.preview = await getBase64(file.originFileObj);
    }
    setPreviewImage(file.url || file.preview);
    setPreviewOpen(true);
  };
  return (
    <>
      {contextHolder}
      {!successState && (
        <div className={styles.main}>
          <Form
            {...layout}
            style={{ minWidth: 600, maxWidth: 750 }}
            form={form}
            onFinish={onFinish}
          >
            <Form.Item wrapperCol={{ span: 24 }} style={{ marginBottom: 12 }}>
              <div className={styles.formHeader}>
                <div className="leading-[32px]">请填写图片制作模型需求：</div>
                <Button
                  type="text"
                  color="primary"
                  variant="link"
                  icon={<Lightbulb size={14} />}
                  onClick={() => {
                    updateDemoOpen(!demoOpen.picToVideo);
                  }}
                >
                  案例
                </Button>
                {/* <Button
                  icon={<UnorderedListOutlined />}
                  color="primary"
                  variant="link"
                  onClick={openList}
                >
                  查看历史记录
                </Button> */}
              </div>
            </Form.Item>
            <Form.Item wrapperCol={{ span: 24 }}>
              <div className={styles.noteBlock}>
                <Space align={"start"} size={15}>
                  <div className="mt-[2px]">
                    <InfoCircleOutlined
                      style={{ color: "#407BFF", fontSize: 18 }}
                    />
                  </div>
                  <Space direction="vertical" size={4}>
                    <div className={styles.noteTitle}>温馨提示</div>
                    <div>
                      <div className={styles.noteSubTitle}>
                        · 生成的视频与原图人物相似度在80%-90%（说话/表情变化时）
                      </div>
                      <div className={styles.noteSubTitle}>
                        · 建议不修改原图（如去耳环/换眼镜等），避免相似度下降
                      </div>
                    </div>
                  </Space>
                </Space>
              </div>
            </Form.Item>
            <Form.Item label="上传图片" required>
              <Upload
                name="files"
                accept={MIMEType}
                listType="picture-card"
                maxCount={1}
                // className="avatar-uploader"
                fileList={fileList}
                // showUploadList={false}
                action={`${GetServiceUrl()}/common/v3/file/upload`}
                beforeUpload={beforeUpload}
                onChange={handleChange}
                multiple={false}
                onPreview={handlePreview}
              >
                {fileList.length >= 1 ? null : (
                  <button
                    style={{ border: 0, background: "none" }}
                    type="button"
                  >
                    {loading ? <LoadingOutlined /> : <PlusOutlined />}
                    <div style={{ marginTop: 8 }}>点击上传</div>
                  </button>
                )}
              </Upload>
              <div className={styles.uploadNode}>
                仅支持png/jpg格式；为避免相似度下降，建议上传五官清晰正面图
              </div>
            </Form.Item>
            <Form.Item
              label="专属模型类型"
              required
              name="model_type"
              initialValue="口播 (面对镜头)"
            >
              <Radio.Group
                onChange={(e) => {
                  setModalType(e.target.value);
                }}
              >
                <Radio.Button value="口播 (面对镜头)">
                  口播 (面对镜头)
                </Radio.Button>
                <Radio.Button value="访谈 (不面对镜头)">
                  访谈 (不面对镜头)
                </Radio.Button>
              </Radio.Group>
            </Form.Item>
            {modalType === "口播 (面对镜头)" && (
              <Form.Item
                label="口播风格"
                required
                name="speech_style"
                initialValue="激情"
              >
                <Radio.Group>
                  <Radio.Button value="激情">激情</Radio.Button>
                  <Radio.Button value="笑容">笑容</Radio.Button>
                  <Radio.Button value="严肃">严肃</Radio.Button>
                  <Radio.Button value="悲伤">悲伤</Radio.Button>
                </Radio.Group>
              </Form.Item>
            )}
            {modalType === "访谈 (不面对镜头)" && (
              <Form.Item
                label="访谈风格"
                required
                name="interview_style"
                initialValue="对谈"
              >
                <Radio.Group>
                  <Radio.Button value="对谈">对谈</Radio.Button>
                  <Radio.Button value="教育">教育</Radio.Button>
                  <Radio.Button value="知识分享">知识分享</Radio.Button>
                </Radio.Group>
              </Form.Item>
            )}

            <Form.Item
              label="模型时长"
              required
              name="duration"
              initialValue="40-50s"
            >
              <Radio.Group>
                <Space>
                  <Radio.Button value="40-50s">40-50s</Radio.Button>
                  <div className={styles.priceTxt}>
                    <span className="text-white">¥</span>
                    <span className={styles.priceNumber}>699</span>
                    <span className="text-white">元</span>
                    <span> (包含1个图片制作视频模型)</span>
                  </div>
                </Space>
                {/* <div className={styles.uploadNode}>
                  模型制作费用请联系您的业务对接专员
                </div> */}
              </Radio.Group>
            </Form.Item>
            <Form.Item
              label="支付方式"
              name="pay_type"
              initialValue="wxpay"
              required
            >
              <Radio.Group size="large">
                <Radio.Button value="wxpay">
                  <div className={styles.payBtn}>
                    <CommonWx />
                    微信
                  </div>
                </Radio.Button>
                <Radio.Button value="alipay">
                  <div className={styles.payBtn}>
                    <CommonZfb />
                    支付宝
                  </div>
                </Radio.Button>
              </Radio.Group>
            </Form.Item>
            <Form.Item
              label="形象要求备注"
              name="appearance_note"
              initialValue=""
            >
              <TextArea
                showCount
                maxLength={500}
                placeholder="例：男士，坐在沙发前，身穿精致的西服，戴名贵手表，接受访谈"
                style={{
                  height: 120,
                  resize: "none",
                  width: "90%",
                  maxWidth: 500,
                }}
              />
            </Form.Item>
            <Form.Item {...tailLayout}>
              <Space>
                <Button type="primary" htmlType="submit" loading={saveLoading}>
                  ¥699元扫码支付
                </Button>
                <Button
                  htmlType="button"
                  onClick={onReset}
                  disabled={saveLoading}
                >
                  恢复默认
                </Button>
              </Space>
            </Form.Item>
          </Form>
          {/* {previewImage && (
            <Image
              wrapperStyle={{
                display: "none",
              }}
              preview={{
                visible: previewOpen,
                onVisibleChange: (visible) => setPreviewOpen(visible),
                afterOpenChange: (visible) => !visible && setPreviewImage(""),
              }}
              src={previewImage}
            />
          )} */}
        </div>
      )}
    </>
  );
};
export default CreatForm;
