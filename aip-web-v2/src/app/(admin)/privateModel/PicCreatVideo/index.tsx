import style from "./style.module.css";
import CreatForm from "./CreatForm";
import ShowDemo from "./ShowDemo";
import SuccessPage from "./SuccessPage";
// import DrawerPlanEle from "./DrawerPlan";
import { useState } from "react";
const PicCreatVideo = () => {
  const [success, setSuccess] = useState(false);
  const [payType, setPayType] = useState("wxpay");
  // const [open, setOpen] = useState(false);
  const [qrcode_url, setQrcode_url] = useState("");
  const [trade_no, setTrade_no] = useState("");
  const createCallbackEve = () => {
    setSuccess(true);
  };
  return (
    <>
      <div className={style.continer}>
        {success ? (
          <SuccessPage
            setPayType={setPayType}
            payType={payType}
            qrcode_url={qrcode_url}
            setQrcode_url={setQrcode_url}
            trade_no={trade_no}
            setTrade_no={setTrade_no}
            callbackEve={() => {
              setSuccess(false);
            }}
          />
        ) : (
          <>
            <CreatForm
              setPayType={setPayType}
              createCallback={createCallbackEve}
              setQrcode_url={setQrcode_url}
              setTrade_no={setTrade_no}
              // openList={() => {
              //   setOpen(true);
              // }}
            />
            <ShowDemo />
          </>
        )}
      </div>
      {/* <DrawerPlanEle open={open} setOpen={setOpen} /> */}
    </>
  );
};
export default PicCreatVideo;
