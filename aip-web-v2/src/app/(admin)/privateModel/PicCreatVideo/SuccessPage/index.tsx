import {
  CheckCircleFilled,
  CloseCircleFilled,
  SyncOutlined,
} from "@ant-design/icons";
import styles from "./style.module.css";
import Image from "next/image";
import { Button, Space, message } from "antd";
import {
  Dispatch,
  FC,
  SetStateAction,
  useEffect,
  useRef,
  useState,
} from "react";
import { paymentQuery } from "@/service/fetchData";
import ImageFile from "@/assets/images";
import IconSvg from "@/assets/svg";
import Agreement from "@/components/Agreement";
const { ContactUs } = ImageFile;
const { CommonMore, CommonWx, CommonZfb } = IconSvg;
interface SuccessPageProps {
  callbackEve: () => void;
  qrcode_url: string;
  trade_no: string;
  payType: string;
  setPayType: Dispatch<SetStateAction<string>>;
  setQrcode_url: Dispatch<SetStateAction<string>>;
  setTrade_no: Dispatch<SetStateAction<string>>;
}
const SuccessPage: FC<SuccessPageProps> = ({
  callbackEve,
  qrcode_url,
  trade_no,
  payType,
  setPayType,
  setQrcode_url,
  setTrade_no,
}) => {
  const [payStatus, setPayStatus] = useState(0); // 0:支付订单 1:支付中 2:支付成功 3:支付失败
  const [payBtnLoading, setPayBtnLoading] = useState(false);
  const [messageApi, contextHolder] = message.useMessage();
  const [checked, setChecked] = useState(false);
  const imgRef = useRef<HTMLImageElement>(null);
  const [qrcodeLoading, setQrcodeLoading] = useState(false); // 二维码加在状态
  useEffect(() => {
    const eve = function () {
      setQrcodeLoading(true);
    };
    if (imgRef.current) {
      imgRef.current.onload = eve; // 图片加载完成
    }
  }, []);
  return (
    <div className={styles.successMain}>
      <Agreement
        open={checked}
        setOpen={(val: boolean) => {
          setChecked(val);
        }}
      />
      {contextHolder}
      {/* 支付订单 */}
      {payStatus === 0 && (
        <div className={styles.pay_plan}>
          <div className={styles.pay_plan_title}>支付订单</div>
          <div className={styles.codePic}>
            {!qrcodeLoading && (
              <div className={styles.qrcode_loading}>
                <SyncOutlined spin style={{ fontSize: 36 }} />
              </div>
            )}
            <img
              src={qrcode_url}
              alt=""
              width="100%"
              height="100%"
              ref={imgRef}
            />
          </div>
          <div className={styles.pay_plan_info}>
            <Space size={6}>
              {payType === "wxpay" && (
                <>
                  <CommonWx />
                  <span>使用微信:</span>
                </>
              )}
              {payType === "alipay" && (
                <>
                  <CommonZfb />
                  <span>支付宝支付:</span>
                </>
              )}
              <span className="text-[12px]">¥</span>
              <span className={styles.pay_price}>699</span>
              <span className="text-[12px]">元</span>
            </Space>
            <div>
              支付即视为你同意
              <span
                className="text-[#407BFF] cursor-pointer"
                onClick={() => {
                  setChecked(true);
                }}
              >
                相关协议
              </span>
              条例
            </div>
            <div>若支付成功，请点击下方按钮确认</div>
          </div>
          <div>
            <Space size={16}>
              <Button
                type="primary"
                size="large"
                className="min-w-[128px]"
                loading={payBtnLoading}
                onClick={() => {
                  setTimeout(() => {
                    if (!trade_no) {
                      return;
                    }
                    setPayBtnLoading(true);
                    // 查询支付状态
                    // 1:支付成功 0:支付失败
                    paymentQuery(trade_no)
                      .then((res) => {
                        if (res.code === 200) {
                          if (res.data.status === 1) {
                            setPayStatus(1);
                          } else {
                            setPayStatus(2);
                          }
                          setPayType("wxpay");
                          setQrcode_url("");
                          setTrade_no("");
                        } else {
                          messageApi.error(res.msg);
                        }
                      })
                      .catch((err) => {
                        messageApi.error(`查询支付状态失败: ${err}`);
                      })
                      .finally(() => {
                        setPayBtnLoading(false);
                      });
                  }, 2000);
                }}
              >
                我已支付成功
              </Button>
              <Button size="large" className="w-[128px]" onClick={callbackEve}>
                返回
              </Button>
            </Space>
          </div>
        </div>
      )}

      {/* 支付中 */}
      {/* {payStatus === 1 && (
        <div className={styles.pay_leading}>
          <div></div>
          <div>支付中...</div>
          <div>稍后可前往【查看历史记录】中查看该支付订单</div>
          <div>
            <Button type="primary" onClick={callbackEve}>
              查看支付结果
            </Button>
            <Button>返回</Button>
          </div>
        </div>
      )} */}

      {/* 支付成功 */}
      {payStatus === 1 && (
        <div className={styles.pay_success}>
          <div>
            <CheckCircleFilled style={{ color: "#52c41b", fontSize: 60 }} />
          </div>
          <div className={styles.pay_state_title}>支付成功</div>
          <div className={styles.pay_state_info}>
            <div>视频专属模型生成的细节和交付</div>
            <div>请联系您的业务对接专员</div>
          </div>
          <div>
            <Button
              onClick={callbackEve}
              size="large"
              className="w-[128px] mt-[20px]"
            >
              返回
            </Button>
          </div>
        </div>
      )}

      {/* 支付失败 */}
      {payStatus === 2 && (
        <div className={styles.pay_success}>
          <div>
            <CloseCircleFilled style={{ color: "#FF4D4F", fontSize: 60 }} />
          </div>
          <div className={styles.pay_state_title}>支付失败</div>
          <div className={styles.pay_state_info}>
            系统未检测到支付成功信息，可重新扫码支付
          </div>
          <div className="mt-[20px]">
            <Space size={16}>
              <Button type="primary" onClick={callbackEve} size="large">
                重新扫码支付
              </Button>
              <Button
                onClick={callbackEve}
                size="large"
                className="min-w-[128px]"
              >
                返回
              </Button>
            </Space>
          </div>
        </div>
      )}

      {/* <div>
        <CheckCircleFilled style={{ color: "#52c41b", fontSize: 60 }} />
        <div className={styles.successTxt}></div>
      </div>

      <div className="text-[12px] mb-1">视频专属模型生成的细节和交付</div>
      <div className="text-[12px]">请联系您的业务对接专员</div>
      <div className="mt-4">
        <Button
          onClick={() => {
            callbackEve();
          }}
        >
          返回
        </Button>
      </div> */}
    </div>
  );
};

export default SuccessPage;
