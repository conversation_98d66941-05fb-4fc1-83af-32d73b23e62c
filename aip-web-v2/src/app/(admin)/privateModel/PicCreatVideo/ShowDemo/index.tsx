import { ArrowRightOutlined } from "@ant-design/icons";
import IconSvg from "@/assets/svg";
import ReactPlayer from "react-player";
import { VideoPauseBtn, VideoPlayBtn } from "@/components/VideoBtn";
import styles from "./style.module.css";
import { Al<PERSON>Triangle, Loader } from "lucide-react";
import { FC, useEffect, useRef, useState } from "react";
import { useModalStore } from "@/store/store";
const { WorkPauseSvg, WorkPlaySvg, WorkScreenSvg, WorkflowArrow } = IconSvg;
interface VideoPlayProps {
  url: string | undefined;
  clickState: boolean;
}

const VideoError = ({ error }: { error: string | null }) => {
  return (
    <div className={styles.error_overlay}>
      {error ? (
        <>
          <AlertTriangle className="w-8 h-8 text-red-500 mr-2" />
          <span className={styles.error_message}>{error}</span>
        </>
      ) : (
        <Loader className="w-8 h-8 animate-spin text-white" />
      )}
    </div>
  );
};
/** 视频内容 */
const VideoPlay: FC<VideoPlayProps> = ({ url, clickState }) => {
  const playerRef = useRef<ReactPlayer>(null);
  const [videoUrl, setVideoUrl] = useState<string>();
  const [isPlaying, setIsPlaying] = useState(false);
  const [Error, setError] = useState<string | null>(null);
  const handleReady = () => {
    setError(null);
  };
  const handleError = () => {
    setError("视频加载失败，请检查网络连接或视频源");
  };
  const FullscreenEve = () => {
    try {
      if (!document.fullscreenElement && playerRef.current) {
        const ele = playerRef.current.getInternalPlayer();
        ele.requestFullscreen();
      } else {
        if (document.exitFullscreen) {
          document.exitFullscreen();
        }
      }
    } catch (err) {
      console.error("全屏切换失败:", err);
    }
  };
  const handleEnded = () => {
    setIsPlaying(false);
  };
  useEffect(() => {
    if (clickState) {
      setVideoUrl(url);
      setIsPlaying(true);
    }
  }, [clickState]);
  return (
    <div className={styles.video_ele}>
      {Error ? (
        <VideoError error={Error} />
      ) : (
        <>
          <ReactPlayer
            ref={playerRef}
            url={videoUrl}
            width={"100%"}
            height={"100%"}
            light={false}
            playing={isPlaying}
            playsinline
            controls={false}
            onReady={handleReady}
            onError={handleError}
            onEnded={handleEnded}
          />
          <div className={styles.controls_overlay}>
            {isPlaying ? (
              <div className={styles.controls_btn_bg}>
                <WorkPauseSvg
                  className="size-4 fill-[#fff]"
                  onClick={() => setIsPlaying(false)}
                />
              </div>
            ) : (
              <div className={styles.controls_btn_bg}>
                <WorkPlaySvg
                  className="size-4 fill-[#fff]"
                  onClick={() => setIsPlaying(true)}
                />
              </div>
            )}
            <div className={styles.controls_btn_bg} onClick={FullscreenEve}>
              <WorkScreenSvg className="size-4 fill-[#fff]" />
            </div>
          </div>
        </>
      )}
    </div>
  );
};

interface DemoCaseProps {
  coverPic: string;
  video: string;
  title: string;
}
const DemoCase: FC<DemoCaseProps> = ({ coverPic, video, title }) => {
  const [clickPlay, setClickPlay] = useState(false);
  return (
    <div className={styles.case_showMain}>
      <div className={styles.case_showHead}>{title}</div>
      <div className={styles.case_content}>
        <div className={styles.case_block}>
          <div className={styles.case_blockTitle}>原图片</div>
          <img src={coverPic} className={styles.case_showPic} />
        </div>
        <div className={styles.case_icon}>
          <ArrowRightOutlined />
        </div>
        <div className={styles.case_block}>
          <div className={styles.case_blockTitle}>生成的视频模型</div>
          {clickPlay ? (
            <VideoPlay url={video} clickState={clickPlay} />
          ) : (
            <>
              <img src={coverPic} className={styles.case_showPic} />
              <div
                className={styles.playLayer}
                onClick={() => {
                  setClickPlay(true);
                }}
              >
                <VideoPlayBtn />
              </div>
            </>
          )}
        </div>
      </div>
    </div>
  );
};

const ShowDemo = () => {
  const { demoOpen, updateDemoOpen } = useModalStore((state) => state);
  const demoList = [
    {
      coverPic:
        "https://wechat-luobo.oss-cn-shanghai.aliyuncs.com/static/uploads/ab45836c2b1b11f0b96b00163e2a622e.jpg",
      video:
        "https://wechat-luobo.oss-cn-shanghai.aliyuncs.com/static/uploads/b9a201c42b1b11f0957800163e2a622e.mp4",
      title: "案例一",
    },
    {
      coverPic:
        "https://wechat-luobo.oss-cn-shanghai.aliyuncs.com/static/uploads/c793ffb22b1b11f0b1aa00163e2a622e.jpg",
      video:
        "https://wechat-luobo.oss-cn-shanghai.aliyuncs.com/static/uploads/d6711bfa2b1b11f0a2c400163e2a622e.mp4",
      title: "案例二",
    },
  ];
  return (
    <div
      className={`${styles.demoMain} ${
        demoOpen.picToVideo ? styles.demoMainOpen : ""
      }`}
    >
      <div
        className={styles.openCloseBtn}
        onClick={() => {
          updateDemoOpen(true);
        }}
      >
        <WorkflowArrow />
      </div>
      <div className={styles.demoHead}>图片生成视频模型案例：</div>
      <div className={`${styles.case_main} scroll_transparent`}>
        {demoList.map((item, index) => {
          return (
            <DemoCase
              key={`_${index}`}
              coverPic={item.coverPic}
              video={item.video}
              title={item.title}
            />
          );
        })}
      </div>

      {/* <div className={styles.demoShowMain}>
        <div className={styles.demoBlock}>
          <div className={styles.demoBlockTitle}>原图片</div>
          <img
            src="https://wechat-luobo.oss-cn-shanghai.aliyuncs.com/static/uploads/preview_b03aa1a0f40611ef808c00163e4d8bb0.mp4.jpg"
            className={styles.demoShowPic}
          />
        </div>
        <div className={styles.demoIcon}>
          <ArrowRightOutlined />
        </div>
        <div className={styles.demoBlock}>
          <div className={styles.demoBlockTitle}>生成的视频模型</div>
          {clickPlay ? (
            <VideoPlay
              url={
                "https://wechat-luobo.oss-cn-shanghai.aliyuncs.com/static/uploads/b03aa1a0f40611ef808c00163e4d8bb0.mp4"
              }
              clickState={clickPlay}
            />
          ) : (
            <>
              <img
                src="https://wechat-luobo.oss-cn-shanghai.aliyuncs.com/static/uploads/preview_b03aa1a0f40611ef808c00163e4d8bb0.mp4.jpg"
                className={styles.demoShowPic}
              />
              <div
                className={styles.playLayer}
                onClick={() => {
                  setClickPlay(true);
                }}
              >
                <VideoPlayBtn />
              </div>
            </>
          )}
        </div>
      </div> */}
    </div>
  );
};
export default ShowDemo;
