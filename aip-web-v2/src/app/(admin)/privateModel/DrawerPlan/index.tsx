import {
  Config<PERSON><PERSON><PERSON>,
  Drawer,
  Space,
  Table,
  Tag,
  message,
  Typography,
} from "antd";
import { Dispatch, FC, SetStateAction, useEffect, useState } from "react";
import styles from "./style.module.css";
import Image from "next/image";
import type { TableProps } from "antd";
import { adminPaymentOrderFind } from "@/service/fetchData";
import { useUserInfoStore } from "@/store/store";
import IconSvg from "@/assets/svg";
const { Paragraph, Text } = Typography;
const { CommonWx, CommonZfb } = IconSvg;

interface DataType {
  key: string;
  name: string;
  age: number;
  address: string;
  tags: string[];
}

interface DrawerPlanEleProps {
  open: boolean;
  setOpen: Dispatch<SetStateAction<boolean>>;
}
const DrawerPlanEle: FC<DrawerPlanEleProps> = ({ open, setOpen }) => {
  const currentIpUser = useUserInfoStore((state) => state.currentIpUser);
  const [messageApi, contextHolder] = message.useMessage();
  const [paginationConfig, setPaginationConfig] = useState({
    pageSize: 10,
    total: 0,
    current: 1,
    showSizeChanger: false,
    hideOnSinglePage: true,
  });
  const [columnsData, setColumusData] = useState<any[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const columns = [
    // {
    //   title: "序号",
    //   dataIndex: "index",
    //   key: "index",
    //   render: (text: any) => <a>{text}</a>,
    // },

    {
      title: "订单号",
      dataIndex: "trade_no",
      key: "trade_no",
    },
    {
      title: "产品名称",
      dataIndex: "product_name",
      key: "product_name",
    },
    {
      title: "模型时长",
      key: "duration",
      dataIndex: "duration",
      render: () => {
        return "40s-50s";
      },
    },
    {
      title: "支付方式",
      key: "payment_type",
      dataIndex: "payment_type",
      render: (text: string) => {
        if (text === "WXPAY") {
          return (
            <div className="flex items-center gap-2">
              <CommonWx />
              <span>微信</span>
            </div>
          );
        }
        if (text === "ALIPAY") {
          return (
            <div className="flex items-center gap-2">
              <CommonZfb />
              <span>支付宝</span>
            </div>
          );
        }
      },
    },
    {
      title: "状态",
      key: "trade_status",
      dataIndex: "trade_status",
      render: (text: string) => {
        if (text === "PENDING") {
          return <span className={styles.tag_blue}>支付中</span>;
        } else {
          return <span className={styles.tag_green}>支付成功</span>;
        }
      },
    },
    {
      title: "付款金额（元）",
      key: "total_amount",
      dataIndex: "total_amount",
      render: (txt: string) => {
        return txt || "-";
      },
    },
    {
      title: "创建时间",
      key: "created_at",
      dataIndex: "created_at",
      render: (txt: string) => {
        return txt || "-";
      },
    },
    {
      title: "付款时间",
      key: "pay_time",
      dataIndex: "pay_time",
      render: (txt: string) => {
        return txt || "-";
      },
    },
  ];
  const getVirtualModalData = ({
    page_num,
    page_size,
  }: {
    page_num: number;
    page_size: number;
  }) => {
    adminPaymentOrderFind({
      query: {
        customer_id: currentIpUser.id,
      },
      pager: { page_num, page_size },
    })
      .then((res) => {
        if (res.code === 200) {
          const list = res.data?.content?.map((item: any, index: number) => {
            return {
              ...item,
              index: index + 1,
            };
          });
          setColumusData(list || []);
          setPaginationConfig({
            ...paginationConfig,
            total: res.data?.pager?.total_record || 0,
            current: res.data?.pager?.page_num || 1,
          });
        } else {
          messageApi.error("获取列表数据失败");
        }
      })
      .finally(() => {});
  };
  useEffect(() => {
    if (open) {
      getVirtualModalData({ page_num: 1, page_size: 10 });
    }
  }, [open]);
  return (
    <>
      {contextHolder}
      <Drawer
        closable
        styles={{
          content: {
            backgroundColor: "#222222",
          },
          header: {
            height: "64px",
          },
          mask: {
            background: "rgba(0, 0, 0, 0.30)",
            backdropFilter: "blur(7.5px)",
          },
        }}
        destroyOnClose={true}
        title={<p>购买记录</p>}
        placement="right"
        open={open}
        loading={loading}
        width="80%"
        onClose={() => {
          setOpen(false);
        }}
      >
        <ConfigProvider
          theme={{
            components: {
              Table: {
                headerBg: "rgba(255, 255, 255, 0.06)",
                headerSplitColor: "transparent",
                borderColor: "rgba(107, 112, 117, 0.20)",
              },
            },
          }}
        >
          <Table
            rowKey="id"
            columns={columns}
            dataSource={columnsData}
            pagination={paginationConfig}
            onChange={(pagination) => {
              getVirtualModalData({
                page_num: pagination.current || 1,
                page_size: pagination.pageSize || 10,
              });
            }}
          />
        </ConfigProvider>
      </Drawer>
    </>
  );
};
export default DrawerPlanEle;
