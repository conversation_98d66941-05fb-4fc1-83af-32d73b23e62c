.demoMain {
  flex-shrink: 0;
  flex-grow: 0;
  display: flex;
  flex-direction: column;
  min-width: 460px;
  width: 35%;
  padding: 40px;
  border-left: 1px solid rgba(255, 255, 255, 0.3);
  transition: width 0.3s ease, minWidth 0.3s ease;
  position: relative;
}
.openCloseBtn {
  position: absolute;
  top: 0;
  bottom: 0;
  left: -19px;
  margin: auto;
  z-index: 10;
  width: 16px;
  height: 66px;
  cursor: pointer;
  opacity: 0;
  visibility: hidden;
}
.demoMain:hover .openCloseBtn {
  opacity: 1;
  visibility: visible;
}
.demoMainOpen {
  display: none;
}
.demoHead {
  color: #fff;
  font-size: 16px;
  font-style: normal;
  font-weight: 500;
  line-height: 22px; /* 137.5% */
  margin-bottom: 24px;
}
.case_main {
  flex-grow: 1;
  max-height: 100%;
  overflow-y: auto;
}
.case_showMain {
  width: 100%;
  margin-bottom: 24px;
}
.case_showMain:last-child {
  margin-bottom: 10px;
}
.case_showHead {
  color: #fff;
  font-family: "PingFang SC";
  font-size: 14px;
  font-style: normal;
  font-weight: 500;
  line-height: normal;
  margin-bottom: 16px;
}
.case_materialPic {
  width: 100%;
  aspect-ratio: 52/37;
  border-radius: 4px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}
.case_content {
  display: flex;
  width: 100%;
  align-items: center;
  justify-content: space-between;
}

.case_block {
  position: relative;
  width: 43%;
  max-width: 210px;
  flex-shrink: 1;
  aspect-ratio: 368/640;
}
.case_blockTitle {
  color: rgba(255, 255, 255, 0.65);
  text-align: center;
  font-family: "PingFang SC";
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  margin-bottom: 6px;
}
.case_icon {
  display: flex;
  width: 100%;
  justify-content: center;
  padding: 16px 0;
}
.case_showPic {
  width: 100%;
  height: 100%;
  aspect-ratio: 368/640;
  border-radius: 4px;
  min-width: 100px;
  object-fit: contain;
}

.demoShowVideo {
  width: 100%;
  height: 100%;
  flex-shrink: 0;
  aspect-ratio: 368/640;
  border-radius: 4px;
}

.error_message {
  font-size: 13px;
  color: rgba(255, 255, 255, 0.7);
}
.error_overlay {
  position: absolute;
  z-index: 50;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 12px;
  padding: 0 16px;
}
.video_ele {
  position: relative;
  cursor: pointer;
  height: 100%;
}
.controls_overlay {
  position: absolute;
  z-index: 51;
  width: 100%;
  height: 38px;
  bottom: 0;
  left: 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 12px;
  visibility: hidden;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.3));
  /* background: linear-gradient(
    to bottom,
    0% rgba(0, 0, 0, 0.2),
    80% rgba(0, 0, 0, 0.2),
    100% transparent
  ); */
}
.video_ele:hover .controls_overlay {
  visibility: visible;
}
.controls_btn_bg {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  cursor: pointer;
}
.controls_btn_bg:hover {
  background-color: rgba(102, 102, 102, 0.6);
}
.playLayer {
  position: absolute;
  z-index: 10;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
}
/**======================================================*/
