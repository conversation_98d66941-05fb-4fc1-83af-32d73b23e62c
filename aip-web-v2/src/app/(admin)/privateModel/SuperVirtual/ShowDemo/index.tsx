import { ArrowRightOutlined } from "@ant-design/icons";
import IconSvg from "@/assets/svg";
import ReactPlayer from "react-player";
import { VideoPauseBtn, VideoPlayBtn } from "@/components/VideoBtn";
import styles from "./style.module.css";
import { Al<PERSON><PERSON>riangle, Loader, ArrowDown } from "lucide-react";
import { FC, useEffect, useRef, useState } from "react";
import { useModalStore } from "@/store/store";
const { WorkPauseSvg, WorkPlaySvg, WorkScreenSvg, WorkflowArrow } = IconSvg;
interface VideoPlayProps {
  url: string | undefined;
  clickState: boolean;
}

const VideoError = ({ error }: { error: string | null }) => {
  return (
    <div className={styles.error_overlay}>
      {error ? (
        <>
          <AlertTriangle className="w-8 h-8 text-red-500 mr-2" />
          <span className={styles.error_message}>{error}</span>
        </>
      ) : (
        <Loader className="w-8 h-8 animate-spin text-white" />
      )}
    </div>
  );
};
/** 视频内容 */
const VideoPlay: FC<VideoPlayProps> = ({ url, clickState }) => {
  const playerRef = useRef<ReactPlayer>(null);
  const [videoUrl, setVideoUrl] = useState<string>();
  const [isPlaying, setIsPlaying] = useState(false);
  const [Error, setError] = useState<string | null>(null);
  const handleReady = () => {
    setError(null);
  };
  const handleError = () => {
    setError("视频加载失败，请检查网络连接或视频源");
  };
  const FullscreenEve = () => {
    try {
      if (!document.fullscreenElement && playerRef.current) {
        const ele = playerRef.current.getInternalPlayer();
        ele.requestFullscreen();
      } else {
        if (document.exitFullscreen) {
          document.exitFullscreen();
        }
      }
    } catch (err) {
      console.error("全屏切换失败:", err);
    }
  };
  const handleEnded = () => {
    setIsPlaying(false);
  };
  useEffect(() => {
    if (clickState) {
      setVideoUrl(url);
      setIsPlaying(true);
    }
  }, [clickState]);
  return (
    <div className={styles.video_ele}>
      {Error ? (
        <VideoError error={Error} />
      ) : (
        <>
          <ReactPlayer
            ref={playerRef}
            url={videoUrl}
            width={"100%"}
            height={"100%"}
            light={false}
            playing={isPlaying}
            playsinline
            controls={false}
            onReady={handleReady}
            onError={handleError}
            onEnded={handleEnded}
          />
          <div className={styles.controls_overlay}>
            {isPlaying ? (
              <div className={styles.controls_btn_bg}>
                <WorkPauseSvg
                  className="size-4 fill-[#fff]"
                  onClick={() => setIsPlaying(false)}
                />
              </div>
            ) : (
              <div className={styles.controls_btn_bg}>
                <WorkPlaySvg
                  className="size-4 fill-[#fff]"
                  onClick={() => setIsPlaying(true)}
                />
              </div>
            )}
            <div className={styles.controls_btn_bg} onClick={FullscreenEve}>
              <WorkScreenSvg className="size-4 fill-[#fff]" />
            </div>
          </div>
        </>
      )}
    </div>
  );
};

interface DemoCaseProps {
  coverPic: string;
  video: string;
  title: string;
}

const ShowDemo = () => {
  const [clickPlay, setClickPlay] = useState(false);
  const [videoPlayerList, setVideoPlayerList] = useState([
    {
      coverPic:
        "https://wechat-luobo.oss-cn-shanghai.aliyuncs.com/static/uploads/preview_73e323f6fd8b11efae6c00163e5be64a.mp4.jpg",
      video:
        "https://wechat-luobo.oss-cn-shanghai.aliyuncs.com/static/uploads/73e323f6fd8b11efae6c00163e5be64a.mp4",
      player: false,
      title: "案例一",
    },
    {
      coverPic:
        "https://wechat-luobo.oss-cn-shanghai.aliyuncs.com/static/uploads/preview_9e9bf58cfd8b11efbcc800163e5459f4.mp4.jpg",
      video:
        "https://wechat-luobo.oss-cn-shanghai.aliyuncs.com/static/uploads/9e9bf58cfd8b11efbcc800163e5459f4.mp4",
      player: false,
      title: "案例二",
    },
  ]);
  const { demoOpen, updateVirtalOpen } = useModalStore((state) => state);
  const playerEvent = (index: number) => {
    const list = videoPlayerList.map((item, i) => {
      return {
        ...item,
        player: index === i ? true : item.player,
      };
    });
    setVideoPlayerList(list);
  };
  return (
    <div
      className={`${styles.demoMain} ${
        demoOpen.virtual ? styles.demoMainOpen : ""
      }`}
    >
      <div
        className={styles.openCloseBtn}
        onClick={() => {
          updateVirtalOpen(true);
        }}
      >
        <WorkflowArrow />
      </div>
      <div className={styles.demoHead}>超虚拟人生成视频案例：</div>
      <div className={`scroll_transparent ${styles.case_main}`}>
        <div className={styles.case_showMain}>
          <div className={styles.case_showHead}>提供的形象视频素材</div>
          <div className={styles.case_material}>
            <img
              className={styles.case_materialPic}
              src="https://wechat-luobo.oss-cn-shanghai.aliyuncs.com/static/uploads/0757582cfd8311efb73900163e5be64a.png"
            />
          </div>
          <div className={styles.case_icon}>
            <ArrowDown size={24} color="#A3A3A4" />
          </div>
          <div className={styles.case_showHead}>生成的视频案例</div>
          <div className={styles.case_content}>
            {videoPlayerList.map((item, index) => (
              <div className={styles.case_block} key={`v_${index}`}>
                <div className={styles.case_blockTitle}>{item.title}</div>
                {item.player ? (
                  <VideoPlay url={item.video} clickState={item.player} />
                ) : (
                  <>
                    <img src={item.coverPic} className={styles.case_showPic} />
                    <div
                      className={styles.playLayer}
                      onClick={() => {
                        playerEvent(index);
                      }}
                    >
                      <VideoPlayBtn />
                    </div>
                  </>
                )}
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};
export default ShowDemo;
