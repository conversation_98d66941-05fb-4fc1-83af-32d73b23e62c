import style from "./style.module.css";
import CreatForm from "./CreatForm";
import { useState } from "react";
import SuccessPage from "./SuccessPage";
// import DrawerPlanEle from "./DrawerPlan";
import ShowDemo from "./ShowDemo";
const SuperVirtual = () => {
  const [success, setSuccess] = useState(false);
  // const [open, setOpen] = useState(false);
  const [payType, setPayType] = useState("wxpay");
  const [qrcode_url, setQrcode_url] = useState("");
  const [trade_no, setTrade_no] = useState("");
  const [params, setParams] = useState<{ [key: string]: any }>({});
  const createCallbackEve = () => {
    setSuccess(true);
  };
  return (
    <>
      <div className={style.continer}>
        {success ? (
          <SuccessPage
            qrcode_url={qrcode_url}
            trade_no={trade_no}
            params={params}
            payType={payType}
            setPayType={setPayType}
            setQrcode_url={setQrcode_url}
            setTrade_no={setTrade_no}
            setParams={setParams}
            callbackEve={() => {
              setSuccess(false);
            }}
          />
        ) : (
          <>
            <CreatForm
              setQrcode_url={setQrcode_url}
              setPayType={setPayType}
              setTrade_no={setTrade_no}
              createCallback={createCallbackEve}
              setParams={setParams}
              // openList={() => {
              //   setOpen(true);
              // }}
            />
            <ShowDemo />
          </>
        )}
      </div>
      {/* <DrawerPlanEle open={open} setOpen={setOpen} /> */}
    </>
  );
};
export default SuperVirtual;
