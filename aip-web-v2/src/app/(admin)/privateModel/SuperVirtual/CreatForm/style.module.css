.main {
  display: flex;
  justify-content: center;
  flex-grow: 1;
  width: 100%;
  height: 100%;
  padding-top: 40px;
}
.formHeader {
  width: 100%;
  display: flex;
  justify-content: space-between;
}
.customTxt {
  color: rgba(255, 255, 255, 0.65);
  text-align: center;
  font-family: "PingFang SC";
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
}
.linkCustom {
  display: flex;
  flex-direction: column;
  gap: 10px;
  width: 212px;
}
.noteBlock {
  width: 100%;
  border-radius: 4px;
  background: #27272a;
  padding: 16px 26px;
}
.noteTitle {
  color: rgba(255, 255, 255, 0.85);
  font-family: "PingFang SC";
  font-size: 16px;
  font-style: normal;
  font-weight: 500;
  line-height: 24px; /* 150% */
}
.noteSubTitle {
  color: rgba(255, 255, 255, 0.65);
  font-family: "PingFang SC";
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px; /* 183.333% */
}
.switchNote {
  position: absolute;
  color: rgba(255, 255, 255, 0.85);
  font-family: "PingFang SC";
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: 18px;
  margin-top: 4px;
}
.uploadNode {
  color: rgba(255, 255, 255, 0.65);
  font-family: "PingFang SC";
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  margin-top: 10px;
}
.priceTxt {
  display: flex;
  align-items: baseline;
  gap: 3px;
}
.payBtn {
  display: flex;
  align-items: center;
  gap: 6px;
}
.priceTxt_type,
.priceTxt_unit,
.priceTxt_desc {
  color: #fff;
  font-size: 12px;
  font-weight: 600;
  line-height: 12px;
}
.priceTxt_num {
  color: #fff;
  font-size: 24px;
  line-height: 24px;
  font-weight: 600;
}
.priceTxt_desc {
  opacity: 0.65;
}
