import { Dispatch, FC, SetStateAction, useState } from "react";
import {
  Button,
  Form,
  GetProp,
  Input,
  Radio,
  Space,
  Switch,
  Upload,
  UploadFile,
  UploadProps,
  message,
} from "antd";
import Image from "next/image";
import { saveVirtual, paymentInit } from "@/service/fetchData";
import IconSvg from "@/assets/svg";
import {
  ArrowRightOutlined,
  CheckCircleFilled,
  InfoCircleOutlined,
  LoadingOutlined,
  PlusOutlined,
  UnorderedListOutlined,
} from "@ant-design/icons";
import { GetServiceUrl } from "@/service/config";
import styles from "./style.module.css";
import { useUserInfoStore } from "@/store/store";
import ImageFile from "@/assets/images";
import { Lightbulb } from "lucide-react";
import { useModalStore } from "@/store/store";
const { ContactUs } = ImageFile;
const { CommonWx, CommonZfb } = IconSvg;
const { TextArea } = Input;

const layout = {
  labelCol: { span: 6 },
  wrapperCol: { span: 18 },
};
const tailLayout = {
  wrapperCol: { offset: 6, span: 18 },
};
interface CreateFormProps {
  createCallback: () => void;
  // openList: () => void;
  setPayType: Dispatch<SetStateAction<string>>;
  setQrcode_url: Dispatch<SetStateAction<string>>;
  setTrade_no: Dispatch<SetStateAction<string>>;
  setParams: Dispatch<SetStateAction<{ [key: string]: any }>>;
}
const fileTypes = ["image/jpeg", "image/png", "image/jpg"];
const MIMEType = "image/jpeg, image/png, image/jpg";
type FileType = Parameters<GetProp<UploadProps, "beforeUpload">>[0];
const getBase64 = (file: any) =>
  new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => resolve(reader.result);
    reader.onerror = (error) => reject(error);
  });
const CreatForm: FC<CreateFormProps> = ({
  createCallback,
  setPayType,
  // openList,
  setQrcode_url,
  setTrade_no,
  setParams,
}) => {
  const currentIpUser = useUserInfoStore((state) => state.currentIpUser);
  const [modalType, setModalType] = useState<string>("口播 (面对镜头)");
  const [saveLoading, setSaveLoading] = useState(false);
  const [gather, setGather] = useState(false);
  const [messageApi, contextHolder] = message.useMessage();
  const [successState, setSucessState] = useState(false);
  const [fileList, setFileList] = useState<any[]>([]);
  const [uploadImageUrl, setUploadImageUrl] = useState(""); // 上传图片地址
  const [previewImage, setPreviewImage] = useState(""); // 预览图片地址
  const [loading, setLoading] = useState(false); // 上传loading
  const [previewOpen, setPreviewOpen] = useState(false); // 预览图片弹窗
  const [form] = Form.useForm();
  const { demoOpen, updateVirtalOpen } = useModalStore((state) => state);
  // 重置数据
  const onReset = () => {
    setModalType("口播 (面对镜头)");
    form.resetFields();
  };
  const beforeUpload = (file: FileType) => {
    const isJpgOrPng = fileTypes.includes(file.type);
    if (!isJpgOrPng) {
      messageApi.error("请上传 jpeg、png、jpg、webp 图片");
      return false;
    }
    const isLt2M = file.size / 1024 / 1024 < 50;
    if (!isLt2M) {
      messageApi.error("图片最大支持 50MB 大小");
      return false;
    }
    return isJpgOrPng && isLt2M;
  };
  const handleChange: UploadProps["onChange"] = (info) => {
    const { fileList: newFileList } = info;
    // 过滤掉限制上传图片类型
    setFileList(
      newFileList.filter((item: UploadFile<any>) =>
        fileTypes.includes(item.type || "")
      )
    );
    if (info.file.status === "uploading") {
      setLoading(true);
      return;
    }
    if (info.file.status === "done") {
      const {
        file: { response },
      } = info;
      if (response?.data?.length > 0) {
        setUploadImageUrl(response?.data[0]?.file_url);
      } else {
        setUploadImageUrl("");
      }
      setLoading(false);
    }
  };
  const handlePreview = async (file: any) => {
    if (!file.url && !file.preview) {
      file.preview = await getBase64(file.originFileObj);
    }
    setPreviewImage(file.url || file.preview);
    setPreviewOpen(true);
  };
  // 提交数据
  const onFinish = (values: any) => {
    console.log("values：", values, fileList);
    // if (!uploadImageUrl) {
    //   messageApi.error("请选择上传图片！");
    //   return;
    // }

    form.validateFields().then(() => {
      values.has_collection = gather ? 0 : 1;
      values.pid = currentIpUser.id;
      // 上传有参考图片，就上传
      if (uploadImageUrl) {
        values.image_url = uploadImageUrl; // 图片地址
      }
      const pay_type = values.pay_type;
      delete values.pay_type;
      const params = {
        pay_type: pay_type,
        product_id: gather ? 5 : 6, // 5：超虚拟人模型 699、 6:超虚拟人模型 1500
        param: values,
      };
      paymentInit(params).then((res) => {
        if (res?.code === 200) {
          if (res?.data?.code === 1) {
            setQrcode_url(res?.data?.img);
            setPayType(pay_type);
            setTrade_no(res?.data?.trade_no);
            // messageApi.success("保存成功！");
            setParams(values);
            createCallback();
          } else {
            messageApi.error(`${res.msg}`);
          }
        } else {
          messageApi.error(`失败:${res.msg}`);
        }
      });
    });
    console.log("values：", values);
  };
  return (
    <>
      {contextHolder}
      {!successState && (
        <div className={styles.main}>
          <Form
            {...layout}
            style={{ minWidth: 600, maxWidth: 750 }}
            form={form}
            onFinish={onFinish}
          >
            <Form.Item wrapperCol={{ span: 24 }} style={{ marginBottom: 12 }}>
              <div className={styles.formHeader}>
                <div className="leading-[32px]">请填写图片制作模型需求：</div>
                <Button
                  type="text"
                  color="primary"
                  variant="link"
                  icon={<Lightbulb size={14} />}
                  onClick={() => {
                    updateVirtalOpen(!demoOpen.virtual);
                  }}
                >
                  案例
                </Button>
              </div>
            </Form.Item>

            <Form.Item wrapperCol={{ span: 24 }}>
              <div className={styles.noteBlock}>
                <Space align={"start"} size={15}>
                  <div className="mt-[2px]">
                    <InfoCircleOutlined
                      style={{ color: "#407BFF", fontSize: 18 }}
                    />
                  </div>
                  <Space direction="vertical" size={4}>
                    <div className={styles.noteTitle}>温馨提示</div>
                    <div>
                      <div className={styles.noteSubTitle}>
                        · 人物采集需前往现场（上海总部）进行人物采集
                      </div>
                      <div className={styles.noteSubTitle}>
                        ·
                        现场将采集多段多角度视频，人物还原度90%-100%，可参考【案例】
                      </div>
                      <div className={styles.noteSubTitle}>
                        · 仅支持大致场景/服装指定，不可指定具体款式
                      </div>
                      <div className={styles.noteSubTitle}>
                        · 如需替换为具体的场景图片会大幅降低相似度
                      </div>
                    </div>
                  </Space>
                </Space>
              </div>
            </Form.Item>
            <Form.Item label="上传参考图片">
              <Upload
                name="files"
                accept={MIMEType}
                listType="picture-card"
                maxCount={1}
                // className="avatar-uploader"
                fileList={fileList}
                // showUploadList={false}
                action={`${GetServiceUrl()}/common/v3/file/upload`}
                beforeUpload={beforeUpload}
                onChange={handleChange}
                multiple={false}
                onPreview={handlePreview}
              >
                {fileList.length >= 1 ? null : (
                  <button
                    style={{ border: 0, background: "none" }}
                    type="button"
                  >
                    {loading ? <LoadingOutlined /> : <PlusOutlined />}
                    <div style={{ marginTop: 8 }}>点击上传</div>
                  </button>
                )}
              </Upload>
              <div className={styles.uploadNode}>
                <span>· 仅支持png/jpg格式</span>
                <br />
                <span>· 上传的图片仅作为参考，不支持100%复刻</span>
                <br />
                <span>· 请勿上传受版权保护的图片</span>
              </div>
            </Form.Item>
            <Form.Item label="已完成人物采集" required>
              <Switch
                defaultChecked={false}
                onChange={(checked) => {
                  console.log("checked:", checked);
                  setGather(checked);
                }}
              />
              <div className={styles.switchNote}>
                若未采集，需联系业务对接专员先进行人物采集
              </div>
            </Form.Item>
            {/* {!gather && (
              <Form.Item label="请联系客户成功">
                <div className={styles.linkCustom}>
                  <Image
                    src={ContactUs}
                    alt=""
                    width={210}
                    height={212}
                    style={{ borderRadius: 5, overflow: "hidden" }}
                  />
                  <div className={styles.customTxt}>
                    联系客户成功进行人物采集
                  </div>
                </div>
              </Form.Item>
            )} */}

            <Form.Item
              label="专属模型类型"
              required
              name="model_type"
              initialValue="口播 (面对镜头)"
            >
              <Radio.Group
                onChange={(e) => {
                  setModalType(e.target.value);
                }}
              >
                <Radio.Button value="口播 (面对镜头)">
                  口播 (面对镜头)
                </Radio.Button>
                <Radio.Button value="访谈 (不面对镜头)">
                  访谈 (不面对镜头)
                </Radio.Button>
              </Radio.Group>
            </Form.Item>
            {modalType === "口播 (面对镜头)" && (
              <Form.Item
                label="口播风格"
                required
                name="speech_style"
                initialValue="激情"
              >
                <Radio.Group>
                  <Radio.Button value="激情">激情</Radio.Button>
                  <Radio.Button value="笑容">笑容</Radio.Button>
                  <Radio.Button value="严肃">严肃</Radio.Button>
                  <Radio.Button value="悲伤">悲伤</Radio.Button>
                </Radio.Group>
              </Form.Item>
            )}
            {modalType === "访谈 (不面对镜头)" && (
              <Form.Item
                label="访谈风格"
                required
                name="interview_style"
                initialValue="对谈"
              >
                <Radio.Group>
                  <Radio.Button value="对谈">对谈</Radio.Button>
                  <Radio.Button value="教育">教育</Radio.Button>
                  <Radio.Button value="知识分享">知识分享</Radio.Button>
                </Radio.Group>
              </Form.Item>
            )}

            <Form.Item required label="模型时长">
              <Space size={20}>
                <Form.Item
                  noStyle
                  required
                  name="duration"
                  initialValue="40-50s"

                  // extra={
                  //   <div className={styles.uploadNode}>
                  //     模型制作费用请联系您的业务对接专员
                  //   </div>
                  // }
                >
                  <Radio.Group>
                    <Radio.Button value="40-50s">40-50s</Radio.Button>
                  </Radio.Group>
                </Form.Item>
                {gather ? (
                  <div className={styles.priceTxt}>
                    <span className={styles.priceTxt_type}>¥</span>
                    <span className={styles.priceTxt_num}>699</span>
                    <span className={styles.priceTxt_unit}>元</span>
                    <span className={styles.priceTxt_desc}>
                      (包含1个超虚拟人模型制作)
                    </span>
                  </div>
                ) : (
                  <div className={styles.priceTxt}>
                    <span className={styles.priceTxt_type}>¥</span>
                    <span className={styles.priceTxt_num}>1500</span>
                    <span className={styles.priceTxt_unit}>元</span>
                    <span className={styles.priceTxt_desc}>
                      (包含现场采集+1个超虚拟人模型制作)
                    </span>
                  </div>
                )}
              </Space>
            </Form.Item>
            <Form.Item
              label="支付方式"
              name="pay_type"
              initialValue="wxpay"
              required
            >
              <Radio.Group size="large">
                <Radio.Button value="wxpay">
                  <div className={styles.payBtn}>
                    <CommonWx />
                    微信
                  </div>
                </Radio.Button>
                <Radio.Button value="alipay">
                  <div className={styles.payBtn}>
                    <CommonZfb />
                    支付宝
                  </div>
                </Radio.Button>
              </Radio.Group>
            </Form.Item>
            <Form.Item
              label="形象要求备注"
              name="appearance_note"
              initialValue=""
            >
              <TextArea
                showCount
                maxLength={500}
                placeholder="例：男士，坐在沙发前，身穿精致的西服，戴名贵手表，接受访谈"
                style={{
                  height: 120,
                  resize: "none",
                  width: "90%",
                  maxWidth: 500,
                }}
              />
            </Form.Item>
            <Form.Item {...tailLayout}>
              <Space>
                {gather ? (
                  <Button
                    type="primary"
                    htmlType="submit"
                    loading={saveLoading}
                  >
                    ¥699元扫码支付
                  </Button>
                ) : (
                  <Button
                    type="primary"
                    htmlType="submit"
                    loading={saveLoading}
                  >
                    ¥1500元扫码支付
                  </Button>
                )}

                <Button
                  htmlType="button"
                  onClick={onReset}
                  disabled={saveLoading}
                >
                  恢复默认
                </Button>
              </Space>
            </Form.Item>
          </Form>
        </div>
      )}
    </>
  );
};
export default CreatForm;
