.successMain {
  display: flex;
  justify-content: center;
  width: 100%;
}
.pay_plan {
  padding-top: 78px;
  gap: 20px;
}
.pay_plan_title {
  color: rgba(255, 255, 255, 0.85);
  text-align: center;
  font-size: 24px;
  font-weight: 500;
}
.codePic {
  position: relative;
  display: flex;
  justify-content: center;
  overflow: hidden;
}
.codePic img {
  width: 220px;
  height: 220px;
  flex-shrink: 0;
  aspect-ratio: 1/1;
  border-radius: 8px;
  background-color: rgba(255, 255, 255, 0.3);
}
.pay_plan_info {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  line-height: 16px;
  text-align: center;
  font-size: 16px;
}
.pay_price {
  font-size: 20px;
  color: #fff;
  font-weight: 600;
}
/* =================================================== */
.pay_leading,
.pay_success {
  padding-top: 172px;
}
.pay_plan,
.pay_leading,
.pay_success {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.pay_state_title {
  color: rgba(255, 255, 255, 0.85);
  text-align: center;
  font-size: 24px;
  font-weight: 500;
  margin-top: 20px;
}
.pay_state_info {
  display: flex;
  flex-direction: column;
  color: rgba(255, 255, 255, 0.85);
  text-align: center;
  font-size: 16px;
  line-height: 28px;
  margin-top: 6px;
}
/* .successTxt {
  font-size: 16px;
  margin: 12px 0;
} */
.qrcode_loading {
  position: absolute;
  z-index: 10;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
}
