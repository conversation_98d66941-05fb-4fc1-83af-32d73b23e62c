"use client";
import { <PERSON><PERSON>, Drawer } from "antd";
import type { TabsProps } from "antd";
import PicCreatVideo from "./PicCreatVideo";
import ModelPrivate from "./Model";
import SuperVirtual from "./SuperVirtual";
import styles from "./style.module.css";
import { FC, useState } from "react";
import { Lightbulb } from "lucide-react";
// import { useModalStore } from "@/store/store";
import { UnorderedListOutlined } from "@ant-design/icons";
import DrawerPlanEle from "./DrawerPlan";
interface PlanBlockProps {
  index: number;
  currentIndex: number;
  children: React.ReactNode;
}
const PlanBlock: FC<PlanBlockProps> = ({ index, currentIndex, children }) => {
  return (
    <div
      className={`${styles.planMain} ${
        currentIndex === index ? styles.planShow : ""
      } ${currentIndex > index ? styles.planPrev : ""} 
      ${currentIndex < index ? styles.planNext : ""}`}
    >
      {children}
    </div>
  );
};
const TABS = [
  {
    key: 0,
    index: 0,
    label: "私有模型",
  },
  {
    key: 1,
    index: 1,
    label: "图片制作视频模型",
  },
  {
    key: 2,
    index: 2,
    label: "超虚拟人模型制作",
  },
];
const ModelConstruction = () => {
  // const { demoOpen, updateHistoryOpen } = useModalStore((state) => state);
  const [open, setOpen] = useState(false);
  const [currentPlan, setCurrentPlan] = useState({
    key: 0,
    index: 0,
    label: "私有模型",
  });

  return (
    <div className={styles.page}>
      <div className={styles.header}>
        <div className={styles.tabs}>
          {TABS.map((item, index) => (
            <div
              key={`${item.key}-${index}`}
              className={`${styles.tabsLable} ${
                currentPlan.index === index ? styles.tabActive : ""
              }`}
              onClick={() => {
                setCurrentPlan(item);
              }}
            >
              {item.label}
            </div>
          ))}
        </div>
        <div className={styles.tabBarExtraContent}>
          {[1, 2].includes(currentPlan.index) && (
            <Button
              type="text"
              icon={<UnorderedListOutlined />}
              onClick={() => {
                console.log("*******");
                setOpen(!open);
                // updateHistoryOpen(!demoOpen.history);
              }}
            >
              购买历史
            </Button>
          )}
        </div>
      </div>
      <div className={styles.main}>
        <PlanBlock index={0} currentIndex={currentPlan.index}>
          <ModelPrivate />
        </PlanBlock>
        <PlanBlock index={1} currentIndex={currentPlan.index}>
          <PicCreatVideo />
        </PlanBlock>
        <PlanBlock index={2} currentIndex={currentPlan.index}>
          <SuperVirtual />
        </PlanBlock>
      </div>
      <DrawerPlanEle open={open} setOpen={setOpen} />
    </div>
  );
};
export default ModelConstruction;
