import React, {
  useState,
  useEffect,
  useCallback,
  useRef,
  FC,
  ReactNode,
} from "react";
import { Play, Pause, MoreVertical, Plus, Trash2 } from "lucide-react";
import { Dropdown } from "antd";
import { formatDistance } from "date-fns";
import { zhCN } from "date-fns/locale";
import styles from "./style.module.css";
import AnimationLine from "@/components/AudioAnimation";
import DeleteConfirm from "../DeleteConfirm";
import { AnimatePresence, motion } from "framer-motion";

const AudioBlock: React.FC<{ children: ReactNode; className?: string }> = ({
  children,
  className,
}) => {
  return (
    <div
      className={`flex flex-col justify-between w-[337px] h-[150px] bg-gradient-to-br from-zinc-800/90 to-zinc-700/90 rounded-lg p-3.5 mr-3 mb-3 hover:from-zinc-700/90 hover:to-zinc-600/90 transition-all duration-300 ease-in-out shadow-lg hover:shadow-xl cursor-pointer ${
        className || ""
      }`}
    >
      {children}
    </div>
  );
};

interface AudioListProps {
  children: ReactNode;
}

export const AudioList: React.FC<AudioListProps> = ({ children }) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const audioList = React.Children.toArray(children);
  const visibleAudios = isExpanded ? audioList : audioList.slice(0, 2);

  return (
    <div className="relative">
      <div className="flex flex-wrap">{visibleAudios}</div>
      {audioList.length > 2 && (
        <button
          className="absolute -bottom-8 left-1/2 transform -translate-x-1/2 text-sm text-blue-500 hover:text-blue-600"
          onClick={() => setIsExpanded(!isExpanded)}
        >
          {isExpanded ? "收起" : "展开"}
        </button>
      )}
    </div>
  );
};

export const AudioCardAdd = () => {
  return (
    <AudioBlock className="mb-8">
      <div className="h-full w-full flex justify-center items-center cursor-pointer">
        <Plus size={36} />
      </div>
    </AudioBlock>
  );
};

interface AudioCardProps {
  data: {
    id: number;
    voice_name: string;
    created_at: string;
    status: number;
    playStatus: boolean;
    voice_url: string;
    del_flag: number;
    index: number;
    disabled?: boolean;
  };
  stopAndPlayVideo: (type: string, index: number, play: boolean) => void;
  updateStatus: (data: any) => void;
  deleteAudio: (id: number, callback: () => void) => void;
}

const AudioCard: React.FC<AudioCardProps> = ({
  data,
  stopAndPlayVideo,
  updateStatus,
  deleteAudio,
}) => {
  const [isHovered, setIsHovered] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const audioRef = useRef<HTMLAudioElement>(null);
  const [deleteOpen, setDeleteOpen] = useState(false);
  const [isVisible, setIsVisible] = useState(true);

  const handleDelete = async () => {
    deleteAudio(data.id, () => {
      setDeleteOpen(false);
    });
  };

  // 播放/暂停处理
  const handlePlayPause = () => {
    if (!audioRef.current) return;

    try {
      // 如果当前音频正在播放，则停止播放
      if (data.playStatus) {
        audioRef.current.pause();
        audioRef.current.currentTime = 0; // 重置播放进度
        stopAndPlayVideo("audio", data.id, false);
      } else {
        // 在播放新音频之前，确保所有其他音频都停止播放
        const allAudioElements = document.querySelectorAll("audio");
        allAudioElements.forEach((audio) => {
          audio.pause();
          audio.currentTime = 0;
        });

        // 设置新的音频源并播放
        if (!audioRef.current.src || audioRef.current.src !== data.voice_url) {
          audioRef.current.src = data.voice_url;
        }
        audioRef.current.play();
        stopAndPlayVideo("audio", data.id, true);
      }
    } catch (err) {
      setError("音频播放失败，请检查文件格式或网络连接");
      console.error("音频播放错误:", err);
    }
  };

  // 处理音频加载错误
  const handleAudioError = () => {
    setError("音频加载失败，请检查文件路径");
  };

  // 处理音频播放结束
  const handleAudioEnded = () => {
    stopAndPlayVideo("audio", data.id, false);
  };

  if (data.del_flag !== 1) return null;

  // 禁用卡片排序逻辑
  const orderClass = data.status === 2 ? "order-last" : "";

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className={`relative rounded-lg p-5 border border-white/[0.08] ${orderClass} ${
          data.status === 2
            ? "bg-zinc-900/50 cursor-not-allowed opacity-50"
            : "bg-zinc-800/70 cursor-pointer hover:bg-zinc-700/80"
        }`}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
      >
        <div className="flex justify-between items-center">
          <div>
            <div className="text-white/60">{data.voice_name}</div>
            <div className="text-xs text-white/30 mt-1">
              {formatDistance(new Date(data.created_at), new Date(), {
                addSuffix: true,
                locale: zhCN,
              })}
            </div>
          </div>

          <div className="flex items-center gap-4">
            {/* 播放/暂停按钮 */}
            <div
              onClick={(e) => {
                e.stopPropagation();
                handlePlayPause();
              }}
            >
              {data.playStatus ? (
                <Pause className="w-5 h-5 text-blue-500" />
              ) : (
                <Play className="w-5 h-5 text-white/60" />
              )}
            </div>

            {/* 更多操作下拉菜单 */}
            <Dropdown
              menu={{
                items: [
                  {
                    key: "1",
                    label: (
                      <span className="flex items-center gap-2">
                        {data.status === 2 ? (
                          <Play className="w-5 h-5" />
                        ) : (
                          <Pause className="w-5 h-5" />
                        )}
                        {data.status === 1 ? "禁用" : "启用"}
                      </span>
                    ),
                    onClick: () => {
                      const newStatus = data.status === 2 ? 1 : 2;
                      updateStatus({
                        ...data,
                        status: newStatus,
                        disabled: newStatus === 2,
                      });
                    },
                  },
                  { type: "divider" },
                  {
                    key: "2",
                    label: (
                      <span className="flex items-center gap-2 text-red-500">
                        <Trash2 className="w-5 h-5" />
                        删除
                      </span>
                    ),
                    onClick: (event) => {
                      setDeleteOpen(true);
                    },
                  },
                ],
              }}
              trigger={["click"]}
            >
              <MoreVertical
                className="w-5 h-5 text-white/40"
                onClick={(e) => e.stopPropagation()}
              />
            </Dropdown>
          </div>
        </div>
        {error && <div className="text-red-500 text-sm mt-2">{error}</div>}

        <audio
          ref={audioRef}
          onError={handleAudioError}
          onEnded={handleAudioEnded}
          className="hidden"
        />

        {error && <div className="text-red-500 text-sm mt-2">{error}</div>}

        <DeleteConfirm
          open={deleteOpen}
          setOpen={setDeleteOpen}
          label="确认删除音频？"
          title={`音频"${data.voice_name}"删除后将无法恢复，请确认是否删除。`}
          okClick={handleDelete}
        />
      </motion.div>
    </AnimatePresence>
  );
};

export default AudioCard;
