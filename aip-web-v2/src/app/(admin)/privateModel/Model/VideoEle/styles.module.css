.layer {
  position: absolute;
  z-index: 30;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.3);
  display: flex;
  justify-content: center;
  align-items: center;
}
.play_pause {
  visibility: hidden;
  position: absolute;
  z-index: 30;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.3);
  display: flex;
  justify-content: center;
  align-items: center;
  opacity: 0;
}
.video_main {
  position: relative;
  width: 100%;
  height: 100%;
  cursor: pointer;
  border-radius: var(--radius);
  background-color: black;
}
.video_main:hover .play_pause {
  visibility: visible;
  opacity: 1;
  transition: opacity 0.2s linear;
}
