import {
  FC,
  forwardRef,
  useEffect,
  useImperativeHandle,
  useRef,
  useState,
} from "react";
import IconSvg from "@/assets/svg";
import styles from "./styles.module.css";
import { Loader } from "lucide-react";
const { VideoPauseSvg, VideoPlaySvg } = IconSvg;

interface TProps {
  url: string;
  width?: string;
  height?: string;
  onplay?: () => void;
  onstop?: () => void;
  currentTime?: React.Dispatch<React.SetStateAction<string>>;
}

const VideoPlayer = forwardRef<any, TProps>(
  ({ width, height, url, onplay, onstop, currentTime }, ref) => {
    const videoRef = useRef<HTMLVideoElement>(null);
    const [loading, setLoading] = useState(true);
    const [playStatus, setPlayStatus] = useState(true);
    useImperativeHandle(
      ref,
      () => {
        return {
          play() {
            videoRef.current?.play();
            console.log(2);
            setPlayStatus(false);
          },
          pause() {
            videoRef.current?.pause();
            console.log(1);
            setPlayStatus(true);
          },
        };
      },
      []
    );
    const doubleEvent = () => {
      if (!document.fullscreenElement) {
        if (videoRef.current) {
          videoRef.current.requestFullscreen().catch((err) => {
            console.error(err);
          });
        }
      } else {
        if (document.exitFullscreen) {
          document.exitFullscreen();
        }
      }
    };
    useEffect(() => {
      /** 加载播放态 */
      const onloadEve = function () {
        setLoading(false);
      };
      /** 播放结束，回到0时 */
      const endedEve = function () {
        if (videoRef.current) {
          videoRef.current.currentTime = 0;
        }
      };
      const onloadeddataEve = function () {
        if (currentTime) {
          const time = Math.round(videoRef.current?.duration ?? 0);
          console.log(time);
          currentTime(`${Math.floor(time / 60)} : ${time % 60}`);
        }
      };
      if (videoRef.current) {
        videoRef.current.addEventListener("loadedmetadata", onloadEve);
        videoRef.current.addEventListener("ended", endedEve);
        videoRef.current.addEventListener("loadeddata", onloadeddataEve);
      }
      return () => {
        if (videoRef.current) {
          videoRef.current.removeEventListener("loadedmetadata", onloadEve);
          videoRef.current.removeEventListener("ended", endedEve);
        }
      };
    }, []);
    return (
      <div
        className={styles.video_main}
        style={{ width: `${width ?? "100%"}`, height: `${height ?? "100%"}` }}
      >
        <video
          preload="metadata"
          ref={videoRef}
          className="w-full h-full"
          src={url}
          disablePictureInPicture
        >
          Your browser does not support the video tag.
        </video>
        {loading && (
          <div className={styles.layer}>
            <Loader className="size-10 animate-spin" />
          </div>
        )}

        <div className={styles.play_pause} onDoubleClick={doubleEvent}>
          {playStatus && !loading && (
            <VideoPauseSvg
              className="size-10"
              onClick={() => {
                if (videoRef.current) {
                  if (onplay) onplay();
                  videoRef.current.play();
                  setPlayStatus(false);
                }
              }}
            />
          )}
          {!playStatus && !loading && (
            <VideoPlaySvg
              className="size-10"
              onClick={() => {
                if (videoRef.current) {
                  if (onstop) onstop();
                  videoRef.current.pause();
                  setPlayStatus(true);
                }
              }}
            />
          )}
        </div>
      </div>
    );
  }
);

VideoPlayer.displayName = "VideoPlayer";

export default VideoPlayer;
