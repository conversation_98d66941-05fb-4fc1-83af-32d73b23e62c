import { Dispatch, SetStateAction, useRef, useState } from "react";
import { GetServiceUrl } from "@/service/config";
import { useUserInfoStore } from "@/store/store";
import {
  Modal,
  Input,
  ConfigProvider,
  theme,
  UploadProps,
  Progress,
  Alert,
  Button,
  App,
} from "antd";
import <PERSON>agger from "antd/es/upload/Dragger";
import { RcFile } from "antd/es/upload";
import { UploadEventType } from "@/hooks/useModeUpload";

interface TProps {
  callback: (obj: UploadEventType) => void;
  open: boolean;
  setOpen: Dispatch<SetStateAction<boolean>>;
  loading: boolean;
  uploadParams: UploadEventType;
  progress: number;
}

const UploadType = ["mp3", "wav", "m4a"];
const MIMEType = "audio/mpeg, audio/wav, audio/x-m4a";
const MaxFileSize = 12;

const AddAudio = (props: TProps) => {
  const { open, setOpen, callback } = props;
  const currentIpUser = useUserInfoStore((state) => state.currentIpUser);
  const [fileListArr, setFileListArr] = useState<any[]>([]);
  const [fileName, setFileName] = useState("");
  const BASE_URL = GetServiceUrl();
  const { message } = App.useApp();

  const ParamsRef = useRef({
    voice_name: "",
    status: 1,
    voice_url: "",
    pid: 0,
  });

  const linkChangeEvent = (val: any) => {
    const title = val?.target?.value;
    ParamsRef.current.voice_name = title;
    setFileName(title);
  };

  /** 上传之前事件 */
  const beforeUploadEve = (file: RcFile) => {
    const mediaType = file?.name?.split(".").pop()?.toLocaleLowerCase();
    if (mediaType == undefined || !UploadType.includes(mediaType)) {
      message.error(`${file.name}格式文件不支持请重新上传。`);
      return false;
    }
    const size = file.size / 1024 / 1024;
    if (size > MaxFileSize) {
      message.error(
        `${file.name}文件超过了${MaxFileSize}M，请重新上传此文件。`
      );
      return false;
    }
    setFileListArr([file]);
    return false;
  };

  /** 上传配置 */
  const uploadProps: UploadProps = {
    name: "files",
    multiple: false,
    fileList: fileListArr,
    accept: MIMEType,
    beforeUpload: beforeUploadEve,
    onRemove: (file: any) => {
      if (props.loading) return;
      const filteArr = fileListArr.filter((item: any) => file.uid !== item.uid);
      setFileListArr(filteArr);
    },
    onDrop(e: any) {
      console.log("Dropped files", e.dataTransfer.files);
    },
  };

  /** 上传音频 */
  const submitEvent = () => {
    if (!ParamsRef.current.voice_name) {
      message.error("标题不能为空！");
      return;
    }
    if (fileListArr.length <= 0) {
      message.error("请上传音频！");
      return;
    }

    callback({
      files: fileListArr,
      ip_name: currentIpUser.ip_name,
      fileName: ParamsRef.current.voice_name,
      url: `${BASE_URL}/common/v1/file/upload`,
      id: currentIpUser.id,
      cloneUrl: `${BASE_URL}/audio/fish/clone`,
    });
  };

  const initValue = () => {
    if (props.loading) {
      setFileName(props.uploadParams.fileName);
      setFileListArr(props.uploadParams.files);
    } else {
      setFileListArr([]);
      setFileName("");
      ParamsRef.current = {
        voice_name: "",
        status: 1,
        voice_url: "",
        pid: 0,
      };
    }
  };

  const tips = [
    "支持mp3、wav格式音频",
    "音频时长建议在1分钟以内",
    "环境保持安静，不要有任何噪声",
    "语音清晰自然，语速适中",
    "两句话之间需要停顿1-2秒",
  ];

  return (
    <ConfigProvider theme={{ algorithm: theme.darkAlgorithm }}>
      <style jsx global>{`
        .ant-modal-mask {
          background-color: rgba(0, 0, 0, 0.2) !important;
          backdrop-filter: blur(8px) !important;
        }

        .upload-progress {
          display: flex;
          flex-direction: column;
          gap: 1rem;
          background: rgba(0, 0, 0, 0.2);
          border-radius: 8px;
          padding: 1rem;
          margin-top: 1rem;
        }

        .progress-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
        }

        .file-info {
          font-size: 0.875rem;
          color: rgba(255, 255, 255, 0.8);
        }
      `}</style>

      <Modal
        title="添加音频"
        open={open}
        onCancel={() => {
          setOpen(false);
        }}
        afterOpenChange={(open) => {
          if (!open) {
            initValue();
          }
        }}
        footer={[
          <Button
            key="submit"
            type="primary"
            loading={props.loading}
            onClick={submitEvent}
            disabled={props.loading}
          >
            {props.loading ? "上传中..." : "确定"}
          </Button>,
        ]}
        width={600}
      >
        <div className="mt-4">
          <Alert
            type="info"
            showIcon
            description={
              <ul className="list-disc pl-4">
                {tips.map((tip, index) => (
                  <li key={index} className="text-sm text-gray-300">
                    {tip}
                  </li>
                ))}
              </ul>
            }
            className="mb-6"
          />

          <Input
            placeholder="请输入音频名称"
            value={fileName}
            onChange={linkChangeEvent}
            disabled={props.loading}
            className="mb-6"
          />

          <ConfigProvider theme={{ algorithm: theme.darkAlgorithm }}>
            <Dragger
              {...uploadProps}
              style={{
                display: fileListArr.length > 0 ? "none" : "block",
              }}
            >
              <div className="flex flex-col items-center justify-center h-[150px] w-full tracking-[2px]">
                <span className="text-sm">点击上传/拖拽到此区域</span>
                <span className="text-sm leading-6 text-gray-400">
                  支持{UploadType.join(",")}格式，不超过{MaxFileSize}M
                </span>
              </div>
            </Dragger>
          </ConfigProvider>

          {props.loading && (
            <div className="upload-progress">
              <div className="progress-header">
                <div className="file-info">{fileListArr[0]?.name}</div>
                <div className="text-sm text-blue-400">正在上传,请勿离开</div>
              </div>

              <Progress
                percent={Math.round(props.progress)}
                size="small"
                status="active"
                strokeColor={{
                  "0%": "#108ee9",
                  "100%": "#87d068",
                }}
              />
            </div>
          )}
        </div>
      </Modal>
    </ConfigProvider>
  );
};

export default AddAudio;
