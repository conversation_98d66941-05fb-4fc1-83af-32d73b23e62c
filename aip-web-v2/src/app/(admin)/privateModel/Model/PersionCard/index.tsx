import { FC, useRef, useState, memo } from "react";
import { Plus, Play, Pause, Trash2, MoreVertical } from "lucide-react";
import { Skeleton } from "@nextui-org/react";
import { formatDistance } from "date-fns";
import { zhCN } from "date-fns/locale";
import { MediaType, VideoItem } from "../../../../typing/modelPersonal";
import DropDownMenu from "../DropDownMenu";
import DeleteConfirm from "../DeleteConfirm";
import VideoPlayer, { VideoPlayerRef } from "../VideoPlayer";
import { motion, AnimatePresence } from "framer-motion";
import { Dropdown } from "antd";

// 视频块容器组件
const VideoBlock: FC<{ children: React.ReactNode }> = memo(({ children }) => (
  <motion.div
    initial={{ opacity: 0, y: 20 }}
    animate={{ opacity: 1, y: 0 }}
    exit={{ opacity: 0, y: -20 }}
    className="relative bg-zinc-800/70 rounded-xl overflow-hidden hover:bg-zinc-700/80 transition-all duration-300 h-full"
  >
    {children}
  </motion.div>
));

VideoBlock.displayName = "VideoBlock";

// 添加按钮组件
export const PersionCardAdd: FC = memo(() => (
  <VideoBlock>
    <div className="w-full h-full flex justify-center items-center cursor-pointer hover:bg-white/5 transition-colors">
      <Plus className="w-5 h-5 text-white/60" />
    </div>
  </VideoBlock>
));

PersionCardAdd.displayName = "PersionCardAdd";

// 加载状态组件
export const TransformCoding: FC = memo(() => (
  <VideoBlock>
    <div className="p-4">
      <div className="flex flex-col space-y-3">
        <Skeleton className="h-[250px] rounded-xl" />
        <div className="space-y-2">
          <Skeleton className="h-6" />
          <Skeleton className="h-6" />
        </div>
      </div>
    </div>
  </VideoBlock>
));

TransformCoding.displayName = "TransformCoding";

interface PersionCardProps {
  stopAndPlayVideo: (type: MediaType, index: number, play: boolean) => void;
  updateStatus?: (item: VideoItem) => void;
  data: VideoItem;
  deleteVideo?: (id: number, callback: () => void) => void;
}

const PersionCard: FC<PersionCardProps> = memo(
  ({ data, stopAndPlayVideo, updateStatus, deleteVideo }) => {
    const videoRef = useRef<VideoPlayerRef>(null);
    const [deleteOpen, setDeleteOpen] = useState(false);
    const [duration, setDuration] = useState("");

    const handleDelete = async () => {
      deleteVideo?.(data.id, () => {
        setDeleteOpen(false);
      });
    };

    const timeAgo = data.created_at
      ? formatDistance(new Date(data.created_at), new Date(), {
          addSuffix: true,
          locale: zhCN,
        })
      : "";

    // 修改播放/暂停处理函数
    const handlePlayPause = () => {
      if (videoRef.current) {
        if (data.playStatus) {
          videoRef.current.pause();
          stopAndPlayVideo("video", data.index, false);
        } else {
          videoRef.current.play();
          stopAndPlayVideo("video", data.index, true);
        }
      }
    };

    return (
      <AnimatePresence>
        <VideoBlock>
          <div
            className="aspect-square w-full relative cursor-pointer group overflow-hidden"
            onClick={handlePlayPause}
          >
            <VideoPlayer
              ref={videoRef}
              url={data.media_url}
              pic_url={data.pic_url}
              height="100%"
              onPlay={() => stopAndPlayVideo("video", data.index, true)}
              onPause={() => stopAndPlayVideo("video", data.index, false)}
              onDuration={setDuration}
              className="object-cover"
            />

            {data.status === 2 && (
              <div className="absolute inset-0 bg-zinc-900/50 backdrop-blur-[2px] flex items-center justify-center">
                <span className="px-3 py-1.5 bg-zinc-800/80 rounded-full text-xs text-white/70">
                  已禁用
                </span>
              </div>
            )}
          </div>

          <div className="p-4 space-y-3 bg-zinc-800/70 h-full">
            <h3 className="text-white/90 font-medium line-clamp-2 text-sm leading-snug hover:text-white transition-colors">
              {data.title}
            </h3>

            <div className="flex items-center justify-between">
              <div className="flex items-center text-xs text-white/40">
                <time>{timeAgo}</time>
                <span className="mx-2">·</span>
                <span>
                  {data?.resolution
                    ? `${data.resolution.width} × ${data.resolution.height}`
                    : "-"}
                </span>
                {duration && (
                  <>
                    <span className="mx-2">·</span>
                    <span>{duration}</span>
                  </>
                )}
              </div>

              <Dropdown
                menu={{
                  items: [
                    {
                      key: "1",
                      label: (
                        <span className="flex items-center gap-2">
                          {data.status === 2 ? (
                            <Play className="w-5 h-5" />
                          ) : (
                            <Pause className="w-5 h-5" />
                          )}
                          {data.status === 2 ? "启用视频" : "禁用视频"}
                        </span>
                      ),
                      onClick: () => {
                        updateStatus?.({
                          ...data,
                          status: data.status === 1 ? 2 : 1,
                        });
                      },
                    },
                    { type: "divider" },
                    {
                      key: "2",
                      label: (
                        <span className="flex items-center gap-2 text-red-500">
                          <Trash2 className="w-5 h-5" />
                          删除视频
                        </span>
                      ),
                      onClick: () => setDeleteOpen(true),
                    },
                  ],
                }}
                trigger={["click"]}
                placement="bottomRight"
              >
                <button
                  className="p-1.5 hover:bg-white/5 rounded-full transition-colors ml-2"
                  onClick={(e) => e.stopPropagation()}
                >
                  <MoreVertical className="w-5 h-5 text-white/60" />
                </button>
              </Dropdown>
            </div>
          </div>

          <DeleteConfirm
            open={deleteOpen}
            setOpen={setDeleteOpen}
            label="确认删除视频？"
            title={`视频"${data.title}"删除后将无法恢复，请确认是否删除。`}
            okClick={handleDelete}
          />
        </VideoBlock>
      </AnimatePresence>
    );
  }
);

PersionCard.displayName = "PersionCard";

export default PersionCard;
