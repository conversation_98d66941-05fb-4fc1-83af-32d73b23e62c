import React, {
  FC,
  forwardRef,
  useImperativeHandle,
  memo,
  useState,
  useEffect,
  useRef,
  useCallback,
} from "react";
import ReactPlayer from "react-player";
import {
  Loader,
  AlertTriangle,
  Volume2,
  VolumeX,
  Maximize,
  Minimize,
  Play,
  Pause,
} from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";
import styles from "./styles.module.css";

type FullscreenHTMLElement = HTMLElement & {
  webkitRequestFullScreen?: () => void;
  mozRequestFullScreen?: () => void;
  msRequestFullscreen?: () => void;
};

type FullscreenDocument = Document & {
  webkitExitFullscreen?: () => void;
  mozCancelFullScreen?: () => void;
  msExitFullscreen?: () => void;
};

export interface VideoPlayerRef {
  play: () => void;
  pause: () => void;
  getCurrentTime: () => number;
  getDuration: () => number;
}

interface VideoPlayerProps {
  url: string;
  width?: string;
  height?: string;
  pic_url?: string;
  onPlay?: () => void;
  onPause?: () => void;
  onDuration?: (duration: string) => void;
  resolution?: { width: number; height: number } | null;
  onError?: (error: any) => void;
  className?: string;
}

const formatTime = (seconds: number): string => {
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = Math.floor(seconds % 60);
  return `${minutes}:${remainingSeconds.toString().padStart(2, "0")}`;
};

const VideoPlayer = forwardRef<VideoPlayerRef, VideoPlayerProps>(
  (
    {
      width = "100%",
      height = "100%",
      url,
      pic_url,
      onPlay,
      onPause,
      onDuration,
      resolution,
      onError,
    },
    ref
  ) => {
    const [isMounted, setIsMounted] = useState(false);
    useEffect(() => {
      setIsMounted(true);
    }, []);
    const [isFullscreen, setIsFullscreen] = useState(false);
    const [isLoading, setIsLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const containerRef = useRef<HTMLDivElement>(null);
    const playerRef = useRef<ReactPlayer>(null);
    const [aspectRatio, setAspectRatio] = useState<"portrait" | "landscape">(
      "landscape"
    );
    const [currentTime, setCurrentTime] = useState(0);
    const [duration, setDuration] = useState(0);
    const [isPlaying, setIsPlaying] = useState(false);
    const [volume, setVolume] = useState(1);
    const [isMuted, setIsMuted] = useState(true);
    const [seeking, setSeeking] = useState(false);
    const progressRef = useRef<HTMLDivElement>(null);

    useImperativeHandle(ref, () => ({
      play: () => {
        setIsPlaying(true);
        onPlay?.();
      },
      pause: () => {
        setIsPlaying(false);
        onPause?.();
      },
      getCurrentTime: () => currentTime,
      getDuration: () => duration,
    }));

    const handleReady = useCallback(() => {
      setIsLoading(false);
      setError(null);
    }, []);

    const handleError = useCallback(
      (error: any) => {
        setIsLoading(false);
        setError("视频加载失败，请检查网络连接或视频源");
        onError?.(error);
      },
      [onError]
    );

    const requestFullscreen = useCallback(
      async (element: FullscreenHTMLElement) => {
        const methods = [
          "requestFullscreen",
          "webkitRequestFullScreen",
          "mozRequestFullScreen",
          "msRequestFullscreen",
        ];

        for (const method of methods) {
          if (method === "requestFullscreen" && element.requestFullscreen) {
            return await element.requestFullscreen();
          }
          if (
            method === "webkitRequestFullScreen" &&
            element.webkitRequestFullScreen
          ) {
            return element.webkitRequestFullScreen();
          }
          if (
            method === "mozRequestFullScreen" &&
            element.mozRequestFullScreen
          ) {
            return element.mozRequestFullScreen();
          }
          if (method === "msRequestFullscreen" && element.msRequestFullscreen) {
            return element.msRequestFullscreen();
          }
        }
      },
      []
    );

    const exitFullscreen = useCallback(() => {
      const doc = document as FullscreenDocument;
      if (doc.exitFullscreen) {
        doc.exitFullscreen();
      } else if (doc.webkitExitFullscreen) {
        doc.webkitExitFullscreen();
      } else if (doc.mozCancelFullScreen) {
        doc.mozCancelFullScreen();
      } else if (doc.msExitFullscreen) {
        doc.msExitFullscreen();
      }
    }, []);

    const handleDoubleClick = async () => {
      try {
        const fullscreenDoc = document as FullscreenDocument;
        if (!fullscreenDoc.fullscreenElement && containerRef.current) {
          await requestFullscreen(
            containerRef.current as FullscreenHTMLElement
          );
          setIsFullscreen(true);
        } else {
          exitFullscreen();
          setIsFullscreen(false);
        }
      } catch (err) {
        console.error("全屏切换失败:", err);
      }
    };

    useEffect(() => {
      const fullscreenDoc = document as FullscreenDocument;
      const handleFullscreenChange = () => {
        setIsFullscreen(!!fullscreenDoc.fullscreenElement);
      };

      const events = [
        "fullscreenchange",
        "webkitfullscreenchange",
        "mozfullscreenchange",
        "MSFullscreenChange",
      ];

      events.forEach((event) => {
        fullscreenDoc.addEventListener(event, handleFullscreenChange);
      });

      return () => {
        events.forEach((event) => {
          fullscreenDoc.removeEventListener(event, handleFullscreenChange);
        });
      };
    }, []);

    useEffect(() => {
      if (resolution) {
        setAspectRatio(
          resolution.width >= resolution.height ? "landscape" : "portrait"
        );
      }
    }, [resolution]);

    const videoContainerClass = `
    ${styles.video_container} 
    ${isFullscreen ? styles.fullscreen : ""} 
    ${aspectRatio === "portrait" ? styles.portrait : styles.landscape}
  `;

    return (
      <div
        ref={containerRef}
        className={videoContainerClass}
        style={{ width, height }}
        onDoubleClick={handleDoubleClick}
      >
        <div
          className={styles.clickable_area}
          onClick={(e) => {
            e.stopPropagation();
            const newPlayingState = !isPlaying;
            setIsPlaying(newPlayingState);
            if (newPlayingState) {
              onPlay?.();
            } else {
              onPause?.();
            }
          }}
        />

        {pic_url && !isPlaying && !isFullscreen && (
          <img src={pic_url} alt="视频封面" className={styles.cover_image} />
        )}

        <div className={styles.controls_overlay}>
          <button
            className={styles.play_button}
            onClick={(e) => {
              e.stopPropagation();
              const newPlayingState = !isPlaying;
              setIsPlaying(newPlayingState);
              if (newPlayingState) {
                onPlay?.();
              } else {
                onPause?.();
              }
            }}
          >
            {isPlaying ? (
              <Pause className="w-5 h-5" />
            ) : (
              <Play className="w-5 h-5" />
            )}
          </button>

          <button
            className={styles.fullscreen_button}
            onClick={handleDoubleClick}
          >
            {isFullscreen ? (
              <Minimize className="w-5 h-5" />
            ) : (
              <Maximize className="w-5 h-5" />
            )}
          </button>
        </div>

        {isMounted && (isPlaying || !pic_url) && (
          <div className={styles.player_wrapper}>
            <ReactPlayer
              ref={playerRef}
              url={url}
              width="100%"
              height="100%"
              playing={isPlaying}
              playsinline
              controls={false}
              light={false}
              volume={isMuted ? 0 : volume}
              muted={isMuted}
              onReady={handleReady}
              onError={handleError}
              onPlay={() => {
                setIsPlaying(true);
                onPlay?.();
                setIsLoading(false);
              }}
              onPause={() => {
                setIsPlaying(false);
                onPause?.();
              }}
              onProgress={(state) => setCurrentTime(state.playedSeconds)}
              onDuration={(duration) => {
                setDuration(duration);
                onDuration?.(formatTime(duration));
              }}
              config={{
                file: {
                  attributes: {
                    crossOrigin: "anonymous",
                    style: {
                      objectFit: isFullscreen
                        ? "contain"
                        : aspectRatio === "portrait"
                        ? "cover"
                        : "contain",
                      width: "100%",
                      height: "100%",
                      position: "absolute",
                      top: 0,
                      left: 0,
                    },
                  },
                  forceVideo: true,
                  forceAudio: true,
                },
              }}
            />
          </div>
        )}

        <AnimatePresence mode="wait">
          {(isLoading || error) && !isMounted && (
            <motion.div
              key={error ? "error" : "loading"}
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className={styles.loading_overlay}
            >
              {error ? (
                <>
                  <AlertTriangle className="w-8 h-8 text-red-500 mr-2" />
                  <span className={styles.error_message}>{error}</span>
                </>
              ) : (
                <Loader className="w-8 h-8 animate-spin text-white" />
              )}
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    );
  }
);

VideoPlayer.displayName = "VideoPlayer";

export default memo(VideoPlayer);
