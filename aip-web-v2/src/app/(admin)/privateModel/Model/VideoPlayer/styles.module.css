.video_container {
  width: 100%;
  height: 100%;
  background-color: #000;
  overflow: hidden;
}

.player_wrapper {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.player_wrapper > div {
  position: absolute !important;
  top: 0;
  left: 0;
  width: 100% !important;
  height: 100% !important;
}

.loading_overlay {
  @apply absolute inset-0 flex items-center justify-center bg-black/50;
  z-index: 10;
}

.error_overlay {
  @apply absolute inset-0 flex items-center justify-center bg-black/50;
  z-index: 10;
}

.error_message {
  @apply text-white text-sm bg-red-500/80 px-4 py-2 rounded;
}

.clickable_overlay {
  @apply absolute inset-0 w-full h-full;
  z-index: 1;
  cursor: pointer;
}

.clickable_overlay:hover ~ :global(.react-player__controls) {
  opacity: 1 !important;
}

.cover_image {
  @apply absolute inset-0 w-full h-full;
  z-index: 1;
  pointer-events: none;
}

/* 横屏视频样式 */
.landscape .player_wrapper {
  aspect-ratio: 16/9;
  max-width: 100%;
  max-height: 100%;
}

/* 竖屏视频样式 */
.portrait .player_wrapper {
  aspect-ratio: 9/16;
  max-width: 100%;
  max-height: 100%;
}

/* 全屏模式样式 */
.fullscreen {
  @apply fixed inset-0 z-50 bg-black;
  border-radius: 0 !important;
}

/* 全屏模式下的通用样式 */
.fullscreen {
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 横屏视频全屏样式 */
.fullscreen.landscape .player_wrapper {
  width: 100vw;
  height: 100vh;
  max-width: none;
  max-height: none;
}

/* 竖屏视频全屏样式 */
.fullscreen.portrait .player_wrapper {
  width: auto;
  height: 100vh;
}

.controls_overlay {
  @apply absolute inset-0;
  opacity: 0;
  z-index: 3;
}

.video_container:hover .controls_overlay {
  opacity: 1;
}

/* 基础按钮样式 */
.play_button,
.fullscreen_button {
  @apply absolute bottom-4 p-2 rounded-full;
  background: rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(8px);
  color: white;
}

.play_button {
  left: 16px;
}

.fullscreen_button {
  right: 16px;
}

.play_button svg,
.fullscreen_button svg {
  @apply w-5 h-5;
}

/* 点击区域样式 */
.clickable_area {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.cover_image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  pointer-events: none;
}

.player_wrapper :global(.react-player__controls),
.player_wrapper :global([class*="control"]) {
  z-index: 2;
  opacity: 0 !important;
  transition: opacity 0.3s ease !important;
}

.player_wrapper:hover :global(.react-player__controls),
.player_wrapper:hover :global([class*="control"]) {
  opacity: 1 !important;
}

/* 确保移动到控件上时控件保持显示 */
.player_wrapper :global(.react-player__controls:hover),
.player_wrapper :global([class*="control"]:hover) {
  opacity: 1 !important;
}

/* 视频容器样式 */
.player_wrapper {
  position: relative;
  background: #000;
}

/* 非全屏下的样式 */
.video_container:not(.fullscreen).landscape .player_wrapper video {
  object-fit: contain !important;
}

.video_container:not(.fullscreen).portrait .player_wrapper video {
  object-fit: cover !important;
}

/* 全屏下的样式 */
.fullscreen .player_wrapper video {
  object-fit: contain !important;
}

/* ReactPlayer 容器样式 */
.video_container > div > div {
  @apply w-full h-full;
}