import {
  Button,
  Dropdown,
  DropdownTrigger,
  DropdownMenu,
  DropdownItem,
} from "@nextui-org/react";
// import { MediaType, VideoItem, AudioItem } from "@/types/modelPersonal";
// import {
//   DropdownMenu,
//   DropdownMenuContent,
//   DropdownMenuGroup,
//   DropdownMenuItem,
//   DropdownMenuLabel,
//   DropdownMenuPortal,
//   DropdownMenuSeparator,
//   DropdownMenuShortcut,
//   DropdownMenuSub,
//   DropdownMenuSubContent,
//   DropdownMenuSubTrigger,
//   DropdownMenuTrigger,
// } from "@/components/ui/dropdown-menu";
interface TPrope {
  itemList: {
    label: string;
    click: () => void;
  }[];
}
const DropDownMenu: React.FC<TPrope> = ({ itemList }) => {
  return (
    <Dropdown>
      <DropdownTrigger>
        <div className="h-7 px-2 cursor-pointer">...</div>
      </DropdownTrigger>
      <DropdownMenu>
        {itemList.map((list, index) => (
          <DropdownItem onPress={list.click} key={index}>
            {list.label}
          </DropdownItem>
        ))}
      </DropdownMenu>
    </Dropdown>
  );
};
export default DropDownMenu;
