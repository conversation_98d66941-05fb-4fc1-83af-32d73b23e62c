"use client";
import SpinLoading from "@/components/SpinLoading/SpinLoading";
import AudioCard from "./AudioCard";
import PersionCard, { TransformCoding } from "./PersionCard";
import AddAudio from "./AddAudio";
import AddVideo from "./AddVideo";
import {
  getAudioList,
  getVideoList,
  voiceModelUpdate,
  mediaModelUpdate,
  audioDelete,
  videoDelete,
} from "@/service/fetch/privateMode";
import { useEffect, useState } from "react";
import { useUserInfoStore } from "@/store/store";
import { useVideoModeUpload, useAudioModeUpload } from "@/hooks/useModeUpload";
import { VideoItem } from "@/typing/modelPersonal";
import { message } from "antd";
import { Plus, ChevronsDown } from "lucide-react";
import styles from "./style.module.css";

const ROWS_TO_SHOW = 2; // 默认显示的行数
const COLS_PER_ROW = {
  sm: 1, // 移动端 1列
  md: 2, // 平板 2列
  lg: 3, // 桌面 3列
  xl: 4, // 大屏 4列
};

const ModelPrivate = () => {
  const { currentIpUser } = useUserInfoStore();
  const [messageApi, contextHolder] = message.useMessage();
  const [audioList, setAudioList] = useState<any[]>([]);
  const [videoList, setVideoList] = useState<any[]>([]);
  const [isVideoOpen, setVideoOpen] = useState(false);
  const [isAudioOpen, setAudioOpen] = useState(false);
  const [pageLoading, setPageLoading] = useState(true);
  const [videoLoading, setVideoLoading] = useState<boolean>(false);
  const [audioLoading, setAudioLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [showAllAudio, setShowAllAudio] = useState(false);
  const [showAllVideo, setShowAllVideo] = useState(false);
  const [audioRefs] = useState<{ [key: number]: HTMLAudioElement }>({});
  const [currentCols, setCurrentCols] = useState(COLS_PER_ROW.xl);

  const { uploadEve, progress, loading, uploadParams } = useVideoModeUpload(
    () => {
      setVideoOpen(false);
      videoData({ pid: currentIpUser.id });
    }
  );
  const {
    uploadEve: AUploadEve,
    progress: AProgress,
    loading: ALoading,
    uploadParams: AUploadParams,
  } = useAudioModeUpload(() => {
    setAudioOpen(false);
    audioData({ pid: currentIpUser.id, del_flag: 1 });
  });

  /** 获取视频列数据 */
  interface ApiResponse<T> {
    code: number;
    data: T;
    message?: string;
  }
  const videoData = (params: { pid: number }) => {
    getVideoList(params).then((result: ApiResponse<any[]>) => {
      if (result.code === 200) {
        const videoListData = result.data
          ? result.data
              .map((item: any, index: number) => {
                let size: { width: number; height: number } | null = {
                  width: 0,
                  height: 0,
                };
                if (item.resolution) {
                  try {
                    size = JSON.parse(item.resolution);
                  } catch (error) {
                    size = null;
                  }
                } else {
                  size = null;
                }

                const mappedItem = {
                  ...item,
                  index,
                  type: "video",
                  playStatus: false,
                  resolution: size,
                };
                return mappedItem;
              })
              .filter((item) => item.transcode !== 1)
              .sort((a, b) => {
                if (a.status === 2 && b.status !== 2) return 1;
                if (a.status !== 2 && b.status === 2) return -1;
                return 0;
              })
          : [];
        setVideoList(videoListData);
      }
    });
  };

  const stopAndPlayVideo = (type: string, id: number, play: boolean) => {
    let videoListData: any[] = [];
    let audioListData: any[] = [];

    if (type === "audio") {
      console.log("Audio type:", id, play);

      videoListData = videoList.map((item) => ({
        ...item,
        playStatus: false,
      }));
      audioListData = audioList.map((item) => ({
        ...item,
        playStatus: item.id === id ? play : false,
      }));
    }

    if (type === "video") {
      videoListData = videoList.map((item) => ({
        ...item,
        playStatus: item.id === id ? play : false,
      }));
      audioListData = audioList.map((item) => ({
        ...item,
        playStatus: false,
      }));
    }

    setVideoList(videoListData);
    setAudioList(audioListData);

    console.log("After update:", audioListData, audioRefs);
  };

  /** 获取音频列表数据 */
  interface AudioData {
    id: number;
    clone_name: string;
    [key: string]: any;
  }

  const audioData = (params: { pid: number; del_flag?: number }) => {
    getAudioList(params).then((result: ApiResponse<AudioData[]>) => {
      if (result.code === 200) {
        const audioListData = result.data
          ? result.data
              ?.filter((item: any) => {
                if (item.clone_name || item.back_clone_name) {
                  return true;
                }
                return false;
              })
              ?.map((item: any, index: number) => ({
                ...item,
                index,
                type: "audio",
                playStatus: false,
              }))
              .sort((a, b) => {
                if (a.status === 2 && b.status !== 2) return 1;
                if (a.status !== 2 && b.status === 2) return -1;
                return 0;
              })
          : [];
        setAudioList(audioListData);
      }
    });
  };

  /** 更新视频状态 */
  const updateVideoStatusEve = (item: VideoItem) => {
    setVideoLoading(true);
    mediaModelUpdate({ id: item.id, status: item.status })
      .then((data: ApiResponse<any>) => {
        if (data.code === 200) {
          videoData({ pid: currentIpUser.id });
        }
      })
      .finally(() => {
        setVideoLoading(false);
      });
  };

  /** 更新音频状态 */
  const updateAudioStatueEve = (item: any) => {
    setAudioLoading(true);
    voiceModelUpdate({ id: item.id, status: item.status })
      .then((data) => {
        if (data.code === 200) {
          audioData({ pid: currentIpUser.id, del_flag: 1 });
        }
      })
      .finally(() => {
        setAudioLoading(false);
      });
  };

  /** 音频删除 */
  const deleteAudioEve = (id: number, callback: () => void) => {
    audioDelete(id)
      .then((res) => {
        if (res.code === 200) {
          console.log(audioList, "audioList");
          setAudioList((prev) => prev.filter((item) => item.id !== id));
          console.log(audioList, "audioList");
          messageApi.success({
            content: "删除成功",
            duration: 3,
          });
        } else {
          messageApi.error({
            content: res?.message || "删除失败",
            duration: 3,
          });
        }
      })
      .catch(() => {
        messageApi.error({
          content: "删除失败，请重试",
          duration: 3,
        });
      })
      .finally(() => {
        callback?.();
      });
  };

  /** 视频删除 */
  const deleteVideoEve = (id: number, callback: () => void) => {
    videoDelete(id)
      .then((res) => {
        if (res.code === 200) {
          messageApi.success({
            content: "删除成功",
            duration: 3,
          });
          videoData({ pid: currentIpUser.id });
        } else {
          messageApi.error({
            content: "删除失败",
            duration: 3,
          });
        }
      })
      .catch(() => {
        messageApi.error({
          content: "删除失败",
          duration: 3,
        });
      })
      .finally(() => {
        callback?.();
      });
  };

  // 监听屏幕尺寸变化并更新列数
  useEffect(() => {
    const updateCols = () => {
      if (window.matchMedia("(min-width: 1280px)").matches) {
        setCurrentCols(COLS_PER_ROW.xl); // 4列
      } else if (window.matchMedia("(min-width: 1024px)").matches) {
        setCurrentCols(COLS_PER_ROW.lg); // 3列
      } else if (window.matchMedia("(min-width: 768px)").matches) {
        setCurrentCols(COLS_PER_ROW.md); // 2列
      } else {
        setCurrentCols(COLS_PER_ROW.sm); // 1列
      }
    };

    // 初始化
    updateCols();

    // 添加监听器
    const mediaQueries = [
      window.matchMedia("(min-width: 1280px)"),
      window.matchMedia("(min-width: 1024px)"),
      window.matchMedia("(min-width: 768px)"),
    ];

    mediaQueries.forEach((query) => {
      query.addEventListener("change", updateCols);
    });

    return () => {
      mediaQueries.forEach((query) => {
        query.removeEventListener("change", updateCols);
      });
    };
  }, []);

  // 计算当前应显示的数量
  const visibleItems = ROWS_TO_SHOW * currentCols;

  // 初始化数据
  useEffect(() => {
    if (currentIpUser.id) {
      const initializeData = async () => {
        try {
          setPageLoading(true);
          await Promise.all([
            videoData({ pid: currentIpUser.id }),
            audioData({ pid: currentIpUser.id, del_flag: 1 }),
          ]);
        } catch (err) {
          setError("加载数据失败");
          setTimeout(() => setError(null), 3000);
        } finally {
          setPageLoading(false);
        }
      };

      initializeData();
    }
  }, [currentIpUser.id]);

  return (
    <div className="h-screen overflow-hidden">
      {contextHolder}
      <div className="h-full overflow-y-auto">
        <div className="max-w-7xl mx-auto p-6 space-y-6 pb-20">
          {pageLoading ? (
            // 简化加载状态的样式
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {[...Array(6)].map((_, i) => (
                <div
                  key={i}
                  className="aspect-video bg-zinc-900/50 rounded-lg animate-pulse"
                />
              ))}
            </div>
          ) : (
            <>
              {/* 声音模型区块 - 简化样式 */}
              <section className="bg-zinc-900/30 rounded-xl p-5">
                <div className="flex justify-between items-center mb-5">
                  <h2 className="text-lg font-medium text-white flex items-center gap-2">
                    声音模型
                    <span className="text-xs px-2 py-0.5 bg-blue-500/5 text-blue-400/90 rounded-full">
                      {audioList.length}
                    </span>
                  </h2>
                  <button
                    onClick={() => setAudioOpen(true)}
                    className="p-2 rounded-full hover:bg-blue-500/10 transition-colors group"
                  >
                    <Plus className="w-5 h-5 text-blue-400/90" />
                  </button>
                  <AddAudio
                    open={isAudioOpen}
                    setOpen={setAudioOpen}
                    loading={ALoading}
                    progress={AProgress}
                    callback={AUploadEve}
                    uploadParams={AUploadParams}
                  />
                </div>

                {audioLoading ? (
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
                    {[...Array(3)].map((_, i) => (
                      <div
                        key={i}
                        className="aspect-[4/3] bg-zinc-800/50 rounded-lg animate-pulse"
                      />
                    ))}
                  </div>
                ) : audioList.length === 0 ? (
                  <div className="flex flex-col items-center justify-center py-12 bg-zinc-800/20 rounded-lg">
                    <div className="text-sm text-zinc-400">
                      点击右上角"+"按钮添加声音模型
                    </div>
                  </div>
                ) : (
                  <>
                    <div
                      className={`grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 ${
                        !showAllAudio ? "max-h-[340px] overflow-hidden" : ""
                      }`}
                    >
                      {(showAllAudio
                        ? audioList
                        : audioList.slice(0, visibleItems)
                      )
                        .sort((a, b) => {
                          // status === 2 表示禁用状态，将其排在后面
                          if (a.status === 2 && b.status !== 2) return 1;
                          if (a.status !== 2 && b.status === 2) return -1;
                          return 0;
                        })
                        .map((item, index) => (
                          <AudioCard
                            key={index}
                            data={item}
                            stopAndPlayVideo={stopAndPlayVideo}
                            updateStatus={updateAudioStatueEve}
                            deleteAudio={deleteAudioEve}
                          />
                        ))}
                    </div>
                    {audioList.length > visibleItems && (
                      <div className="w-full mt-5 hover:bg-zinc-800/10 transition-colors flex items-center justify-center h-8">
                        <button
                          className={styles.chevronsDown}
                          onClick={() => setShowAllAudio(!showAllAudio)}
                        >
                          <ChevronsDown
                            className={`w-8 h-8 text-white transition-transform duration-200 ${
                              showAllAudio ? "rotate-180" : ""
                            }`}
                          />
                        </button>
                      </div>
                    )}
                  </>
                )}
              </section>

              {/* 形象模型区块 - 简化样式 */}
              <section className="bg-zinc-900/30 rounded-xl p-5">
                <div className="flex justify-between items-center mb-5">
                  <h2 className="text-lg font-medium text-white flex items-center gap-2">
                    形象模型
                    <span className="text-xs px-2 py-0.5 bg-purple-500/5 text-purple-400/90 rounded-full">
                      {videoList.length}
                    </span>
                  </h2>
                  <button
                    onClick={() => setVideoOpen(true)}
                    className="p-2 rounded-full hover:bg-purple-500/10 transition-colors group"
                  >
                    <Plus className="w-5 h-5 text-purple-400/90" />
                  </button>
                  <AddVideo
                    open={isVideoOpen}
                    setOpen={setVideoOpen}
                    loading={loading}
                    progress={progress}
                    uploadParams={uploadParams}
                    callback={uploadEve}
                  />
                </div>

                {videoLoading ? (
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
                    {[...Array(3)].map((_, i) => (
                      <div
                        key={i}
                        className="aspect-video bg-zinc-800/50 rounded-lg animate-pulse"
                      />
                    ))}
                  </div>
                ) : videoList.length === 0 ? (
                  <div className="flex flex-col items-center justify-center py-12 bg-zinc-800/20 rounded-lg">
                    <div className="text-sm text-zinc-400">
                      点击右上角"+"按钮添加形象模型
                    </div>
                  </div>
                ) : (
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
                    {videoList
                      .sort((a, b) => {
                        // status === 2 表示禁用状态，将其排在后面
                        // transcode  0 1 2
                        if (a.status === 2 && b.status !== 2) return 1;
                        if (a.status !== 2 && b.status === 2) return -1;
                        return 0;
                      })
                      .map((item, index) => {
                        if (item.transcode === 0) {
                          return <TransformCoding key={index} />;
                        }
                        if (item.transcode === 2) {
                          return (
                            <PersionCard
                              deleteVideo={deleteVideoEve}
                              key={index}
                              data={item}
                              stopAndPlayVideo={stopAndPlayVideo}
                              updateStatus={updateVideoStatusEve}
                            />
                          );
                        }
                        return null;
                      })}
                  </div>
                )}
              </section>
            </>
          )}
        </div>
      </div>
    </div>
  );
};

export default ModelPrivate;
