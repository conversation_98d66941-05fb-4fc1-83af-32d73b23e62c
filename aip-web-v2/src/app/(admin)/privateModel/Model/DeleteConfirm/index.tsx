import { Modal } from "antd";
import { AlertTriangle } from "lucide-react";

interface DeleteConfirmProps {
  open: boolean;
  setOpen: (open: boolean) => void;
  title: string;
  label: string;
  okClick: () => void;
}

const DeleteConfirm: React.FC<DeleteConfirmProps> = ({
  open,
  setOpen,
  title,
  label,
  okClick,
}) => {
  return (
    <Modal
      title={null}
      open={open}
      onOk={okClick}
      onCancel={() => setOpen(false)}
      width={400}
      centered
      className="delete-confirm-modal"
      okText="删除"
      cancelText="取消"
      okButtonProps={{
        danger: true,
        className: "bg-red-500 hover:bg-red-600",
      }}
    >
      <div className="flex flex-col items-center pt-6 pb-3">
        <div className="w-12 h-12 rounded-full bg-red-50 flex items-center justify-center mb-4">
          <AlertTriangle className="w-6 h-6 text-red-500" />
        </div>
        <h3 className="text-lg font-medium mb-2">{label}</h3>
        <p className="text-sm text-zinc-500 text-center">{title}</p>
      </div>
    </Modal>
  );
};

export default DeleteConfirm;
