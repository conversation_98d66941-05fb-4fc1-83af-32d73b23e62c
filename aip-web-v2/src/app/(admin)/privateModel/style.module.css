.page {
  display: flex;
  height: 100%;
  flex-direction: column;
}
.main {
  flex-grow: 1;
  height: 100%;
  position: relative;
}
/**=========================================================*/
.header {
  display: flex;
  width: 100%;
  height: 64px;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid rgba(255, 255, 255, 0.3);
  padding-right: 36px;
}
.tabs {
  display: flex;
  gap: 40px;
  padding-left: 40px;
}
.tabsLable {
  color: rgba(255, 255, 255, 0.5);
  font-family: "PingFang SC";
  font-size: 18px;
  font-style: normal;
  font-weight: 400;
  line-height: 62px; /* 122.222% */
  border-bottom: 2px solid transparent;
  cursor: pointer;
}
.tabActive {
  color: #fff;
  border-bottom: 2px solid #fff;
}

/**========================================================*/
.planMain {
  width: 100%;
  height: 100%;
  overflow-y: auto;
  position: absolute;
  z-index: 10;
  top: 0;
  left: 0;
  transform: scale(1, 1);
  opacity: 1;
  visibility: visible;
}
.planMain:where(.planShow) {
  left: 0;
  opacity: 1;
  transform: scale(1, 1);
  visibility: visible;
  transition: left 0.3s ease;
}
.planPrev {
  opacity: 0;
  left: -101%;
  transform: scale(0.7, 0.7);
  z-index: 9;
  transition: left 0.001s ease 0.3s, transform 0.3s ease-in-out,
    opacity 0.3s ease-in-out;
}
.planNext {
  opacity: 0;
  left: 101%;
  transform: scale(0.7, 0.7);
  z-index: 9;
  transition: left 0.001s ease 0.3s, transform 0.3s ease-in-out,
    opacity 0.3s ease-in-out;
}
