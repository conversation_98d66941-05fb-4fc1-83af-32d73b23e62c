'use client';

import { useCompletion } from 'ai/react';
import { useMemo } from 'react';
import { List, Button } from 'antd';
import { motion } from 'framer-motion';

const extractHeadersAndContent = (text: string) => {
    // 使用正则表达式匹配 ### 和 <CUT> 之间的内容
    const headerRegex = /###\s*(.*?)\s*<CUT>/gs;
    const matches = text.matchAll(headerRegex);

    // 提取所有匹配的标题
    const headers = Array.from(matches, match => match[1].trim());

    // 过滤掉已解析的部分，保留剩余内容
    const filteredContent = text.replace(headerRegex, '').trim();

    return {
        headers,
        filteredContent
    };
};

const MotionListItem = motion(List.Item);

const listVariants = {
    hidden: { opacity: 0, y: -20 },
    visible: { opacity: 1, y: 0, transition: { duration: 0.5 } },
};

const itemVariants = {
    hidden: { opacity: 0, x: -20 },
    visible: { opacity: 1, x: 0, transition: { duration: 0.5 } },
};



export default function Page() {
    const { completion, input, handleInputChange, handleSubmit } = useCompletion({
        api: '/app/api/chat3',
    });

    const { headers, filteredContent } = useMemo(() => {
        return completion ? extractHeadersAndContent(completion) : { headers: [], filteredContent: '' };
    }, [completion]);

    return (
        <div className="relative h-screen mx-auto w-[70%] flex flex-col">
            <main className="flex-1 overflow-y-auto">
                {/* <div className="content">
                    {filteredContent ? filteredContent : ''}
                </div> */}

                <List
                    bordered
                    dataSource={headers}
                    renderItem={(header, index) => (
                        <MotionListItem
                            key={index}
                            variants={itemVariants}
                            initial="hidden"
                            animate="visible"
                            actions={[
                                <Button type="primary">Edit</Button>,
                                <Button danger>Delete</Button>,
                            ]}
                        >
                            {header}
                        </MotionListItem>
                    )}
                />

            </main>



            <footer className="">
                <form onSubmit={handleSubmit} className="mx-auto">
                    <input
                        className=""
                        name="prompt"
                        value={input}
                        onChange={handleInputChange}
                        id="input"
                    />
                    <button type="submit">Submit</button>
                </form>
            </footer>


        </div>
    );
}