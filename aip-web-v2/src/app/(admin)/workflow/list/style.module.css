.container {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  overflow-y: auto;
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;
}
.head {
  display: flex;
  justify-content: space-between;
}
.main {
  display: flex;
  flex-grow: 1;
  flex-shrink: 1;
  height: calc(100% - 56px);
}
.block {
  display: flex;
  flex-direction: column;
  width: 49%;
  margin-right: 2%;
  height: 100%;
}
.block:last-child {
  margin-right: 0;
}
.listContain {
  height: 100%;
  overflow-y: auto;
  flex-shrink: 1;
  margin-top: 10px;
}
