"use client";
import { useEffect, useState } from "react";
import { TaskFindAll } from "@/typing/Workflow";
import { getTaskList } from "@/service/fetchData";
import { useUserInfoStore } from "@/store/store";
import OralDraft from "./components/OralDraft";
import SearchFilter from "./components/SearchFilter";
import { Tabs, Spin } from "antd";
import { LoadingOutlined } from "@ant-design/icons";
import { motion, AnimatePresence } from "framer-motion";
import { App as AntdApp } from "antd";

interface WorkflowState {
  query: string;
  pid: number;
  date: {
    start: string | undefined;
    end: string | undefined;
  };
}

const PAGE_SIZE = 24; // 每页加载数量，确保首屏有足够数据

function WorkflowListPage() {
  const currentIpUser = useUserInfoStore((state) => state.currentIpUser);
  const [allTasks, setAllTasks] = useState<TaskFindAll[]>([]);
  const [displayTasks, setDisplayTasks] = useState<TaskFindAll[]>([]);
  const [activeTab, setActiveTab] = useState<string>("all");
  const [loading, setLoading] = useState(true);
  const [loadingMore, setLoadingMore] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [page, setPage] = useState(1);
  const [type, setType] = useState<string>("all");
  const [params, setParams] = useState<WorkflowState>({
    pid: currentIpUser.id,
    date: {
      start: undefined,
      end: undefined,
    },
    query: "",
  });

  // 获取任务数据
  const getTaskData = async () => {
    setLoading(true);
    try {
      const scope = {
        pid: currentIpUser.id,
        date_from: params.date?.start
          ? `${params.date.start} 00:00:00`
          : undefined,
        date_to: params.date?.end ? `${params.date.end} 23:59:59` : undefined,
        search_term: params.query,
      };

      const result = await getTaskList(scope);
      if (result.code === 200) {
        // 按创建时间倒序排序
        const sortedData = result.data.sort(
          (a: TaskFindAll, b: TaskFindAll) =>
            new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
        );
        setAllTasks(sortedData);
        setPage(1);
        updateDisplayTasks(sortedData, type, 1, 0);
      }
    } catch (error) {
      console.error("Failed to fetch tasks:", error);
    } finally {
      setLoading(false);
    }
  };

  // 更新显示的任务列表
  const updateDisplayTasks = (
    tasks: TaskFindAll[],
    type: string,
    currentPage: number,
    progress?: number
  ) => {
    setType(type);
    let filteredTasks = tasks;

    if (type !== "all" && progress !== undefined) {
      filteredTasks = tasks.filter((item) => item.progress === progress);
    }

    // filteredTasks = tasks.filter((item) => (item.progress = progress));
    // 根据类型筛选
    // if (type === "oral") {
    //   // 口播稿阶段：待处理、处理中、口播稿完成
    //   filteredTasks = tasks.filter((item) => [1, 2, 3].includes(item.progress));
    // } else if (type === "audio") {
    //   // 音频阶段：音频完成
    //   filteredTasks = tasks.filter((item) => [4, 5, 6].includes(item.progress));
    // } else if (type === "video") {
    //   // 视频阶段：音频完成、视频完成
    //   filteredTasks = tasks.filter((item) => [7, 8, 9].includes(item.progress));
    // }
    console.log(type, progress, filteredTasks, "progress");

    // 计算分页
    const totalItems = currentPage * PAGE_SIZE;
    const displayItems = filteredTasks.slice(0, totalItems);

    setDisplayTasks(displayItems);
    setHasMore(totalItems < filteredTasks.length);
  };

  // 加载更多
  const loadMore = async () => {
    if (loadingMore) return;
    setLoadingMore(true);

    try {
      const nextPage = page + 1;
      setPage(nextPage);
      updateDisplayTasks(allTasks, activeTab, nextPage, 0);
    } finally {
      // 添加小延迟，避免加载过快
      setTimeout(() => {
        setLoadingMore(false);
      }, 300);
    }
  };

  // 监听滚动加载更多
  useEffect(() => {
    const handleScroll = () => {
      const mainElement = document.getElementById("workflow-main");
      if (!mainElement) return;

      const { scrollTop, clientHeight, scrollHeight } = mainElement;
      // 当滚动到距离底部 50px 时触发加载
      const isNearBottom = scrollHeight - scrollTop - clientHeight < 50;

      if (isNearBottom && hasMore && !loadingMore) {
        loadMore();
      }
    };

    const mainElement = document.getElementById("workflow-main");
    if (mainElement) {
      mainElement.addEventListener("scroll", handleScroll);
    }

    return () => {
      if (mainElement) {
        mainElement.removeEventListener("scroll", handleScroll);
      }
    };
  }, [hasMore, loadingMore, page]);

  useEffect(() => {
    if (currentIpUser.id) {
      getTaskData();
    }
  }, [currentIpUser.id, params.date, params.query]);

  // 优化初始加载检查
  useEffect(() => {
    const mainElement = document.getElementById("workflow-main");
    if (mainElement) {
      const { clientHeight, scrollHeight } = mainElement;
      // 如果初始内容不足一屏，自动加载更多
      if (clientHeight === scrollHeight && hasMore) {
        loadMore();
      }
    }
  }, [displayTasks.length]);

  // Tab 切换
  const handleTabChange = (key: string, progress?: number) => {
    setActiveTab(key);
    setPage(1);
    updateDisplayTasks(allTasks, key, 1, progress);
  };

  return (
    <AntdApp>
      <div className="h-screen flex flex-col bg-[#18181B]">
        <SearchFilter
          query={params?.query}
          date={params?.date}
          activeTab={activeTab}
          onChangeQuery={(query) => setParams({ ...params, query })}
          onSelectDate={(date) => setParams({ ...params, date })}
          onTabChange={handleTabChange}
        />

        <main id="workflow-main" className="flex-1 overflow-y-auto">
          <div className="container mx-auto px-6 py-8 space-y-8">
            {loading ? (
              <div className="space-y-6">
                {[...Array(2)].map((_, i) => (
                  <div key={i} className="animate-pulse">
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                      {[...Array(3)].map((_, j) => (
                        <div
                          key={j}
                          className="h-40 bg-white/5 rounded-lg"
                        ></div>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <>
                <AnimatePresence mode="popLayout">
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {displayTasks.map((item, index) => (
                      <motion.div
                        key={item.id}
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        exit={{ opacity: 0, y: -20 }}
                        transition={{ duration: 0.3, delay: (index % 3) * 0.1 }}
                      >
                        <OralDraft list={item} />
                      </motion.div>
                    ))}
                  </div>
                </AnimatePresence>

                {loadingMore && (
                  <div className="flex justify-center py-8">
                    <Spin
                      indicator={
                        <LoadingOutlined
                          style={{ fontSize: 24, color: "#fff" }}
                          spin
                        />
                      }
                    />
                  </div>
                )}

                {!hasMore && displayTasks.length > 0 && (
                  <div className="text-center py-8 text-white/60">
                    没有更多数据了
                  </div>
                )}

                {displayTasks.length === 0 && (
                  <div className="text-center py-20 text-white/60">
                    暂无数据
                  </div>
                )}
              </>
            )}
          </div>
        </main>
      </div>
    </AntdApp>
  );
}

export default WorkflowListPage;
