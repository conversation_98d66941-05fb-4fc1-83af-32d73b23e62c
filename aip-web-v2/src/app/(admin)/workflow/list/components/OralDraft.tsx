import React, { FC, Component } from 'react';
import { formatDistance } from 'date-fns';
import { zhCN } from 'date-fns/locale';
import { TaskFindAll } from '@/typing/Workflow';
import { motion } from 'framer-motion';
import { MoreHorizontal, Edit3, PlayCircle } from 'lucide-react';
import { Dropdown, Modal, App } from 'antd';
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';

export interface OralDraftProps {
  list: TaskFindAll;
}

export const OralDraft: FC<OralDraftProps> = ({ list }) => {
  const router = useRouter();
  const { modal } = App.useApp();

  const getStatusInfo = (progress: number) => {
    // const statusMap = {
    //   0: { color: 'bg-blue-500/10 text-blue-500', text: '待处理', icon: Edit3 },
    //   1: { color: 'bg-yellow-500/10 text-yellow-500', text: '处理中', icon: Edit3 },
    //   2: { color: 'bg-green-500/10 text-green-500', text: '处理中', icon: Edit3 },
    //   4: { color: 'bg-emerald-500/10 text-emerald-500', text: '口播稿完成', icon: Edit3 },
    //   5: { color: 'bg-purple-500/10 text-purple-500', text: '音频已完成', icon: PlayCircle },
    //   8: { color: 'bg-indigo-500/10 text-indigo-500', text: '视频已完成', icon: PlayCircle },
    // }[progress] || { color: 'bg-gray-500/10 text-gray-500', text: '未知状态', icon: Edit3 };
    // return statusMap;
    const statusMap = {
      0: { color: 'bg-blue-500/10 text-blue-500', text: '初始化', icon: Edit3 },
      1: { color: 'bg-yellow-500/10 text-yellow-500', text: '文案生成中', icon: Edit3 },
      2: { color: 'bg-green-500/10 text-green-500', text: '文案已生成', icon: Edit3 },
      3: { color: 'bg-red-500/10 text-red-500', text: '文案生成失败', icon: Edit3 },
      4: { color: 'bg-yellow-500/10 text-yellow-500', text: '音频生成中', icon: PlayCircle },
      5: { color: 'bg-green-500/10 text-green-500', text: '音频已生成', icon: PlayCircle },
      6: { color: 'bg-red-500/10 text-red-500', text: '音频生成失败', icon: PlayCircle },
      7: { color: 'bg-yellow-500/10 text-yellow-500', text: '视频生成中', icon: PlayCircle },
      8: { color: 'bg-green-500/10 text-green-500', text: '视频已生成', icon: PlayCircle },
      9: { color: 'bg-red-500/10 text-red-500', text: '视频制作失败', icon: PlayCircle },
    }[progress] || { color: 'bg-gray-500/10 text-gray-500', text: '未知状态', icon: Edit3 };
    return statusMap;
  };

  const timeAgo = list.created_at
    ? formatDistance(
        new Date(list.created_at.replace(' ', 'T')),
        new Date(),
        {
          addSuffix: true,
          locale: zhCN,
        }
      )
    : '';

  const { color, text, icon: StatusIcon } = getStatusInfo(list.progress);

  const handleCardClick = (e: React.MouseEvent) => {
    const target = e.target as HTMLElement;
    if (target.closest('.more-actions') || target.closest('.ant-dropdown')) {
      e.stopPropagation();
      return;
    }
    router.push(`/workflow/detail/${list.id}`);
  };

  const handleEdit = () => {
    router.push(`/workflow/detail/${list.id}`);
  };

  const handleDelete = () => {
    modal.warning({
      title: '功能开发中',
      content: '暂时无法删除，如需删除请联系客服处理。',
      okText: '知道了',
      className: 'dark-theme-modal',
      okButtonProps: {
        className: 'bg-blue-500 hover:bg-blue-600 border-none',
      },
      centered: true,
      styles: {
        content: {
          background: '#27272A',
          borderRadius: '12px',
        },
        body: {
          color: '#A1A1AA',
        },
      },
    });
  };

  return (
    <motion.div 
      className="group bg-[#27272A] rounded-lg p-4 h-[160px] flex flex-col hover:bg-[#323237] transition-all duration-200 cursor-pointer"
      whileHover={{ y: -2 }}
      transition={{ duration: 0.2 }}
      onClick={handleCardClick}
    >
      <div className="flex-1 min-h-0">
        <h3 className="text-white font-medium text-base line-clamp-2 leading-6 mb-3">
          {list.title}
        </h3>
        {list.remark && (
          <p className="text-white/60 text-sm line-clamp-2">
            {list.remark}
          </p>
        )}
      </div>

      <div className="flex items-center justify-between pt-2 border-t border-white/5">
        <div className="flex items-center gap-2 text-sm">
          <StatusIcon className={`w-4 h-4 ${color.split(' ')[1]}`} />
          <span className={`px-2 py-0.5 rounded-full ${color}`}>
            {text}
          </span>
          <span className="text-white/40">{timeAgo}</span>
        </div>
        <Dropdown
          menu={{
            items: [
              { 
                key: 'edit', 
                label: '编辑',
                onClick: handleEdit,
                className: 'text-white/90 hover:text-white' 
              },
              { 
                key: 'delete', 
                label: '删除', 
                danger: true,
                onClick: handleDelete,
                className: 'text-red-500 hover:text-red-400'
              },
            ],
          }}
          placement="bottomRight"
          trigger={['click']}
          overlayStyle={{ 
            zIndex: 1000,
          }}
          dropdownRender={(menu) => (
            <div className="min-w-[120px] bg-[#27272A] rounded-lg shadow-lg border border-white/5">
              <div className="py-1">
                {React.cloneElement(menu as React.ReactElement<any>, {
                  style: {
                    background: 'transparent',
                    boxShadow: 'none',
                  },
                  items: (menu as any).props.items.map((item: any) => ({
                    ...item,
                    className: `${item.className} px-4 py-2 hover:bg-[#3F3F46]`,
                  })),
                } as any)}
              </div>
            </div>
          )}
        >
          <button 
            className="opacity-0 group-hover:opacity-100 p-1.5 hover:bg-white/10 rounded-full transition-all more-actions"
            onClick={(e) => e.stopPropagation()}
          >
            <MoreHorizontal className="w-4 h-4 text-white/60" />
          </button>
        </Dropdown>
      </div>
    </motion.div>
  );
};

class ErrorBoundary extends Component<
  { children: React.ReactNode },
  { hasError: boolean }
> {
  constructor(props: { children: React.ReactNode }) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError() {
    return { hasError: true };
  }

  render() {
    if (this.state.hasError) {
      return <div>加载失败</div>;
    }

    return this.props.children;
  }
}

const OralDraftWithErrorBoundary: FC<OralDraftProps> = (props) => (
  <ErrorBoundary>
    <OralDraft {...props} />
  </ErrorBoundary>
);

export default OralDraftWithErrorBoundary; 