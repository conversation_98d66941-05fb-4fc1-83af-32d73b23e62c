import { TaskFindAll } from "@/typing/Workflow";
import OralDraft from "./OralDraft";
import { motion, AnimatePresence } from "framer-motion";

interface TaskListProps {
  tasks: TaskFindAll[];
  hasMore: boolean;
  isEmpty: boolean;
}

export const TaskList = ({ tasks, hasMore, isEmpty }: TaskListProps) => (
  <>
    <AnimatePresence mode="popLayout">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {tasks.map((task, index) => (
          <motion.div
            key={task.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.3, delay: (index % 3) * 0.1 }}
          >
            <OralDraft list={task} />
          </motion.div>
        ))}
      </div>
    </AnimatePresence>

    {!hasMore && tasks.length > 0 && (
      <div className="text-center py-8 text-white/60">
        没有更多数据了
      </div>
    )}

    {isEmpty && (
      <div className="text-center py-20 text-white/60">暂无数据</div>
    )}
  </>
); 