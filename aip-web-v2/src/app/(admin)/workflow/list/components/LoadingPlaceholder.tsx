export const LoadingPlaceholder = () => (
  <div className="space-y-6">
    {Array.from({ length: 2 }).map((_, i) => (
      <div key={i} className="animate-pulse">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {Array.from({ length: 3 }).map((_, j) => (
            <div key={j} className="h-40 bg-white/5 rounded-lg" />
          ))}
        </div>
      </div>
    ))}
  </div>
); 