"use client";
import { FC } from "react";
import { Input, DatePicker, Config<PERSON>rovider, theme, Popover } from "antd";
import dayjs from "dayjs";
import "dayjs/locale/zh-cn";
import locale from "antd/locale/zh_CN";
import { X } from "lucide-react";
import { SearchIcon, CalendarDays } from "lucide-react";

const { RangePicker } = DatePicker;

interface SearchFilterProps {
  query: string;
  date: {
    start: string | undefined;
    end: string | undefined;
  };
  activeTab: string;
  onChangeQuery: (query: string) => void;
  onSelectDate: (date: {
    start: string | undefined;
    end: string | undefined;
  }) => void;
  onTabChange: (key: string, progress?: number) => void;
}

const darkThemeConfig = {
  algorithm: theme.darkAlgorithm,
  token: {
    colorBgElevated: "#333333",
    boxShadow: "0 4px 12px rgba(0, 0, 0, 0.5)",
    borderRadius: 8,
    colorBorder: "transparent",
    colorBgContainer: "#27272A",
  },
};

const tabs = [
  { key: "all", label: "全部任务" },
  { key: "oral", label: "口播稿制作" },
  { key: "audio", label: "音频制作" },
  { key: "video", label: "视频制作" },
];

type StatusConfig = {
  all: { label: string; progress: null };
  init: { label: string; progress: number };
  oral: Record<string, { label: string; progress: number }>;
  audio: Record<string, { label: string; progress: number }>;
  video: Record<string, { label: string; progress: number }>;
};

const STATUS_CONFIG: StatusConfig = {
  all: { label: "全部", progress: null },
  init: { label: "初始化", progress: 0 },
  oral: {
    pending: { label: "文案生成中", progress: 1 },
    processing: { label: "文案已生成", progress: 2 },
    completed: { label: "文案生成失败", progress: 3 },
  },
  audio: {
    pending: { label: "音频生成中", progress: 4 },
    processing: { label: "音频已生成", progress: 5 },
    completed: { label: "音频生成失败", progress: 6 },
  },
  video: {
    pending: { label: "视频生成中", progress: 7 },
    processing: { label: "视频已生成", progress: 8 },
    completed: { label: "视频生成失败", progress: 9 },
  },
} as const;

export const SearchFilter: FC<SearchFilterProps> = ({
  query,
  date,
  activeTab,
  onChangeQuery,
  onSelectDate,
  onTabChange,
}) => {
  const renderFilterContent = () => (
    <div className="min-w-[120px] py-1 w-[190px]">
      {Object.entries(STATUS_CONFIG).map(([type, config]) => {
        if (type === "all") {
          return (
            <button
              key={type}
              onClick={() => onTabChange(type)}
              className={`w-full px-4 py-2 text-sm text-left hover:bg-[#3F3F46] transition-colors ${
                activeTab === type ? "text-white" : "text-white/60"
              }`}
            >
              {(config as StatusConfig["all"]).label}
            </button>
          );
        }
        if (type === "init") {
          return (
            <button
              key={type}
              onClick={() => onTabChange(type, 0)}
              className={`w-full px-4 py-2 text-sm text-left hover:bg-[#3F3F46] transition-colors ${
                activeTab === type ? "text-white" : "text-white/60"
              }`}
            >
              {(config as StatusConfig["all"]).label}
            </button>
          );
        }

        return Object.entries(config).map(([status, { label, progress }]) => (
          <button
            key={`${type}_${status}`}
            onClick={() => onTabChange(type, progress)}
            className={`w-full px-4 py-2 text-sm text-left hover:bg-[#3F3F46] transition-colors ${
              activeTab === `${type}_${status}` ? "text-white" : "text-white/60"
            }`}
          >
            {label}
          </button>
        ));
      })}
    </div>
  );

  const renderDateContent = () => (
    <ConfigProvider theme={darkThemeConfig} locale={locale}>
      <div className="min-w-[360px]">
        <div className="flex flex-wrap gap-2 mb-4">
          <button
            onClick={() => {
              const today = dayjs();
              onSelectDate({
                start: today.format("YYYY-MM-DD"),
                end: today.format("YYYY-MM-DD"),
              });
            }}
            className="px-3 py-1.5 rounded bg-[#323237] text-white/80 text-sm hover:bg-[#3F3F46] transition-colors"
          >
            今天
          </button>
          <button
            onClick={() => {
              const yesterday = dayjs().subtract(1, "day");
              onSelectDate({
                start: yesterday.format("YYYY-MM-DD"),
                end: yesterday.format("YYYY-MM-DD"),
              });
            }}
            className="px-3 py-1.5 rounded bg-[#323237] text-white/80 text-sm hover:bg-[#3F3F46] transition-colors"
          >
            昨天
          </button>
          <button
            onClick={() => {
              const end = dayjs();
              const start = dayjs().subtract(6, "day");
              onSelectDate({
                start: start.format("YYYY-MM-DD"),
                end: end.format("YYYY-MM-DD"),
              });
            }}
            className="px-3 py-1.5 rounded bg-[#323237] text-white/80 text-sm hover:bg-[#3F3F46] transition-colors"
          >
            最近7天
          </button>
        </div>
        <RangePicker
          onChange={(dates) => {
            if (dates) {
              onSelectDate({
                start: dates[0]?.format("YYYY-MM-DD"),
                end: dates[1]?.format("YYYY-MM-DD"),
              });
            } else {
              onSelectDate({
                start: undefined,
                end: undefined,
              });
            }
          }}
          className="w-full dark-theme-picker"
          placeholder={["开始日期", "结束日期"]}
          value={[
            date.start ? dayjs(date.start) : null,
            date.end ? dayjs(date.end) : null,
          ]}
          format="YYYY-MM-DD"
          allowClear
        />
      </div>
    </ConfigProvider>
  );

  return (
    <div className="sticky top-0 z-10 w-full bg-[#18181B]/80 backdrop-blur-sm py-4 px-6 flex flex-col md:flex-row gap-4 items-center justify-between border-b border-white/10">
      <div className="w-full md:w-96">
        <ConfigProvider theme={darkThemeConfig}>
          <div className="relative">
            <Input
              value={query}
              onChange={(e) => onChangeQuery(e.target.value)}
              placeholder="搜索工作流..."
              className="w-full hover:bg-[#323237] focus:bg-[#323237] placeholder:text-white/50 placeholder:text-sm"
              style={{
                height: "40px",
                background: "#27272A",
                border: "none",
                borderRadius: "9999px",
                paddingLeft: "44px",
                fontSize: "14px",
                color: "#fff",
              }}
            />
            <div className="absolute left-4 top-1/2 -translate-y-1/2 pointer-events-none">
              <SearchIcon className="w-5 h-5 text-white/50" />
            </div>
          </div>
        </ConfigProvider>
      </div>

      <div className="flex items-center gap-2">
        <ConfigProvider theme={darkThemeConfig}>
          <Popover
            content={renderFilterContent()}
            trigger="click"
            placement="bottomRight"
            overlayClassName="dark-theme-popover"
          >
            <button className="flex items-center gap-2 px-4 py-2 rounded-full bg-[#27272A] text-white/80 hover:bg-[#323237] transition-colors">
              <span className="text-sm font-medium">
                {activeTab === "all"
                  ? "全部"
                  : activeTab === "oral"
                  ? "口播稿"
                  : activeTab === "audio"
                  ? "音频"
                  : "短视频"}
              </span>
            </button>
          </Popover>

          <Popover
            content={renderDateContent()}
            trigger="click"
            placement="bottomRight"
            arrow={false}
          >
            <button className="flex items-center gap-2 px-4 py-2 rounded-full bg-[#27272A] text-white/80 hover:bg-[#323237] transition-colors">
              <CalendarDays className="w-4 h-4" />
              <span className="text-sm font-medium">
                {date.start ? `${date.start} ~ ${date.end}` : "日期筛选"}
              </span>
            </button>
          </Popover>
        </ConfigProvider>
        {(date.start || date.end) && (
          <button
            onClick={() => onSelectDate({ start: undefined, end: undefined })}
            className="p-2 rounded-full bg-[#27272A] text-white/80 hover:bg-[#323237] transition-colors"
          >
            <X className="w-4 h-4" />
          </button>
        )}
      </div>
    </div>
  );
};

export default SearchFilter;
