import { Settings2 } from "lucide-react";
import styles from "./style.module.css";
import { useWorkflowStore } from "@/store/store";
import { Loader } from "lucide-react";
import { useEffect, useState } from "react";

const ChatHeader = () => {
  const [taskNameValue, setTaskName] = useState("");
  const { updateWorkflowSlider, taskName } = useWorkflowStore((state) => state);
  const workflowSliderOpen = useWorkflowStore(
    (state) => state.workflowSlider.open
  );
  useEffect(() => {
    if (taskName) {
      setTaskName(taskName);
    } else {
      setTaskName("");
    }
  }, [taskName]);
  return (
    <section className={styles.header}>
      <span className={styles.title}>
        {taskNameValue ? (
          taskNameValue
        ) : (
          <>
            新任务
            <span className={styles.loadingIcon}>
              <Loader size={20} />
            </span>
          </>
        )}
      </span>
      <div
        className={styles.contractIcon}
        onClick={() => {
          updateWorkflowSlider({
            open: !workflowSliderOpen,
            tabType: "oral",
          });
        }}
      >
        <Settings2 color="#fff" />
      </div>
    </section>
  );
};
export default ChatHeader;
