.header {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 64px;
  border-bottom: 1px solid #333;
  box-shadow: 0 12px 24px rgb(24, 24, 27, 0.7);
  z-index: 10;
}
.title {
  display: flex;
  align-items: center;
  color: #fff;
  font-size: 20px;
  font-style: normal;
  font-weight: 500;
  line-height: 22px;
  max-width: 70%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.contractIcon {
  width: 24px;
  height: 24px;
  cursor: pointer;
  position: absolute;
  top: 0;
  bottom: 0;
  right: 20px;
  margin: auto 0;
  color: #a1a1aa;
  opacity: 1;
  transition: opacity 0.2s ease;
}
.contractIcon:hover {
  opacity: 0.7;
}
.loadingIcon {
  margin-left: 6px;
  opacity: 0.8;
  animation: runloading 2s linear infinite;
}
@keyframes runloading {
  0% {
    transform: rotateZ(0deg);
  }
  100% {
    transform: rotateZ(360deg);
  }
}
