"use client";
import _ from "lodash";
import { useEffect, useState, useCallback } from "react";
import ChatHeader from "./Header";
import ChatPlan from "./ChatPlan";
import OperationPlan from "./OperationPlan";
import styles from "./style.module.css";
import { useParams } from "next/navigation";
import { XProvider } from "@ant-design/x";
import { Splitter, ConfigProvider, theme } from "antd";
import { AntdRegistry } from "@ant-design/nextjs-registry";
import { useWorkflowStore } from "@/store/store";
const WorkFlowPage = () => {
  const { taskid } = useParams<{ taskid: string }>();
  const [sizes, setSizes] = useState<(number | string)[]>(["100%", "0%"]);
  // const updateOralText = useWorkflowStore((state) => state.updateOralText);
  const {
    updateOralText,
    updateTaskName,
    updateKnowledgeExpand,
    updateWorkflowSlider,
  } = useWorkflowStore((state) => state);
  const workflowSliderOpen = useWorkflowStore(
    (state) => state.workflowSlider.open
  );
  const handleResize = useCallback((newSizes: number[]) => {
    // 实时更新UI
    setSizes(newSizes);
    // // 防抖只用于最终状态保存
    // const debouncedSave = _.debounce((sizes: number[]) => {
    //   // 这里可以添加保存逻辑（如果需要）
    // }, 300);
    // debouncedSave(newSizes);
    // // 清理防抖函数
    // return () => debouncedSave.cancel();
  }, []);

  // 处理面板展开/收起
  // const togglePanel = useCallback(() => {
  //   setOpen((prev) => !prev);
  //   setSizes((prev) => (prev[0] === "100%" ? ["60%", "40%"] : ["100%", "0%"]));
  // }, []);

  // 监听开关侧边模板
  useEffect(() => {
    if (workflowSliderOpen) {
      setSizes(["60%", "40%"]);
    } else {
      setSizes(["100%", "0%"]);
    }
  }, [workflowSliderOpen]);
  useEffect(() => {
    if (taskid) {
      // 初始化口播稿件
      updateOralText({
        label: "",
        think_txt: "",
        think_other_txt: "",
        id: 0,
        uuid: "",
      });
      //知识点
      updateKnowledgeExpand([]);
      //初始化
      updateWorkflowSlider({
        open: false,
        tabType: "oral",
      });
    }
    return () => {
      localStorage.removeItem("workflow_task_id");
      // 初始化口播稿件
      updateOralText({
        label: "",
        think_txt: "",
        think_other_txt: "",
        id: 0,
        uuid: "",
      });
      //知识点
      updateKnowledgeExpand([]);
      //初始化
      updateWorkflowSlider({
        open: false,
        tabType: "oral",
      });
    };
  }, [taskid]);

  return (
    <ConfigProvider
      theme={{
        algorithm: theme.darkAlgorithm,
        components: {
          Splitter: {
            splitBarSize: 2,
            splitBarDraggableSize: 0,
            splitTriggerSize: 6,
          },
        },
        token: {
          controlItemBgHover: "transparent",
        },
      }}
    >
      <AntdRegistry>
        <div className="h-[100vh]">
          <XProvider>
            <ChatHeader />
            <Splitter
              style={{ height: "calc(100vh - 64px)" }}
              onResize={handleResize}
            >
              <Splitter.Panel size={sizes[0]} min="40%" max="80%">
                <ChatPlan />
              </Splitter.Panel>
              <Splitter.Panel size={sizes[1]} min="20%" max="60%">
                <OperationPlan />
              </Splitter.Panel>
            </Splitter>
          </XProvider>
        </div>
      </AntdRegistry>
    </ConfigProvider>
  );
};
export default WorkFlowPage;
