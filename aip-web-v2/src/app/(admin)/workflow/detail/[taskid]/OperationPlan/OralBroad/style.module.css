.oral_broad_contenter {
  display: flex;
  width: 100%;
  height: 100%;
  flex-direction: column;
  padding: 12px;
}
.oral_broad_text {
  width: 100%;
  height: 100%;
  flex-shrink: 1;
  flex-grow: 1;
  outline-color: transparent;
  overflow: auto;
}
.oral_broad_footer {
  width: 100%;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  flex-shrink: 0;
  flex-grow: 0;
  font-size: 14px;
  color: rgb(219, 219, 215);
  gap: 12px;
}
.circle_run {
  animation: circle_run_spin 1s linear infinite;
}
.hyAudio {
  margin-right: auto;
}
@keyframes circle_run_spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
