import { useEffect, useRef, useState } from "react";
import styles from "./style.module.css";
import { Check, Loader, X } from "lucide-react";
import { upDateOral, upDateWork } from "@/service/fetchData";

import { useWorkflowStore, useUserInfoStore } from "@/store/store";
import { useWorkflowInformation } from "@/hooks/useWorkflowhooks";
import { Button, Input } from "antd";
import { getTxtLength } from "@/utils/helper";
const { TextArea } = Input;
type UpdateOralState = "error" | "success" | "loading";
const OralBorad = () => {
  const { WResult, oralInformation, chatLoading, docLength, languageType } =
    useWorkflowStore((state) => state);
  const currentIpUser = useUserInfoStore((state) => state.currentIpUser);
  const { synchronousOralTxt, senderQuery } = useWorkflowInformation();
  const timerRef = useRef<any>(null);
  const [text, setText] = useState("");
  const [txtLength, setTxtLength] = useState(1500);
  const [updateLoading, setUpdateLoading] =
    useState<UpdateOralState>("success");
  const hyAudio = () => {
    console.log("合成音频");
    setUpdateLoading("loading");
    senderQuery({
      question: "帮我将文案合成音频",
      params: {
        pid: currentIpUser?.id,
        query: "帮我将文案合成音频",
        is_pass: 0,
        task_id: localStorage.getItem("workflow_task_id")
          ? Number(localStorage.getItem("workflow_task_id"))
          : undefined,
      },
      lastWResult: WResult,
      callback: () => {
        setUpdateLoading("success");
      },
    });
  };
  const onInputEvent = (e: any) => {
    let str = e.target.value;
    // if (str?.length > docLength) {
    //   str = str.slice(0, docLength);
    // }
    setText(str);
    setTxtLength(getTxtLength({ txt: str, type: languageType }));
    clearTimeout(timerRef.current);
    // 同步修改内容到工作流
    synchronousOralTxt({
      content: `${oralInformation.think_txt}${str}`,
      think_other_txt: str,
      uuid: oralInformation.uuid,
      lastWResult: WResult,
    });
    // update text to service
    timerRef.current = setTimeout(() => {
      setUpdateLoading("loading");
      upDateWork({
        pid: currentIpUser.id,
        task_id: localStorage.getItem("workflow_task_id"),
        content: `${str}`,
        uuid: oralInformation.uuid,
      }).then((res) => {
        if (res.code === 200) {
          setUpdateLoading("success");
        } else {
          setUpdateLoading("error");
        }
      });
    }, 500);
  };
  useEffect(() => {
    if (
      oralInformation.think_other_txt ||
      oralInformation.think_other_txt === ""
    ) {
      setText(oralInformation.think_other_txt);
      setTxtLength(
        getTxtLength({
          txt: oralInformation.think_other_txt,
          type: languageType,
        })
      );
    }
  }, [oralInformation.think_other_txt]);
  return (
    <div className={styles.oral_broad_contenter}>
      <TextArea
        disabled={chatLoading}
        placeholder="在此输入口播稿内容..."
        value={text}
        onChange={onInputEvent}
        autoSize={true}
        styles={{
          textarea: {
            outline: "none",
            minHeight: "clac(100% - 22px)",
            maxHeight: "clac(100% - 22px)",
            flexGrow: "1",
            flexShrink: "1",
            padding: 0,
            border: "none",
            backgroundColor: "transparent",
          },
        }}
      />
      <div className={styles.oral_broad_footer}>
        <Button
          type="primary"
          onClick={hyAudio}
          className={styles.hyAudio}
          disabled={
            txtLength <= 0 ||
            txtLength > 1500 ||
            updateLoading === "loading" ||
            chatLoading
          }
        >
          合成音频
        </Button>
        <span>
          {new Date().getHours()}:{new Date().getMinutes()}
        </span>
        <span className="flex gap-[3px] items-center">
          <span>自动保存</span>
          <span>
            {updateLoading === "loading" && (
              <Loader size="16px" className={styles.circle_run} />
            )}
            {updateLoading === "success" && <Check size="16px" />}
            {updateLoading === "error" && <X size="16px" />}
          </span>
        </span>
        <span>
          字数: {txtLength}/{docLength}
        </span>
      </div>
    </div>
  );
};
export default OralBorad;
