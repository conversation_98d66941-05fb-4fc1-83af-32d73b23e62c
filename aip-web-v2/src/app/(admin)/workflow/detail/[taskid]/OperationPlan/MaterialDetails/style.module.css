.articleItem {
  display: flex;
  align-items: center;
  padding: 4px 12px;
  cursor: pointer;
  transition: padding 0.2s ease;
}
.articleTitle {
  display: block;
  padding-left: 12px;
  width: 412px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.material_content {
  padding: 0 24px 24px 24px;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
  height: 100%;
  width: 100%;
}
.imgPicItem {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 120px;
  width: auto;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 5px;
  overflow: hidden;
  cursor: pointer;
}
.checkbox_layer {
  position: absolute;
  z-index: 100;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  background-color: rgba(0, 0, 0, 0.2);
  opacity: 0;
  transition: opacity 0.2s ease;
  padding: 7px 0 0 7px;
}
.checked_show {
  opacity: 1;
}
.imgPicItem > img {
  transform: scale(1, 1);
  transition: transform 0.3s ease;
}
.imgPicItem:hover > img {
  transform: scale(1.2, 1.2);
}
.imgPicItem:hover .checkbox_layer {
  opacity: 1;
}

.articleItem:hover {
  color: #fff;
  padding-left: 18px;
}
.waterfallContent {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}
.operationList {
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
