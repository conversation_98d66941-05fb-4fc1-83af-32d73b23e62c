.knowledge_main {
  width: 100%;
  height: 100%;
  padding: 0 24px;
  overflow-y: auto;
}
.knowledge_block {
  padding: 24px 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.knowledge_block:last-child {
  border-bottom: 1px solid rgba(255, 255, 255, 0);
}
.knowledge_block_title {
  position: relative;
  display: flex;
  align-items: center;
  padding-left: 36px;
  height: 30px;
  border-radius: 4px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0px 0px 1px 0px rgba(0, 0, 0, 0.3),
    0px 4px 14px 0px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  width: 100%;
}

.knowledge_index {
  position: absolute;
  top: 0;
  left: 0;
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-size: 14px;
  border-radius: 4px 0px 0px 4px;
  background: rgba(255, 255, 255, 0.08);
}
.knowledge_title_text {
  width: 100%;
  font-size: 12px;
  color: rgba(255, 255, 255, 0.85);
  flex-shrink: 1;
  text-overflow: ellipsis;
  overflow: hidden;
}
.knowledge_title_text > span {
  /* display: block; */
  /* width: 100%;
  overflow: hidden;
  text-overflow: ellipsis; */
  white-space: nowrap;
}
.knowledge_block_content {
  padding-top: 8px;
}
/* .knowledge_title_text {
  color: rgba(255, 255, 255, 0.85);
  font-family: "PingFang SC";
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  
  margin-left: 4px;
  flex-shrink: 1;
  max-width: 100%;
}
.knowledge_title_text > span {
  display: block;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.knowledge_block_content {
  color: rgba(255, 255, 255, 0.7);
  font-size: 14px;
  font-weight: 400;
  line-height: 22px; 
  margin-top: 10px;
} */
