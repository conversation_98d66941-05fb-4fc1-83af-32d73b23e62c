import { getComments } from "@/service/fetchData";
import { FC, useEffect, useState } from "react";
import { Copy } from "lucide-react";
import { useClipboard } from "@/hooks/useClipboard";
import { useUserInfoStore } from "@/store/store";
import { ECommentType, ICommentData } from "@/typing/types";
import { Avatar } from "@nextui-org/react";
import { useWorkflowStore } from "@/store/store";
// import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";

const TYPES = {
  1: "作者自评",
  2: "搞笑专家",
  3: "讽刺专家",
  4: "短视频标题",
  5: "朋友圈标题",
  6: "短视频标签",
};

const CommentTitle = ({ type }: { type: ECommentType }) => {
  return <div className="text-base text-white">{TYPES[type]}</div>;
};

const AuthorCommentContent = ({ content }: { content: string }) => {
  const { onCopy } = useClipboard(content, <Copy />);
  const currentIpUser = useUserInfoStore((state) => state.currentIpUser);
  // const { currentIp } = userStore();

  return (
    <div className="group text-xs leading-5 text-white px-4 py-3 rounded-sm hover:bg-[#212124] cursor-pointer flex items-center justify-between gap-2">
      <div className="flex flex-row items-center gap-4">
        <Avatar
          className="flex-shrink-0 size-10 text-center leading-6 bg-[#7F818A] rounded-full overflow-hidden text-xs"
          name={currentIpUser?.ip_name.slice(-2)}
          src={currentIpUser?.avatar}
        ></Avatar>
        <div className="flex flex-col gap-1">
          <span className="text-base">{currentIpUser?.ip_name}</span>
          <span className="text-xs">{content}</span>
        </div>
      </div>
      <Copy
        className="size-5 flex-shrink-0 opacity-0 group-hover:opacity-100"
        onClick={onCopy}
      />
    </div>
  );
};

const CommentContent = ({ content }: { content: string }) => {
  const { onCopy } = useClipboard(content, <Copy />);

  return (
    <div className="group text-xs leading-5 text-white px-4 py-3 rounded-sm hover:bg-[#212124]  flex items-center justify-between gap-2">
      <span onDoubleClick={onCopy} className="cursor-pointer select-none">
        {content}
      </span>
      <Copy
        className="cursor-pointer size-5 flex-shrink-0 hidden group-hover:block"
        onClick={onCopy}
      />
    </div>
  );
};

const CommentDetails = () => {
  const [list, setList] = useState<ICommentData[]>([]);
  const tabType = useWorkflowStore((state) => state.workflowSlider.tabType);

  const fetchData = async (id: any) => {
    if (id) {
      const res = await getComments(id);
      if (res?.code === 200) {
        setList(res?.data);
      }
    }
  };

  useEffect(() => {
    const task_id = localStorage.getItem("workflow_task_id");
    if (tabType === "comment" && task_id) {
      fetchData(task_id);
    }
  }, [tabType]);

  return (
    <div className="p-6 h-[calc(100%_-_3rem)] text-white flex-1">
      <div className="grid gap-4">
        {list.map((item, index) => {
          return (
            <div key={index} className="grid gap-4">
              <CommentTitle type={item?.comment_type} />
              {/* {item?.comment_type === 1 ? (
                <AuthorCommentContent content={item?.content} />
              ) : ( */}
              <CommentContent content={item?.content} />
              {/* )} */}
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default CommentDetails;
