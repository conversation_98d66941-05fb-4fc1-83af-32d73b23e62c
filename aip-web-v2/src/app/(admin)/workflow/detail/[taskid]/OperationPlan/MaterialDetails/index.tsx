import { cloneElement, FC, ReactNode, useEffect, useState } from "react";
import { Row, Col, Button, Checkbox } from "antd";
import { FileImage, Film, Newspaper } from "lucide-react";
import Image from "next/image";
import { downloadImgByUrl } from "@/utils/helper";
import { useWorkflowStore } from "@/store/store";
import { PhotoProvider, PhotoView } from "react-photo-view";
import "react-photo-view/dist/react-photo-view.css";
import styles from "./style.module.css";
import { DownloadOutlined } from "@ant-design/icons";
import IconSvg from "@/assets/svg";

const { WorksNullSvg } = IconSvg;

const TITLE = {
  image: { title: "相关图片", icon: <FileImage size={13} /> },
  video: { title: "相关视频", icon: <Film size={14} /> },
  article: { title: "相关文章", icon: <Newspaper size={14} /> },
};

type TContainerType = keyof typeof TITLE;

const Container = ({
  type,
  children,
}: {
  type: TContainerType;
  children?: ReactNode;
}) => {
  return (
    <div className="w-full mb-3">
      <div className="flex flex-row items-center px-[14px] py-2 gap-2 bg-[#27272A] rounded-md text-[#FAFAFA] w-fit text-xs leading-5 font-medium">
        {cloneElement(TITLE[type].icon, { className: "size-4" })}
        <span>{TITLE[type]?.title}</span>
      </div>
      <div className="mt-3 w-full">{children}</div>
    </div>
  );
};

const ImageContent = ({ images }: { images: any }) => {
  return (
    <Container type="image">
      <div className="flex space-x-4 mt-2 overflow-auto cursor-pointer">
        <PhotoProvider>
          {images?.map((item: any, index: number) => (
            <PhotoView key={index} src={item}>
              <div
                key={index}
                className="flex-shrink-0 h-[97px] rounded-xl overflow-hidden"
              >
                <img
                  src={item}
                  alt={"img"}
                  width={100}
                  height={100}
                  className={styles.imgPicItem}
                  key={index}
                />
              </div>
            </PhotoView>
          ))}
        </PhotoProvider>
      </div>
    </Container>
  );
};

const VideoContent = ({ videos }: { videos: any }) => {
  return (
    <Container type="video">
      <div className="flex space-x-4 mt-2 overflow-auto">
        {videos?.map((item: any, index: number) => (
          <div
            className="cursor-pointer flex flex-col items-center"
            key={index}
            onClick={() => {
              window.open(item?.link, "_blank");
            }}
          >
            <div className="w-[100px] h-[134px] relative rounded-lg overflow-hidden">
              <Image
                src={item?.cover}
                alt={item?.title}
                layout="fill"
                objectFit="cover"
              />
            </div>
            <p className="mt-2 text-xs line-clamp-2 w-[100px] text-center">
              {item?.title}
            </p>
          </div>
        ))}
      </div>
    </Container>
  );
};

const ArticleContent = ({ articles }: { articles: any }) => {
  return (
    <Container type="article">
      {articles?.map((item: any, index: number) => (
        <div
          className={styles.articleItem}
          key={index}
          onClick={() => {
            window.open(item.href);
          }}
        >
          <Image
            src={item.favicon}
            alt="out！"
            width={18}
            height={18}
            className="rounded"
          />
          <span className={styles.articleTitle}>{item.title}</span>
        </div>
      ))}
    </Container>
  );
};

const MaterialDetails = () => {
  const aiSearchData = useWorkflowStore((state) => state.aiSearchData);
  const [imgInformation, setImgInformation] = useState<
    { img_url: string; show: boolean; checked: boolean }[]
  >([
    {
      img_url:
        "https://img9.doubanio.com/view/group_topic/l/public/p667575014.webpitem.img_url",
      show: false,
      checked: false,
    },
  ]);
  const [selectAll, setSelectAll] = useState(false);

  // 选中图片
  const checkedEve = (val: boolean, index: number) => {
    const data = JSON.parse(JSON.stringify(imgInformation));
    data[index].checked = val;
    setImgInformation(data);

    setTimeout(() => {
      if (data.filter((item: any) => !item.checked).length <= 0) {
        setSelectAll(true);
      } else {
        setSelectAll(false);
      }
    }, 0);
  };
  // 全选事件
  const checkedAllEve = (val: boolean) => {
    setSelectAll(val);
    const data = imgInformation.map((item, index) => {
      return {
        ...item,
        checked: val,
      };
    });
    setImgInformation(data);
  };
  // 批量下载
  const downloadImg = () => {
    const checked = imgInformation.filter((item) => item.checked);
    checked.forEach((item) => {
      downloadImgByUrl(item.img_url);
    });
  };
  const indeterminate =
    imgInformation.filter((item) => !item.checked).length > 0 &&
    imgInformation.filter((item) => item.checked).length > 0;
  useEffect(() => {
    if (aiSearchData.image_urls?.length > 0) {
      setImgInformation(
        aiSearchData.image_urls.map((item, index) => {
          let url = /^http:/.test(item)
            ? item.replace(/^http:/, "https:")
            : item;
          return {
            img_url: url,
            show: true,
            checked: false,
          };
        })
        // .filter((item) => /^https:/.test(item.img_url))
      );
    }
    return () => {
      setImgInformation([]);
    };
  }, [aiSearchData.image_urls]);
  return (
    <div className={styles.material_content}>
      {imgInformation.length > 0 ? (
        <>
          <div className={styles.operationList}>
            <Checkbox
              indeterminate={indeterminate}
              onChange={(e) => {
                checkedAllEve(e.target.checked);
              }}
              checked={selectAll}
            >
              全选
            </Checkbox>
            <Button
              icon={<DownloadOutlined />}
              onClick={downloadImg}
              disabled={
                imgInformation.filter((item) => item.checked).length <= 0
              }
            >
              下载
            </Button>
          </div>
          <Row gutter={[16, 16]}>
            <PhotoProvider>
              {imgInformation?.map((item: any, index: number) => (
                <Col
                  span={6}
                  style={{ display: `${item.show}?'':'none'` }}
                  key={`${index}_${item.img_url}`}
                >
                  <PhotoView key={index} src={item.img_url}>
                    <div className={styles.imgPicItem}>
                      <div
                        className={`${styles.checkbox_layer} ${
                          item.checked ? styles.checked_show : ""
                        }`}
                      >
                        <Checkbox
                          onClick={(e) => {
                            e.stopPropagation();
                          }}
                          onChange={(e) => {
                            checkedEve(e.target.checked, index);
                          }}
                          checked={item.checked}
                        />
                      </div>
                      <img
                        src={item.img_url}
                        alt={"img"}
                        width={100}
                        height={100}
                        className="object-cover w-full h-full"
                        onError={() => {
                          setTimeout(() => {
                            const imgs = imgInformation.filter(
                              (val) => val.img_url !== item.img_url
                            );
                            setImgInformation(imgs);
                          }, 0);
                        }}
                      />
                    </div>
                  </PhotoView>
                </Col>
              ))}
            </PhotoProvider>
          </Row>
        </>
      ) : (
        <div className="flex flex-col items-center justify-center h-full">
          <WorksNullSvg style={{ opacity: 0.5 }} />
          <div>暂无素材</div>
        </div>
      )}
      {/* {aiSearchData?.image_urls && aiSearchData?.image_urls?.length > 0 && (
        <ImageContent images={aiSearchData?.image_urls} />
      )} */}
      {/* {aiSearchData?.douyin && aiSearchData?.douyin?.length > 0 && (
        <VideoContent videos={aiSearchData?.douyin} />
      )}
      {aiSearchData?.article && aiSearchData?.article?.length > 0 && (
        <ArticleContent articles={aiSearchData?.article} />
      )} */}
    </div>
  );
};

export default MaterialDetails;
