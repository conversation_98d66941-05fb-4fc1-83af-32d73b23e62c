import styles from "./style.module.css";
import { File } from "lucide-react";
import { useWorkflowStore } from "@/store/store";
import IconSvg from "@/assets/svg";
const { WorksNullSvg } = IconSvg;
const KnowledgeDot = () => {
  const knowledgeExpand = useWorkflowStore((state) => state.knowledgeExpand);
  return (
    <div className={styles.knowledge_main}>
      {knowledgeExpand.length > 0 ? (
        knowledgeExpand.map((item, index) => {
          return (
            <div className={styles.knowledge_block} key={`${index}_`}>
              <div className={styles.knowledge_block_title}>
                <span className={styles.knowledge_index}>{index + 1}</span>
                <File
                  size={14}
                  color="#407BFF"
                  className="flex-shrink-0 flex-grow-0 mr-[4px]"
                />
                <div className={styles.knowledge_title_text}>
                  <span>{item.file_name}</span>
                </div>
              </div>
              <div className={styles.knowledge_block_content}>{item.text}</div>
            </div>
          );
        })
      ) : (
        <div className="flex flex-col items-center justify-center h-full">
          <WorksNullSvg style={{ opacity: 0.5 }} />
          <div>暂无知识点</div>
        </div>
      )}
    </div>
  );
};
export default KnowledgeDot;
