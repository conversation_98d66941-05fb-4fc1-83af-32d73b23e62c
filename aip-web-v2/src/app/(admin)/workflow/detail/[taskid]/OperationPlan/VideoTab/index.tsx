import { ChevronLeft, ChevronRight } from "lucide-react";
import { FC, useEffect, useRef, useState } from "react";
import styles from "./style.module.css";
import { numberToTime } from "@/utils/helper";
import {
  Dropdown,
  MenuProps,
  Slider,
  Tooltip,
  Button,
  Space,
  Modal,
} from "antd";
import IconSvg from "@/assets/svg";
import RegenerateVideo from "@/components/RegenerateVideo";
import { downloadAssets } from "@/utils/helper";
import { getWork } from "@/service/fetchData";
import { useUserInfoStore } from "@/store/store";
import {
  DownloadOutlined,
  ExclamationCircleOutlined,
  SyncOutlined,
} from "@ant-design/icons";
const {
  OkSvg,
  WorkPauseSvg,
  WorkPlaySvg,
  WorkMuteSvg,
  WorkPiPSvg,
  WorkScreenSvg,
  WorkSpeakerSvg,
  WorksNullSvg,
} = IconSvg;

const items: MenuProps["items"] = [
  {
    key: "1",
    label: <div className={styles.video_speed_item}>0.9 X</div>,
  },
  {
    key: "2",
    label: <div className={styles.video_speed_item}>1.0 X</div>,
  },
  {
    key: "3",
    label: <div className={styles.video_speed_item}>1.2 X</div>,
  },
];
// const deafultArr = [1, 2, 3, 4];
type ACTIVE_VIDEO = "prev" | "next" | "active";
interface VideoEleProps {
  // index: number;
  activeState: ACTIVE_VIDEO;
  // isPlaying: boolean;
  // setIsPlay?: Dispatch<SetStateAction<boolean>>;
  item: any;
  // currentTime?: number;
  // setCurrentTime?: Dispatch<SetStateAction<number>>;
  // setDuration?: Dispatch<SetStateAction<number>>;
  // fullScreenCallBack?: () => void;
}
const VideoEle: FC<VideoEleProps> = ({ item, activeState }) => {
  const videoRef = useRef<HTMLVideoElement>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [duration, setDuration] = useState(0);
  const [currentTime, setCurrentTime] = useState(0);
  const [isMuted, setIsMuted] = useState(false);
  const [thisRun, setThisRun] = useState(false);

  // 播放事件
  const playEve = () => {
    if (videoRef.current) {
      if (!videoRef.current.src) {
        videoRef.current.src = item.file_url;
      }
      setThisRun(true);
      if (isPlaying) {
        setIsPlaying(false);
        videoRef.current.pause();
      } else {
        setIsPlaying(true);
        videoRef.current.currentTime = currentTime;
        videoRef.current.play();
      }
    }
  };
  const requestFullscreenEve = () => {
    if (videoRef.current && videoRef.current.requestFullscreen) {
      videoRef.current.requestFullscreen();
    }
  };
  const handleMute = (mutedState: boolean) => {
    if (videoRef.current) {
      videoRef.current.muted = mutedState;
    }
  };
  const handlePictureInPicture = () => {
    if (videoRef.current) {
      if (document.pictureInPictureElement) {
        document.exitPictureInPicture();
      } else {
        videoRef.current.requestPictureInPicture();
      }
    }
  };
  useEffect(() => {
    const endedEve = function () {
      setIsPlaying(false);
    };
    const loadedmetadataEve = function () {
      if (videoRef.current) {
        setDuration(videoRef.current.duration);
      }
    };
    const timeupdateEve = function () {
      if (videoRef.current) {
        setCurrentTime(videoRef.current.currentTime);
      }
    };
    if (videoRef.current) {
      videoRef.current.addEventListener("timeupdate", timeupdateEve);
      videoRef.current.addEventListener("loadedmetadata", loadedmetadataEve);
      videoRef.current.addEventListener("ended", endedEve);
    }
    return () => {
      if (videoRef.current) {
        videoRef.current.removeEventListener("timeupdate", timeupdateEve);
        videoRef.current.removeEventListener(
          "loadedmetadata",
          loadedmetadataEve
        );
        videoRef.current.removeEventListener("ended", endedEve);
      }
    };
  }, []);

  return (
    <div
      className={`${styles.videoPlan} ${
        activeState === "active"
          ? styles.video_plan_active
          : activeState === "next"
          ? styles.video_plan_next
          : styles.video_plan_prev
      }`}
    >
      <div className={styles.videoTab_container}>
        <div className={`${styles.video_plan} `}>
          {/* 头部信息 */}
          <div className={styles.videoTab_header}>
            <div className={styles.videoTab_timer}>
              生成时间：{item.updated_at?.replace("T", " ")}
            </div>
            <div className={styles.videoTab_operation}>
              <Space>
                <RegenerateVideo />
                <Button
                  icon={<DownloadOutlined />}
                  onClick={() => {
                    downloadAssets(item.file_url ?? "");
                  }}
                >
                  下载
                </Button>
              </Space>
            </div>
          </div>
          {/* 视频 */}
          <div className={styles.videoTab_video}>
            <div className={styles.videoTab_video_control}>
              {isPlaying ? (
                <span className="cursor-pointer" onClick={playEve}>
                  <WorkPauseSvg className="size-10 fill-[#fff]" />
                </span>
              ) : (
                <span className="cursor-pointer" onClick={playEve}>
                  <WorkPlaySvg className="size-10 fill-[#fff]" />
                </span>
              )}
            </div>
            {/* 封面图 */}
            {!videoRef.current?.src && (
              <div className={styles.video_revice}>
                <img src={item.file_review_pic ?? ""} alt="" />
              </div>
            )}

            <video
              ref={videoRef}
              className="h-full w-full object-contain"
              data-src={item.file_url ?? ""}
              // src={item.file_url ?? ""}
              controls={false}
              id={"AIPGPT_video"}
            />
          </div>
        </div>
      </div>
      {/* 底部操作栏 */}
      <div className={styles.videoTab_footer}>
        <div className={styles.videoTab_page}>
          <span>
            <ChevronLeft size={24} className="cursor-pointer" />
          </span>
          <span>1/2</span>
          <span>
            <ChevronRight size={24} className="cursor-pointer" />
          </span>
        </div>
        <div className="mt-2">
          <Slider
            value={currentTime}
            min={0}
            max={duration}
            style={{ flexGrow: 1 }}
            onChange={(value) => {
              setIsPlaying(false);
              videoRef.current?.pause();
              setCurrentTime(value);
            }}
            styles={{
              track: {
                backgroundColor: "#fff",
              },
              rail: {
                backgroundColor: "rgba(255, 255, 255, 0.50)",
              },
            }}
          />
        </div>
        <div className={styles.videoTab_operation_container}>
          <div className={`${styles.more_btns}`}>
            {isPlaying ? (
              <span
                className="cursor-pointer"
                onClick={() => {
                  playEve();
                }}
              >
                <WorkPauseSvg className="size-5 fill-[#fff]" />
              </span>
            ) : (
              <span
                className="cursor-pointer"
                onClick={() => {
                  playEve();
                }}
              >
                <WorkPlaySvg className="size-5 fill-[#fff]" />
              </span>
            )}
            <div className={styles.video_time_label}>
              <span>{numberToTime(currentTime)}</span>/
              <span>{numberToTime(duration)}</span>
            </div>
          </div>

          <div className={styles.more_btns}>
            <Dropdown
              trigger={["click"]}
              menu={{ items }}
              placement="top"
              arrow={{ pointAtCenter: true }}
            >
              <div className={styles.speed_btn}>倍速</div>
            </Dropdown>
            {/* 是否静音 */}
            {!isMuted ? (
              <span
                className={
                  thisRun ? styles.optionDefault : styles.optionDisabled
                }
                onClick={() => {
                  setIsMuted(true);
                  handleMute(true);
                }}
              >
                <WorkSpeakerSvg />
              </span>
            ) : (
              <span
                className={
                  thisRun ? styles.optionDefault : styles.optionDisabled
                }
                onClick={() => {
                  setIsMuted(false);
                  handleMute(false);
                }}
              >
                <WorkMuteSvg />
              </span>
            )}
            <Tooltip placement="top" title="开启画中画">
              <span
                className={
                  thisRun ? styles.optionDefault : styles.optionDisabled
                }
                onClick={() => {
                  handlePictureInPicture();
                }}
              >
                <WorkPiPSvg />
              </span>
            </Tooltip>
            <Tooltip placement="top" title="视频全屏">
              <span
                className={
                  thisRun ? styles.optionDefault : styles.optionDisabled
                }
                onClick={() => {
                  requestFullscreenEve();
                }}
              >
                <WorkScreenSvg />
              </span>
            </Tooltip>
          </div>
        </div>
      </div>
    </div>
  );
};

const VideoTab = () => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [videoList, setVideoList] = useState<any[]>([]);
  const currentIpUser = useUserInfoStore((state) => state.currentIpUser);

  useEffect(() => {
    if (localStorage.getItem("workflow_task_id")) {
      getWork({
        task_id: Number(localStorage.getItem("workflow_task_id")),
        pid: currentIpUser.id,
      }).then((res) => {
        if (res) {
          const result = res.data
            .filter((item: any) => item.work_type === 3)
            .map((item: any) => {
              return {
                ...item,
                isPlaying: false,
              };
            });
          setVideoList([...result]);
        }
      });
    }
  }, []);
  return (
    <div className={styles.videoTab}>
      {videoList.length <= 0 && (
        <div className="flex flex-col items-center justify-center h-full">
          <WorksNullSvg style={{ opacity: 0.5 }} />
          <div>暂无视频</div>
        </div>
      )}
      {videoList.length > 0 &&
        videoList.map((item: any, index: number) => (
          <VideoEle
            item={item}
            activeState={
              currentIndex === index
                ? "active"
                : currentIndex > index
                ? "prev"
                : "next"
            }
            key={index}
          />
        ))}
      {videoList.length > 0 && (
        <div className={styles.videoTab_page_controller}>
          <span>
            <ChevronLeft
              size={24}
              className="cursor-pointer"
              onClick={() => {
                if (currentIndex > 0) {
                  setCurrentIndex(currentIndex - 1);
                }
              }}
            />
          </span>
          <span>
            {currentIndex + 1}/{videoList.length}
          </span>
          <span>
            <ChevronRight
              size={24}
              className="cursor-pointer"
              onClick={() => {
                if (currentIndex < videoList.length - 1) {
                  setCurrentIndex(currentIndex + 1);
                }
              }}
            />
          </span>
        </div>
      )}
    </div>
  );
};
export default VideoTab;
