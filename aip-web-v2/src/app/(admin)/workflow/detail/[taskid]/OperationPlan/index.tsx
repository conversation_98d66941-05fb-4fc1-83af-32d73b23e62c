import { Tabs } from "antd";
import type { TabsProps } from "antd";
import { useWorkflowStore } from "@/store/store";
import CommentDetails from "./CommentDetails";
import VideoTab from "./VideoTab";
import MaterialDetails from "./MaterialDetails";
import styles from "./style.module.css";
import OralBorad from "./OralBroad";
import KnowledgeDot from "./KnowledgeDot";
import MapEle from "./Map";
const OperationPlan = () => {
  const { updateWorkflowSlider, workflowSlider } = useWorkflowStore(
    (state) => state
  );
  return (
    <section className={styles.operationPlan}>
      <Tabs
        id="wfTabs"
        defaultActiveKey="oral"
        activeKey={workflowSlider.tabType}
        destroyInactiveTabPane={true}
        items={[
          {
            key: "oral",
            label: "口播稿",
            children: <OralBorad />,
          },
          {
            key: "knowledge",
            label: "知识点",
            children: <KnowledgeDot />,
          },
          // {
          //   key: "4",
          //   label: "音频",
          //   children: "Content of Tab Pane 3",
          // },
          {
            key: "video",
            label: "视频",
            children: <VideoTab />,
          },
          {
            key: "1",
            label: "素材",
            children: <MaterialDetails />,
          },
          {
            key: "map",
            label: "知识图谱",
            children: <MapEle />,
          },
          // {
          //   key: "map",
          //   label: "知识图谱",
          //   children: <MapEle />,
          // },
          // {
          //   key: "comment",
          //   label: (
          //     <div style={{ color: "rgba(255, 255, 255, 0.50)" }}>描述</div>
          //   ),
          //   children: <CommentDetails />,
          // },
        ]}
        onChange={(val: any) => {
          console.log("切换tab：", val);
          updateWorkflowSlider({
            open: true,
            tabType: val,
          });
        }}
        className="h-full"
        tabBarStyle={{
          height: "60px",
          lineHeight: "60px",
          padding: "0 15px",
          margin: 0,
        }}
      />
    </section>
  );
};
export default OperationPlan;
