.videoTab {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
}
.videoPlan {
  position: absolute;
  display: flex;
  width: 100%;
  height: 100%;
  flex-direction: column;
  padding: 24px;
  visibility: visible;
  opacity: 1;
  transform: scale(1);
}
.video_plan_active {
  transition: left 0.3s ease-in-out;
  top: 0;
  left: 0;
}
.video_plan_next {
  transition: transform 0.3s ease-in-out, opacity 0.3s ease-in-out,
    left 0.001s ease-in-out 0.3s;
  visibility: visible;
  opacity: 0;
  transform: scale(0.7);
  left: 101%;
  top: 0;
}
.video_plan_prev {
  transition: transform 0.3s ease-in-out, opacity 0.3s ease-in-out,
    left 0.001s ease-in-out 0.3s;
  visibility: visible;
  opacity: 0;
  transform: scale(0.7);
  left: -101%;
  top: 0;
}
.videoTab_page_controller {
  position: absolute;
  bottom: 85px;
  left: 0;
  right: 0;
  margin: auto;
  z-index: 100;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
}
.videoTab_container {
  position: relative;
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  gap: 12px;
  overflow: hidden;
}
.video_plan {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
}

.videoTab_footer {
  flex-grow: 0;
  flex-shrink: 0;
  padding: 10px;
}

.videoTab_header {
  position: relative;
  flex-grow: 0;
  display: flex;
  justify-content: space-between;
  align-items: center;

  color: rgba(255, 255, 255, 0.85);
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 14px; /* 100% */
}
.video_revice {
  position: absolute;
  top: 0;
  right: 0;
  z-index: 10;
  width: 100%;
  height: 100%;
}
.video_revice > img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}
.videoTab_video {
  position: relative;
  flex-grow: 1;
  flex-shrink: 1;
  margin-top: 12px;
  height: 100%;
  margin-top: 12px;
}
.videoTab_video_control {
  display: flex;
  justify-content: center;
  align-items: center;
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  z-index: 12;
  background-color: rgba(0, 0, 0, 0.1);
  opacity: 0;
}
.videoTab_video:hover .videoTab_video_control {
  opacity: 1;
}
.videoTab_page {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  opacity: 0;
}

.videoTab_operation_container {
  display: flex;
  justify-content: space-between;
}
.more_btns {
  display: flex;
  align-items: center;
  gap: 28px;
}
.video_time_label {
  display: flex;
  align-items: center;
  gap: 6px;
}
.speed_btn {
  cursor: pointer;
}
@keyframes video_plan_prev {
  0% {
    visibility: visible;
    opacity: 1;
    transform: scale(1);
  }
  99% {
    opacity: 1;
    transform: scale(1);
    visibility: hidden;
  }
}
.optionDefault {
  cursor: pointer;
}
.optionDisabled {
  cursor: not-allowed;
  opacity: 0.5;
  pointer-events: none;
}
