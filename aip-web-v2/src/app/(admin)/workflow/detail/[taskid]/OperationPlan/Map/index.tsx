import { useEffect, useRef, useState } from "react";
import ForceGraph3D from "react-force-graph-3d";
import SpriteText from "three-spritetext";
import { useWorkflowStore } from "@/store/store";
const myData = {
  nodes: [
    {
      id: "id1",
      name: "股价大跌",
      value: "股价大跌",
      nodeLabel: "股价大跌",
      nodeVal: "股价大跌",
      nodeDesc: "股价大跌",
      nodeColor: "red",
    },
    {
      id: "id2",
      name: "资本市场失望",
      value: "资本市场失望",
      nodeLabel: "资本市场失望",
      nodeVal: "资本市场失望",
      nodeDesc: "资本市场失望",
      nodeColor: "red",
    },
    {
      id: "id3",
      name: "特斯拉新品发布与中美科技较量",
      value: "特斯拉新品发布与中美科技较量",
    },
    {
      id: "id4",
      name: "股价下跌",
      value: "股价下跌",
    },
    {
      id: "id5",
      name: "杨幂在节目上合作杨丽",
      value: "杨幂在节目上合作杨丽",
    },
    {
      id: "id6",
      name: "股指下跌",
      value: "股指下跌",
    },
    {
      id: "id7",
      name: "牛市与熊市",
      value: "牛市与熊市",
    },
    {
      id: "id8",
      name: "投资中国科技股",
      value: "投资中国科技股",
    },
    {
      id: "id9",
      name: "RESPEC",
      value: "RESPEC",
    },
    {
      id: "id10",
      name: "婴幼儿大脑发育与营养",
      value: "婴幼儿大脑发育与营养",
    },
    {
      id: "id11",
      name: "冷静投资",
      value: "冷静投资",
    },
    {
      id: "id12",
      name: "投资需谨慎",
      value: "投资需谨慎",
    },
    {
      id: "id13",
      name: "新手投资者焦虑",
      value: "新手投资者焦虑",
    },
    {
      id: "id14",
      name: "股票回调",
      value: "股票回调",
    },
    {
      id: "id15",
      name: "低风险高收益投资方式",
      value: "低风险高收益投资方式",
    },
    {
      id: "id16",
      name: "利率水平持续走低",
      value: "利率水平持续走低",
    },
    {
      id: "id17",
      name: "新手体验股市起伏",
      value: "新手体验股市起伏",
    },
    {
      id: "id18",
      name: "市场冷静",
      value: "市场冷静",
    },
    {
      id: "id19",
      name: "市场波动、高风险",
      value: "市场波动、高风险",
    },
    {
      id: "id20",
      name: "指数基金的优势与风险",
      value: "指数基金的优势与风险",
    },
    {
      id: "id21",
      name: "享受整体市场波动收益",
      value: "享受整体市场波动收益",
    },
    {
      id: "id22",
      name: "ETF的优势",
      value: "ETF的优势",
    },
    {
      id: "id23",
      name: "价格波动大",
      value: "价格波动大",
    },
    {
      id: "id24",
      name: "艺术品洗钱",
      value: "艺术品洗钱",
    },
    {
      id: "id25",
      name: "投资组合分散化以降低风险",
      value: "投资组合分散化以降低风险",
    },
    {
      id: "id26",
      name: "小Lin",
      value: "小Lin",
    },
    {
      id: "id27",
      name: "经济低迷周期的投资策略",
      value: "经济低迷周期的投资策略",
    },
    {
      id: "id28",
      name: "以史为鉴",
      value: "以史为鉴",
    },
    {
      id: "id29",
      name: "经济低迷",
      value: "经济低迷",
    },
    {
      id: "id30",
      name: "经济低迷期",
      value: "经济低迷期",
    },
    {
      id: "id31",
      name: "债券、现金、股票、商品",
      value: "债券、现金、股票、商品",
    },
    {
      id: "id32",
      name: "美林时钟",
      value: "美林时钟",
    },
    {
      id: "id33",
      name: "经济低迷时配置债券、现金、股票、商品",
      value: "经济低迷时配置债券、现金、股票、商品",
    },
    {
      id: "id34",
      name: "跨地区、跨行业、跨资产类别",
      value: "跨地区、跨行业、跨资产类别",
    },
    {
      id: "id35",
      name: "寻找安全稳健且收益不错的理财方式",
      value: "寻找安全稳健且收益不错的理财方式",
    },
    {
      id: "id36",
      name: "多元化配置资产",
      value: "多元化配置资产",
    },
    {
      id: "id37",
      name: "分散投资",
      value: "分散投资",
    },
    {
      id: "id38",
      name: "投资策略与经济周期",
      value: "投资策略与经济周期",
    },
    {
      id: "id39",
      name: "需要多元化投资",
      value: "需要多元化投资",
    },
    {
      id: "id40",
      name: "中东国家的石油收入和转型需求",
      value: "中东国家的石油收入和转型需求",
    },
  ],
  links: [
    {
      source: "id1",
      target: "id2",
      value: "原因",
    },
    {
      source: "id1",
      target: "id3",
      value: "结果",
    },
    {
      source: "id4",
      target: "id5",
      value: "结果",
    },
    {
      source: "id6",
      target: "id7",
      value: "熊市",
    },
    {
      source: "id8",
      target: "id9",
      value: "企业",
    },
    {
      source: "id8",
      target: "id10",
      value: "外资动态",
    },
    {
      source: "id11",
      target: "id12",
      value: "建议",
    },
    {
      source: "id13",
      target: "id14",
      value: "影响",
    },
    {
      source: "id15",
      target: "id16",
      value: "投资选择",
    },
    {
      source: "id17",
      target: "id18",
      value: "影响",
    },
    {
      source: "id19",
      target: "id20",
      value: "风险",
    },
    {
      source: "id21",
      target: "id22",
      value: "高效",
    },
    {
      source: "id23",
      target: "id24",
      value: "风险",
    },
    {
      source: "id25",
      target: "id26",
      value: "观点",
    },
    {
      source: "id27",
      target: "id28",
      value: "建议",
    },
    {
      source: "id27",
      target: "id29",
      value: "背景",
    },
    {
      source: "id30",
      target: "id31",
      value: "资产配置",
    },
    {
      source: "id30",
      target: "id32",
      value: "阶段",
    },
    {
      source: "id33",
      target: "id34",
      value: "举例",
    },
    {
      source: "id8",
      target: "id9",
      value: "企业",
    },
    {
      source: "id8",
      target: "id10",
      value: "外资动态",
    },
    {
      source: "id35",
      target: "id36",
      value: "原因",
    },
    {
      source: "id25",
      target: "id26",
      value: "观点",
    },
    {
      source: "id37",
      target: "id32",
      value: "经济周期",
    },
    {
      source: "id37",
      target: "id34",
      value: "跨领域分散",
    },
    {
      source: "id37",
      target: "id38",
      value: "投资理念",
    },
    {
      source: "id39",
      target: "id40",
      value: "转型",
    },
  ],
};

const MapEle = () => {
  const eleRef = useRef<HTMLDivElement>(null);
  const [WH, setWH] = useState({ h: 0, w: 0 });
  const knowledgeGraph = useWorkflowStore((state) => state.knowledgeGraph);
  useEffect(() => {
    function eve() {
      if (eleRef.current) {
        const h = eleRef.current.offsetHeight;
        const w = eleRef.current.offsetWidth;
        setWH({ h, w });
      }
      console.log("reseize");
    }
    eve();
    if (eleRef.current) {
      eleRef.current.addEventListener("resize", eve);
    }
    return () => {
      if (eleRef.current) {
        eleRef.current.removeEventListener("resize", eve);
      }
    };
  }, []);
  return (
    <div ref={eleRef} className="h-full w-full">
      <ForceGraph3D
        graphData={myData}
        width={WH.w}
        height={WH.h}
        linkColor={"red"}
        linkThreeObjectExtend={true}
        nodeThreeObject={(node: any) => {
          const sprite = new SpriteText(node.name);
          sprite.color = "rgba(255,255,255,0.8)";
          sprite.textHeight = 8;
          return sprite;
        }}
      />
    </div>
  );
};
export default MapEle;
