import {
  useState,
  memo,
  useEffect,
  useMemo,
  useRef,
  useLayoutEffect,
} from "react";
import { <PERSON>ton, Flex, GetProp, notification } from "antd";
import DotLoading from "@/components/DotLoading";
import { useParams, useRouter } from "next/navigation";
import VirtualList, { ListRef } from "rc-virtual-list";

import { Attachments, Bubble, Prompts, Sender } from "@ant-design/x";
import { useWorkflowInformation } from "@/hooks/useWorkflowhooks";
import { useMCP } from "@/hooks/useMCP";
import { postWorkflowStop } from "@/service/fetch/workflow";
import {
  useUserInfoStore,
  useWorkflowStore,
  useConfigStore,
  useGlobalConfigStore,
} from "@/store/store";
import { HandleAgentItem } from "./Roles";
import { styleMap } from "@/utils/setting";

import styles from "./style.module.css";
import { LinkOutlined } from "@ant-design/icons";
const ChatPlan = () => {
  const { MCPRunInitEvent } = useMCP();

  // const [api, contextHolder] = notification.useNotification();
  const [content, setContent] = useState("");
  const [VH, setVH] = useState(0);
  const { taskid } = useParams<{ taskid: string }>();
  const cacheNotificationType = useRef<string[]>([]);
  const listRef = useRef<ListRef>(null);
  const scrollRef = useRef<boolean>(true);

  const router = useRouter();
  // store 数据
  const homeSetting = useGlobalConfigStore((state) => state.homeSetting);
  const updateNotificationInformation = useConfigStore(
    (state) => state.updateNotificationInformation
  );
  const pid = useUserInfoStore((state) => state.currentIpUser.id);
  const {
    startParams,
    WResult,
    chatLoading,
    updateTaskId,
    updateTaskName,
    clearStreamInterfance,
  } = useWorkflowStore((state) => state);

  const {
    runInitEvent,
    getHistoreyEvent,
    senderQuery,
    overWorkflowTask,
    // clearAbort,
    // clearTimeoutEve,
  } = useWorkflowInformation();

  console.log("WResult:", taskid, WResult);

  // const [agent] = useXAgent({
  //   request: async ({ message }, { onSuccess }) => {
  //     onSuccess(`Mock success return. You said: ${message}`);
  //   },
  // });
  // const { onRequest, messages, setMessages } = useXChat({
  //   agent,
  // });
  const onSubmit = (nextContent: string) => {
    if (!nextContent) return;
    senderQuery({
      question: nextContent,
      params: {
        pid: pid,
        doc_length: startParams.doc_length,
        query: nextContent,
        is_pass: 0,
        task_id: localStorage.getItem("workflow_task_id")
          ? Number(localStorage.getItem("workflow_task_id"))
          : undefined,
      },
      lastWResult: WResult,
    });
    setContent("");
  };
  const onChange = (nextContent: string) => {
    setContent(nextContent);
  };
  /**
   * 请求数据处理
   */
  const items: GetProp<typeof Bubble.List, "items"> = useMemo(() => {
    return Object.values(WResult)
      .filter((val) => val.agent_action !== "SPIDER")
      .map(
        (
          {
            id,
            content,
            agent_name_cn,
            agent_id,
            agent_style,
            code,
            last,
            agent_uuid,
            uuid,
            work_id,
            agent_action,
            think_txt,
            think_other_txt,
          },
          index
        ) => {
          const { error, agentItem } = HandleAgentItem({
            id,
            content,
            agent_name_cn,
            agent_id,
            agent_style,
            code,
            last,
            agent_uuid,
            uuid,
            work_id,
            agent_action,
            think_txt,
            think_other_txt,
          });
          // 判断agent解析是否错误，然后进行错误提示
          // 缓存错误类型，避免流式渲染重复提示相同的错误
          if (
            error.state &&
            agent_action &&
            !cacheNotificationType.current.includes(uuid)
          ) {
            setTimeout(() => {
              cacheNotificationType.current.push(uuid as string);
              updateNotificationInformation({
                key: Math.random(),
                type: "error",
                message: agent_name_cn
                  ? agent_name_cn + "错误"
                  : agentItem.role,
                description: error.message,
              });
            }, 10);
          }
          return {
            ...agentItem,
            shape: "corner" as const, // 明确指定 shape 为 "round" 或 "corner"
          };
        }
      );
  }, [WResult]);
  /**
   * 上传功能
   */
  const attachmentsNode = (
    <Attachments
      beforeUpload={() => false}
      placeholder={{
        icon: "AI",
        title: "Drag & Drop files here",
        description: "Support file type: image, video, audio, document, etc.",
      }}
    >
      <LinkOutlined />
    </Attachments>
  );
  /**
   * Prompts 自定义组件
   */
  // const senderPromptsItems: GetProp<typeof Prompts, "items"> = [
  //   {
  //     key: "1",
  //     description: "打断当前对话",
  //     icon: <></>,
  //   },
  // ];
  /**
   * Prompts 事件
   */
  // const onPromptsItemClick: GetProp<typeof Prompts, "onItemClick"> = (info) => {
  //   if (info.data.key === "1") {
  //     postWorkflowStop({ id: task_id as number });
  //   }
  // };
  useLayoutEffect(() => {
    let h = document.body.clientHeight - 140;
    setVH(h);
    MCPRunInitEvent();
  }, []);
  useEffect(() => {
    if (listRef.current && scrollRef.current) {
      listRef.current.scrollTo({
        index: items.length - 1,
        align: "bottom",
      });
    }
  }, [items]);
  /**
   * 判断进入方式
   * 如果taskid为query，就是传入搜索信息对话
   * 入股taskid为数字，就是根据任务查询历史对话
   */
  useEffect(() => {
    if (pid) {
      // 清空当前工作流标题
      updateTaskName("");
      // 控制当切换账号的时候，禁止重新请求工作流
      if (localStorage.getItem("initWorkflow") === "false") {
        return;
      }
      if (taskid === "query") {
        let t = sessionStorage.getItem("WF_IS_RELOAD");
        if (t === "1") {
          sessionStorage.setItem("WF_IS_RELOAD", "2");
        } else {
          sessionStorage.removeItem("WF_IS_RELOAD");
          router.push(
            `/workflow/detail/${localStorage.getItem("workflow_task_id")}`
          );
        }
        // 获取配置信息参数
        const {
          is_knowledge,
          is_person,
          is_search,
          doc_length,
          language,
          read_score,
          is_rag,
          style,
        } = homeSetting[`${pid}`] ?? {};
        runInitEvent(
          {
            ...startParams,
            is_knowledge: is_knowledge ?? 0,
            is_person,
            is_search,
            doc_length: doc_length?.key,
            language: language?.key,
            read_score: read_score?.key ?? 0,
            is_rag: is_rag ?? 0,
            style: styleMap[style?.label as string] ?? "",
          },
          startParams.query as string
        );
      } else {
        sessionStorage.removeItem("WF_IS_RELOAD");
        localStorage.setItem("workflow_task_id", taskid);
        getHistoreyEvent(Number(taskid));
      }
    }
  }, [pid]);
  /**
   * 切换页面更新数据
   */
  useEffect(() => {
    return () => {
      if (localStorage.getItem(`QueryTask_${taskid}`)) {
        const val = localStorage.getItem(`QueryTask_${taskid}`);
        localStorage.removeItem(`${val}`);
        localStorage.removeItem(`QueryTask_${taskid}`);
      }
      updateTaskId(0);
      clearStreamInterfance();
      overWorkflowTask();
      // 退出清空当前流缓存的错误提示信息
      cacheNotificationType.current = [];
    };
  }, []);
  /**
   * 监听滚动
   * 能够在输出内容的时候，滚动鼠标查看之前内容
   */
  useEffect(() => {}, []);
  return (
    <div className={styles.charPlan}>
      <Button onClick={MCPRunInitEvent}>开始MCP</Button>
      <div className={styles.chat}>
        {/* 欢迎占位 */}
        {!items.length && <DotLoading />}
        {/* 消息列表 */}
        {items.length > 0 && (
          <>
            <div style={{ height: VH }}>
              <VirtualList
                data={items}
                ref={listRef}
                height={VH}
                className={styles.VirtualListBubble}
                itemHeight={50}
                itemKey="id"
                onScroll={(e) => {
                  const H = e.currentTarget.scrollHeight;
                  const T = e.currentTarget.scrollTop;
                  const CH = e.currentTarget.clientHeight;
                  if (CH + T >= H - 10) {
                    scrollRef.current = true;
                  } else {
                    scrollRef.current = false;
                  }
                }}
              >
                {(item: any, index) => (
                  <div
                    key={`${item.id}_${index}`}
                    style={{ marginBottom: "22px" }}
                  >
                    <Bubble {...item} />
                  </div>
                )}
              </VirtualList>
            </div>
            {/* 提示词 */}
            {/* <Prompts
            items={senderPromptsItems}
            onItemClick={onPromptsItemClick}
          /> */}
            {/* 输入框 */}
            <Sender
              value={content}
              onChange={onChange}
              onSubmit={onSubmit}
              // prefix={attachmentsNode}
              loading={chatLoading}
              className={styles.sender}
              placeholder="补充需求..."
              onCancel={() => {
                if (localStorage.getItem("workflow_task_id")) {
                  postWorkflowStop({
                    id: Number(localStorage.getItem("workflow_task_id")),
                  });
                }
              }}
              styles={{
                input: {
                  height: "auto",
                },
                prefix: { position: "absolute" },
                actions: {
                  position: "absolute",
                  right: "12px",
                  bottom: "10px",
                },
              }}
            />
          </>
        )}
      </div>
    </div>
  );
};
ChatPlan.displayName = "ChatPlan";
export default ChatPlan;
