"use client";
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  TopicSelection,
  DataExpert,
  CopyWriter,
  AgentStep_1,
  Agent<PERSON>tep_2,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  // --------
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  AudioList,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  // --------
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  Agent<PERSON>ideoList,
  Agent<PERSON>ideoLoading,
  <PERSON><PERSON><PERSON><PERSON>,
  AgentEmbellish,
  AgentIP,
  //------------
  <PERSON><PERSON><PERSON><PERSON><PERSON>LEDGE,
  RAG<PERSON><PERSON>OWLEDGEFooter,
} from "../Agents";
import Image from "next/image";
import ImageFile from "@/assets/images";
import { Avatar, message, Space, Spin, type GetProp } from "antd";
import { Bubble } from "@ant-design/x";
import { UserOutlined } from "@ant-design/icons";
import aipLogo from "@/assets/images/pic/aip_logo.png";
import { error } from "console";
import { avatar } from "@nextui-org/theme";

const {
  <PERSON><PERSON><PERSON>_3,
  <PERSON><PERSON><PERSON>_5,
  AgentPic_6,
  <PERSON><PERSON><PERSON>_7,
  <PERSON><PERSON><PERSON>_13,
  <PERSON>Pic_17,
  AgentPic_18,

  <PERSON><PERSON><PERSON><PERSON>,
  PERSON,
  CHOICE,
  OUTSET,
  STRUC<PERSON>RE,
  VIEWPOINT,
  WRITER,
  STYLE,
  TITLE,
  UPLOAD,
  VOICE1,
  VOICE2,
  VOICE3,
  VIDEO1,
  VIDEO2,
  VIDEO3,
  COPYWRITER,
  SEARCH1,
  RAGDATA,
  SEARCH2,
  SEARCH3,
  HOOK,
} = ImageFile;

/**
 * agent 不同角色
 * 设置气泡默认属性，items 中的 role 会进行自动对应
 */
export const Roles: GetProp<typeof Bubble.List, "roles"> = {
  /** 基础ai */
  ai: {
    placement: "start",
    avatar: {
      icon: <UserOutlined />,
      style: {
        background: "rgba(255, 255, 255, 0.1)",
        padding: "4px",
        borderRadius: "50%",
        boxShadow: "0 0 0 1px rgba(255,255,255,0.1)",
      },
    },
    messageRender: AgentMD,
    styles: {
      content: {
        background: "rgba(255, 255, 255, 0.03)",
        border: "1px solid rgba(255, 255, 255, 0.07)",
      },
    },
  },
  /** 基础用户 */
  USER: {
    placement: "end",
    styles: {
      content: {
        background: "#006FEE",
        maxWidth: 598,
        // border: "1px solid rgba(255, 255, 255, 0.07)",
      },
    },
  },
  /** 意图识别/需求分析 专属 */
  RA: {
    placement: "start",
    avatar: {
      icon: (
        <div className="rounded-full bg-white/10 p-1 shadow-[0_0_0_1px_rgba(255,255,255,0.1)]">
          <Image src={aipLogo} alt="AIP Assistant" />
        </div>
      ),
      style: { background: "transparent" },
    },
    typing: { step: 5, interval: 20 },
    style: {
      maxWidth: 600,
    },
    header: <AgentHeader name="AIP 小助理" />,
    messageRender: (content: any) => {
      return <RequirementAnalysis content={content} />;
    },
    styles: {
      content: {
        background: "rgba(255, 255, 255, 0.03)",
        border: "1px solid rgba(255, 255, 255, 0.07)",
      },
    },
  },
  /** 选题专家 单选 */
  TS: {
    placement: "start",
    avatar: {
      icon: <Image src={aipLogo} alt="选题专家" />,
      style: { background: "rgba(255,255,255,0.2)" },
    },
    typing: { step: 5, interval: 20 },
    style: {
      maxWidth: 600,
    },
    messageRender: (content: any) => {
      return <TopicSelection content={content} />;
    },
    styles: {
      content: {
        background: "rgba(255, 255, 255, 0.03)",
        border: "1px solid rgba(255, 255, 255, 0.07)",
      },
    },
  },
  /** 结构专家 数组 */
  SE: {
    placement: "start",
    avatar: {
      icon: <Image src={aipLogo} alt="结构专家" />,
      style: { background: "rgba(255,255,255,0.2)" },
    },
    messageRender: (content: any) => {
      return <StructuralExpert content={content} />;
    },
    styles: {
      content: {
        position: "relative",
        background: "rgba(255, 255, 255, 0.03)",
        border: "1px solid rgba(255, 255, 255, 0.07)",
      },
    },
  },
  /** 数据专家 */
  DE: {
    placement: "start",
    avatar: {
      icon: <Image src={aipLogo} alt="数据专家" />,
      style: { background: "rgba(255,255,255,0.2)" },
    },
    messageRender: (content: any) => {
      return <DataExpert content={content} collapse={true} />;
    },
    styles: {
      content: {
        position: "relative",
        background: "rgba(255, 255, 255, 0.03)",
        border: "1px solid rgba(255, 255, 255, 0.07)",
        padding: "0 16px",
        width: "552px",
      },
    },
  },
  /** 文案写手 */
  CW: {
    placement: "start",
    avatar: {
      icon: <Image src={aipLogo} alt="文案写手" />,
      style: { background: "rgba(255,255,255,0.2)" },
    },
    messageRender: (content: string) => {
      return <CopyWriter content={content} />;
    },
    styles: {
      content: {
        position: "relative",
        background: "rgba(255, 255, 255, 0.03)",
        border: "1px solid rgba(255, 255, 255, 0.07)",
      },
    },
  },
  /** 润色专家 */
  EL: {
    placement: "start",
    avatar: {
      icon: <Image src={aipLogo} alt="" />,
      style: { background: "rgba(255,255,255,0.2)" },
    },
    messageRender: (content: any) => {
      return <AgentEmbellish content={content} />;
    },
    styles: {
      content: {
        position: "relative",
        background: "rgba(255, 255, 255, 0.03)",
        border: "1px solid rgba(255, 255, 255, 0.07)",
      },
    },
  },
  /** 搜索专家 */
  AS_1: {
    placement: "start",
    avatar: {
      icon: <Image src={aipLogo} alt="搜索专家" />,
      style: { background: "rgba(255,255,255,0.2)" },
    },
    styles: {
      content: {
        width: "400px",
        position: "relative",
        background: "rgba(255, 255, 255, 0.03)",
        border: "1px solid rgba(255, 255, 255, 0.07)",
      },
    },
    messageRender: (content: any) => {
      return <AgentStep_1 step={content.step} code={content.code} />;
    },
  },
  /** 搜索专家 */
  AS_2: {
    placement: "start",
    avatar: {
      icon: <Image src={aipLogo} alt="搜索专家" />,
      style: { background: "rgba(255,255,255,0.2)" },
    },
    styles: {
      content: {
        width: "400px",
        position: "relative",
        background: "rgba(255, 255, 255, 0.03)",
        border: "1px solid rgba(255, 255, 255, 0.07)",
      },
    },
    messageRender: (content: any) => {
      return <AgentStep_2 step={content.step} code={content.code} />;
    },
  },
  /** 音频专家-list */
  AA: {
    placement: "start",
    avatar: {
      icon: <Image src={aipLogo} alt="音频专家" />,
      style: { background: "rgba(255,255,255,0.2)" },
    },
    styles: {
      content: {
        width: "470px",
        position: "relative",
        background: "rgba(255, 255, 255, 0.03)",
        border: "1px solid rgba(255, 255, 255, 0.07)",
      },
    },
    messageRender: (content: any) => {
      return <AudioList content={content} />;
    },
  },
  /** 上传音频-upload */
  AU: {
    placement: "start",
    avatar: {
      icon: <Image src={aipLogo} alt="上传音频" />,
      style: { background: "rgba(255,255,255,0.2)" },
    },
    styles: {
      content: {
        width: "452px",
        position: "relative",
        background: "rgba(255, 255, 255, 0.03)",
        border: "1px solid rgba(255, 255, 255, 0.07)",
      },
    },
    messageRender: (content: any) => {
      return <AgentUploadAudio content={content} />;
    },
  },
  /** 音频专家-loading */
  AL: {
    placement: "start",
    avatar: {
      icon: <Image src={aipLogo} alt="音频专家" />,
      style: { background: "rgba(255,255,255,0.2)" },
    },
    styles: {
      content: {
        width: "260px",
        position: "relative",
      },
    },
    messageRender: (content: any) => {
      return <AgentLoading />;
    },
  },
  /** 音频专家-detail */
  AD: {
    placement: "start",
    avatar: {
      icon: <Image src={aipLogo} alt="音频专家" />,
      style: { background: "rgba(255,255,255,0.2)" },
    },
    styles: {
      content: {
        width: "260px",
        position: "relative",
        padding: "0",
      },
    },
    footer: <AudioFooter />,
    messageRender: (content: any) => {
      return <AudioDetail content={content} />;
    },
  },
  /** 视频专家-list */
  AVL: {
    placement: "start",
    avatar: {
      icon: (
        <div className="rounded-full bg-white/10 p-1 shadow-[0_0_0_1px_rgba(255,255,255,0.1)]">
          <Image src={aipLogo} alt="视频专家" />
        </div>
      ),
      style: { background: "transparent" },
    },
    styles: {
      content: {
        position: "relative",
        width: "auto",
        background: "rgba(255, 255, 255, 0.03)",
        border: "1px solid rgba(255, 255, 255, 0.07)",
      },
    },
    messageRender: (content: any) => {
      return <AgentVideoList content={content} />;
    },
  },
  /** 视频专家-loading */
  AVO: {
    placement: "start",
    avatar: {
      icon: <Image src={aipLogo} alt="视频专家" />,
      style: { background: "rgba(255,255,255,0.2)" },
    },
    styles: {
      content: {
        position: "relative",
        background: "rgba(255, 255, 255, 0.03)",
        border: "1px solid rgba(255, 255, 255, 0.015)",
        padding: 0,
        overflow: "hidden",
      },
    },
    messageRender: (content: any) => {
      return <AgentVideoLoading />;
    },
  },
  /** 视频专家-detail*/
  AVD: {
    placement: "start",
    avatar: {
      icon: <Image src={aipLogo} alt="视频专家" />,
      style: { background: "rgba(255,255,255,0.2)" },
    },
    styles: {
      content: {
        position: "relative",
        background: "rgba(255, 255, 255, 0.03)",
        border: "1px solid rgba(255, 255, 255, 0.07)",
        padding: 0,
      },
    },
    messageRender: (content: any) => {
      return <AgentVideoDetail content={content} />;
    },
  },
  IP: {
    placement: "start",
    avatar: {
      icon: <Image src={aipLogo} alt="IP 人设" />,
      style: { background: "rgba(255,255,255,0.2)" },
    },
    messageRender: (content: any) => {
      return <AgentIP />;
    },
  },
};
/** 兼容选题专家数据 */
const compatibleTsData = (
  data: any
): { error: { state: boolean; message: any }; result: any[] } => {
  let result: any[] = [];

  try {
    if (typeof data === "string") {
      let d = data.replace("```json", "").replace("```", "").split("_CHOICE_");
      d.forEach((val, index) => {
        let p = [];
        try {
          p = JSON.parse(val);
        } catch (error) {
          p = [];
        }
        result.push(...p);
      });
      let keyVal: { [key: string]: string } = {};
      result.forEach((val, index) => {
        keyVal[`${val.order_cn}`] = val;
      });
      result = Object.values(keyVal);
      if (Array.isArray(result)) {
        return {
          result,
          error: { state: false, message: "" },
        };
      } else {
        return {
          result: [],
          error: { state: true, message: JSON.stringify(data) },
        };
      }
    } else {
      return {
        result: [],
        error: { state: true, message: JSON.stringify(data) },
      };
    }
  } catch (error) {
    return {
      result: [],
      error: { state: true, message: `选题数据格式解析失败:${String(error)}` },
    };
  }
};
interface compatibleContentDataProp {
  data: string;
  defaultData: any;
}
/**
 * 兼容 content 数据信息为 “”
 * 不可解析的json数据
 * @param params
 * */
const compatibleContentData = (
  params: compatibleContentDataProp
): { error: { state: boolean; message: any }; result: any } => {
  try {
    if (params.data === "") {
      return {
        result: params.defaultData,
        error: { state: false, message: "" },
      };
    }
    const data = JSON.parse(params.data);
    return {
      result: data,
      error: { state: false, message: "" },
    };
  } catch (error) {
    return {
      error: { state: true, message: `数据格式解析失败:${String(error)}` },
      result: params.defaultData,
    };
  }
};
/**
 * 兼容MD 需要字符串情况
 */
const compatibleMdData = (
  data: any
): { error: { state: boolean; message: any }; result: any } => {
  try {
    let d = "";
    if (data) {
      if (typeof data === "object") {
        d = JSON.stringify(data);
      } else {
        d = `${data}`;
      }
    } else {
      d = "";
    }
    return {
      error: { state: false, message: "" },
      result: d,
    };
  } catch (error) {
    return {
      error: { state: true, message: `数据格式解析失败:${String(error)}` },
      result: "",
    };
  }
};
/**
 * 获取md文件里面的json数据
 */
const compatibleMdJSON = (
  data: any
): { error: { state: boolean; message: any }; result: any; text: any } => {
  if (typeof data !== "string") {
    return {
      error: {
        state: true,
        message: "获取数据类型错误",
      },
      result: "",
      text: "",
    };
  }
  let jsonObject: any = {};
  const filterText = data.replace(/```json[\s\S]*?```/g, function (val) {
    const obj = val.replace(/```(json)?/g, "");
    try {
      jsonObject = JSON.parse(obj);
    } catch (error) {
      jsonObject = {};
    }
    return "";
  });

  return {
    error: {
      state: false,
      message: "",
    },
    result: jsonObject?.taskname ?? "",
    text: filterText,
  };
};

const headerPics: { [key: number | string]: any } = {
  INTENTION: AipLogo,

  CHOICE,
  COPYWRITER,
  HOOK,
  OUTSET,
  STRUCTURE,
  VIEWPOINT,
  RAGDATA,
  // 27: {
  //   name: "钩子专家",
  //   agent_id: 27,
  //   agent_style: 1,
  //   headerPic: AgentPic_19,
  // },

  WRITER,
  STYLE,
  TITLE,
  UPLOAD,
  VOICE1,
  VOICE2,
  VOICE3,
  VIDEO1,
  VIDEO2,
  VIDEO3,
  SEARCH1,
  SEARCH2,
  SEARCH3,
  PERSON,
};

const getHeaderPic = (agent_action: string | undefined) => {
  if (agent_action && headerPics[agent_action]) {
    return headerPics[agent_action].pic;
  } else {
    return aipLogo;
  }
};
interface GetAgentItemProps {
  id: any;
  content: string;
  agent_name_cn: string;
  agent_id: number;
  agent_style: string;
  code?: "done" | null;
  last?: boolean;
  agent_uuid?: string;
  uuid?: string;
  work_id?: number;
  agent_action?: string;
  think_txt?: string;
  think_other_txt?: string;
}

/**
 * agent_style 说明：
 * USER: 用户输入
 * USUALLY: 通用输出（用于观点专家、钩子专家、标题专家等） ai
 * COMMAND: 需求分析输出 RA
 * TITLE_CHOOSE: 选题专家输出（返回选题列表） TS
 * DATA_LOADING_1 搜索专家 loading
 * DATA_LOADING_2 搜索专家 loading
 * DATA: 搜索专家输出（返回相关视频链接）DE
 * STRUCT: 结构专家输出（返回内容结构）
 * EMBELLISH: 润色专家输出
 * WRITER: 文案写手 CW
 * VOICE_1: 音频专家1（返回多个音频选项）
 * VOICE_2: 音频专家2（生成音频进度）
 * VOICE_3: 音频专家3（返回最终音频URL）
 * VIDEO_1: 视频专家1（返回多个视频选项）
 * VIDEO_2: 视频专家2（生成视频进度）
 * VIDEO_3: 视频专家3（返回最终视频URL）
 * RAGDATA: 知识库 专家
 */
const avatarContainerStyle =
  "rounded-full bg-white/10 shadow-[0_0_0_1px_rgba(255,255,255,0.1)]";
const agentBaseTemplate = (item: GetAgentItemProps) => {
  const { id, agent_action, agent_name_cn } = item;
  return {
    id,
    typing: false,
    shape: "corner",
    avatar: (
      <div className={avatarContainerStyle}>
        <Image
          src={getHeaderPic(agent_action)}
          alt="专家"
          className="flex-shrink-0 flex-grow-0 w-[36px] h-[36px]"
        />
      </div>
    ),
    header: <AgentHeader name={agent_name_cn} />,
  };
};
/** 需求分析 专属消息气泡样式 */
const COMMAND_Template = (item: GetAgentItemProps) => {
  const BaseTemplate = agentBaseTemplate(item);
  const { content, code, id } = item;
  const data = {
    content,
    taskname: "",
  };
  let errorState = { state: false, message: "" };
  if (code === "done") {
    const { error, result, text } = compatibleMdJSON(content);
    data.content = text;
    data.taskname = result;
    errorState = error;
  }
  return {
    error: errorState,
    agentItem: {
      ...BaseTemplate,
      placement: "start",
      content: data,
      loading: code === "done" ? false : content?.length <= 0 ? true : false,
      messageRender: (content: any) => {
        return <RequirementAnalysis content={content} />;
      },
      style: {
        maxWidth: 600,
      },
      styles: {
        content: {
          background: "rgba(255, 255, 255, 0.03)",
          border: "1px solid rgba(255, 255, 255, 0.07)",
        },
      },
    },
  };
};
/** 选题专家 单选 */
const TITLE_CHOOSE_Template = (item: GetAgentItemProps) => {
  const { content, last } = item;
  const BaseTemplate = agentBaseTemplate(item);
  const { error, result } = compatibleTsData(content);
  const c: { [key: string]: any } = {
    last: last,
    list: result,
  };
  return {
    error: error,
    agentItem: {
      ...BaseTemplate,
      content: c,
      placement: "start",
      messageRender: (content: any) => {
        return <TopicSelection content={content} />;
      },
      style: {
        maxWidth: 600,
      },
      styles: {
        content: {
          background: "rgba(255, 255, 255, 0.03)",
          border: "1px solid rgba(255, 255, 255, 0.07)",
        },
      },
    },
  };
};
/** 数据搜索专家_1 */
const DATA_LOADING_1_Template = (item: GetAgentItemProps) => {
  const { code } = item;
  const BaseTemplate = agentBaseTemplate(item);
  let errorState = {
    state: false,
    message: "",
  };
  const c = { step: 1, code: code };
  return {
    error: errorState,
    agentItem: {
      ...BaseTemplate,
      placement: "start",
      content: c,
      loading: false,
      styles: {
        content: {
          width: "400px",
          position: "relative",
          background: "rgba(255, 255, 255, 0.03)",
          border: "1px solid rgba(255, 255, 255, 0.07)",
        },
      },
      messageRender: (content: any) => {
        return <AgentStep_1 step={content.step} code={content.code} />;
      },
    },
  };
};
/** 数据搜索专家_2 */
const DATA_LOADING_2_Template = (item: GetAgentItemProps) => {
  const { code } = item;
  const BaseTemplate = agentBaseTemplate(item);
  let errorState = {
    state: false,
    message: "",
  };
  const c = { step: 2, code: code };
  return {
    error: errorState,
    agentItem: {
      ...BaseTemplate,
      content: c,
      placement: "start",
      loading: false,
      styles: {
        content: {
          width: "400px",
          position: "relative",
          background: "rgba(255, 255, 255, 0.03)",
          border: "1px solid rgba(255, 255, 255, 0.07)",
        },
      },
      messageRender: (content: any) => {
        return <AgentStep_2 step={content.step} code={content.code} />;
      },
    },
  };
};
/** 数据专家 展示数据 */
const DATA_Template = (item: GetAgentItemProps) => {
  const { code, content } = item;
  const BaseTemplate = agentBaseTemplate(item);
  let l = true;
  let c = {};
  let errorState = {
    state: false,
    message: "",
  };
  if (code === "done") {
    try {
      c = JSON.parse(content);
      l = false;
    } catch (error) {
      l = false;
      errorState = {
        state: true,
        message: `数据解析失败:${String(error)}`,
      };
    }
  }
  return {
    error: errorState,
    agentItem: {
      ...BaseTemplate,
      placement: "start",
      content: c,
      loading: l,
      messageRender: (content: any) => {
        return <DataExpert content={content} collapse={true} />;
      },
      styles: {
        content: {
          position: "relative",
          background: "rgba(255, 255, 255, 0.03)",
          border: "1px solid rgba(255, 255, 255, 0.07)",
          padding: "0 16px",
          width: "552px",
        },
      },
    },
  };
};
/** 结构专家 列数据 */
const STRUCT_Template = (item: GetAgentItemProps) => {
  const { content, code } = item;
  const BaseTemplate = agentBaseTemplate(item);
  const { error, result } = compatibleMdData(content);
  return {
    error: error,
    agentItem: {
      ...BaseTemplate,
      placement: "start",
      content: result,
      messageRender: (content: any) => {
        return <StructuralExpert content={content} />;
      },
      styles: {
        content: {
          position: "relative",
          background: "rgba(255, 255, 255, 0.03)",
          border: "1px solid rgba(255, 255, 255, 0.07)",
        },
      },
      loading: code === "done" ? false : result?.length <= 0 ? true : false,
    },
  };
};
/** 用户的数据 */
const USER_Template = (item: GetAgentItemProps) => {
  const { content, code } = item;
  const BaseTemplate = agentBaseTemplate(item);
  return {
    error: { state: false, message: "" },
    agentItem: {
      ...BaseTemplate,
      placement: "end",
      content: content,
      loading: code !== "done" ? true : false,
      header: null,
      avatar: null,
      styles: {
        content: {
          background: "#006FEE",
          maxWidth: 598,
        },
      },
    },
  };
};
/** 音频专家列表 */
const VOICE_1_Template = (item: GetAgentItemProps) => {
  const { content, code, agent_uuid } = item;
  const BaseTemplate = agentBaseTemplate(item);
  let errorState = { state: false, message: "" };
  let l = code !== "done" ? true : false;
  let c: any;
  if (code !== "done" || !content) {
    c = { agent_uuid: agent_uuid ?? "", content: [] };
  } else {
    try {
      const d = JSON.parse(content);
      c = { agent_uuid: agent_uuid ?? "", content: d };
    } catch (error) {
      errorState = {
        state: true,
        message: `数据解析失败:${String(error)}`,
      };
      c = { agent_uuid: agent_uuid ?? "", content: [] };
    }
  }
  return {
    error: errorState,
    agentItem: {
      ...BaseTemplate,
      placement: "start",
      content: c,
      loading: l,
      styles: {
        content: {
          width: "470px",
          position: "relative",
          background: "rgba(255, 255, 255, 0.03)",
          border: "1px solid rgba(255, 255, 255, 0.07)",
        },
      },
      messageRender: (content: any) => {
        return <AudioList content={content} />;
      },
    },
  };
};
const VOICE_2_Template = (item: GetAgentItemProps) => {
  const BaseTemplate = agentBaseTemplate(item);
  let errorState = { state: false, message: "" };

  return {
    error: errorState,
    agentItem: {
      ...BaseTemplate,
      content: "",
      loading: false,
      placement: "start",
      styles: {
        content: {
          width: "260px",
          position: "relative",
          padding: 0,
        },
      },
      messageRender: (content: any) => {
        return <AgentLoading />;
      },
    },
  };
};
const VOICE_3_Template = (item: GetAgentItemProps) => {
  const { code, content, uuid } = item;
  const BaseTemplate = agentBaseTemplate(item);
  let errorState = { state: false, message: "" };
  let c: any;
  let r: any = {};
  let l = false;
  if (code === "done") {
    l = false;
    r = {
      styles: {
        content: {
          width: "260px",
          position: "relative",
          padding: "0",
        },
      },
      footer: <AudioFooter />,
      messageRender: (content: any) => {
        return <AudioDetail content={content} />;
      },
    };
    try {
      const d = JSON.parse(content);
      c = { uuid: uuid, ...d };
    } catch (error) {
      errorState = {
        state: true,
        message: `数据解析失败:${String(error)}`,
      };
      c = { uuid: uuid };
    }
  } else {
    c = "";
    r = {
      styles: {
        content: {
          width: "260px",
          position: "relative",
        },
      },
      messageRender: (content: any) => {
        return <AgentLoading />;
      },
    };
  }
  l = false;
  return {
    error: errorState,
    agentItem: {
      ...BaseTemplate,
      content: c,
      loading: l,
      ...r,
      placement: "start",
      styles: {
        content: {
          background: "transparent",
          padding: 0,
        },
      },
    },
  };
};
/** 音频上传专家 */
const VOICE_UPLOAD_Template = (item: GetAgentItemProps) => {
  const { code, agent_uuid } = item;
  const BaseTemplate = agentBaseTemplate(item);
  return {
    error: { state: false, message: "" },
    agentItem: {
      ...BaseTemplate,
      placement: "start",
      content: { agent_uuid: agent_uuid ?? "" },
      loading: code !== "done" ? true : false,
      styles: {
        content: {
          width: "452px",
          position: "relative",
          background: "rgba(255, 255, 255, 0.03)",
          border: "1px solid rgba(255, 255, 255, 0.07)",
        },
      },
      messageRender: (content: any) => {
        return <AgentUploadAudio content={content} />;
      },
    },
  };
};
/** 视频专家 */
const VIDEO_1_Template = (item: GetAgentItemProps) => {
  const { code, content, agent_uuid } = item;
  const BaseTemplate = agentBaseTemplate(item);
  let c: any = { content: [], agent_uuid: "" };
  let l = true;
  let f = null;
  let errorState = { state: false, message: "" };
  if (code === "done") {
    try {
      let d = JSON.parse(content);
      c = { content: d, agent_uuid: agent_uuid };
      l = false;
    } catch (error) {
      errorState = {
        state: true,
        message: `数据解析失败:${String(error)}`,
      };
      c = { content: [], agent_uuid: "" };
    }
  }
  return {
    error: errorState,
    agentItem: {
      ...BaseTemplate,
      placement: "start",
      content: c,
      loading: l,
      styles: {
        content: {
          position: "relative",
          width: "auto",
          background: "rgba(255, 255, 255, 0.03)",
          border: "1px solid rgba(255, 255, 255, 0.07)",
        },
      },
      messageRender: (content: any) => {
        return <AgentVideoList content={content} />;
      },
    },
  };
};
const VIDEO_2_Template = (item: GetAgentItemProps) => {
  const {} = item;
  const BaseTemplate = agentBaseTemplate(item);
  let c: any = "";
  let r = "";
  let l = false;
  let f = null;
  let errorState = { state: false, message: "" };
  return {
    error: errorState,
    agentItem: {
      ...BaseTemplate,
      placement: "start",
      content: c,
      loading: l,
      styles: {
        content: {
          position: "relative",
          background: "rgba(255, 255, 255, 0.03)",
          border: "1px solid rgba(255, 255, 255, 0.015)",
          padding: 0,
          overflow: "hidden",
        },
      },
      messageRender: (content: any) => {
        return <AgentVideoLoading />;
      },
    },
  };
};
const VIDEO_3_Template = (item: GetAgentItemProps) => {
  const { content, code } = item;
  const BaseTemplate = agentBaseTemplate(item);
  let errorState = { state: false, message: "" };
  let c: any;
  let r: { [key: string]: any } = {};
  let f = null;
  if (code === "done") {
    if (content === "") {
      c = "";
      f = null;
      r = {
        styles: {
          content: {
            position: "relative",
            background: "rgba(255, 255, 255, 0.03)",
            border: "1px solid rgba(255, 255, 255, 0.015)",
            padding: 0,
            overflow: "hidden",
          },
        },
        messageRender: (content: any) => {
          return <AgentVideoLoading />;
        },
      };
    } else {
      const { error, result } = compatibleContentData({
        data: content,
        defaultData: {
          video_url: "",
          preview_url: "",
        },
      });
      errorState = error;
      c = result;
      r = {
        styles: {
          content: {
            position: "relative",
            background: "rgba(255, 255, 255, 0.03)",
            border: "1px solid rgba(255, 255, 255, 0.07)",
            padding: 0,
          },
        },
        messageRender: (content: any) => {
          return <AgentVideoDetail content={content} />;
        },
      };
      f = c?.video_url ? <VideoFooter /> : null;
    }
  }
  return {
    error: errorState,
    agentItem: {
      ...BaseTemplate,
      placement: "start",
      content: c,
      ...r,
      loading: code !== "done" ? true : false,
      footer: f,
    },
  };
};
/** 文案写手 */
const WRITER_Template = (item: GetAgentItemProps) => {
  const { content, code } = item;
  const BaseTemplate = agentBaseTemplate(item);
  let c = "";
  let l = false;
  let errorState = {
    state: false,
    message: "",
  };
  if (typeof content !== "string") {
    errorState = {
      state: true,
      message: "数据格式错误",
    };
    c = "";
  } else {
    c = content;
    l = code === "done" ? false : c?.length <= 0 ? true : false;
  }
  return {
    error: errorState,
    agentItem: {
      ...BaseTemplate,
      placement: "start",
      content: c,
      loading: l,
      messageRender: (content: string) => {
        return <CopyWriter content={content} />;
      },
      styles: {
        content: {
          position: "relative",
          background: "rgba(255, 255, 255, 0.03)",
          border: "1px solid rgba(255, 255, 255, 0.07)",
        },
      },
    },
  };
};
/** 润色专家 */
const EMBELLISH_Template = (item: GetAgentItemProps) => {
  const { content, think_txt, think_other_txt, work_id, uuid, code } = item;
  const BaseTemplate = agentBaseTemplate(item);
  let c = "";
  let errorState = {
    state: false,
    message: "",
  };
  if (typeof content !== "string") {
    errorState = {
      state: true,
      message: "数据格式错误",
    };
    c = "";
  } else {
    c = content;
  }

  return {
    error: errorState,
    agentItem: {
      ...BaseTemplate,
      placement: "start",
      content: {
        content: c,
        think_txt: think_txt,
        think_other_txt: think_other_txt,
        work_id: work_id,
        uuid: uuid,
        code: code,
      },
      loading: code === "done" ? false : c?.length <= 0 ? true : false,
      styles: {
        content: {
          position: "relative",
          background: "rgba(255, 255, 255, 0.03)",
          border: "1px solid rgba(255, 255, 255, 0.07)",
        },
      },
      messageRender: (content: any) => {
        return <AgentEmbellish content={content} />;
      },
    },
  };
};
/** IP 人设 */
const IPPERSONA_Template = (item: GetAgentItemProps) => {
  const { code } = item;
  const BaseTemplate = agentBaseTemplate(item);
  return {
    error: {
      state: false,
      message: "",
    },
    agentItem: {
      ...BaseTemplate,
      placement: "start",
      content: "",
      role: "IP",
      loading: code !== "done" ? true : false,
      messageRender: (content: any) => {
        return <AgentIP />;
      },
    },
  };
};
/** 知识库 */
const ISRAG_Template = (item: GetAgentItemProps) => {
  const { content, code, think_txt } = item;
  const BaseTemplate = agentBaseTemplate(item);
  let errorState = { state: false, message: "" };
  let c: any;
  let r: { [key: string]: any } = {};
  let f = null;
  if (code === "done") {
    if (think_txt && Number(think_txt) > 0) {
      c = "👌🏻 已完成知识库信息检索";
    } else {
      c = "👌🏻 已完成知识库信息检索,未检索到相关内容";
    }
    f = <RAGKNOWLEDGEFooter number={think_txt ?? "0"} />;
  }
  return {
    error: errorState,
    agentItem: {
      ...BaseTemplate,
      placement: "start",
      content: c,
      ...r,
      loading: code !== "done" ? true : false,
      messageRender: AgentMD,
      footer: f,
      loadingRender: () => (
        <Space>
          <Spin size="small" />
          知识库专家正在检索中
        </Space>
      ),
    },
  };
};
/** default */
const DEFAULT_Template = (item: GetAgentItemProps) => {
  const { content, code } = item;
  const BaseTemplate = agentBaseTemplate(item);
  return {
    error: {
      state: false,
      message: "",
    },
    agentItem: {
      ...BaseTemplate,
      placement: "start",
      content: content,
      messageRender: AgentMD,
      styles: {
        content: {
          background: "rgba(255, 255, 255, 0.03)",
          border: "1px solid rgba(255, 255, 255, 0.07)",
        },
      },
      loading: code === "done" ? false : content?.length <= 0 ? true : false,
    },
  };
};

export const HandleAgentItem = (
  item: GetAgentItemProps
): {
  error: { state: boolean; message: any };
  agentItem: { [key: string]: any };
} => {
  const { agent_style } = item;

  if (agent_style === "COMMAND") {
    const config = COMMAND_Template(item);
    return {
      ...config,
    };
  } else if (agent_style === "TITLE_CHOOSE") {
    const config = TITLE_CHOOSE_Template(item);
    return {
      ...config,
    };
  } else if (agent_style === "DATA_LOADING_1") {
    const config = DATA_LOADING_1_Template(item);
    return {
      ...config,
    };
  } else if (agent_style === "DATA_LOADING_2") {
    const config = DATA_LOADING_2_Template(item);
    return {
      ...config,
    };
  } else if (agent_style === "DATA") {
    const config = DATA_Template(item);
    return {
      ...config,
    };
  } else if (agent_style === "STRUCT") {
    const config = STRUCT_Template(item);
    return {
      ...config,
    };
  } else if (agent_style === "USER") {
    const config = USER_Template(item);
    return {
      ...config,
    };
  } else if (agent_style === "VOICE_1") {
    const config = VOICE_1_Template(item);
    return {
      ...config,
    };
  } else if (agent_style === "VOICE_2") {
    const config = VOICE_2_Template(item);
    return {
      ...config,
    };
  } else if (agent_style === "VOICE_3") {
    const config = VOICE_3_Template(item);
    return {
      ...config,
    };
  } else if (agent_style === "VOICE_UPLOAD") {
    const config = VOICE_UPLOAD_Template(item);
    return {
      ...config,
    };
  } else if (agent_style === "VIDEO_1") {
    const config = VIDEO_1_Template(item);
    return {
      ...config,
    };
  } else if (agent_style === "VIDEO_2") {
    const config = VIDEO_2_Template(item);
    return {
      ...config,
    };
  } else if (agent_style === "VIDEO_3") {
    const config = VIDEO_3_Template(item);
    return {
      ...config,
    };
  } else if (agent_style === "WRITER") {
    const config = WRITER_Template(item);
    return {
      ...config,
    };
  } else if (agent_style === "EMBELLISH") {
    const config = EMBELLISH_Template(item);
    return {
      ...config,
    };
  } else if (agent_style === "IPPERSONA") {
    const config = IPPERSONA_Template(item);
    return {
      ...config,
    };
  } else if (agent_style === "RAGDATA") {
    const config = ISRAG_Template(item);
    return {
      ...config,
    };
  } else {
    const config = DEFAULT_Template(item);
    return {
      ...config,
    };
  }
};
