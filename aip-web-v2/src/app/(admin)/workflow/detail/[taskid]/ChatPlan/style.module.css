.charPlan {
  height: 100%;
  overflow-y: auto; /* 将滚动条移到最外层 */
  position: relative; /* 添加相对定位 */
  flex-direction: column;
  min-width: 320px; /* 设置最小宽度 */
  width: 100%;
  padding: 10px 2px 0;
}
.chat {
  height: 100%;
  width: 100%;
  margin: 0 auto;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  gap: 0;
}
.messages::-webkit-scrollbar {
  width: 0;
}
.charPlan::-webkit-scrollbar {
  width: 0;
}
/* .VirtualListBubble {
  padding-top: 20px;
} */
/* .VirtualListBubble::-webkit-scrollbar {
  width: 0px;
}
.VirtualListBubble::-webkit-scrollbar-track {
  background-color: transparent;
}
.VirtualListBubble::-webkit-scrollbar-thumb {
  border-radius: 4px;
  background-color: transparent;
} */
.messages {
  flex: 1; /* flex: 1; */
  /* overflow: visible; 取消内部滚动 */
  padding: 36px 0;
  margin: 0; /* 内容居中 */
  padding-bottom: 20px;
}
.sender {
  position: sticky;
  bottom: 0;
  width: 100%;
  max-width: 650px;
  margin: 0 auto;
  padding: 3px 0;
  background: rgb(34, 34, 39);
  z-index: 100;
  box-shadow: 0 0 20px rgba(0, 4, 39, 0.5);
  display: flex;
  align-items: flex-end;
}
