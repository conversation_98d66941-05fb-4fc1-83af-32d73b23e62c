.agent_video_detail {
  position: relative;
  width: 200px;
  height: 266.7px;
  overflow: hidden;
  border-radius: 10px;
  border-start-start-radius: 2px;
}
.video_container {
  position: relative;
  width: 100%;
  height: 100%;
}
.agent_video_loading_layer {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  justify-content: center;
  position: absolute;
  z-index: 100;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  padding: 0 30px;
  background-color: rgba(0, 0, 0, 0.5);
}
.agent_video_loading_text {
  color: rgba(255, 255, 255, 0.7);
  letter-spacing: 1px;
  font-weight: bold;
  text-align: center;
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: 18px; /* 150% */
}

.previewLayer {
  position: absolute;
  z-index: 50;
  width: 100%;
  height: 100%;
  object-fit: contain;
}
.playLayer {
  visibility: hidden;
  display: flex;
  justify-content: center;
  align-items: center;
  position: absolute;
  z-index: 51;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  opacity: 0;
  transition: opacity 0.2s ease;
  cursor: pointer;
  background-color: rgba(0, 0, 0, 0.1);
}
.video_container:hover .playLayer {
  visibility: visible;
  opacity: 1;
}
.error_message {
  font-size: 13px;
  color: rgba(255, 255, 255, 0.7);
}
.error_overlay {
  position: absolute;
  z-index: 50;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 12px;
  padding: 0 16px;
}

.controls_overlay {
  position: absolute;
  z-index: 51;
  width: 100%;
  height: 38px;
  bottom: 0;
  left: 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 12px;
  visibility: hidden;
}
.video_ele {
  cursor: pointer;
  height: 100%;
}
.video_ele:hover .controls_overlay {
  visibility: visible;
}
.controls_btn_bg {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  cursor: pointer;
}
.controls_btn_bg:hover {
  background-color: rgba(102, 102, 102, 0.6);
}
