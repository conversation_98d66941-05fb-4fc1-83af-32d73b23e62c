import { CheckCircleOutlined, UserOutlined } from "@ant-design/icons";
import { ChevronDown, ChevronUp, Pen<PERSON><PERSON>, Copy } from "lucide-react";
import { useClipboard } from "@/hooks/useClipboard";
import { AgentMD } from "../AgentMD";
import styles from "./style.module.css";
import { FC, memo, useEffect, useRef, useState } from "react";
interface CopyWriterProps {
  title?: string;
  content: string;
  status?: boolean;
}
const CopyWriter: FC<CopyWriterProps> = memo(
  ({ title = "", content, status }) => {
    const [collapseOpen, setCollapseOpen] = useState(false);
    const { onCopy } = useClipboard(content);
    return (
      <section>
        {/* <div
        className={styles.header}
        onClick={() => setCollapseOpen(!collapseOpen)}
      >
        <div className={styles.title}>
          <CheckCircleOutlined className={styles.icon} />
          {<label>{title}</label>}
        </div>
        {collapseOpen ? (
          <ChevronDown className={styles.down_icon} />
        ) : (
          <ChevronUp className={styles.down_icon} />
        )}
      </div> */}

        <div className={styles.content}>
          <div className={styles.header_txt}>👇 已为您创作口播稿文案</div>
          <div>
            <div className={styles.content_header}>
              <span className={styles.content_header_txt}>口播稿</span>
              <span className={styles.icon_group}>
                <Copy
                  size={14}
                  className="cursor-pointer"
                  onClick={() => {
                    onCopy();
                  }}
                />
              </span>
            </div>
            <div className={styles.content_txt}>
              <AgentMD content={content} />
            </div>
            {/* <span className={`${collapseOpen ? styles.txt : ""}`}>{content}</span> */}
          </div>
        </div>
      </section>
    );
  }
);
export default CopyWriter;
