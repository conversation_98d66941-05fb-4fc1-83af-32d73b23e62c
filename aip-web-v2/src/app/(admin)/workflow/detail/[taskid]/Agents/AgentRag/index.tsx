import styles from "./style.module.css";
import { useWorkflowStore } from "@/store/store";
interface RAGProps {
  content: string;
}

const RAGKNOWLEDGE = ({ content }: RAGProps) => {
  return <span>{content}</span>;
};
interface RAGFooterProps {
  number: string;
}
const RAGKNOWLEDGEFooter = ({ number }: RAGFooterProps) => {
  const updateWorkflowSlider = useWorkflowStore(
    (state) => state.updateWorkflowSlider
  );
  return (
    <div
      className={styles.footerRAG}
      onClick={() => {
        updateWorkflowSlider({
          open: true,
          tabType: "knowledge",
        });
      }}
    >
      {number} 条引用
    </div>
  );
};
export { RAGKNOWLEDGE, RAGK<PERSON>OWLEDGEFooter };
