import { Progress } from "antd";
import styles from "./style.module.css";
import { FC, memo, useEffect, useRef, useState } from "react";
interface AgentStepProps {
  step: number;
  code: string;
}
export const AgentStep_1: FC<AgentStepProps> = memo(({ step, code }) => {
  const [percentValue, setPecrentValue] = useState(0);
  const percentRef = useRef(0);
  useEffect(() => {
    const sleepVal = setInterval(() => {
      percentRef.current = percentRef.current + 2;
      if (percentRef.current + 2 > 95) {
        clearInterval(sleepVal);
        return;
      }
      setPecrentValue(percentRef.current);
    }, 100);
    return () => {
      clearInterval(sleepVal);
    };
  }, []);
  useEffect(() => {
    if (code === "done") {
      setPecrentValue(100);
    }
  }, [code]);
  return (
    <section>
      <div className={styles.header}>
        <span>数据分析专家正在理解选题</span>
      </div>
      <div className={styles.step_container}>
        <div className={styles.step_item}>
          <Progress
            percent={percentValue}
            showInfo={false}
            trailColor="#fff"
            strokeColor="#407BFF"
          />
          <div className={styles.step_label}>理解选题</div>
        </div>
        <div className={styles.step_item}>
          <Progress
            percent={0}
            showInfo={false}
            trailColor="#fff"
            strokeColor="#407BFF"
          />
          <div className={styles.step_label}>
            <label>全网搜索</label>
            <label>进行中</label>
          </div>
        </div>
      </div>
    </section>
  );
});
export const AgentStep_2: FC<AgentStepProps> = memo(({ step, code }) => {
  const [percentValue, setPecrentValue] = useState(0);
  const percentRef = useRef(0);
  useEffect(() => {
    percentRef.current = percentRef.current + 2;
    const sleepVal = setInterval(() => {
      percentRef.current = percentRef.current + 2;
      if (percentRef.current + 2 > 95) {
        clearInterval(sleepVal);
        return;
      }
      setPecrentValue(percentRef.current);
    }, 100);
    return () => {
      clearInterval(sleepVal);
    };
  }, []);
  useEffect(() => {
    if (code === "done") {
      setPecrentValue(100);
    }
  }, [code]);
  return (
    <section>
      <div className={styles.header}>
        <span>数据清洗专家正在全网搜索</span>
      </div>
      <div className={styles.step_container}>
        <div className={styles.step_item}>
          <Progress
            percent={100}
            showInfo={false}
            trailColor="#fff"
            strokeColor="#407BFF"
          />
          <div className={styles.step_label}>理解选题</div>
        </div>
        {/* <div className={styles.step_item}>
          <Progress
            percent={50}
            showInfo={false}
            trailColor="#fff"
            strokeColor="#407BFF"
          />
          <div className={styles.step_label}>分析人设</div>
        </div> */}
        <div className={styles.step_item}>
          <Progress
            percent={percentValue}
            showInfo={false}
            trailColor="#fff"
            strokeColor="#407BFF"
          />
          <div className={styles.step_label}>
            <label>全网搜索</label>
            <label>{code === "done" ? "完成" : "进行中"}</label>
          </div>
        </div>
      </div>
    </section>
  );
});
