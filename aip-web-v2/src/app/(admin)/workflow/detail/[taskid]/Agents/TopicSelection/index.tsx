import { Button } from "antd";
import { FC, useEffect, useState } from "react";
import commonStyles from "../style.module.css";
import styles from "./style.module.css";
import { Forward, ChevronDown, Copy } from "lucide-react";
import { useWorkflowInformation } from "@/hooks/useWorkflowhooks";
import { useUserInfoStore, useWorkflowStore } from "@/store/store";
import { useClipboard } from "@/hooks/useClipboard";

interface SelectItemProps {
  item: { [key: string]: string };
  active: boolean;
  onActive: (val: { [key: string]: string }) => void;
}
const SelectItem: FC<SelectItemProps> = ({ item, active, onActive }) => {
  const [show, setShow] = useState(true);
  const { onCopyToClipboard } = useClipboard("");
  return (
    <div
      className={`${styles.topic_list_item} ${
        active ? styles.topic_list_item_active : ""
      }`}
    >
      <div
        className={`${styles.topic_list_title}`}
        onClick={() => {
          onActive(item);
        }}
      >
        <Forward className={styles.topic_list_icon} />
        <span className={styles.topic_list_txt}>{item.title}</span>
        <span className={styles.copy_icon}>
          <Copy
            size={14}
            className="cursor-pointer"
            onClick={() => {
              onCopyToClipboard(
                `根据[${item.title}。${item.because}]为选题，不需要选题专家，生成一个短视频`
              );
            }}
          />
        </span>
        <span
          className={styles.arrow_icon}
          onClick={(e) => {
            setShow(!show);
            e.stopPropagation();
          }}
        >
          <ChevronDown
            size={14}
            className={`${styles.arrow_icon_svg} ${
              show && styles.becase_item_active
            }`}
          />
        </span>
      </div>
      {show && (
        <div className={`${styles.becase_item} `}>{`推荐理由：${
          item.because || ""
        }`}</div>
      )}
    </div>
  );
};

/**
 * 选题专家 Agent
 */
const TopicSelection = ({
  content,
}: {
  content: { last: boolean; list: any[] };
}) => {
  console.log("list:", content);
  const [activeId, setActiveId] = useState(0);
  const [activeStr, setActiveStr] = useState("");
  const [loading, setLoading] = useState(false);
  // store
  const WResult = useWorkflowStore((state) => state.WResult);
  const chatLoading = useWorkflowStore((state) => state.chatLoading);
  const startParams = useWorkflowStore((state) => state.startParams);
  const currentIpUser = useUserInfoStore((state) => state.currentIpUser);

  const { runContinueEvevt } = useWorkflowInformation();

  const handleClick = () => {
    setLoading(true);
    runContinueEvevt({
      params: {
        pid: currentIpUser.id,
        task_id: localStorage.getItem("workflow_task_id")
          ? Number(localStorage.getItem("workflow_task_id"))
          : undefined,
        query: activeStr,
        is_pass: 1,
      },
      lastWResult: WResult,
      callback: () => {},
    });
  };
  useEffect(() => {
    if (content.list.length > 0 && activeStr == "") {
      setActiveId(0);
      setActiveStr(content.list[0].title);
    }
  }, [content]);
  useEffect(() => {
    localStorage.setItem("continueTopicSelection", `true`);
  }, []);
  return (
    <>
      <div className={commonStyles.RA_header}>
        👇 根据您的IP策划案及当前的热点推荐以下选题：
      </div>
      <div
        className={styles.topic_list}
        style={
          content.last
            ? {
                pointerEvents: "none",
                filter: "grayscale(100%)",
                cursor: "not-allowed",
              }
            : chatLoading || (!content.last && loading)
            ? {
                pointerEvents: "none",
              }
            : {}
        }
      >
        {content.list.map((item, index) => {
          return (
            <SelectItem
              key={`${index}_`}
              item={item}
              active={activeId === index}
              onActive={(val) => {
                setActiveId(index);
                setActiveStr(val.title);
              }}
            />
            // <div
            //   className={`${styles.topic_list_item} ${
            //     activeId === index ? styles.topic_list_item_active : ""
            //   }`}
            //   key={index}
            // >
            //   <div
            //     className={`${styles.topic_list_title}`}
            //     onClick={() => {
            //       setActiveId(index);
            //       setActiveStr(item.title);
            //     }}
            //   >
            //     <Forward className={styles.topic_list_icon} />
            //     <span className={styles.topic_list_txt}>{item.title}</span>
            //     <span className={`${styles.arrowIcon}`}>
            //       <ChevronDown size={14} />
            //     </span>
            //   </div>
            //   <div>{item.because}</div>
            // </div>
          );
        })}
      </div>
      <div className="mt-[10px]">
        <Button
          color="primary"
          variant="solid"
          disabled={activeStr === "" || content.last}
          loading={(chatLoading || loading) && !content.last}
          onClick={() => {
            handleClick();
          }}
        >
          继续
        </Button>
      </div>
    </>
  );
};
export default TopicSelection;
