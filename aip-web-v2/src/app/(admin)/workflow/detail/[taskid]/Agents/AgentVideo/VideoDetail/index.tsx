import { Button, Space, Spin } from "antd";
import { DownloadOutlined } from "@ant-design/icons";
import { downloadAssets } from "@/utils/helper";
import { useWorkflowStore } from "@/store/store";
import RegenerateVideo from "@/components/RegenerateVideo";
import { VideoPauseBtn, VideoPlayBtn } from "@/components/VideoBtn";
import ReactPlayer from "react-player";
import styles from "./style.module.css";
import { FC, useEffect, useRef, useState } from "react";
import IconSvg from "@/assets/svg";
import { AlertTriangle, Loader } from "lucide-react";
const { WorkPauseSvg, WorkPlaySvg, WorkScreenSvg } = IconSvg;
let VIDEO_DETAIL: any = {};

const VideoError = ({ error }: { error: string | null }) => {
  return (
    <div className={styles.error_overlay}>
      {error ? (
        <>
          <AlertTriangle className="w-8 h-8 text-red-500 mr-2" />
          <span className={styles.error_message}>{error}</span>
        </>
      ) : (
        <Loader className="w-8 h-8 animate-spin text-white" />
      )}
    </div>
  );
};
interface VideoPlayProps {
  url: string | undefined;
  clickState: boolean;
}
const VideoPlay: FC<VideoPlayProps> = ({ url, clickState }) => {
  const playerRef = useRef<ReactPlayer>(null);
  const [videoUrl, setVideoUrl] = useState<string>();
  const [isPlaying, setIsPlaying] = useState(false);
  const [Error, setError] = useState<string | null>(null);
  const handleReady = () => {
    setError(null);
  };
  const handleError = () => {
    setError("视频加载失败，请检查网络连接或视频源");
  };
  const FullscreenEve = () => {
    try {
      if (!document.fullscreenElement && playerRef.current) {
        const ele = playerRef.current.getInternalPlayer();
        ele.requestFullscreen();
      } else {
        if (document.exitFullscreen) {
          document.exitFullscreen();
        }
      }
    } catch (err) {
      console.error("全屏切换失败:", err);
    }
  };
  useEffect(() => {
    if (clickState) {
      setVideoUrl(url);
      setIsPlaying(true);
    }
  }, [clickState]);
  return (
    <div className={styles.video_ele}>
      {Error ? (
        <VideoError error={Error} />
      ) : (
        <>
          <ReactPlayer
            ref={playerRef}
            url={videoUrl}
            width={"100%"}
            height={"100%"}
            light={false}
            playing={isPlaying}
            playsinline
            controls={false}
            onReady={handleReady}
            onError={handleError}
          />
          <div className={styles.controls_overlay}>
            {isPlaying ? (
              <div className={styles.controls_btn_bg}>
                <WorkPauseSvg
                  className="size-4 fill-[#fff]"
                  onClick={() => setIsPlaying(false)}
                />
              </div>
            ) : (
              <div className={styles.controls_btn_bg}>
                <WorkPlaySvg
                  className="size-4 fill-[#fff]"
                  onClick={() => setIsPlaying(true)}
                />
              </div>
            )}
            <div className={styles.controls_btn_bg} onClick={FullscreenEve}>
              <WorkScreenSvg className="size-4 fill-[#fff]" />
            </div>
          </div>
        </>
      )}
    </div>
  );
};
const AgentVideoDetail = ({ content }: { content: any }) => {
  const [clickPlay, setClickPlay] = useState(false);
  VIDEO_DETAIL = content;
  return (
    <section className={styles.agent_video_detail}>
      {content?.video_url ? (
        <div className={styles.video_container}>
          {clickPlay ? (
            <VideoPlay url={content?.video_url} clickState={clickPlay} />
          ) : (
            <>
              <img
                className={styles.previewLayer}
                src={content?.preview_url}
                alt=""
              />
              <div
                className={styles.playLayer}
                onClick={() => {
                  setClickPlay(true);
                }}
              >
                <VideoPlayBtn />
              </div>
            </>
          )}
        </div>
      ) : (
        <>
          <img
            src={content?.preview_url}
            alt=""
            className="w-full h-full object-cover"
          />
          <div className={styles.agent_video_loading_layer}>
            <Spin size="large" />
            <span className={styles.agent_video_loading_text}>
              生成视频需要几分钟时间，请耐心等待...
            </span>
          </div>
        </>
      )}
    </section>
  );
};
const AgentVideoLoading = () => {
  return (
    <section className={styles.agent_video_detail}>
      <div className={styles.agent_video_loading_layer}>
        <Spin size="large" />
        <span className={styles.agent_video_loading_text}>
          生成视频需要几分钟时间，请耐心等待...
        </span>
      </div>
    </section>
  );
};
const VideoFooter = () => {
  const chatLoading = useWorkflowStore((state) => state.chatLoading);
  return (
    <div>
      <Space>
        <RegenerateVideo />
        <Button
          disabled={chatLoading}
          icon={<DownloadOutlined />}
          onClick={() => {
            downloadAssets(VIDEO_DETAIL?.video_url);
          }}
          type="primary"
        >
          下载
        </Button>
      </Space>
    </div>
  );
};

export { AgentVideoDetail, VideoFooter, AgentVideoLoading };
