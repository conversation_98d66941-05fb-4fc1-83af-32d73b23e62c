.topic_list {
  display: flex;
  gap: 10px;
  flex-direction: column;
}
.topic_list_item {
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.15);
}
.topic_list_title {
  display: flex;
  padding: 0 16px;
  align-items: center;
  border-radius: 12px 12px 0 0;
  background: rgba(255, 255, 255, 0);
  cursor: pointer;
  transition: background 0.15s ease-in-out;
}
.topic_list_title:hover {
  background: rgba(255, 255, 255, 0.08);
}
.copy_icon {
  visibility: hidden;
  opacity: 0;
  transition: opacity 0.2s ease;
  padding: 5px;
  border: 1px solid transparent;
}
.topic_list_title:hover .copy_icon {
  visibility: visible;
  opacity: 0.8;
  border-radius: 3px;
}
.topic_list_icon {
  color: #407bff;
  transform: rotateX(180deg);
}
.topic_list_txt {
  color: rgba(255, 255, 255, 0.65);
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px; /* 157.143% */
  margin-left: 4px;
  padding: 10px 0;
  flex-grow: 1;
}
.topic_list_item_active {
  border: 1px solid #407bff;
}
.topic_list_item_active .topic_list_txt {
  color: #407bff;
}
.becase_item {
  padding: 0 16px;
  line-height: 18px;
  color: rgba(255, 255, 255, 0.6);
  font-size: 12px;
  cursor: pointer;
  margin-left: auto;
  padding: 10px 18px;
  letter-spacing: 1px;
}
.arrow_icon {
  padding: 5px;
  border: 1px solid transparent;
}
.arrow_icon:hover {
  opacity: 0.8;
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 3px;
}
.arrow_icon_svg {
  transform: rotate(0);
  transition: transform 0.2s ease;
}
.becase_item_active {
  transform: rotate(180deg);
}
