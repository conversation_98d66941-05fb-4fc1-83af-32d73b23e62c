import { useEffect, useState } from "react";
import { Button, Dropdown, message, Modal, Slider, Space, Spin } from "antd";
import { <PERSON><PERSON><PERSON>t, CircleCheck } from "lucide-react";
import {
  DownloadOutlined,
  ExclamationCircleOutlined,
  SyncOutlined,
  VideoCameraAddOutlined,
} from "@ant-design/icons";
import type { MenuProps } from "antd";
import { useUserInfoStore, useWorkflowStore } from "@/store/store";
import { useWorkflowInformation } from "@/hooks/useWorkflowhooks";
import { changeAudioSpeed } from "@/service/fetchData";
import { downloadAssets, getAudioDuration, numberToTime } from "@/utils/helper";
import AudioAnimation from "@/components/AudioAnimation";
import { VideoPauseBtn, VideoPlayBtn } from "@/components/VideoBtn";
import styles from "./style.module.css";
let audioDetail: any = { audio_url: "" };
export const AudioDetail = ({
  content,
}: {
  content: {
    audio_url: string | null;
    uuid: string;
    speed?: number;
    [key: string]: any;
  };
}) => {
  const currentIpUser = useUserInfoStore((state) => state.currentIpUser);
  const { WResult, updateCahtLoading } = useWorkflowStore((state) => state);

  const [messageApi, contextHolder] = message.useMessage();
  const [isPlay, setIsPLay] = useState(false);
  const [audioTime, setAudioTime] = useState<string>();
  const [currentTimes, setCurrentTimes] = useState(0);
  const [currentTotal, setCurrentTotal] = useState(0);
  const [speedLoading, setSpeedLoading] = useState(false);
  const [speedVal, setSpeedVal] = useState(1);
  const { getHistoreyEvent, updateAudioSpeed } = useWorkflowInformation();

  const getSpendAudio = async (speedNumber: number) => {
    setSpeedVal(speedNumber);
    const task_id = localStorage.getItem("workflow_task_id")
      ? parseInt(localStorage.getItem("workflow_task_id") as string)
      : 0;
    if (content?.audio_url) {
      setSpeedLoading(true);
      updateCahtLoading(true);
      changeAudioSpeed({
        input_url: content?.audio_url,
        speed: speedNumber,
        pid: currentIpUser.id,
        task_id: task_id,
        uuid: content.uuid,
      })
        .then((result) => {
          if (result.code === 200) {
            // getHistoreyEvent(task_id);
            updateAudioSpeed({
              uuid: content.uuid,
              speed: speedNumber,
              new_audio_url: result.data?.output_url || "",
              lastWResult: WResult,
            });
          } else {
            messageApi.error("更新历史失败");
          }
        })
        .catch((err) => {
          messageApi.error("变速音频生成失败");
        })
        .finally(() => {
          setSpeedLoading(false);
          updateCahtLoading(false);
        });
    }
  };
  const items: MenuProps["items"] = [
    {
      key: "1",
      label: (
        <div
          className={styles.audio_speed_item}
          onClick={() => {
            getSpendAudio(1);
          }}
        >
          1 X
        </div>
      ),
    },
    {
      key: "1.1",
      label: (
        <div
          className={styles.audio_speed_item}
          onClick={() => {
            getSpendAudio(1.1);
          }}
        >
          1.1 X
        </div>
      ),
    },
    {
      key: "1.2",
      label: (
        <div
          className={styles.audio_speed_item}
          onClick={() => {
            getSpendAudio(1.2);
          }}
        >
          1.2 X
        </div>
      ),
    },
    {
      key: "1.3",
      label: (
        <div
          className={styles.audio_speed_item}
          onClick={() => {
            getSpendAudio(1.3);
          }}
        >
          1.3 X
        </div>
      ),
    },
    {
      key: "1.4",
      label: (
        <div
          className={styles.audio_speed_item}
          onClick={() => {
            getSpendAudio(1.4);
          }}
        >
          1.4 X
        </div>
      ),
    },
    {
      key: "1.5",
      label: (
        <div
          className={styles.audio_speed_item}
          onClick={() => {
            getSpendAudio(1.5);
          }}
        >
          1.5 X
        </div>
      ),
    },
  ];

  useEffect(() => {
    if (content?.speed && content.speed !== 1) {
      setSpeedVal(content.speed as number);
    }
  }, [content?.speed]);

  useEffect(() => {
    if (content?.audio_url) {
      getAudioDuration(
        content?.new_audio_url ?? content?.audio_url,
        (_, currentTime, total) => {
          setCurrentTotal(total || 0);
          setCurrentTimes(0);
          setAudioTime(currentTime);
        }
      );
      audioDetail = content;
    }
  }, [content?.audio_url, content?.new_audio_url]);

  return (
    <>
      {content?.voice_model_name && (
        <div className={styles.audio_modal_type}>
          <CircleCheck color="rgba(255,255,255,0.8)" size={14} />
          <span className={styles.audio_modal_type_txt}>
            已选择 {content?.voice_model_name} 音频模型
          </span>
        </div>
      )}

      <section className={styles.audio_detail_content}>
        {contextHolder}
        <div className={styles.audio_detail_header}>
          <CircleAlert size={16} color="#407bff" />
          <span>请试听音频，并确认当前语速</span>
        </div>
        <div className={styles.audio_detail_line}>
          <AudioAnimation
            setDuration={setCurrentTimes}
            durationNumber={currentTimes}
            audioSrc={content?.new_audio_url ?? content?.audio_url ?? ""}
            isPlaying={isPlay}
          />
          <Dropdown
            trigger={["click"]}
            menu={{ items }}
            placement="bottom"
            arrow={{ pointAtCenter: true }}
          >
            {/* todo 功能之后修改 */}
            <div className={styles.speed_btn}>
              {speedVal === 1 ? "倍速" : `${speedVal} X`}
            </div>
          </Dropdown>
        </div>
        <div className={styles.audio_detail_play}>
          {isPlay ? (
            <VideoPauseBtn
              style={{ backgroundColor: "#407BFF" }}
              onClick={() => {
                setIsPLay(false);
              }}
            />
          ) : (
            <VideoPlayBtn
              style={{ backgroundColor: "#407BFF" }}
              onClick={() => {
                setIsPLay(true);
              }}
            />
          )}
        </div>
        <div className={styles.audio_slider_content}>
          <label>{numberToTime(currentTimes)}</label>
          <Slider
            max={currentTotal}
            min={0}
            value={currentTimes}
            style={{ margin: "0 15px", flexGrow: 1 }}
            tooltip={{
              open: false,
            }}
            onChangeComplete={(value) => {}}
            onChange={(value) => {
              setIsPLay(false);
              setCurrentTimes(value);
            }}
            id="agent_slider"
            styles={{
              track: {
                backgroundColor: "#fff",
              },
              rail: {
                backgroundColor: "rgba(255, 255, 255, 0.50)",
              },
            }}
          />
          <label>{audioTime || "00:00"}</label>
        </div>
        {speedLoading && (
          <div className={styles.layer_loading}>
            <Spin size="large" />
          </div>
        )}
      </section>
    </>
  );
};

export const AudioFooter = () => {
  const [modal, contextHolder] = Modal.useModal();
  const { senderQuery } = useWorkflowInformation();
  const [btnLoading, setBtnLoading] = useState({
    restartAudioBtn: false,
    startVideo: false,
  });
  const [btnDisabled, setBtnDisabled] = useState(false);
  const { chatLoading, WResult, startParams } = useWorkflowStore(
    (state) => state
  );
  const currentIpUser = useUserInfoStore((state) => state.currentIpUser);
  console.log("我是视频组件");
  const createVideo = () => {
    setBtnLoading({ ...btnLoading, startVideo: true });
    setBtnDisabled(true);
    senderQuery({
      question: "视频专家，请帮我将音频直接合成视频",
      params: {
        pid: currentIpUser?.id,
        query: "视频专家，请帮我将音频直接合成视频",
        is_pass: 0,
        task_id: localStorage.getItem("workflow_task_id")
          ? Number(localStorage.getItem("workflow_task_id"))
          : undefined,
      },
      lastWResult: WResult,
      callback: () => {
        setBtnLoading({ ...btnLoading, startVideo: false });
        setBtnDisabled(false);
      },
    });
  };
  const confirm = () => {
    modal.confirm({
      title: "确定重新生成音频",
      icon: <ExclamationCircleOutlined />,
      content: "重新生成的音频将覆盖当前已生成音频，且不可撤销，请谨慎操作！",
      okText: "确认",
      cancelText: "取消",
      onOk: () => {
        setBtnLoading({ ...btnLoading, restartAudioBtn: true });
        setBtnDisabled(true);
        senderQuery({
          question: "生成音频",
          params: {
            pid: currentIpUser?.id,
            query: "生成音频",
            is_pass: 0,
            task_id: localStorage.getItem("workflow_task_id")
              ? Number(localStorage.getItem("workflow_task_id"))
              : undefined,
          },
          lastWResult: WResult,
          callback: () => {
            setBtnLoading({ ...btnLoading, restartAudioBtn: false });
            setBtnDisabled(false);
          },
        });
      },
    });
  };
  return (
    <div>
      <Space>
        <Button
          icon={<SyncOutlined />}
          onClick={confirm}
          type="primary"
          disabled={chatLoading || btnDisabled}
          loading={btnLoading.restartAudioBtn}
        >
          重新生成
        </Button>
        <Button
          icon={<VideoCameraAddOutlined />}
          onClick={createVideo}
          type="primary"
          disabled={chatLoading || btnDisabled}
          loading={btnLoading.startVideo}
        >
          生成视频
        </Button>
        <Button
          disabled={btnDisabled}
          icon={<DownloadOutlined />}
          onClick={() => {
            downloadAssets(
              audioDetail?.new_audio_url ?? audioDetail?.audio_url ?? ""
            );
          }}
          type="primary"
        >
          下载
        </Button>
      </Space>
      {contextHolder}
    </div>
  );
};

export const AgentLoading = () => {
  return (
    <div className={styles.audio_detail_content}>
      <section className={styles.loading_content}>
        <div className={styles.audio_detail_header}>
          <CircleAlert size={16} color="#407bff" />
          <span>请试听音频，并确认当前语速</span>
        </div>
        <div className={styles.audio_detail_line}>
          <AudioAnimation durationNumber={0} audioSrc={""} isPlaying={false} />
          <Dropdown
            trigger={["click"]}
            menu={{ items: [] }}
            placement="bottom"
            arrow={{ pointAtCenter: true }}
          >
            {/* todo 功能修改 */}
            <div className={styles.speed_btn}>倍速</div>
          </Dropdown>
        </div>
        <div className={styles.audio_detail_play}>
          <VideoPlayBtn style={{ backgroundColor: "#407BFF" }} />
        </div>
        <div className={styles.audio_slider_content}>
          <label>{numberToTime(0)}</label>
          <Slider
            max={100}
            min={0}
            style={{ margin: "0 15px", flexGrow: 1 }}
            tooltip={{
              open: false,
            }}
            id="agent_slider"
            styles={{
              track: {
                backgroundColor: "#fff",
              },
              rail: {
                backgroundColor: "rgba(255, 255, 255, 0.50)",
              },
            }}
          />
          <label>{numberToTime(100)}</label>
        </div>
      </section>
      <div className={styles.layer_loading}>
        <Spin size="large" />
      </div>
    </div>
  );
};
