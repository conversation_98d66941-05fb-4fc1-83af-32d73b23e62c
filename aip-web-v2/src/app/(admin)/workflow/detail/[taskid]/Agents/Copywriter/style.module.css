.header {
  padding: 12px 16px 10px 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
}
.title {
  display: flex;
  align-items: center;
  gap: 4px;
}
.title .icon {
  width: 14px;
  height: 14px;
  color: rgba(255, 255, 255, 0.5);
}
.title > label {
  color: rgba(255, 255, 255, 0.5);
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
}
.down_icon {
  width: 14px;
  height: 16px;
  color: rgba(255, 255, 255, 0.5);
}
.header_txt {
  font-size: 16px;
  font-weight: 600;
  line-height: 24px;
  text-align: left;
  color: rgba(255, 255, 255, 0.85);
}
.content_header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 36px;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 8px 8px 0px 0px;
  padding: 8px 16px;
  margin-top: 10px;
}
.content_header_txt {
  color: #fff;
  font-family: Inter;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 20px; /* 142.857% */
}
.icon_group {
  display: flex;
  gap: 20px;
}
.content {
  color: rgba(255, 255, 255, 0.7);
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px; /* 157.143% */
}
.content .txt {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
  height: 44px;
}
.content_txt {
  padding: 15px;
  background-color: rgba(255, 255, 255, 0.05);
}
