import { Typography } from "antd";
import commonStyles from "../style.module.css";
import markdownit from "markdown-it";
import { useWorkflowStore } from "@/store/store";
import { memo, useEffect } from "react";
const md = markdownit({ html: true, breaks: true });
interface Props {
  content: {
    content: string;
    taskname: string;
  };
}
/**
 * 需求分析 Agent
 */
const RequirementAnalysis = memo(({ content }: Props) => {
  const updateTaskName = useWorkflowStore((state) => state.updateTaskName);
  useEffect(() => {
    if (content.taskname) {
      updateTaskName(content.taskname);
    }
  }, [content.taskname]);
  return (
    <>
      <div className={commonStyles.label}>
        <Typography>
          {/* biome-ignore lint/security/noDangerouslySetInnerHtml: used in demo */}
          <div
            className={commonStyles.markdownContent}
            dangerouslySetInnerHTML={{ __html: md.render(content.content) }}
          />
        </Typography>
      </div>
    </>
  );
});

export default RequirementAnalysis;
