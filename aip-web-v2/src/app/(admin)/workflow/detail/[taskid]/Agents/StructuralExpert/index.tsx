import { Typography } from "antd";
import styles from "./style.module.css";
import markdownit from "markdown-it";
import { memo } from "react";
const md = markdownit({ html: true, breaks: true });
const StructuralExpert = memo(({ content }: { content: string }) => {
  // const data = content
  //   .map((item, index) => {
  //     return index + 1 + "." + " " + item;
  //   })
  //   .join(" 👉 ");
  return (
    <Typography>
      {/* biome-ignore lint/security/noDangerouslySetInnerHtml: used in demo */}
      <div dangerouslySetInnerHTML={{ __html: md.render(content) }} />
    </Typography>
  );
  // return <div className={styles.structuralExpert}>{content}</div>;
});
export default StructuralExpert;
