import { FC, useMemo, useState } from "react";
import { InboxOutlined } from "@ant-design/icons";
import { Button, Upload, message } from "antd";
import type { MenuProps, UploadProps } from "antd";
import { GetWorkflowDataProps } from "@/service/fetchData";
import { useUserInfoStore, useWorkflowStore } from "@/store/store";
import { useWorkflowInformation } from "@/hooks/useWorkflowhooks";
import { GetServiceUrl } from "@/service/config";
import styles from "./style.module.css";
import { RcFile } from "antd/es/upload";

const { Dragger } = Upload;
const MaxFileSize = 45;
interface AgentUploadAudioProps {
  content: {
    agent_uuid?: string;
  };
}
const fileTypes = ["audio/mpeg", "audio/wav", "audio/x-m4a"];
const MIMEType = "audio/mpeg, audio/wav, audio/x-m4a";
const AgentUploadAudio: FC<AgentUploadAudioProps> = ({
  content: { agent_uuid },
}) => {
  const [fileList, setFileList] = useState<any[]>([]);
  const [messageApi, contextHolder] = message.useMessage();
  const { runContinueEvevt } = useWorkflowInformation();

  const chatLoading = useWorkflowStore((state) => state.chatLoading);
  const currentIpUsers = useUserInfoStore((state) => state.currentIpUser);
  const WResult = useWorkflowStore((state) => state.WResult);

  /** 主动上传事件 */
  const submitSubmitEve = () => {
    const params: GetWorkflowDataProps = {
      pid: currentIpUsers.id,
      task_id: localStorage.getItem("workflow_task_id")
        ? parseInt(localStorage.getItem("workflow_task_id") as string)
        : 0,
      is_pass: 1,
      agent_uuid: agent_uuid,
    };
    if (fileList.length > 0) {
      const { response } = fileList[0];
      console.log("response:", response);
      params.voice_is_upload = true;
      params.voice_upload_url = response?.data?.file_url;
    } else {
      messageApi.warning("请上传音频文件");
      return;
    }
    runContinueEvevt({ params, lastWResult: WResult, callback: () => {} });
  };

  /** 上传地址 */
  const postUrl = useMemo(() => {
    return (
      GetServiceUrl() +
      "/admin/work/uploadAudio?task_id=" +
      localStorage.getItem("workflow_task_id") +
      "&pid=" +
      currentIpUsers.id
    );
  }, [currentIpUsers.id]);

  /** 文件上传之前拦截 */
  const beforeUploadEve = (file: RcFile) => {
    const isJpgOrPng = fileTypes.includes(file.type);
    if (!isJpgOrPng) {
      messageApi.error("请上传 mp3、wav、m4a 音频格式文件");
      return false;
    }

    const size = file.size / 1024 / 1024;
    if (size > MaxFileSize) {
      messageApi.error(
        `${file.name}文件超过了${MaxFileSize}M，请重新上传此文件。`
      );
      return false;
    }
    setFileList([file]);
    return true;
  };

  /** 文件变化监听 */
  const onChangeEve = (info: any) => {
    const { status } = info.file;
    setFileList([info.file]);
    if (status !== "uploading") {
      console.log(info.file, info.fileList);
    }
    if (status === "done") {
    } else if (status === "error") {
      messageApi.open({
        type: "error",
        content: `${info.file.name} file upload failed.`,
      });
    }
  };

  return (
    <section className="w-full max-w-[430px]">
      <div className={styles.header}>👇 根据您的口播稿推荐以下音频：</div>
      {contextHolder}
      <Dragger
        name="file"
        multiple={false}
        action={postUrl}
        fileList={fileList}
        accept={MIMEType}
        headers={{
          authorization: "Bearer " + localStorage.getItem("AIPGPT_TOKEN"),
        }}
        onChange={onChangeEve}
        beforeUpload={beforeUploadEve}
        onDrop={(e) => {
          const files = Array.from(e.dataTransfer.files);
          const file = files.filter((file) => fileTypes.includes(file.type));
          if (file.length <= 0) {
            messageApi.error("请上传 mp3、wav、m4a 音频格式文件");
            return false;
          }
          console.log("Dropped files", e.dataTransfer.files);
        }}
        disabled={chatLoading}
      >
        <p className="ant-upload-drag-icon">
          <InboxOutlined />
        </p>
        <p className="ant-upload-text">上传音频文件</p>
      </Dragger>
      <div className="mt-4">
        <Button
          disabled={fileList.length <= 0}
          type="primary"
          onClick={() => {
            submitSubmitEve();
          }}
        >
          确定
        </Button>
      </div>
    </section>
  );
};
export default AgentUploadAudio;
