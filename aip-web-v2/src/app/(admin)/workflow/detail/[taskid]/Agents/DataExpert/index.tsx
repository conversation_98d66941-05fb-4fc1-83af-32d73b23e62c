import { CheckCircleOutlined, UserOutlined } from "@ant-design/icons";
import { Avatar, TableProps, Carousel } from "antd";
import { ChevronDown } from "lucide-react";
import Image from "next/image";
import commonStyle from "../style.module.css";
import styles from "./style.module.css";
import { FC, useEffect, useRef, useState } from "react";
import ImageFile from "@/assets/images";
const { Bing, TouTiao, ImageError } = ImageFile;
interface DateCardType {
  title: string;
  iconImg: any;
  type: string;
  index: number;
}
const DATE_ARTICLE_PIC = {
  "5": Bing,
  "4": TouTiao,
};
const DateCard: FC<DateCardType> = ({ title, iconImg, type, index }) => {
  return (
    <div className={styles.date_card}>
      <div className={styles.date_card_title}>
        银行卡借给别人走账避税后果是什么-法律知识|律图法律知识|律图...
      </div>
      <div className={styles.date_card_footer}>
        <div className={styles.date_card_footer_left}>
          <Image src="" alt="头条" width={16} height={16} className="mr-1" />
          <span>头条</span>
        </div>
        <div className={styles.date_card_footer_right}>1</div>
      </div>
    </div>
  );
};
/**
 * 数据专家 Agent
 */
const DataExpert = ({
  content,
  collapse,
}: {
  content: any;
  collapse: boolean;
}) => {
  console.log("数据专家:", content);
  const [collapseOpen, setCollapseOpen] = useState<boolean>(true);
  const DERef = useRef<HTMLDivElement>(null);
  const DEShowRef = useRef<HTMLDivElement>(null);

  const btnBlock = ({ txt }: { txt: string }) => {
    return <div className={styles.DE_btn_block}>{txt}</div>;
  };
  const collapseOpenEvent = () => {
    if (collapseOpen && DERef.current && DEShowRef.current) {
      DERef.current.style.height = "56px";
      setCollapseOpen(false);
    }
    if (!collapseOpen && DERef.current && DEShowRef.current) {
      DERef.current.style.height = DEShowRef.current.offsetHeight + 24 + "px";
      setCollapseOpen(true);
    }
  };
  useEffect(() => {
    if (!collapseOpen && DERef.current && DEShowRef.current) {
      DERef.current.style.height = DEShowRef.current.offsetHeight + 24 + "px";
      setCollapseOpen(true);
    }
  }, []);

  return (
    <section className={styles.DE_content} ref={DERef}>
      {/* 不展示时候内容 */}
      <div
        className={styles.hide_collapsed_content}
        style={
          !collapseOpen
            ? { display: "flex", opacity: "1" }
            : { display: "none", opacity: "0" }
        }
        onClick={collapseOpenEvent}
      >
        <div className={styles.hide_content_left}>
          <span className={styles.hide_content_text}>
            🔍 数据搜索专家已找到 {content?.link_info?.length ?? 0} 个相关链接
          </span>
        </div>
        <div className={styles.hide_content_right}>
          <Avatar.Group>
            <Avatar style={{ backgroundColor: "#fff" }}>
              <Image
                alt=""
                src={DATE_ARTICLE_PIC["4"]}
                width={16}
                height={16}
              />
            </Avatar>
            <Avatar style={{ backgroundColor: "#fff" }}>
              <Image
                alt=""
                src={DATE_ARTICLE_PIC["5"]}
                width={16}
                height={16}
              />
            </Avatar>
          </Avatar.Group>
          <ChevronDown className={styles.hide_content_arrow} />
        </div>
      </div>
      {/* 展示时候内容 */}
      <div
        className={styles.show_collapsed_content}
        ref={DEShowRef}
        style={
          collapseOpen
            ? { visibility: "visible", opacity: "1" }
            : { visibility: "hidden", opacity: "0" }
        }
      >
        <div className={styles.DE_content_title} onClick={collapseOpenEvent}>
          <div className={styles.hide_content_left}>
            <span className={styles.hide_content_text}>
              🔍 数据搜索专家已找到 {content?.link_info?.length ?? 0} 个相关链接
            </span>
          </div>
          <div className={styles.hide_content_right}>
            <ChevronDown className={styles.content_arrow} />
          </div>
        </div>
        {content?.link_info?.length > 0 ? (
          <div className={styles.DE_content_list}>
            {content?.link_info?.map((item: any, index: number) => (
              <div
                className={styles.DE_content_item}
                key={index}
                onClick={() => {
                  window.open(item?.href, "_blank");
                }}
              >
                <Image
                  src={item?.favicon ?? ImageError}
                  alt="data expert"
                  width={100}
                  height={100}
                  className={styles.DE_content_item_img}
                />
                <span className={styles.DE_content_item_title}>
                  {item.title}
                </span>
              </div>
            ))}
          </div>
        ) : (
          <div className={styles.loading_content}>
            <div className={styles.hide_content_left}>
              <span className={styles.hide_content_text}>
                🔍 数据搜索专家正在搜索相关链接...
              </span>
            </div>
            <div className={styles.loading_dots}>
              <span className={styles.dot}></span>
              <span className={styles.dot}></span>
              <span className={styles.dot}></span>
            </div>
          </div>
        )}

        {/* todo */}
        {/* <div className={styles.collapse_content} onClick={collapseOpenEvent}>
          <ChevronDown className={styles.collapse_icon} size={14} />
        </div>
        <div className={styles.block}>
          <div className={styles.DE_header}>
            <CheckCircleOutlined />
            <span>数据专家全网搜索</span>
          </div>
          <div className="mt-[10px] pl-[22px]">
            {btnBlock({ txt: "借卡避税大揭秘：你可能面临的法律严惩！" })}
          </div>
        </div>
        <div className={styles.block}>
          <div className={`${styles.DE_header}`}>
            <CheckCircleOutlined />
            <span>客户编辑分析人设</span>
          </div>
          <div className="mt-[10px] pl-[22px]">
            {btnBlock({ txt: "老邢说生活" })}
          </div>
        </div>
        <div className={styles.block}>
          <div className={`${styles.DE_header}`}>
            <CheckCircleOutlined />
            <span>数据专家已找到 12 个相关链接</span>
          </div>
          <div className="pl-[22px] mt-[10px]">
            <Carousel
              arrows
              infinite={false}
              className="max-w-[390px]"
              dots={false}
            >
              <div>
                <div className={styles.carousel_content}>
                  <DateCard title="" iconImg="" type="" index={1} />
                </div>
              </div>
              <div>
                <div className={styles.carousel_content}>2</div>
              </div>
              <div>
                <div className={styles.carousel_content}>3</div>
              </div>
              <div>
                <div className={styles.carousel_content}>4</div>
              </div>
            </Carousel>
          </div>
        </div> */}
      </div>
    </section>
  );
};
export default DataExpert;
