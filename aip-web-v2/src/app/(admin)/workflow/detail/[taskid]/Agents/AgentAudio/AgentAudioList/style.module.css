.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: rgba(255, 255, 255, 0.85);
  font-size: 16px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
}
.audio_list {
  position: relative;
  height: 73px;
  width: 436px;
  margin-top: 8px;
}
.audio_list::-webkit-scrollbar {
  width: 0;
  height: 0;
}
.audio_list::-webkit-scrollbar-track {
  width: 0;
}
.left_btn,
.right_btn {
  position: absolute;
  top: 0;
  bottom: 0;
  z-index: 100;
  margin: auto;
  display: flex;
  width: 30px;
  height: 30px;
  padding: 3px;
  justify-content: center;
  align-items: center;
  border-radius: 4px;
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(3px);
  cursor: pointer;
}
.left_btn {
  left: 0px;
}
.right_btn {
  right: 0px;
}
.audio_card {
  position: relative;
  width: 138px;
  height: 72px;
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.07);
  padding: 14px 12px;
  flex-shrink: 0;
  flex-grow: 0;
  overflow: hidden;
  cursor: pointer;
}
.audio_card_active {
  border: 1px solid #006fee;
}
.audio_card_active .avtive_tag {
  visibility: visible;
}
.avtive_tag {
  position: absolute;
  top: -1px;
  right: -1px;
  visibility: hidden;
}
.audio_title {
  color: #fff;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 14px; /* 100% */
  width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.audio_operator {
  display: flex;
  width: 100%;
  justify-content: space-between;
  margin-top: 10px;
}
.audio_detail_play {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 60px;
  width: 100%;
}
.video_note {
  display: flex;
  align-items: center;
  gap: 3px;
  margin-top: 10px;
}
.note_text {
  color: rgba(255, 255, 255, 0.5);
  font-family: "PingFang SC";
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: 12px; /* 100% */
}
.audio_swiper_slide {
  display: flex;
  width: 100%;
  height: 100%;
  gap: 10px;
}
.audio_content {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 2px;
  margin-top: 8px;
  padding: 12px 0 0 0;
}
.audio_content_txt {
  color: rgba(255, 255, 255, 0.5);
  text-align: center;
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: 12px; /* 100% */
}
/* ================================================ */
.modal_content {
  padding: 24px;
  box-shadow: 0px -1px 0px 0px rgba(255, 255, 255, 0.07) inset,
    0px 1px 0px 0px rgba(255, 255, 255, 0.07) inset;
  max-height: 520px;
  overflow-y: auto;
}
.modal_footer {
  display: flex;
  justify-content: flex-start;
  padding: 0 24px 12px 24px;
}
