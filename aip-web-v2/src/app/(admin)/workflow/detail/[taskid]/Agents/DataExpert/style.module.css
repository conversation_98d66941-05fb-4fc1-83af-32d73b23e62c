/** 数据专家 */
.DE_content {
  position: relative;
  transition: all 0.22s ease;
  padding: 12px 0;
}
.hide_collapsed_content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 3px 0;
  cursor: pointer;
  transition: all 0.3s ease;
  width: 100%;
}
.show_collapsed_content {
  transition: opacity 0.6s ease;
  padding: 0;
  width: 100%;
}
.collapse_content {
  position: absolute;
  z-index: 1;
  height: 44px;
  top: 0;
  left: 0;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  cursor: pointer;
}
.DE_header {
  display: flex;
  gap: 8px;
}

.collapse_icon {
  position: absolute;
  right: 16px;
  top: 0px;
  bottom: 0;
  margin: auto;
  color: rgba(255, 255, 255, 0.5);
}
.collapse_header {
  width: 100%;
  height: 44px;
  display: flex;
  align-items: center;
  cursor: pointer;
}
/* .collapse_header:hover {
  background-color: rgba(255, 255, 255, 0.08);
} */
.DE_btn_block {
  display: inline-flex;
  padding: 6px 16px;
  align-items: flex-start;
  border-radius: 6px;
  border: 1px solid rgba(255, 255, 255, 0.15);
  background: rgba(255, 255, 255, 0);
}
.tableTitle {
  color: rgba(255, 255, 255, 0.65);
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  max-width: 302px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.tableUrl {
  display: flex;
  align-items: baseline;
  color: rgba(255, 255, 255, 0.65);
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  max-width: 160px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  gap: 8px;
}
.block {
  margin-bottom: 16px;
  padding: 0 16px;
  width: 100%;
}
/** data card */
.carousel_content {
  margin: 0;
  height: 112px;
  color: #fff;
  text-align: center;
  background: transparent;
}
.date_card {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.07);
  padding: 12px 16px;
  width: 50%;
  gap: 12px;
}
.date_card_title {
  color: #fff;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px; /* 157.143% */
  height: 44px;
  text-align: left;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
}
.date_card_footer {
  display: flex;
  width: 100%;
  justify-content: space-between;
  align-items: center;
  height: 22px;
}
.date_card_footer_left {
  display: flex;
  align-items: center;
}
.date_card_footer_left > span {
  color: #fff;
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
}
.date_card_footer_right {
  width: 20px;
  height: 20px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 50%;

  color: rgba(255, 255, 255, 0.7);
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: 12px; /* 100% */
  display: flex;
  justify-content: center;
  align-items: center;
}

/** */
.DE_content_list {
  display: flex;
  flex-direction: column;
  gap: 2px;
  padding: 8px 12px;
  width: 100%;
}
.DE_content_item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 8px 12px;
  margin: 0 -12px;
  cursor: pointer;
  transition: all 0.2s ease;
  width: 100%;
  border-radius: 6px;
  position: relative;
}
.DE_content_item:hover {
  background-color: rgba(255, 255, 255, 0.05);
  transform: translateX(4px);
}
.DE_content_item_img {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  flex-shrink: 0;
  opacity: 0.9;
}
.DE_content_item_title {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  color: rgba(255, 255, 255, 0.65);
  font-size: 14px;
  line-height: 20px;
  text-decoration: none;
  transition: color 0.2s ease;
}
.DE_content_item:hover .DE_content_item_title {
  color: rgba(255, 255, 255, 1);
}
.DE_content_item:hover .DE_content_item_img {
  opacity: 1;
}
.DE_content_title {
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 5px 0;
  width: 100%;
  cursor: pointer;
  font-size: 14px;
  color: rgba(255, 255, 255, 0.9);
}

.hide_content_left {
  display: flex;
  align-items: center;
}

.hide_content_text {
  font-size: 16px;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 600;
}

.hide_content_right {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* Avatar Group 样式调整 */
.hide_content_right :global(.ant-avatar-group) {
  display: flex;
  align-items: center;
}

.hide_content_right :global(.ant-avatar) {
  width: 20px;
  height: 20px;
  margin-left: -4px;
}

/* 箭头样式 */
.content_arrow {
  width: 16px;
  height: 16px;
  color: rgba(255, 255, 255, 0.5);
  transition: transform 0.3s ease;
}

/* 收起状态箭头 */
.hide_content_arrow {
  width: 16px;
  height: 16px;
  color: rgba(255, 255, 255, 0.5);
  transition: transform 0.3s ease;
  transform: rotate(-90deg);
}

/* 展开状态箭头 */
.DE_content_title .content_arrow {
  transform: rotate(0deg);
}

/* 加载状态容器 */
.loading_content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10px 2px;
  width: 100%;
}

/* 加载动画点 */
.loading_dots {
  display: flex;
  align-items: center;
  gap: 4px;
  margin-right: 4px;
}

.dot {
  width: 4px;
  height: 4px;
  background-color: rgba(255, 255, 255, 0.5);
  border-radius: 50%;
  animation: dotPulse 1.4s infinite ease-in-out;
}

.dot:nth-child(2) {
  animation-delay: 0.2s;
}

.dot:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes dotPulse {
  0%, 80%, 100% {
    transform: scale(0.6);
    opacity: 0.4;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

/* 加载状态文字样式 */
.loading_content .hide_content_text {
  color: rgba(255, 255, 255, 0.7);  /* 略微降低对比度 */
}

/* 确保加载状态下的内边距与其他状态一致 */
.loading_content,
.DE_content_list {
  padding: 10px 2px;
}
