import { FC, useEffect, useRef, useState } from "react";
import { Button, Col, Modal, Row, Space, Tooltip } from "antd";

import { useRouter } from "next/navigation";
import {
  ChevronLeft,
  ChevronRight,
  CircleAlert,
  Expand,
  Shrink,
} from "lucide-react";
import { useWorkflowInformation } from "@/hooks/useWorkflowhooks";
import { useUserInfoStore, useWorkflowStore } from "@/store/store";
import VideoSwiper from "@/components/AgentSwiper";
import IconSvg from "@/assets/svg";
import styles from "./style.module.css";
import { PlusOutlined } from "@ant-design/icons";
const { OkSvg, AgentNull } = IconSvg;
interface IAgentVideoListProps {
  content: {
    content: any[];
    agent_uuid: string;
  };
}
export const AgentVideoList: FC<IAgentVideoListProps> = ({
  content: { content, agent_uuid },
}) => {
  const [swiperCurrentIndex, setSwiperCurrentIndex] = useState(0);
  const [open, setOpen] = useState(false);
  const [videoList, setVideoList] = useState<any[][]>([]);
  const [btnLoading, setBtnLoading] = useState(false);
  const [activeVideo, setActiveVideo] = useState<{
    id: number;
    [key: string]: any;
  }>();
  const { runContinueEvevt } = useWorkflowInformation();
  const currentIpUser = useUserInfoStore((state) => state.currentIpUser);
  const WResult = useWorkflowStore((state) => state.WResult);
  const router = useRouter();
  /**
   * 视频卡片
   */
  const VideoCardItem = ({ val, index }: { val: any; index: number }) => {
    return (
      <div
        className={`${styles.video_card} ${
          val.id === activeVideo?.id ? styles.video_card_active : ""
        }`}
        onClick={() => {
          setActiveVideo(val);
          setSwiperCurrentIndex(index);
        }}
      >
        <div className={styles.avtive_tag}>
          <OkSvg />
        </div>
        <div className={styles.play_btn}></div>
        <div className={styles.video_img}>
          {val.pic_url ? (
            <img
              src={val.pic_url}
              alt=""
              className="w-full h-full object-cover"
            />
          ) : (
            <video
              src={val.media_url}
              controls={false}
              className="w-full h-full"
            />
          )}
        </div>
        <div className={styles.video_title}>
          <span>{val.title}</span>
        </div>
      </div>
    );
  };
  useEffect(() => {
    if (content && content.length > 0) {
      let vl: any[][] = [];
      setActiveVideo(content[0]);
      content
        .filter((item) => item.transcode !== 1)
        .forEach((item, index) => {
          let i = Math.floor(index / 4);
          if (vl[i]) {
            vl[i].push(item);
          } else {
            vl[i] = [item];
          }
        });
      setVideoList(vl);
    }
  }, [content]);
  return (
    <>
      <section>
        <div className={styles.header}>
          <span>👇 根据您的口播稿推荐以下视频：</span>
          {videoList.flat(1).length > 0 && (
            <Tooltip placement="top" title={"展开"} arrow={true}>
              <Expand
                color="#8C8C8D"
                size={20}
                className="cursor-pointer"
                onClick={() => {
                  setOpen(true);
                }}
              />
            </Tooltip>
          )}
        </div>
        {
          // 当没有视频时展示
          videoList.flat(1).length <= 0 && (
            <>
              <div className={styles.video_content}>
                <AgentNull />
                <div className={styles.video_content_txt}>无视频模型</div>
              </div>
              <div className="mt-5">
                <Button
                  loading={btnLoading}
                  type="primary"
                  icon={<PlusOutlined style={{ fontSize: 12 }} />}
                  onClick={() => {
                    router.push("/privateModel");
                  }}
                >
                  视频模型
                </Button>
              </div>
            </>
          )
        }
        {
          // 当有视频时展示
          videoList.flat(1).length > 0 && (
            <>
              <div className={styles.video_note}>
                <CircleAlert color="#407BFF" size={16} />
                <span className={styles.note_text}>
                  生成视频将消耗视频生成时长，且不可撤销
                </span>
              </div>
              <div className={styles.video_list}>
                <VideoSwiper
                  currentKey={swiperCurrentIndex}
                  nodes={videoList.map((item, index) => {
                    return (
                      <div
                        className={styles.video_swiper_slide}
                        key={`video_${index}`}
                      >
                        {item.map((val, i) => {
                          return (
                            <VideoCardItem
                              val={val}
                              index={index}
                              key={`${val.id}_${i}`}
                            />
                          );
                        })}
                      </div>
                    );
                  })}
                />
              </div>
              <div className="mt-5">
                <Button
                  loading={btnLoading}
                  type="primary"
                  onClick={() => {
                    setOpen(false);
                    setBtnLoading(true);
                    runContinueEvevt({
                      params: {
                        pid: currentIpUser.id,
                        task_id: localStorage.getItem("workflow_task_id")
                          ? parseInt(
                              localStorage.getItem("workflow_task_id") as string
                            )
                          : 0,
                        video_model_id: activeVideo?.id.toString(),
                        video_url: activeVideo?.media_url,
                        agent_uuid: agent_uuid,
                        video_model_pic_url: activeVideo?.pic_url,
                        is_pass: 1,
                      },
                      lastWResult: WResult,
                      callback: () => {
                        setBtnLoading(false);
                      },
                    });
                  }}
                >
                  生成视频
                </Button>
              </div>
            </>
          )
        }
      </section>
      <Modal
        onCancel={() => {
          setOpen(false);
        }}
        title={
          <div
            className={styles.header}
            style={{ lineHeight: "32px", paddingLeft: 18 }}
          >
            👇 根据您的口播稿推荐以下形象：
          </div>
        }
        open={open}
        closeIcon={<Shrink size={20} />}
        width={860}
        styles={{
          header: {
            background: "transparent",
          },
          content: {
            padding: "12px 0 0 0",
            background: "#222",
            border: "1px solid rgba(255,255,255,0.1)",
          },
          mask: {
            background: "rgba(0,0,0, 0.3)",
            backdropFilter: "blur(10px)" /* 关键属性 */,
          },
        }}
        footer={
          <div className={styles.modal_footer}>
            <Button
              type="primary"
              loading={btnLoading}
              onClick={() => {
                setOpen(false);
                setBtnLoading(true);
                runContinueEvevt({
                  params: {
                    pid: currentIpUser.id,
                    task_id: localStorage.getItem("workflow_task_id")
                      ? parseInt(
                          localStorage.getItem("workflow_task_id") as string
                        )
                      : 0,
                    video_model_id: activeVideo?.id.toString(),
                    video_url: activeVideo?.media_url,
                    agent_uuid: agent_uuid,
                    video_model_pic_url: activeVideo?.pic_url,
                    is_pass: 1,
                  },
                  lastWResult: WResult,
                  callback: () => {
                    setBtnLoading(false);
                  },
                });
              }}
            >
              生成视频
            </Button>
          </div>
        }
      >
        <div className={styles.modal_content}>
          <Row gutter={[16, 16]}>
            {videoList.map((item, index) => {
              return item.map((val, i) => {
                return (
                  <Col span={4} key={`${val.id}_${index}_${i}`}>
                    <VideoCardItem val={val} index={index} />
                  </Col>
                );
              });
            })}
          </Row>
        </div>
      </Modal>
    </>
  );
};
export default AgentVideoList;
