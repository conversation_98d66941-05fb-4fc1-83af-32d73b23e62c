import { FC, useEffect, useRef, useState } from "react";
import { <PERSON><PERSON>, Col, message, Modal, Row, Tooltip } from "antd";
import { useRouter } from "next/navigation";
import { useWorkflowInformation } from "@/hooks/useWorkflowhooks";
import { useUserInfoStore, useWorkflowStore } from "@/store/store";
import {
  ChevronLeft,
  ChevronRight,
  CircleAlert,
  Expand,
  Shrink,
} from "lucide-react";
import { GetWorkflowDataProps } from "@/service/fetchData";
import AudioSwiper from "@/components/AgentSwiper";
import { Swiper, SwiperSlide } from "swiper/react";
import "swiper/css";
import "swiper/css/scrollbar";
import "swiper/css/navigation";
import "swiper/css/pagination";
import AudioAnimation from "@/components/AudioAnimation";
import { VideoPauseBtn, VideoPlayBtn } from "@/components/VideoBtn";
import { getTxtLength } from "@/utils/helper";
import IconSvg from "@/assets/svg";
import styles from "./style.module.css";
import { PlusOutlined } from "@ant-design/icons";

const { OkSvg, AgentNull } = IconSvg;

interface AudioListProps {
  content: {
    content: any[];
    agent_uuid?: string;
  };
}
export const AudioList: FC<AudioListProps> = ({
  content: { content, agent_uuid },
}) => {
  const [swiperCurrentIndex, setSwiperCurrentIndex] = useState(0);
  const [open, setOpen] = useState(false);
  const [messageApi, contextHolder] = message.useMessage();
  const [audioListData, setAudioListData] = useState<any[][]>([]);
  const [activeId, setActiveId] = useState<{ id: any; [key: string]: any }>();
  const [btnLoading, setBtnLoading] = useState(false);
  const currentIpUsers = useUserInfoStore((state) => state.currentIpUser);
  const router = useRouter();
  const { WResult, oralInformation, docLength, languageType } =
    useWorkflowStore((state) => state);
  // const swiperRef = useRef<any>(null);
  // const eleRef = useRef<HTMLDivElement>(null);

  const { runContinueEvevt } = useWorkflowInformation();
  // useEffect(() => {
  //   setAudioListData(content);
  // }, [content]);

  const submitSubmitEve = () => {
    // 判断口播稿内容是否超过1500字
    if (
      getTxtLength({
        txt: oralInformation.think_other_txt,
        type: languageType,
      }) > docLength
    ) {
      messageApi.warning(`口播稿已经超过${docLength}字，请重新编辑`);
      return;
    }
    const params: GetWorkflowDataProps = {
      pid: currentIpUsers.id,
      task_id: localStorage.getItem("workflow_task_id")
        ? parseInt(localStorage.getItem("workflow_task_id") as string)
        : 0,
      is_pass: 1,
      agent_uuid: agent_uuid,
    };
    if (activeId?.id) {
      params.audio_model_id = activeId?.id;
      params.audio_url = activeId?.voice_url;
    } else {
      messageApi.warning("请选择音频文件");
      return;
    }
    setBtnLoading(true);
    runContinueEvevt({
      params,
      lastWResult: WResult,
      callback: () => {
        setBtnLoading(false);
      },
    });
  };
  /** 播放 停止 音频 */
  const playOrStopEve = (s: boolean, i: number, id: number) => {
    const data = JSON.parse(JSON.stringify(audioListData));
    data.forEach((item: any, index: number) => {
      item.forEach((v: any, i: number) => {
        if (v.id === id) {
          data[index][i].isPlay = s;
        } else {
          data[index][i].isPlay = false;
        }
      });
    });
    setAudioListData(data);
  };
  /**  */
  const AudioItems = ({ val, index }: { val: any; index: number }) => {
    return (
      <div
        className={`${styles.audio_card} ${
          activeId?.id === val.id ? styles.audio_card_active : ""
        }`}
        onClick={() => {
          setActiveId(val);
          setSwiperCurrentIndex(index);
        }}
      >
        <div className={styles.avtive_tag}>
          <OkSvg />
        </div>
        <div className={styles.audio_title}>{val.voice_name}</div>
        <div className={styles.audio_operator}>
          <AudioAnimation audioSrc={val.voice_url} isPlaying={val.isPlay} />
          {val.isPlay ? (
            <VideoPauseBtn
              onClick={(e) => {
                playOrStopEve(false, index, val.id);
                e.stopPropagation();
              }}
            />
          ) : (
            <VideoPlayBtn
              onClick={(e) => {
                playOrStopEve(true, index, val.id);
                e.stopPropagation();
              }}
            />
          )}
        </div>
      </div>
    );
  };

  useEffect(() => {
    if (content && content.length > 0) {
      const availableAudio = content.filter((item: any) => item.status !== 2);
      let vl: any[][] = [];
      availableAudio.forEach((item: any, index: number) => {
        let i = Math.floor(index / 3);
        if (vl[i]) {
          vl[i].push(item);
        } else {
          vl[i] = [item];
        }
      });
      setAudioListData(vl);
      if (availableAudio.length > 0) {
        setActiveId(availableAudio[0]);
      }
    }
  }, [content]);
  return (
    <>
      {contextHolder}
      <section className="w-full max-w-[460px]">
        <div className={styles.header}>
          <span>👇 根据您的口播稿推荐以下音频：</span>
          {audioListData.flat(1).length > 0 && (
            <Tooltip placement="top" title={"展开"} arrow={true}>
              <Expand
                color="#8C8C8D"
                size={20}
                className="cursor-pointer"
                onClick={() => {
                  setOpen(true);
                }}
              />
            </Tooltip>
          )}
        </div>

        {audioListData.flat(1).length <= 0 && (
          <>
            <div className={styles.audio_content}>
              <AgentNull />
              <div className={styles.audio_content_txt}>无音频模型</div>
            </div>
            <div className="mt-5">
              <Button
                loading={btnLoading}
                type="primary"
                icon={<PlusOutlined style={{ fontSize: 12 }} />}
                onClick={() => {
                  router.push("/privateModel");
                }}
              >
                音频模型
              </Button>
            </div>
          </>
        )}
        {audioListData.flat(1).length > 0 && (
          <>
            <div className={styles.video_note}>
              <CircleAlert color="#407BFF" size={16} />
              <span className={styles.note_text}>
                生成视频将消耗视频生成时长，且不可撤销
              </span>
            </div>
            <div className={styles.audio_list}>
              <AudioSwiper
                currentKey={swiperCurrentIndex}
                nodes={audioListData.map((item: any, index: number) => (
                  <div
                    className={styles.audio_swiper_slide}
                    key={`audio_${index}`}
                  >
                    {item.map((val: any, i: number) => {
                      return (
                        <AudioItems
                          val={val}
                          index={index}
                          key={`audio_${index}_${i}`}
                        />
                      );
                    })}
                  </div>
                ))}
              />
            </div>
            <div className="mt-5">
              <Button
                loading={btnLoading}
                disabled={activeId?.id === undefined}
                type="primary"
                onClick={() => {
                  submitSubmitEve();
                }}
              >
                确定
              </Button>
            </div>
          </>
        )}
      </section>
      <Modal
        onCancel={() => {
          setOpen(false);
        }}
        title={
          <div
            className={styles.header}
            style={{ lineHeight: "32px", paddingLeft: 18 }}
          >
            👇 根据您的口播稿推荐以下形象：
          </div>
        }
        open={open}
        closeIcon={<Shrink size={20} />}
        width={814}
        styles={{
          header: {
            background: "transparent",
          },
          content: {
            padding: "12px 0 0 0",
            background: "#222",
            border: "1px solid rgba(255,255,255,0.1)",
          },
          mask: {
            background: "rgba(0,0,0, 0.3)",
            backdropFilter: "blur(10px)" /* 关键属性 */,
          },
        }}
        footer={
          <div className={styles.modal_footer}>
            <Button
              type="primary"
              loading={btnLoading}
              onClick={() => {
                setOpen(false);
                submitSubmitEve();
              }}
            >
              确定
            </Button>
          </div>
        }
      >
        <div className={styles.modal_content}>
          <Row gutter={[16, 16]}>
            {audioListData.map((item, index) => {
              return item.map((val, i) => {
                return (
                  <Col span={4.8} key={`${val.id}_${index}_${i}`}>
                    <AudioItems val={val} index={index} />
                  </Col>
                );
              });
            })}
          </Row>
        </div>
      </Modal>
    </>
  );
};
