.header {
  color: rgba(255, 255, 255, 0.85);
  font-size: 16px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
}
.video_list {
  position: relative;
  height: 178px;
  overflow-x: auto;
  width: 513px;
  margin-top: 10px;
}
.video_list::-webkit-scrollbar {
  width: 0;
  height: 0;
}
.video_list::-webkit-scrollbar-track {
  width: 0;
}
.left_btn,
.right_btn {
  position: absolute;
  z-index: 10;
  top: 0;
  bottom: 0;
  margin: auto;
  display: flex;
  width: 30px;
  height: 30px;
  padding: 3px;
  justify-content: center;
  align-items: center;
  border-radius: 4px;
  background: #666;
  backdrop-filter: blur(3px);
  cursor: pointer;
}
.left_btn::after,
.right_btn::after {
  content: "";
  width: 60px;
  position: absolute;
  pointer-events: none;
  height: 178px;
  background-image: linear-gradient(
    to right,
    rgba(0, 0, 0, 0.7),
    rgba(0, 0, 0, 0)
  );
}
.left_btn::after {
  left: -6px;
}
.right_btn::after {
  right: -6px;
  background-image: linear-gradient(
    to left,
    rgba(0, 0, 0, 0.7),
    rgba(0, 0, 0, 0)
  );
}
.left_btn {
  left: 6px;
}
.right_btn {
  right: 6px;
}
.video_content {
  display: flex;
  gap: 8px;
}
.video_card {
  position: relative;
  width: 120px;
  height: 178px;
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.07);
  overflow: hidden;
  cursor: pointer;
}
.avtive_tag {
  position: absolute;
  top: -1px;
  right: -1px;
  visibility: hidden;
}
.video_card_active {
  border: 1px solid #006fee;
}
.video_card_active .avtive_tag {
  visibility: visible;
}
.video_title {
  color: rgba(255, 255, 255, 0.85);
  font-family: "PingFang SC";
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: 28px;
  padding: 0 8px;
}
.video_title > span {
  display: inline-block;
  width: 100%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.video_img {
  width: 100%;
  height: 150px;
}
/* ===================video loading ==================== */
.agent_video_detail {
  position: relative;
  width: 200px;
  height: 266.7px;
  overflow: hidden;
  border-radius: 10px;
  border-start-start-radius: 2px;
}
.agent_video_loading_layer {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  justify-content: center;
  position: absolute;
  z-index: 100;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  padding: 0 30px;
  background-color: rgba(0, 0, 0, 0.5);
}
.agent_video_loading_text {
  color: rgba(255, 255, 255, 0.7);
  letter-spacing: 1px;
  font-weight: bold;
  text-align: center;
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: 18px; /* 150% */
}
/* ===================video detail====================== */

.video_layer {
  display: none;
}
.agent_video_detail:hover .video_layer {
  opacity: 1;
  transition: opacity 0.2s ease;
}
.video_layer_loading {
  background: rgba(255, 255, 255, 0.35);
  z-index: 12;
  border-radius: 8px;
  overflow: hidden;
}
.speed_btn {
  color: rgba(255, 255, 255, 0.85);
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 12px; /* 85.714% */
  cursor: pointer;
}
.video_speed_item {
  color: rgba(255, 255, 255, 0.85);
  text-align: center;
  font-family: Roboto;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 14px; /* 100% */
}
.video_speed_item:hover {
  background: rgba(255, 255, 255, 0.07);
}
.audio_slider_content {
  display: flex;
  align-items: center;
  width: 100%;
}
.video_layer_play {
  position: absolute;
  z-index: 101;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  margin: auto;
  width: 32px;
  height: 32px;
  opacity: 0.8;
}
.video_swiper_slide {
  display: flex;
  width: 100%;
  height: 100%;
  gap: 10px;
}
.video_container {
  position: relative;
  width: 100%;
  height: 100%;
}
