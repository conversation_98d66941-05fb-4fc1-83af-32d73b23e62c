.header {
  color: rgba(255, 255, 255, 0.85);
  font-size: 16px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
}
.audio_list {
  position: relative;
  height: 70px;
  overflow-x: auto;
  width: 100%;
}
.audio_list::-webkit-scrollbar {
  width: 0;
  height: 0;
}
.audio_list::-webkit-scrollbar-track {
  width: 0;
}
.left_btn,
.right_btn {
  position: absolute;
  top: 0;
  bottom: 0;
  z-index: 100;
  margin: auto;
  display: flex;
  width: 30px;
  height: 30px;
  padding: 3px;
  justify-content: center;
  align-items: center;
  border-radius: 4px;
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(3px);
  cursor: pointer;
}
.left_btn {
  left: 0px;
}
.right_btn {
  right: 0px;
}
.audio_content {
  display: flex;
  gap: 8px;
}
.audio_card {
  position: relative;
  width: 134px;
  height: 70px;
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.07);
  padding: 14px 12px;
  flex-shrink: 0;
  flex-grow: 0;
  overflow: hidden;
  cursor: pointer;
}
.avtive_tag {
  position: absolute;
  top: -1px;
  right: -1px;
  visibility: hidden;
}
.audio_card_active {
  border: 1px solid #006fee;
}
.audio_card_active .avtive_tag {
  visibility: visible;
}
.audio_title {
  color: #fff;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 14px; /* 100% */
  width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.audio_operator {
  display: flex;
  width: 100%;
  justify-content: space-between;
  margin-top: 10px;
}
/* ================audio detail=============== */
.audio_detail_header {
  display: flex;
  align-items: center;
  gap: 4px;
}
.audio_detail_header > span {
  color: rgba(255, 255, 255, 0.5);
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: 12px; /* 100% */
}
.audio_detail_line {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 15px;
}
.speed_btn {
  color: rgba(255, 255, 255, 0.85);
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 12px; /* 85.714% */
  cursor: pointer;
}
.audio_speed_item {
  color: rgba(255, 255, 255, 0.85);
  text-align: center;
  font-family: Roboto;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 14px; /* 100% */
}
.audio_speed_item:hover {
  background: rgba(255, 255, 255, 0.07);
}
.audio_detail_play {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 60px;
  width: 100%;
}
.audio_slider_content {
  display: flex;
  align-items: center;
  width: 100%;
}
.audio_slider_content > label {
  display: inline-block;
  width: 30px;
  white-space: nowrap;
}
.loading_content {
  position: relative;
  filter: grayscale(100%);
}
.layer_loading {
  position: absolute;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  z-index: 100;
  backdrop-filter: blur(1px);
}
