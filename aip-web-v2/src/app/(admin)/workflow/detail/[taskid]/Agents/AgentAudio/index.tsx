// import {
//   Button,
//   Dropdown,
//   Slider,
//   Space,
//   Modal,
//   Radio,
//   Upload,
//   message,
//   Spin,
// } from "antd";
// import type { MenuProps } from "antd";
// import { ChevronLeft, ChevronRight, CircleAlert } from "lucide-react";
// import { toast } from "sonner";
// import { downloadAssets } from "@/utils/helper";
// import { Swiper, SwiperSlide, useSwiper } from "swiper/react";
// import "swiper/css";
// import "swiper/css/scrollbar";
// import "swiper/css/navigation";
// import "swiper/css/pagination";
// import { GetWorkflowDataProps, changeAudioSpeed } from "@/service/fetchData";
// import AudioAnimation from "@/components/AudioAnimation";
// import { useWorkflowInformation } from "@/hooks/useWorkflowhooks";
// import { useUserInfoStore } from "@/store/store";
// import IconSvg from "@/assets/svg";
// import styles from "./style.module.css";
// import {
//   DownloadOutlined,
//   ExclamationCircleOutlined,
//   SyncOutlined,
//   VideoCameraAddOutlined,
// } from "@ant-design/icons";
// import { FC, useEffect, useRef, useState } from "react";
// import { getAudioDuration, numberToTime } from "@/utils/helper";
// import { VideoPauseBtn, VideoPlayBtn } from "@/components/VideoBtn";
// import { useWorkflowStore } from "@/store/store";
// const { OkSvg } = IconSvg;
// interface AudioListProps {
//   content: {
//     content: any[];
//     agent_uuid?: string;
//   };
// }
// let audioDetail: any = { audio_url: "" };
// export const AudioList: FC<AudioListProps> = ({
//   content: { content, agent_uuid },
// }) => {
//   const currentIpUsers = useUserInfoStore((state) => state.currentIpUser);
//   const [audioListData, setAudioListData] = useState<any[]>([]);
//   const [activeId, setActiveId] = useState<{ id: any; [key: string]: any }>();
//   const [btnLoading, setBtnLoading] = useState(false);
//   const WResult = useWorkflowStore((state) => state.WResult);
//   const swiperRef = useRef<any>(null);
//   const eleRef = useRef<HTMLDivElement>(null);

//   const { runContinueEvevt } = useWorkflowInformation();
//   useEffect(() => {
//     setAudioListData(content);
//   }, [content]);

//   const submitSubmitEve = () => {
//     const params: GetWorkflowDataProps = {
//       pid: currentIpUsers.id,
//       task_id: localStorage.getItem("workflow_task_id")
//         ? parseInt(localStorage.getItem("workflow_task_id") as string)
//         : 0,
//       is_pass: 1,
//       agent_uuid: agent_uuid,
//     };
//     if (activeId?.id) {
//       params.audio_model_id = activeId?.id;
//       params.audio_url = activeId?.voice_url;
//     } else {
//       toast.warning("请选择音频文件");
//       return;
//     }
//     localStorage.setItem("continueAudio", `true`);
//     setBtnLoading(true);
//     runContinueEvevt({
//       params,
//       lastWResult: WResult,
//       callback: () => {
//         setBtnLoading(false);
//       },
//     });
//   };
//   useEffect(() => {
//     if (content && content.length > 0) {
//       const availableAudios = content.filter((item: any) => item.status !== 2);
//       setAudioListData(
//         availableAudios.map((item: any) => {
//           return {
//             ...item,
//             isPlay: false,
//           };
//         })
//       );
//       if (availableAudios.length > 0) {
//         setActiveId(availableAudios[0]);
//       }
//     }
//   }, [content]);
//   return (
//     <section className="w-full max-w-[460px]">
//       <div className={styles.header}>👇 根据您的口播稿推荐以下音频：</div>
//       <div className="py-2"></div>
//       <div className={styles.audio_list} ref={eleRef}>
//         <div
//           className={styles.left_btn}
//           onClick={() => swiperRef.current?.slidePrev()}
//         >
//           <ChevronLeft size={16} />
//         </div>
//         <div className="px-2">
//           <Swiper
//             slidesPerView={3}
//             centeredSlides={false}
//             slidesPerGroupSkip={1}
//             grabCursor={true}
//             slidesPerGroup={3}
//             onSwiper={(swiper) => {
//               swiperRef.current = swiper;
//             }}
//             className="mySwiper"
//           >
//             {audioListData.map((item: any, index: number) => {
//               return (
//                 <SwiperSlide key={item.id + "-" + index}>
//                   <div
//                     className={`${styles.audio_card} ${
//                       activeId?.id === item.id ? styles.audio_card_active : ""
//                     }`}
//                     onClick={() => {
//                       setActiveId(item);
//                     }}
//                   >
//                     <div className={styles.avtive_tag}>
//                       <OkSvg />
//                     </div>
//                     <div className={styles.audio_title}>{item.voice_name}</div>
//                     <div className={styles.audio_operator}>
//                       <AudioAnimation
//                         audioSrc={item.voice_url}
//                         isPlaying={item.isPlay}
//                       />
//                       {item.isPlay ? (
//                         <VideoPauseBtn
//                           onClick={() => {
//                             setAudioListData(
//                               audioListData.map((val, index) => {
//                                 return {
//                                   ...val,
//                                   isPlay: false,
//                                 };
//                               })
//                             );
//                           }}
//                         />
//                       ) : (
//                         <VideoPlayBtn
//                           onClick={() => {
//                             setAudioListData(
//                               audioListData.map((val, index) => {
//                                 return {
//                                   ...val,
//                                   isPlay: item.id === val.id ? true : false,
//                                 };
//                               })
//                             );
//                           }}
//                         />
//                       )}
//                     </div>
//                   </div>
//                 </SwiperSlide>
//               );
//             })}
//           </Swiper>
//         </div>
//         <div
//           className={styles.right_btn}
//           onClick={() => swiperRef.current?.slideNext()}
//         >
//           <ChevronRight size={16} />
//         </div>
//       </div>
//       <div className="mt-5">
//         <Button
//           loading={btnLoading}
//           disabled={activeId?.id === undefined}
//           type="primary"
//           onClick={() => {
//             submitSubmitEve();
//           }}
//         >
//           确定
//         </Button>
//       </div>
//     </section>
//   );
// };
// export const AudioDetail = ({
//   content,
// }: {
//   content: {
//     audio_url: string | null;
//     uuid: string;
//     speed?: number;
//     [key: string]: any;
//   };
// }) => {
//   const currentIpUser = useUserInfoStore((state) => state.currentIpUser);
//   const WResult = useWorkflowStore((state) => state.WResult);

//   const [messageApi, contextHolder] = message.useMessage();
//   const [isPlay, setIsPLay] = useState(false);
//   const [audioTime, setAudioTime] = useState<string>();
//   const [currentTimes, setCurrentTimes] = useState(0);
//   const [currentTotal, setCurrentTotal] = useState(0);
//   const [speedLoading, setSpeedLoading] = useState(false);
//   const [speedVal, setSpeedVal] = useState(1);
//   const { getHistoreyEvent, updateAudioSpeed } = useWorkflowInformation();
//   const getSpendAudio = async (speedNumber: number) => {
//     setSpeedVal(speedNumber);

//     const task_id = localStorage.getItem("workflow_task_id")
//       ? parseInt(localStorage.getItem("workflow_task_id") as string)
//       : 0;
//     if (content?.audio_url) {
//       setSpeedLoading(true);
//       changeAudioSpeed({
//         input_url: content?.audio_url,
//         speed: speedNumber,
//         pid: currentIpUser.id,
//         task_id: task_id,
//         uuid: content.uuid,
//       })
//         .then((result) => {
//           if (result.code === 200) {
//             // console.log("result:", result);
//             //         uuid,
//             // speed,
//             // new_audio_url,
//             // lastWResult,
//             // updateAudioSpeed({
//             //   uuid: content.uuid,
//             //   speed: result.data.speed,
//             //   new_audio_url: result.data.output_url,
//             //   lastWResult: WResult,
//             // });
//             getHistoreyEvent(task_id);
//             // updateSpeedHistory({
//             //   voice_name: "未命名",
//             //   voice_url: content?.audio_url ?? "",
//             //   new_voice_url: result?.data?.output_url,
//             //   uuid: content.uuid,
//             //   task_id: task_id,
//             //   pid: currentIpUser.id,
//             // }).then((res) => {
//             //   if (res.code === 200) {
//             //     getHistoreyEvent(task_id);
//             //   } else {
//             //     messageApi.error("更新历史失败");
//             //   }
//             // });
//           } else {
//             messageApi.error("更新历史失败");
//           }
//         })
//         .catch((err) => {
//           messageApi.error("变速音频生成失败");
//         })
//         .finally(() => {
//           setSpeedLoading(false);
//         });
//     }
//   };
//   const items: MenuProps["items"] = [
//     {
//       key: "1",
//       label: (
//         <div
//           className={styles.audio_speed_item}
//           onClick={() => {
//             getSpendAudio(1);
//           }}
//         >
//           1 X
//         </div>
//       ),
//     },
//     {
//       key: "1.1",
//       label: (
//         <div
//           className={styles.audio_speed_item}
//           onClick={() => {
//             getSpendAudio(1.1);
//           }}
//         >
//           1.1 X
//         </div>
//       ),
//     },
//     {
//       key: "1.2",
//       label: (
//         <div
//           className={styles.audio_speed_item}
//           onClick={() => {
//             getSpendAudio(1.2);
//           }}
//         >
//           1.2 X
//         </div>
//       ),
//     },
//     {
//       key: "1.3",
//       label: (
//         <div
//           className={styles.audio_speed_item}
//           onClick={() => {
//             getSpendAudio(1.3);
//           }}
//         >
//           1.3 X
//         </div>
//       ),
//     },
//     {
//       key: "1.4",
//       label: (
//         <div
//           className={styles.audio_speed_item}
//           onClick={() => {
//             getSpendAudio(1.4);
//           }}
//         >
//           1.4 X
//         </div>
//       ),
//     },
//     {
//       key: "1.5",
//       label: (
//         <div
//           className={styles.audio_speed_item}
//           onClick={() => {
//             getSpendAudio(1.5);
//           }}
//         >
//           1.5 X
//         </div>
//       ),
//     },
//   ];

//   useEffect(() => {
//     if (content?.speed && content.speed !== 1) {
//       setSpeedVal(content.speed as number);
//     }
//   }, [content?.speed]);

//   useEffect(() => {
//     if (content?.audio_url) {
//       getAudioDuration(
//         content?.new_audio_url ?? content?.audio_url,
//         (_, currentTime, total) => {
//           setCurrentTotal(total || 0);
//           setCurrentTimes(0);
//           setAudioTime(currentTime);
//         }
//       );
//       audioDetail = content;
//     }
//   }, [content?.audio_url, content?.new_audio_url]);

//   return (
//     <section className="relative px-3 py-4">
//       {contextHolder}
//       <div className={styles.audio_detail_header}>
//         <CircleAlert size={16} color="#407bff" />
//         <span>请试听音频，并确认当前语速</span>
//       </div>
//       <div className={styles.audio_detail_line}>
//         <AudioAnimation
//           setDuration={setCurrentTimes}
//           durationNumber={currentTimes}
//           audioSrc={content?.new_audio_url ?? content?.audio_url ?? ""}
//           isPlaying={isPlay}
//         />
//         <Dropdown
//           trigger={["click"]}
//           menu={{ items }}
//           placement="bottom"
//           arrow={{ pointAtCenter: true }}
//         >
//           {/* todo 功能之后修改 */}
//           <div className={styles.speed_btn}>
//             {speedVal === 1 ? "倍速" : `${speedVal} X`}
//           </div>
//         </Dropdown>
//       </div>
//       <div className={styles.audio_detail_play}>
//         {isPlay ? (
//           <VideoPauseBtn
//             style={{ backgroundColor: "#407BFF" }}
//             onClick={() => {
//               setIsPLay(false);
//             }}
//           />
//         ) : (
//           <VideoPlayBtn
//             style={{ backgroundColor: "#407BFF" }}
//             onClick={() => {
//               setIsPLay(true);
//             }}
//           />
//         )}
//       </div>
//       <div className={styles.audio_slider_content}>
//         <label>{numberToTime(currentTimes)}</label>
//         <Slider
//           max={currentTotal}
//           min={0}
//           value={currentTimes}
//           style={{ margin: "0 15px", flexGrow: 1 }}
//           tooltip={{
//             open: false,
//           }}
//           onChangeComplete={(value) => {}}
//           onChange={(value) => {
//             setIsPLay(false);
//             setCurrentTimes(value);
//           }}
//           id="agent_slider"
//           styles={{
//             track: {
//               backgroundColor: "#fff",
//             },
//             rail: {
//               backgroundColor: "rgba(255, 255, 255, 0.50)",
//             },
//           }}
//         />
//         <label>{audioTime || "00:00"}</label>
//       </div>
//       {speedLoading && (
//         <div className={styles.layer_loading}>
//           <Spin size="large" />
//         </div>
//       )}
//     </section>
//   );
// };

// export const AudioFooter = () => {
//   const [modal, contextHolder] = Modal.useModal();
//   const { senderQuery } = useWorkflowInformation();
//   const [btnLoading, setBtnLoading] = useState({
//     restartAudioBtn: false,
//     startVideo: false,
//   });
//   const [btnDisabled, setBtnDisabled] = useState(false);
//   const { chatLoading, WResult, startParams } = useWorkflowStore(
//     (state) => state
//   );
//   const currentIpUser = useUserInfoStore((state) => state.currentIpUser);
//   console.log("我是视频组件");
//   const createVideo = () => {
//     setBtnLoading({ ...btnLoading, startVideo: true });
//     setBtnDisabled(true);
//     senderQuery({
//       question: "视频专家，请帮我将音频直接合成视频",
//       params: {
//         pid: currentIpUser?.id,
//         query: "视频专家，请帮我将音频直接合成视频",
//         is_pass: 0,
//         task_id: localStorage.getItem("workflow_task_id")
//           ? Number(localStorage.getItem("workflow_task_id"))
//           : undefined,
//       },
//       lastWResult: WResult,
//       callback: () => {
//         setBtnLoading({ ...btnLoading, startVideo: false });
//         setBtnDisabled(false);
//       },
//     });
//   };
//   const confirm = () => {
//     modal.confirm({
//       title: "确定重新生成音频",
//       icon: <ExclamationCircleOutlined />,
//       content: "重新生成的音频将覆盖当前已生成音频，且不可撤销，请谨慎操作！",
//       okText: "确认",
//       cancelText: "取消",
//       onOk: () => {
//         setBtnLoading({ ...btnLoading, restartAudioBtn: true });
//         setBtnDisabled(true);
//         senderQuery({
//           question: "生成音频",
//           params: {
//             pid: currentIpUser?.id,
//             query: "生成音频",
//             is_pass: 0,
//             task_id: localStorage.getItem("workflow_task_id")
//               ? Number(localStorage.getItem("workflow_task_id"))
//               : undefined,
//           },
//           lastWResult: WResult,
//           callback: () => {
//             setBtnLoading({ ...btnLoading, restartAudioBtn: false });
//             setBtnDisabled(false);
//           },
//         });
//       },
//     });
//   };
//   return (
//     <div>
//       <Space>
//         <Button
//           icon={<SyncOutlined />}
//           onClick={confirm}
//           type="primary"
//           disabled={chatLoading || btnDisabled}
//           loading={btnLoading.restartAudioBtn}
//         >
//           重新生成
//         </Button>
//         <Button
//           icon={<VideoCameraAddOutlined />}
//           onClick={createVideo}
//           type="primary"
//           disabled={chatLoading || btnDisabled}
//           loading={btnLoading.startVideo}
//         >
//           生成视频
//         </Button>
//         <Button
//           disabled={btnDisabled}
//           icon={<DownloadOutlined />}
//           onClick={() => {
//             downloadAssets(
//               audioDetail?.new_audio_url ?? audioDetail?.audio_url ?? ""
//             );
//           }}
//           type="primary"
//         >
//           下载
//         </Button>
//       </Space>
//       {contextHolder}
//     </div>
//   );
// };

// export const AgentLoading = () => {
//   return (
//     <div className="relative">
//       <section className={styles.loading_content}>
//         <div className={styles.audio_detail_header}>
//           <CircleAlert size={16} color="#407bff" />
//           <span>请试听音频，并确认当前语速</span>
//         </div>
//         <div className={styles.audio_detail_line}>
//           <AudioAnimation durationNumber={0} audioSrc={""} isPlaying={false} />
//           <Dropdown
//             trigger={["click"]}
//             menu={{ items: [] }}
//             placement="bottom"
//             arrow={{ pointAtCenter: true }}
//           >
//             {/* todo 功能修改 */}
//             <div className={styles.speed_btn}>倍速</div>
//           </Dropdown>
//         </div>
//         <div className={styles.audio_detail_play}>
//           <VideoPlayBtn style={{ backgroundColor: "#407BFF" }} />
//         </div>
//         <div className={styles.audio_slider_content}>
//           <label>{numberToTime(0)}</label>
//           <Slider
//             max={100}
//             min={0}
//             style={{ margin: "0 15px", flexGrow: 1 }}
//             tooltip={{
//               open: false,
//             }}
//             id="agent_slider"
//             styles={{
//               track: {
//                 backgroundColor: "#fff",
//               },
//               rail: {
//                 backgroundColor: "rgba(255, 255, 255, 0.50)",
//               },
//             }}
//           />
//           <label>{numberToTime(100)}</label>
//         </div>
//       </section>
//       <div className={styles.layer_loading}>
//         <Spin size="large" />
//       </div>
//     </div>
//   );
// };
import { AudioDetail, AudioFooter, AgentLoading } from "./AgentAudioDetail";
import { AudioList } from "./AgentAudioList";
export { AudioDetail, AudioFooter, AgentLoading, AudioList };
