import { CheckCircleOutlined, UserOutlined } from "@ant-design/icons";
import { ChevronDown, ChevronUp, <PERSON><PERSON><PERSON>, Co<PERSON> } from "lucide-react";
import { useClipboard } from "@/hooks/useClipboard";
import { AgentMD } from "../AgentMD";
import styles from "./style.module.css";
import { FC, useEffect, useRef, useState } from "react";
import { useUserInfoStore, useWorkflowStore } from "@/store/store";
import { Button, Space } from "antd";
interface AgentEmbellishProps {
  content: {
    uuid?: string;
    content: string;
    work_id?: number;
    think_txt: string;
    think_other_txt: string;
    code?: string;
  };
}
const AgentEmbellish: FC<AgentEmbellishProps> = ({
  content: { content, work_id, uuid, think_txt, think_other_txt, code },
}) => {
  const [collapseOpen, setCollapseOpen] = useState(false);
  const { updateOralText } = useWorkflowStore((state) => state);
  const updateWorkflowSlider = useWorkflowStore(
    (state) => state.updateWorkflowSlider
  );
  const { onCopy } = useClipboard(think_other_txt);
  return (
    <section>
      {/* <div
        className={styles.header}
        onClick={() => setCollapseOpen(!collapseOpen)}
      >
        <div className={styles.title}>
          <CheckCircleOutlined className={styles.icon} />
          {<label>{title}</label>}
        </div>
        {collapseOpen ? (
          <ChevronDown className={styles.down_icon} />
        ) : (
          <ChevronUp className={styles.down_icon} />
        )}
      </div> */}

      <div className={styles.content}>
        {think_txt?.length > 0 && (
          <div className={styles.think_txt}>
            <AgentMD content={think_txt || ""} />
            {}
          </div>
        )}
        {(think_other_txt?.length > 0 || code === "done") && (
          <>
            <div className={styles.header_txt}>
              👇 下方是为您润色完成的口播稿文案
            </div>
            <div>
              <div className={styles.content_header}>
                <span className={styles.content_header_txt}>口播稿</span>
                <span className={styles.icon_group}>
                  <Pencil
                    size={14}
                    className="cursor-pointer"
                    onClick={() => {
                      updateWorkflowSlider({
                        tabType: "oral",
                        open: true,
                      });
                      updateOralText({
                        label: content,
                        think_txt: think_txt || "",
                        think_other_txt: think_other_txt || "",
                        id: work_id as number,
                        uuid: uuid as string,
                      });
                    }}
                  />
                  <Copy
                    size={14}
                    className="cursor-pointer"
                    onClick={() => {
                      onCopy();
                    }}
                  />
                </span>
              </div>
              <div className={styles.content_txt}>
                <AgentMD content={think_other_txt || ""} />
              </div>
              {/* <span className={`${collapseOpen ? styles.txt : ""}`}>{content}</span> */}
            </div>
          </>
        )}
      </div>
    </section>
  );
};
// export const AgentRefineFooter = () => {
//   const { chatLoading, startParams, WResult } = useWorkflowStore(
//     (state) => state
//   );
//   const { senderQuery } = useWorkflowInformation();
//   const currentIpUser = useUserInfoStore((state) => state.currentIpUser);
//   const synthesisAudio = () => {
//     senderQuery({
//       question: "音频专家，请帮我将口播稿生成音频",
//       params: {
//         pid: currentIpUser?.id,
//         query: "音频专家，请帮我将口播稿生成音频",
//         is_pass: 0,
//         task_id: localStorage.getItem("workflow_task_id")
//           ? Number(localStorage.getItem("workflow_task_id"))
//           : undefined,
//       },
//       lastWResult: WResult,
//     });
//   };
//   return (
//     <div>
//       <Space>
//         <Button
//           disabled={chatLoading}
//           type="primary"
//           onClick={() => synthesisAudio}
//         >
//           合成音频
//         </Button>
//       </Space>
//     </div>
//   );
// };
export default AgentEmbellish;
