.audio_detail_content {
  position: relative;
  padding: 13px 16px;
  background-color: rgba(255, 255, 255, 0.12);
  border-radius: 2px 10px 10px 10px;
  width: 260px;
}
.audio_modal_type {
  display: flex;
  align-items: center;
  gap: 4px;
  height: 22px;
  margin-bottom: 10px;
}
.audio_modal_type_txt {
  color: rgba(255, 255, 255, 0.7);
  font-family: "PingFang SC";
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px; /* 157.143% */
}
.audio_speed_item {
  color: rgba(255, 255, 255, 0.85);
  text-align: center;
  font-family: Roboto;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 14px; /* 100% */
}
.audio_speed_item:hover {
  background: rgba(255, 255, 255, 0.07);
}
.audio_detail_header {
  display: flex;
  align-items: center;
  gap: 4px;
}
.audio_detail_header > span {
  color: rgba(255, 255, 255, 0.5);
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: 12px; /* 100% */
}
.audio_detail_line {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 15px;
}
.speed_btn {
  color: rgba(255, 255, 255, 0.85);
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 12px; /* 85.714% */
  cursor: pointer;
}
.audio_slider_content {
  display: flex;
  align-items: center;
  width: 100%;
}
.audio_slider_content > label {
  display: inline-block;
  width: 30px;
  white-space: nowrap;
}
.loading_content {
  position: relative;
  filter: grayscale(100%);
}
.layer_loading {
  position: absolute;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  z-index: 100;
  backdrop-filter: blur(1px);
}
.audio_detail_play {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 60px;
  width: 100%;
}
