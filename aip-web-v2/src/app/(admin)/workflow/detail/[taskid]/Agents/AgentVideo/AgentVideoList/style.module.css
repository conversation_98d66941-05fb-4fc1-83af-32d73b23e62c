.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: rgba(255, 255, 255, 0.85);
  font-size: 16px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
}
.video_list {
  position: relative;
  height: 178px;
  /* overflow-x: auto; */
  width: 513px;
  margin-top: 4px;
}
.video_list::-webkit-scrollbar {
  width: 0;
  height: 0;
}
.video_list::-webkit-scrollbar-track {
  width: 0;
}

.video_swiper_slide {
  display: flex;
  width: 100%;
  height: 100%;
  gap: 10px;
}
.video_card {
  position: relative;
  width: 120px;
  height: 178px;
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.07);
  overflow: hidden;
  cursor: pointer;
}
.avtive_tag {
  position: absolute;
  top: -1px;
  right: -1px;
  visibility: hidden;
}
.video_card_active {
  border: 1px solid #006fee;
}
.video_card_active .avtive_tag {
  visibility: visible;
}
.video_img {
  width: 100%;
  height: 150px;
}
.video_title {
  color: rgba(255, 255, 255, 0.85);
  font-family: "PingFang SC";
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: 28px;
  padding: 0 8px;
}
.video_title > span {
  display: inline-block;
  width: 100%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.video_note {
  display: flex;
  align-items: center;
  gap: 3px;
  margin-top: 10px;
}
.note_text {
  color: rgba(255, 255, 255, 0.5);
  font-family: "PingFang SC";
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: 12px; /* 100% */
}
.video_content {
  display: flex;
  width: 513px;
  height: 178px;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 8px;
}
.video_content_txt {
  color: rgba(255, 255, 255, 0.5);
  text-align: center;
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: 12px; /* 100% */
}
/* ================================================ */
.modal_content {
  padding: 24px;
  box-shadow: 0px -1px 0px 0px rgba(255, 255, 255, 0.07) inset,
    0px 1px 0px 0px rgba(255, 255, 255, 0.07) inset;
  max-height: 520px;
  overflow-y: auto;
}
.modal_footer {
  display: flex;
  justify-content: flex-start;
  padding: 0 24px 12px 24px;
}
