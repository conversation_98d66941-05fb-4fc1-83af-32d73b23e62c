import { Dispatch, SetStateAction, useRef, useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>H<PERSON><PERSON>,
  <PERSON>dal<PERSON>ody,
  <PERSON>dal<PERSON>ooter,
  RadioGroup,
  Radio,
  Textarea,
} from "@nextui-org/react";
import { Layers } from "@/components/SpinLoading/SpinLoading";
import type { UploadFile, UploadProps } from "antd";
import { RcFile } from "antd/es/upload";
import { ConfigProvider, message, theme, Upload } from "antd";
import { linkPersonalData } from "@/service/fetchData";
import { GetServiceUrl } from "@/service/config";
import { useUserInfoStore } from "@/store/store";
import styles from "./style.module.css";
import { Upload as LucideUpload, Link } from "lucide-react";

interface TProps {
  isOpen: boolean;
  setIsOpen: Dispatch<SetStateAction<boolean>>;
  callback: () => void;
  data: {
    id: number;
    knowledge_type: number;
  };
}

const UploadType = [
  "md",
  "txt",
  "pdf",
  "docx",
  "doc",
  "xlsx",
  "xls",
  "csv",
  "pptx",
];

const accept = ".doc,.docx,.xlsx,.xls,.pdf,text/plan,.cvs,.md,.txt,.pptx";

const MaxFileSize = 100;

const AddContentDialog = ({ isOpen, setIsOpen, data, callback }: TProps) => {
  // const [radioValue, setRadioValue] = useState("file");
  const [fileListArr, setFileListArr] = useState<any[]>([]);
  const [isDisabled, setIsDisabled] = useState<boolean>(false);
  const [link, setLink] = useState<string>("");
  const [messageApi, contextHolder] = message.useMessage();
  const { Dragger } = Upload;
  // const { currentIp } = userStore();
  const currentIpUser = useUserInfoStore((state) => state.currentIpUser);
  const uploadRef = useRef({
    index: 0,
  });
  const ParamsRef = useRef({
    voice_name: "",
    status: 1,
    voice_url: "",
    pid: 0,
  });
  const BASE_URL = GetServiceUrl();

  /** 上传之前事件 */
  const beforeUploadEve = (file: RcFile, fileList: UploadFile[]) => {
    const fileType = file.name.split(".").pop()?.toLocaleLowerCase() ?? "";
    if (!UploadType.includes(fileType)) {
      messageApi.error(`${file.name} 的文件格式不支持`);
      // toast.message("文件大小提示", {
      //   description: `${file.name} 的文件格式不支持`,
      // });
      return false;
    }

    const size = file.size / 1024 / 1024;
    if (size > MaxFileSize) {
      messageApi.error(
        `${file.name}文件超过了${MaxFileSize}M，请重新上传此文件。`
      );
      // toast.message("文件大小提示", {
      //   description: `${file.name}文件超过了${MaxFileSize}M，请重新上传此文件。`,
      // });
      return false;
    }
    const p = [...fileListArr, ...fileList];
    setFileListArr(p);
    return false;
  };

  /** 上传配置信息 */
  const props: UploadProps = {
    name: "files",
    multiple: true,
    accept: accept,
    fileList: fileListArr,
    beforeUpload: beforeUploadEve,
    onRemove: (file) => {
      setFileListArr(fileListArr.filter((item) => item.uid !== file.uid));
    },
    onDrop(e) {
      console.log("Dropped files", e.dataTransfer.files);
    },
  };

  /** 上传文件 */
  const handleUploadEve = () => {
    if (fileListArr.length === 0) {
      messageApi.warning("请选择文件");
      return;
    }
    /** 当前的接口状态 */
    setIsDisabled(true);
    fileListArr.forEach((file) => {
      uploadEve(file);
    });
  };

  const uploadEve = (file: any) => {
    const formData = new FormData();
    formData.append("files", file);
    formData.append("pid", currentIpUser.id.toString());
    formData.append("emb_type", "1");

    fetch(`${BASE_URL}/knowledgefile/upload`, {
      method: "POST",
      body: formData,
    })
      .then((res) => res.json())
      .then((result) => {
        console.log(formData);
        if (result.code === 200) {
          messageApi.success(`${result.data[0].file_url} 上传成功!`);
          callback();
        } else {
          messageApi.error(`${file.fileName} 上传失败`);
        }
      })
      .catch((e) => {
        messageApi.error(`${file.fileName} 上传失败`);
      })
      .finally(() => {
        uploadRef.current.index++;
        if (uploadRef.current.index >= fileListArr.length) {
          setFileListArr([]);
          uploadRef.current.index = 0;
          setIsOpen(false);
          setIsDisabled(false);
        }
      });
  };

  // const radioChangeEvent = (e: any) => {
  //   setRadioValue(e);
  // };

  // const okEvent = () => {
  //   if (radioValue === "link") {
  //     const status =
  //       /(https\:\/\/www\.zhihu\.com\/question\/)\d+(\/answer\/)\d+/;

  //     if (status.test(link)) {
  //       const param = {
  //         zhihu_url: link,
  //         num: 3,
  //         pid: data.id,
  //         knowledge_id: data.knowledge_type,
  //       };
  //       linkPersonalData(param).then((res: any) => {
  //         if (res.code === 200) {
  //           setLink("");
  //           setIsOpen(false);
  //         }
  //       });
  //     } else {
  //       messageApi.error("请输入正确的知乎链接");
  //     }
  //   }
  // };

  return (
    <>
      {contextHolder}
      <Modal
        isOpen={isOpen}
        onOpenChange={() => {
          setIsOpen(!isOpen);
          setFileListArr([]);
        }}
      >
        {isDisabled && <Layers loadingTxt="文件上传中..."></Layers>}
        <ModalContent className="sm:max-w-[600px]">
          <ModalHeader className="flex flex-col gap-1">
            <h2 className="text-lg font-medium">添加训练内容</h2>
            {/* <p className="text-sm text-gray-400">选择添加方式并上传您的内容</p> */}
          </ModalHeader>

          <ModalBody>
            {/* <RadioGroup
              orientation="horizontal"
              value={radioValue}
              onValueChange={radioChangeEvent}
              className="gap-6"
            >
              <Radio value="file" className="group">
                <div className="flex items-center gap-2">
                  <LucideUpload className="w-4 h-4 group-hover:text-primary" />
                  <span>文件上传</span>
                </div>
              </Radio>
              <Radio value="link" className="group">
                <div className="flex items-center gap-2">
                  <Link className="w-4 h-4 group-hover:text-primary" />
                  <span>链接导入</span>
                </div>
              </Radio>
            </RadioGroup> */}

            <div className="mt-4">
              {/* {radioValue === "file" ? ( */}
              <Dragger {...props} className="bg-default-100 rounded-lg">
                <div className="p-8 text-center">
                  <LucideUpload className="w-12 h-12 mx-auto text-gray-400" />
                  <p className="mt-4 text-sm">点击上传或拖拽文件到此区域</p>
                  <p className="mt-2 text-xs text-gray-400">
                    支持格式: doc, docx, pdf, txt 等
                  </p>
                </div>
              </Dragger>
              {/* ) : (
                <Textarea
                  placeholder="请输入知乎问答链接..."
                  value={link}
                  onValueChange={setLink}
                  className="min-h-[120px]"
                />
              )} */}
            </div>
          </ModalBody>

          <ModalFooter>
            <Button variant="bordered" onPress={() => setIsOpen(false)}>
              取消
            </Button>
            <Button
              color="primary"
              // onPress={radioValue === "link" ? okEvent : handleUploadEve}
              onPress={handleUploadEve}
              isLoading={isDisabled}
            >
              确认添加
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </>
  );
};

export default AddContentDialog;
