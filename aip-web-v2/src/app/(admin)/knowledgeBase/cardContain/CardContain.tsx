import { useEffect, useRef, useState } from "react";
import Image from "next/image";
import { Input, Button } from "@nextui-org/react";
import { Pagination, ConfigProvider, theme, message } from "antd";
import SpinLoading from "@/components/SpinLoading/SpinLoading";
import AddContentDialog from "../AddContentDialog";
import styles from "./style.module.css";
import { getPersonalData, deletePersonalData } from "@/service/fetchData";
import { downloadByXML } from "@/utils/helper";
import { debounce } from "lodash-es";
import zhCN from "antd/locale/zh_CN";
import { CloudDownload, Trash2, Plus } from "lucide-react";
import { useUserInfoStore } from "@/store/store";
import ImageFile from "@/assets/images";
import WXDialog from "../WxDialog";
const { Null } = ImageFile;
interface CardContainProps {
  id?: string | number;
  knowledge_type?: number | string;
}

export default function CardContain({ id, knowledge_type }: CardContainProps) {
  // const { isOpen, onOpen, onOpenChange } = useDisclosure();
  const [isWxOpen, setIsWxOpen] = useState(false);
  const [isContentOpen, setIsContentOpen] = useState(false);
  const [list, setList] = useState<any[]>([]);
  const [status, setStatus] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [total, setTotal] = useState(0);
  const [currentPageSize, setCurrentPageSize] = useState(10);
  const currentIpUser = useUserInfoStore((state) => state.currentIpUser);
  const inputRef = useRef<HTMLInputElement>(null);
  const paramsRef = useRef({});
  const timeFormat = (val: string) => {
    const f = val?.split("T");
    return f;
  };
  /** 获取知识库信息 */
  const getPersonalDatalist = () => {
    setStatus(true);
    getPersonalData(paramsRef.current)
      .then((result) => {
        setList([]);
        if (result.data?.content) {
          /** 过滤掉已经生成的视频 */
          const filteList =
            result.data?.content?.filter((item: { emb_type: number }) =>
              [1, 2, 3].includes(item.emb_type)
            ) || [];
          setList(filteList);
          setCurrentPage(result.data.pager.page_num);
          setTotal(result.data.pager.total_record);
        }
      })
      .finally(() => {
        setStatus(false);
      });
  };
  const searchEvent = function (val: any) {
    const searchFilename = val.target.value;
    if (id !== undefined) {
      paramsRef.current = {
        t_ip_knowledge_id: id,
        file_name: searchFilename,
        page_size: 10,
        page_num: 1,
      };
      getPersonalDatalist();
    }
  };
  const onShowSizeChange = (page: number, pageSize: number) => {
    paramsRef.current = {
      ...paramsRef.current,
      page_size: pageSize,
      page_num: page,
    };
    setCurrentPageSize(pageSize);
    getPersonalDatalist();
  };

  /** [RAG]删除嵌入数据*/
  const deleteEvent = (fileId: number) => {
    deletePersonalData({ pid: currentIpUser.id, file_id: fileId }).then(
      (result) => {
        if (result.code === 200 && result.data) {
          getPersonalDatalist();
        }
      }
    );
  };
  /** 切换时候重置搜索内容 */
  useEffect(() => {
    if (id) {
      // 如果前端模拟我的文件，则不请求我的数据列表
      if (id === "myFile") return;
      if (inputRef.current) {
        inputRef.current.value = "";
      }
      paramsRef.current = {
        t_ip_knowledge_id: id,
      };
      getPersonalDatalist();
    }
  }, [id]);

  const [isAddContentOpen, setIsAddContentOpen] = useState(false);

  const handleAddContent = () => {
    if (!id || !knowledge_type) {
      message.warning("请先选择知识库");
      return;
    }
    setIsAddContentOpen(true);
  };

  return (
    <div className={styles.container}>
      {list.length > 0 && (
        <div className={styles.cardHead}>
          <div>
            <Input
              variant="bordered"
              ref={inputRef}
              placeholder="搜索内容"
              className={styles.searchInput}
              onChange={debounce(searchEvent, 1000)}
            />
          </div>
          <div>
            {/* <Button variant="outline" className="mr-6">绑定</Button> */}
            <Button onPress={handleAddContent}>
              <Plus className="mr-3 size-[18px]" />
              训练内容
            </Button>
          </div>
        </div>
      )}

      <SpinLoading loading={status}>
        <div className={styles.cardMain}>
          {list.length > 0 &&
            list.map((item, index) => {
              const time = timeFormat(item.updated_at);
              return (
                <div
                  className={`${styles.cardItem} group hover:bg-default-100 transition-all duration-200 rounded-lg p-4`}
                  key={item.id}
                >
                  {item.file_review_pic && (
                    <div
                      className={`${styles.review} overflow-hidden rounded-md`}
                    >
                      <Image
                        src={item.file_review_pic}
                        alt={`${item.file_name}预览图`}
                        width={90}
                        height={128}
                        className="object-cover transition-transform group-hover:scale-105"
                      />
                    </div>
                  )}

                  <div className="flex-1 min-w-0">
                    <div className={styles.cardTitle}>
                      <h3 className="text-lg font-medium">{item.file_name}</h3>
                    </div>

                    <p className="mt-2 text-sm text-gray-400 line-clamp-2">
                      {item.first_page_content}
                    </p>

                    <div className="mt-4 flex items-center justify-between">
                      <div className="flex items-center gap-4 text-sm text-gray-500">
                        <span>
                          {item.file_educate_status === 0
                            ? "训练中"
                            : `${item.file_word_num}字`}
                        </span>
                        <span>{item.source}</span>
                        <span>
                          {time[0]} {time[1]}
                        </span>
                      </div>

                      <div className="flex items-center gap-3 opacity-0 group-hover:opacity-100 transition-opacity">
                        <Button
                          isIconOnly
                          variant="light"
                          size="sm"
                          onClick={() =>
                            downloadByXML(item.file_url, item.file_name)
                          }
                        >
                          <CloudDownload className="w-4 h-4" />
                        </Button>
                        <Button
                          isIconOnly
                          variant="light"
                          size="sm"
                          onClick={() => deleteEvent(item.id)}
                        >
                          <Trash2 className="w-4 h-4" />
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>
              );
            })}
          {list.length <= 0 && (
            <div className={styles.nullContent}>
              <Image src={Null} alt="暂无内容" className={styles.pic} />
              {/* <img className={styles.pic} src="/app/hot_icon/null.png" /> */}
              <span className={styles.nullTxt}>暂无内容</span>
              <div className={styles.nullBtnList}>
                {/* <Button variant="outline" className={styles.btns} onClick={()=>setIsWxOpen(true)}>
                                    <span className="mr-2"><WXSvg/> </span>通过微信添加
                                </Button> */}
                {knowledge_type === 1 && (
                  <Button className={styles.btns} onPress={handleAddContent}>
                    <span className="mr-2">
                      <Plus />
                    </span>{" "}
                    在线添加
                  </Button>
                )}
              </div>
            </div>
          )}
        </div>
        {list.length > 0 && (
          <ConfigProvider
            theme={{ algorithm: theme.darkAlgorithm }}
            locale={zhCN}
          >
            <div className="pt-3" id="antd-pagination">
              <Pagination
                showSizeChanger
                pageSize={currentPageSize}
                onChange={onShowSizeChange}
                defaultCurrent={currentPage}
                align="end"
                total={total}
              />
            </div>
          </ConfigProvider>
        )}
      </SpinLoading>
      <AddContentDialog
        isOpen={isAddContentOpen}
        setIsOpen={setIsAddContentOpen}
        callback={() => {
          // 新开账号，没有初始化我的文件，前端mock没有一个准确的id
          // 上传成功之后，判断是前端模拟的情况下，重新刷新页面获取最新的 我的文件的 id
          if (id === "myFile") {
            location.reload();
          } else {
            getPersonalDatalist();
          }
        }}
        data={{
          id: Number(id),
          knowledge_type: (knowledge_type as number) || 1,
        }}
      />
      <WXDialog isOpen={isWxOpen} setIsOpen={setIsWxOpen} />
      {/* <Modal isOpen={isOpen} onOpenChange={onOpenChange}>
        <ModalContent>
          {(onClose) => (
            <>
              <ModalHeader className="flex flex-col gap-1">

              </ModalHeader>
              <ModalBody>
                <p></p>
              </ModalBody>
              <ModalFooter>
                <Button color="danger" variant="light" onPress={onClose}>
                  确定删除
                </Button>
                <Button color="primary" onPress={onClose}>
                  取消
                </Button>
              </ModalFooter>
            </>
          )}
        </ModalContent>
      </Modal> */}
    </div>
  );
}
