.container {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
}
.cardHead {
  flex-shrink: 0;
  flex-grow: 0;
  height: 76px;
  display: flex;
  justify-content: space-between;
}
.searchInput {
  width: 218px;
  padding: 0 16px;
}
.cardMain {
  height: 100%;
  flex-shrink: 1;
  flex-grow: 1;
  overflow-y: auto;
}
.cardItem {
  display: flex;
  /* flex-direction: column; */
  border: 1px solid var(--borderColor);
  border-radius: 6px;
  padding: 20px;
  margin-bottom: 12px;
}
.cardTitle {
  display: flex;
  align-items: flex-start;
}
.cardTitleTxt {
  font-size: 16px;
  font-style: normal;
  font-weight: 600;
  line-height: 21px;
  white-space: nowrap;
  overflow: hidden;
  width: 68%;
  text-overflow: ellipsis;
  margin-right: 14px;
}
.mainTxt {
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 18px;
  margin: 10px 0;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  height: 36px;
}
.options {
  display: flex;
  justify-content: space-between;
  margin-top: 24px;
}
.downloadBtn {
  margin: 0 8px;
}
.gray {
  color: rgba(255, 255, 255, 0.6);
}
.review {
  height: 128px;
  width: 90px;
  flex-shrink: 0;
  flex-grow: 0;
  border-radius: 8px;
  margin-right: 10px;
  overflow: hidden;
}
.picContain {
  width: calc(100% - 100px);
  flex-shrink: 1;
  flex-grow: 1;
}
.deleteBtn {
  cursor: pointer;
}
.btns {
  margin: 10px;
  height: 36px;
}
.pic {
  width: 300px;
}
.nullTxt {
  color: #bcbcbc;
  font-family: Actor;
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px;
}
.btnList {
  display: flex;
  align-items: center;
  visibility: hidden;
}
.cardItem:hover .btnList {
  visibility: visible;
}
.nullBtnList {
  display: flex;
  align-items: center;
  margin-top: 58px;
}
.nullContent {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
}
