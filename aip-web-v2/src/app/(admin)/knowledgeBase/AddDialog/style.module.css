.dialogContent {
  padding: 0;
  width: 556px;
}
.dialogTitle {
  display: flex;
  align-items: center;
  font-size: 16px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
}
.hoverCardContent {
  width: 380px;
  padding: 8px 12px;
  font-size: 12px;
  font-style: normal;
  font-weight: 500;
  line-height: 20px;
  color: rgba(255, 255, 255, 0.6);
}
.extendsInfo {
  font-size: 14px;
  color: red;
  margin-top: 0;
  visibility: hidden;
  height: 0;
  opacity: 0;
}
.extendsInfo.active {
  visibility: visible;
  margin-top: 4px;
  opacity: 1;
  transition: opacity 0.2s ease, margin-top 0.2s ease;
}
.hoverCardTrigger {
  margin-left: 3px;
  margin-top: 2px;
  cursor: pointer;
}
.btnList {
  display: flex;
  justify-content: center;
  margin-top: 58px;
}
.btn {
  height: 36px;
}
