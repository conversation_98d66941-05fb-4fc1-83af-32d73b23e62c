import { useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>dal<PERSON>ody,
  <PERSON><PERSON><PERSON>oot<PERSON>,
  Button,
  Input,
  Card,
  CardBody,
} from "@nextui-org/react";
import { toast } from "sonner";
import { postAddMedia } from "@/service/fetchData";
import { useUserInfoStore } from "@/store/store";
import Image from "next/image";
import { Info, ExternalLink } from "lucide-react";
import douyinIcon from "@/assets/images/hot_icon/douyin.png";
import xiaohongshuIcon from "@/assets/images/hot_icon/xiaohongshu.png";
import kuaishouIcon from "@/assets/images/hot_icon/kuaishou.png";
import { motion, AnimatePresence } from "framer-motion";

interface AddDialogProps {
  callback: () => void;
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  isMyself: number; // 0: 我的账号、1: 其他账号
}

const PLATFORM_INFO = [
  {
    name: "抖音",
    icon: douyinIcon,
    example: "https://v.douyin.com/***",
    desc: "复制抖音主页链接",
    pattern: /^https:\/\/v\.douyin\.com\//,
    steps: [
      {
        text: "电脑访问抖音官网",
        url: "https://www.douyin.com",
        urlText: "www.douyin.com",
      },
      "搜索作者昵称",
      "进入作者主页",
      "点击右上角「分享主页」",
      "复制分享链接",
    ],
    enabled: true,
  },
  {
    name: "小红书",
    icon: xiaohongshuIcon,
    example: "https://www.xiaohongshu.com/user/profile/***",
    desc: "复制小红书个人主页链接",
    pattern: /^https:\/\/www\.xiaohongshu\.com\/user\/profile\//,
    steps: ["访问小红书网站", "进入您的个人主页", "复制浏览器地址栏的链接"],
    enabled: false,
    comingSoonMessage: "小红书账号关联功能即将上线，敬请期待",
  },
  {
    name: "快手",
    icon: kuaishouIcon,
    example: "https://www.kuaishou.com/profile/***",
    desc: "复制快手主页链接",
    pattern: /^https:\/\/www\.kuaishou\.com\/profile\//,
    steps: ["访问快手网站", "进入您的个人主页", "复制浏览器地址栏的链接"],
    enabled: false,
    comingSoonMessage: "快手账号关联功能即将上线，敬请期待",
  },
] as const;

export default function AddDialog({
  isOpen,
  onOpenChange,
  callback,
  isMyself,
}: AddDialogProps) {
  const currentIpUser = useUserInfoStore((state) => state.currentIpUser);
  const [loading, setLoading] = useState(false);
  const [accountUrl, setAccountUrl] = useState("");
  const [isInvalidUrl, setIsInvalidUrl] = useState(false);
  const [selectedPlatform, setSelectedPlatform] = useState<
    (typeof PLATFORM_INFO)[number] | null
  >(PLATFORM_INFO[0]); // 默认选中抖音

  const extractUrl = (text: string): string => {
    const urlRegex = /(https?:\/\/[^\s]+)/g; // 匹配 http 或 https 开头的链接
    const match = text.match(urlRegex);
    return match ? match[0] : text; // 如果找到了链接，取第一个；否则原样返回
  };

  const validateUrl = (url: string) => {
    return PLATFORM_INFO.some((platform) => platform.pattern.test(url));
  };

  const handleUrlChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const rawText = e.target.value.trim();
    const url = extractUrl(rawText);
    setAccountUrl(url);
    setIsInvalidUrl(url !== "" && !validateUrl(url));
  };

  const handleSubmit = async () => {
    if (!accountUrl || isInvalidUrl) {
      toast.error("请输入正确的链接格式");
      return;
    }

    if (selectedPlatform && !selectedPlatform.enabled) {
      toast.info(selectedPlatform.comingSoonMessage);
      return;
    }

    const isExampleLink = PLATFORM_INFO.some(
      (platform) => platform.example === accountUrl
    );

    if (isExampleLink) {
      toast.error("请不要使用示例链接，请复制您的实际账号主页链接");
      return;
    }

    try {
      setLoading(true);
      const res = await fetch(
        `/app/api/resolve?url=${encodeURIComponent(accountUrl)}`
      );
      const data = await res.json();

      if (res.ok) {
        // setResult(data);
        if (data.type === "user") {
          submitFun();
        } else {
          toast.error("请复制您的账号主页链接");
        }
      } else {
        setLoading(false);
        // setError(data.error || '解析失败');
      }
    } catch (err) {
      setLoading(false);
      // setError('请求出错');
    } finally {
      setLoading(false);
    }
  };

  const submitFun = async () => {
    try {
      setLoading(true);
      const res = await postAddMedia({
        account_url: accountUrl,
        pid: currentIpUser.id,
        is_myself: isMyself,
      });

      if (res.code === 200) {
        toast.success("添加成功！系统开始同步账号数据，预计2-6小时后可查看");
        callback?.();
        handleClose();
      } else {
        toast.error(res.message || "添加失败");
      }
    } catch (error) {
      toast.error("操作失败，请重试");
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    onOpenChange(false);
    setAccountUrl("");
    setIsInvalidUrl(false);
  };

  const handleCardClick = (platform: (typeof PLATFORM_INFO)[number]) => {
    setSelectedPlatform(platform);
  };

  return (
    <Modal
      size="2xl"
      isOpen={isOpen}
      onOpenChange={handleClose}
      backdrop="blur"
      classNames={{
        backdrop: "bg-black/70 backdrop-blur-sm",
        base: "border border-default-200 dark:border-default-100/20 bg-background/95 dark:bg-default-100/30 backdrop-blur-md",
        header: "border-b border-default-200 dark:border-default-100/20",
        footer: "border-t border-default-200 dark:border-default-100/20",
        closeButton: "hover:bg-default-100 active:bg-default-200",
      }}
      motionProps={{
        variants: {
          enter: {
            opacity: 1,
            scale: 1,
            y: 0,
            transition: {
              duration: 0.3,
              ease: [0.32, 0.72, 0, 1],
            },
          },
          exit: {
            opacity: 0,
            scale: 0.98,
            y: 10,
            transition: {
              duration: 0.2,
              ease: [0.32, 0, 0.67, 0],
            },
          },
        },
      }}
    >
      <ModalContent>
        <ModalHeader className="flex flex-col gap-1">
          <h3 className="text-lg font-medium">添加账户</h3>
        </ModalHeader>

        <ModalBody className="gap-6">
          <div className="grid grid-cols-3 gap-4">
            {PLATFORM_INFO.map((platform) => (
              <Card
                key={platform.name}
                isPressable
                className={`border transition-colors ${
                  selectedPlatform?.name === platform.name
                    ? "border-primary"
                    : "border-default-200 dark:border-default-100/20 hover:border-primary/50"
                }`}
                onPress={() => handleCardClick(platform)}
              >
                <CardBody className="gap-2 p-4 relative">
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 relative">
                      <Image
                        src={platform.icon}
                        alt={platform.name}
                        width={32}
                        height={32}
                        className="object-contain"
                      />
                    </div>
                    <span className="font-medium">{platform.name}</span>
                  </div>
                  <div className="mt-2 p-2 bg-default-100 dark:bg-default-50/50 rounded text-xs font-mono text-default-600">
                    <span className="text-default-400">链接格式：</span>
                    {platform.example}
                  </div>
                  {!platform.enabled && (
                    <div className="absolute top-2 right-2 px-2 py-1 bg-primary/10 rounded-full">
                      <span className="text-xs text-primary">即将上线</span>
                    </div>
                  )}
                </CardBody>
              </Card>
            ))}
          </div>

          <motion.div
            layout
            transition={{
              layout: { duration: 0.2, ease: "easeOut" },
            }}
          >
            <AnimatePresence mode="wait">
              {selectedPlatform && (
                <motion.div
                  key="steps"
                  initial={{ opacity: 0, height: 0 }}
                  animate={{ opacity: 1, height: "auto" }}
                  exit={{ opacity: 0, height: 0 }}
                  transition={{ duration: 0.2, ease: "easeOut" }}
                >
                  <div className="rounded-lg bg-primary-50/50 dark:bg-primary-100/10 p-3 text-sm">
                    <div className="font-medium text-primary-600 dark:text-primary-400 mb-2">
                      如何获取{selectedPlatform.name}主页链接？
                    </div>
                    <ol className="space-y-1.5 text-default-600 dark:text-default-400 list-decimal list-inside pl-1">
                      {selectedPlatform.steps.map((step, index) => (
                        <li key={index} className="leading-relaxed">
                          {typeof step === "string" ? (
                            step
                          ) : (
                            <span>
                              {step.text}{" "}
                              <a
                                href={step.url}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="inline-flex items-center gap-1 text-primary hover:underline"
                              >
                                {step.urlText}
                                <ExternalLink
                                  size={14}
                                  className="relative top-[1px]"
                                />
                              </a>
                            </span>
                          )}
                        </li>
                      ))}
                    </ol>
                  </div>
                </motion.div>
              )}
            </AnimatePresence>

            <div className="space-y-6 mt-6">
              <div className="space-y-1.5">
                <label className="text-sm font-medium text-default-700 dark:text-default-500">
                  账号主页链接
                </label>
                <Input
                  value={accountUrl}
                  onChange={handleUrlChange}
                  variant="bordered"
                  color={isInvalidUrl ? "danger" : "default"}
                  errorMessage={isInvalidUrl && "请输入正确的链接格式"}
                  placeholder={
                    selectedPlatform
                      ? `请粘贴您的${selectedPlatform.name}主页链接`
                      : "请先选择平台并粘贴账号主页链接"
                  }
                  classNames={{
                    input: "bg-transparent",
                    inputWrapper: "shadow-sm",
                    errorMessage: "text-xs mt-1",
                  }}
                />
              </div>

              <div className="rounded-lg bg-default-50 dark:bg-default-100/5 p-3 flex items-start gap-2">
                <Info size={16} className="text-default-500 mt-0.5" />
                <div className="text-sm text-default-500">
                  系统将在2-6小时内完成数据同步，期间可正常使用其他功能
                </div>
              </div>
            </div>
          </motion.div>
        </ModalBody>

        <ModalFooter>
          <Button
            variant="flat"
            onPress={handleClose}
            className="bg-default-100 dark:bg-default-50/50"
          >
            取消
          </Button>
          <Button
            color="primary"
            onPress={handleSubmit}
            isLoading={loading}
            isDisabled={!accountUrl || isInvalidUrl || loading}
            className="font-medium"
          >
            {selectedPlatform?.enabled ? "确定添加" : "即将上线"}
          </Button>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
}
