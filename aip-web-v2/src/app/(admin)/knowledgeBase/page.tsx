"use client";
import { useEffect, useState, useMemo } from "react";
import {
  <PERSON>ltip,
  Avatar,
  Button,
  Modal,
  <PERSON>dal<PERSON>ontent,
  <PERSON>dal<PERSON>eader,
  <PERSON>dal<PERSON><PERSON>,
  <PERSON>dalFooter,
} from "@nextui-org/react";
import { toast } from "sonner";
import { message } from "antd";
import Image from "next/image";
import AddDialog from "./AddDialog";
import CardContain from "./cardContain/CardContain";
import SpinLoading from "@/components/SpinLoading/SpinLoading";
import { dictToHotIconInPersonalData } from "@/lib";
import { getPersonalKnowledgeClassify, unLinkData } from "@/service/fetchData";
import { useUserInfoStore } from "@/store/store";
import {
  Unlink,
  RefreshCw,
  Plus,
  MoreVertical,
  ExternalLink,
  Users,
  User,
  CircleUserRound,
} from "lucide-react";
import { KnowledgeItem } from "@/types/knowledgeBase";
import {
  Dropdown,
  DropdownTrigger,
  DropdownMenu,
  DropdownItem,
} from "@nextui-org/react";
import myFolder from "@/assets/images/hot_icon/myFolder.png";

const PersionalData = () => {
  const [isMyself, setIsMyself] = useState(0);
  const [navList, setNavList] = useState<KnowledgeItem[]>([]);
  const currentIpUser = useUserInfoStore((state) => state.currentIpUser);
  const [activeId, setActiveId] = useState<KnowledgeItem | null>(null);
  const [isNavLoaing, setIsNavLoaing] = useState(true);
  const [isOpen, setIsOpen] = useState(false);
  const [isUnlinkOpen, setIsUnlinkOpen] = useState(false);
  const [unlinkItem, setUnlinkItem] = useState<KnowledgeItem | null>(null);
  const [messageApi, contextHolder] = message.useMessage();
  const getMenuItems = (item: KnowledgeItem) => {
    const items = [];
    if (item.account_url) {
      items.push(
        <DropdownItem
          key="view"
          startContent={<ExternalLink size={16} />}
          onPress={() => {
            window.open(item.account_url, "_blank", "noopener,noreferrer");
          }}
        >
          查看账号
        </DropdownItem>
      );
    }
    items.push(
      <DropdownItem
        key="unlink"
        className="text-danger"
        color="danger"
        startContent={<Unlink size={16} />}
        onPress={() => {
          unLinkEvent(item);
        }}
      >
        解除关联
      </DropdownItem>
    );
    return items;
  };

  /** IP知识库列 */
  const navItem = useMemo(
    () => (item: KnowledgeItem) => {
      const navData = {
        ...item,
        typeImg: dictToHotIconInPersonalData(String(item.knowledge_type)),
        loadingPersion:
          ["我的抖音", "我的快手", "我的小红书"].includes(
            item.knowledge_source
          ) || ["抖音号", "快手号", "小红书号"].includes(item.knowledge_source),
        activeStyle:
          item.id === activeId?.id ? "bg-content2 dark:bg-content2/70" : "",
      };

      return (
        <div
          key={item.id}
          className={`${navData.activeStyle} group hover:bg-default-100 dark:hover:bg-default-100/20 
        transition-all duration-300 cursor-pointer relative mx-2 rounded-lg`}
          onClick={() => switchEvent(item)}
        >
          {item.id === activeId?.id && (
            <div className="absolute inset-0 bg-content2/30 dark:bg-content2/40 rounded-lg pointer-events-none" />
          )}

          <div className="flex items-center gap-3 p-3 relative z-10">
            {navData?.knowledge_type === 1 ? (
              // 我的文件样式
              <div className="flex items-center gap-3 w-full">
                <div className="w-9 h-9 relative">
                  <Image
                    src={myFolder}
                    alt="我的文件"
                    fill
                    className="transition-transform group-hover:scale-110"
                  />
                </div>
                <p className="font-medium text-foreground">
                  {item.knowledge_source}
                </p>
              </div>
            ) : (
              <div className="flex items-center justify-between w-full">
                <div className="flex items-center gap-3 flex-1 min-w-0">
                  <div className="relative">
                    <Avatar
                      src={item.avatar}
                      fallback={
                        <div className="w-full h-full bg-default-200 dark:bg-default-100/30 rounded-full" />
                      }
                      className="shadow-lg"
                      size="md"
                    />
                    <div className="absolute -bottom-1 -left-1 flex items-center gap-0.5 bg-background dark:bg-default-100/50 rounded-full p-0.5 shadow-sm">
                      {navData.loadingPersion ? (
                        <RefreshCw className="w-3 h-3 text-warning animate-spin" />
                      ) : (
                        navData.typeImg && (
                          <Image
                            width={14}
                            height={14}
                            alt="platform"
                            src={navData.typeImg}
                            className="rounded-full"
                          />
                        )
                      )}
                    </div>
                  </div>
                  <div className="flex flex-col min-w-0">
                    <div className="truncate max-w-[120px] text-foreground">
                      {item.knowledge_source}
                    </div>
                    {navData.loadingPersion && (
                      <span className="text-xs text-default-500 dark:text-default-400">
                        同步中...
                      </span>
                    )}
                  </div>
                </div>

                {/* 更多菜单 */}
                <Dropdown>
                  <DropdownTrigger>
                    <button className="p-2 rounded-full opacity-0 group-hover:opacity-100 hover:bg-default-200 dark:hover:bg-default-100/30 text-default-600 dark:text-default-400 transition-all duration-300">
                      <MoreVertical size={16} />
                    </button>
                  </DropdownTrigger>
                  <DropdownMenu
                    aria-label="Account actions"
                    variant="flat"
                    className="p-0 bg-background dark:bg-default-50"
                    classNames={{
                      base: "shadow-xl border-none",
                    }}
                    itemClasses={{
                      base: [
                        "px-4 py-3",
                        "gap-3",
                        "data-[hover=true]:bg-default-100/80",
                        "dark:data-[hover=true]:bg-default-200/20",
                        "data-[pressed=true]:opacity-80",
                        "transition-opacity",
                      ],
                      title:
                        "text-default-700 dark:text-default-400 font-medium",
                      description:
                        "text-tiny text-default-400 dark:text-default-500",
                    }}
                  >
                    {getMenuItems(item)}
                  </DropdownMenu>
                </Dropdown>
              </div>
            )}
          </div>
        </div>
      );
    },
    [activeId, dictToHotIconInPersonalData]
  );

  /** 切换个人 */
  const switchEvent = (item: KnowledgeItem) => {
    setActiveId(item);
  };
  /** 获取作品库的用户列 */
  const getNavData = async () => {
    if (!currentIpUser?.id) return;

    setIsNavLoaing(true);
    try {
      const result = await getPersonalKnowledgeClassify(`${currentIpUser.id}`);
      if (result?.code === 200 && Array.isArray(result.data)) {
        // 修改返回数据中的命名
        const updatedData = (result.data || [])?.map((item) => ({
          ...item,
          knowledge_source: item.knowledge_source
            .replace("我的抖音", "抖音号")
            .replace("我的快手", "快手号")
            .replace("我的小红书", "小红书号"),
        }));

        // 确保始终有"我的文件"
        const myFileExists = updatedData?.some(
          (item) => item.knowledge_type === 1
        );
        const defaultMyFile = {
          id: "myFile",
          knowledge_type: 1,
          knowledge_source: "我的文件",
        };

        const finalList = myFileExists
          ? updatedData
          : [defaultMyFile, ...(updatedData || [])];

        setNavList(() => finalList);
        console.log("getNavData updated list:", finalList); // 添加日志

        // 如果当前选中项被删除，或者没有选中项，则默认选中"我的文件"
        const currentActiveExists = finalList.find(
          (item) => item.id === activeId?.id
        );
        if (!currentActiveExists) {
          setActiveId(finalList[0]);
        }
      } else {
        console.error("Invalid response format:", result);
      }
    } catch (error) {
      console.error("Failed to fetch nav data:", error);
      toast.error("获取数据失败");
    } finally {
      setIsNavLoaing(false);
    }
  };
  /** 解绑事件 */
  const unLinkEvent = (item: KnowledgeItem) => {
    setIsUnlinkOpen(true);
    setUnlinkItem(item);
  };

  /** 处理解绑确认 */
  const handleUnlinkConfirm = async () => {
    if (!unlinkItem?.id) return;

    // 如果账号正在同步中，不允许解绑
    const isLoading =
      ["我的抖音", "我的快手", "我的小红书"].includes(
        unlinkItem.knowledge_source
      ) ||
      ["抖音号", "快手号", "小红书号"].includes(unlinkItem.knowledge_source);
    if (isLoading) {
      toast.error("账号同步中，请稍后再试");
      setIsUnlinkOpen(false);
      setUnlinkItem(null);
      return;
    }

    try {
      const res = await unLinkData(`${unlinkItem.id}`);
      if (res?.code === 200) {
        // 先更新本地状态
        setNavList((prev) => {
          const updatedList = prev.filter((item) => item.id !== unlinkItem.id);
          // 如果删除的是当前选中项，重置选中状态为"我的文件"
          if (activeId?.id === unlinkItem.id) {
            const myFile = updatedList.find(
              (item) => item.knowledge_type === 1
            );
            if (myFile) {
              setActiveId(myFile);
            }
          }
          return updatedList;
        });

        toast.success("解绑成功");

        // 延迟重新获取数据以确保后端数据同步
        setTimeout(() => {
          getNavData();
        }, 1000);
      } else {
        toast.error(res.message || "解绑失败");
      }
    } catch (error) {
      console.error("解绑失败:", error);
      toast.error("操作失败，请重试");
    } finally {
      setIsUnlinkOpen(false);
      setUnlinkItem(null);
    }
  };
  // 判断绑定抖音号限定3个
  const verifyThree = () => {
    const length = navList.filter((item) => item.knowledge_type !== 1).length;
    if (length >= 3) {
      messageApi.warning("抱歉，当前已达账号最大绑定额度~");
      return true;
    }
    return false;
  };
  useEffect(() => {
    if (currentIpUser.id) {
      getNavData();
    }
  }, [currentIpUser.id]);
  return (
    <SpinLoading loading={isNavLoaing}>
      {contextHolder}
      <div className="flex h-full bg-background dark:bg-background/95">
        <section className="w-80 h-full bg-content1/80 dark:bg-content1/20 backdrop-blur-md border-r border-divider">
          <div className="p-4 border-b border-divider bg-background/40">
            <h2 className="text-large font-bold text-foreground">个人知识库</h2>
          </div>
          <div className="flex flex-col h-[calc(100%-4rem)]">
            {/* 我的文件 */}
            <div className="p-2">
              <div className="flex items-center text-sm text-default-500 px-2 py-1 gap-1">
                <User size={14} />
                我的文件
              </div>
              <div className="space-y-1">
                {navList
                  .filter((item) => item.knowledge_type === 1)
                  .map((item) => navItem(item))}
              </div>
            </div>
            {/* 我的账号 */}
            <div className="p-2">
              <div className="flex items-center justify-between px-2 py-1">
                <span className="flex items-center gap-1 text-sm text-default-500">
                  <CircleUserRound size={14} />
                  <span>我的账号</span>
                </span>
                <button
                  onClick={() => {
                    if (verifyThree()) {
                      return;
                    }
                    setIsMyself(1);
                    setIsOpen(true);
                  }}
                  className="p-1 rounded-full hover:bg-default-200 dark:hover:bg-default-100/20 text-default-500 hover:text-foreground transition-colors"
                >
                  <Plus className="w-4 h-4" />
                </button>
              </div>
              {navList.filter((item) => item.is_myself === 1).length > 0 ? (
                <div className="space-y-1 h-full overflow-x-auto">
                  {navList
                    .filter(
                      (item) =>
                        item.knowledge_type !== 1 && item.is_myself === 1
                    )
                    .map((item) => navItem(item))}
                </div>
              ) : (
                <div className="flex flex-col items-center justify-center p-8 text-center">
                  <div className="w-12 h-12 mb-4 rounded-full bg-default-100 dark:bg-default-100/20 flex items-center justify-center">
                    <Plus className="w-6 h-6 text-default-500" />
                  </div>
                  <p className="text-sm text-default-500 mb-2">暂无关联账号</p>
                  <Button
                    size="sm"
                    color="primary"
                    variant="flat"
                    onPress={() => {
                      setIsMyself(1);
                      setIsOpen(true);
                    }}
                    startContent={<Plus className="w-4 h-4" />}
                  >
                    添加账号
                  </Button>
                </div>
              )}
            </div>
            {/* 对标账号专家组 */}
            <div className="p-2 border-t border-divider flex-grow h-full overflow-hidden">
              <div className="flex items-center justify-between px-2 py-1">
                <span className="flex items-center gap-1 text-sm text-default-500">
                  <Users size={14} />
                  <span>对标账号专家组</span>
                </span>
                <button
                  onClick={() => {
                    if (verifyThree()) {
                      return;
                    }
                    setIsMyself(0);
                    setIsOpen(true);
                  }}
                  className="p-1 rounded-full hover:bg-default-200 dark:hover:bg-default-100/20 text-default-500 hover:text-foreground transition-colors"
                >
                  <Plus className="w-4 h-4" />
                </button>
              </div>
              {/* 对标账号列表 */}
              <AddDialog
                isOpen={isOpen}
                onOpenChange={setIsOpen}
                callback={getNavData}
                isMyself={isMyself}
              />
              {navList.filter(
                (item) => item.knowledge_type !== 1 && item.is_myself === 0
              ).length > 0 ? (
                <div className="space-y-1 h-full overflow-x-auto">
                  {navList
                    .filter(
                      (item) =>
                        item.knowledge_type !== 1 && item.is_myself === 0
                    )
                    .map((item) => navItem(item))}
                </div>
              ) : (
                <div className="flex flex-col items-center justify-center p-8 text-center">
                  <div className="w-12 h-12 mb-4 rounded-full bg-default-100 dark:bg-default-100/20 flex items-center justify-center">
                    <Plus className="w-6 h-6 text-default-500" />
                  </div>
                  <p className="text-sm text-default-500 mb-2">暂无关联账号</p>
                  <Button
                    size="sm"
                    color="primary"
                    variant="flat"
                    onPress={() => {
                      setIsMyself(0);
                      setIsOpen(true);
                    }}
                    startContent={<Plus className="w-4 h-4" />}
                  >
                    添加账号
                  </Button>
                </div>
              )}
            </div>
          </div>
        </section>
        <section className="flex-1 p-4 overflow-auto">
          <CardContain
            id={activeId?.id}
            knowledge_type={activeId?.knowledge_type}
          />
        </section>
        <Modal
          isOpen={isUnlinkOpen}
          onOpenChange={setIsUnlinkOpen}
          placement="center"
          isDismissable={false}
          hideCloseButton={true}
          classNames={{
            base: "dark:bg-content1",
          }}
        >
          <ModalContent>
            <ModalHeader className="flex flex-col gap-1">解除关联</ModalHeader>
            <ModalBody>
              确定要解除关联吗？解除后将无法查看该账号的内容。
            </ModalBody>
            <ModalFooter>
              <Button variant="light" onPress={() => setIsUnlinkOpen(false)}>
                取消
              </Button>
              <Button color="danger" onPress={handleUnlinkConfirm}>
                确定
              </Button>
            </ModalFooter>
          </ModalContent>
        </Modal>
      </div>
    </SpinLoading>
  );
};

export default PersionalData;
