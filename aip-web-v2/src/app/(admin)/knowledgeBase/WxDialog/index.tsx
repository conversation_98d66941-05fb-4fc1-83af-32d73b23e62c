import { Dispatch, SetStateAction } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, ModalBody } from "@nextui-org/react";
import { Copy } from "lucide-react";
import styles from "./style.module.css";
interface TProps {
  isOpen: boolean;
  setIsOpen: Dispatch<SetStateAction<boolean>>;
}
const WXDialog = ({ isOpen, setIsOpen }: TProps) => {
  const copyEvent = () => {};
  return (
    <Modal isOpen={isOpen} onOpenChange={() => setIsOpen(!isOpen)}>
      <ModalContent className={styles.dialogContent}>
        <ModalHeader className={styles.dialogHeader}>
          <span>绑定微信</span>
        </ModalHeader>
        <ModalBody>
          <div className={styles.main}>
            <img className={styles.pic} alt="" />
            <div className={styles.txt}>
              <span>绑定指令： 后端拼指令</span>
              <span className="ml-2 cursor-pointer" onClick={copyEvent}>
                <Copy />
              </span>
            </div>
          </div>
        </ModalBody>
      </ModalContent>
    </Modal>
  );
};
export default WXDialog;
