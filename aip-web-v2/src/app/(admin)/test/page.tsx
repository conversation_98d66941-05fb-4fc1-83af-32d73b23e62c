'use client'
import { useEffect } from "react";

const TestPage = () => {
    useEffect(() => {
        const fetchData = async () => {
            try {
                const response = await fetch('https://test-xinhou-aip-admin.aipgpt.cn/agents/v2.1/continue_creation', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJqYWNsbGEiLCJleHAiOjE3MzkzNjM5OTZ9.XyEy3XZoT85282iNIjoEgFCFnsZI6nvAYH463LhV0xw'
                    },
                    body: JSON.stringify({
                        "pid": 1013,
                        "task_id": 4712,
                        "is_pass": 1,
                        "agent_uuid": "3ed69148-d19e-11ef-895a-00163e2614c3",
                        "audio_model_id": 281,
                        "audio_url": "https://wechat-luobo.oss-cn-shanghai.aliyuncs.com/audio/uploads/fish_voice_1736324182.wav"
                    })
                });
                if (!response.ok) {
                    throw new Error('Network response was not ok');
                }
                const reader = response.body?.getReader();
                if (!reader) {
                    throw new Error('Reader not found');
                }
                while (true) {
                    const { done, value } = await reader?.read();
                    if (done) {
                        break;
                    }
                    console.log(value);
                }
            } catch (error) {
                console.error('There was a problem with the fetch operation:', error);
            }
        };
        fetchData();
    }, []);
    return <div>TestPage</div>;
};



export default TestPage;

