"use client";
import { useEffect } from "react";
import DesktopLayout from "./_layout/Desktop";
import MessageModal from "@/components/MessageModal";
const AdminLayout = ({
  children,
  slider,
}: {
  children: React.ReactNode;
  slider: React.ReactNode;
}) => {
  return (
    <DesktopLayout slider={slider}>
      {children}
      <MessageModal />
    </DesktopLayout>
  );
};
export default AdminLayout;
