// import SliderContainer from "./slider";
import { useEffect, useRef, useState, useCallback } from "react";
import styles from "./style.module.css";
import { useLoginStore } from "@/store/store";
import LoginModal from "@/components/LoginModal";
import { Toaster } from "sonner";
import { GetToken } from "@/service/config";
import useUser from "@/hooks/useUserinfo";

const DesktopLayout = ({
  children,
  slider,
}: {
  children: React.ReactNode;
  slider: React.ReactNode;
}) => {
  const { LogoutEve } = useUser();
  // const [authChecked, setAuthChecked] = useState(false);
  const isLogin = useLoginStore((state) => state.isLogin);
  const updateIsLoginOpen = useLoginStore((state) => state.updateIsLoginOpen);
  const mainRef = useRef<HTMLDivElement>(null);

  const handleEvent = useCallback(
    (e: MouseEvent) => {
      if (!isLogin) {
        updateIsLoginOpen(true);
      }
    },
    [isLogin, updateIsLoginOpen]
  );
  // 判断token 是否过期
  useEffect(() => {
    const cookie = GetToken();
    if (!cookie) {
      LogoutEve();
    }
  }, []);
  /**
   * 监听 登录状态，如果未登录状态
   * 1. 设置不可点击
   * 2. 增加全局监听，没有登录状态下点击显示弹框
   */
  useEffect(() => {
    if (mainRef.current) {
      mainRef.current.style.pointerEvents = isLogin ? "auto" : "none";
    }

    document.body.addEventListener("click", handleEvent);
    return () => document.body.removeEventListener("click", handleEvent);
  }, [isLogin, handleEvent]);

  return (
    <>
      <Toaster />
      <div className={styles.container} ref={mainRef}>
        {slider}
        <section className={styles.main}>{children}</section>
      </div>
      <LoginModal />
    </>
  );
};
export default DesktopLayout;
