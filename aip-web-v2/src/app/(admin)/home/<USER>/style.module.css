.fileTitle {
  color: #fff;
  font-size: 16px;
  font-style: normal;
  font-weight: 600;
  line-height: 22px; /* 137.5% */
  margin-top: 30px;
}
.antdInput {
  margin-bottom: 136px;
}
.ant-sender-actions-btn-disabled {
  background: #fff !important;
}
.actionsContainer {
  display: flex;
  align-items: center;
  gap: 18px;
}
.actinoItem {
  display: flex;
  align-items: center;
  gap: 6px;
  color: rgba(255, 255, 255, 0.6);
  font-family: "PingFang SC", -apple-system, BlinkMacSystemFont, sans-serif;
  font-size: 14px;
  font-style: normal;
  font-weight: 500;
  line-height: 22px;
  border-radius: 4px;
  transition: all 0.2s ease;
  cursor: pointer;
  min-width: 60px;
}
.actinoItem:hover {
  color: rgba(255, 255, 255, 0.85);
}
.cardContent {
  display: flex;
  gap: 5px;
  margin-top: 15px;
}
.urlCard {
  display: flex;
  width: 33%;
  height: 54px;
  padding: 8px 10px;
  align-items: center;
  border-radius: 5px;
  border: 1px solid #2e2e2e;
  gap: 6px;
  cursor: pointer;

  /* --shadow-elevated */
  box-shadow: 0px 0px 1px 0px rgba(0, 0, 0, 0.3),
    0px 4px 14px 0px rgba(0, 0, 0, 0.1);
}
.urlCardImg {
  display: flex;
  width: 36px;
  height: 36px;
  flex-grow: 0;
  flex-shrink: 0;
  align-items: center;
  justify-content: center;
}
.urlCardInfo {
  display: flex;
  flex-direction: column;
  gap: 6px;
  flex-grow: 1;
  flex-shrink: 1;
  width: calc(100% - 36px);
}
.urlCardTxt {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 100%;
  flex-grow: 1;
  flex-shrink: 1;
  color: rgba(255, 255, 255, 0.85);
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: 14px; /* 116.667% */
}
.urlCardSubTxt {
  color: rgba(255, 255, 255, 0.85);
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: 14px; /* 116.667% */
  opacity: 0.6;
  letter-spacing: 1px;
}

/* 添加数字样式类 */
.timeText {
  font-family: -apple-system, BlinkMacSystemFont, sans-serif;
  font-feature-settings: "tnum";
  font-variant-numeric: tabular-nums;
}

.promptBtnContent {
  display: flex;
  align-items: center;
}
.avatarGroup {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background-color: #b190b6;
  border: 1px solid #bca8c0;
  margin-left: -8px;
}
.promptBtnContent .avatarGroup:first-child {
  margin-left: 0;
}
