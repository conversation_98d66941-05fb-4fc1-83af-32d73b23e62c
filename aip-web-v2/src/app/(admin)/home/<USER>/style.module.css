.linkHeader {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 16px;
  margin-top: 32px;
  padding: 0 2px;
}
.linkHeader > span {
  color: #fff;
  font-size: 16px;
  font-weight: 600;
  letter-spacing: 0.3px;
  line-height: 20px;
  opacity: 0.95;
  margin-left: 12px;
}
.listItem {
  display: flex;
  align-items: center;
  width: 100%;
  padding: 0 14px;
  height: 42px;
  font-weight: 400;
  border-radius: 6px;
  border: 1px solid rgba(46, 46, 46, 0.8);
  background: rgba(26, 26, 26, 0.4);
  backdrop-filter: blur(8px);
  transition: all 0.2s ease-in-out;
  cursor: pointer;
}
.txtBlock {
  display: flex;
  height: 100%;
  width: 100%;
  flex-shrink: 1;
  flex-grow: 1;
  align-items: center;
}
.listItem .linkIcon {
  opacity: 0;
  transition: opacity 0.2s ease-in-out;
}
.listItem:hover {
  background: rgba(44, 44, 44, 0.6);
  border-color: rgba(255, 255, 255, 0.1);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}
.listItem:hover .linkIcon {
  opacity: 1;
  background: rgba(255, 255, 255, 0.05);
}
.listItemTxt {
  font-size: 13px;
  line-height: 16px;
  color: rgba(255, 255, 255, 0.85);
  width: 100%;
  flex-shrink: 1;
  margin-right: 12px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  transition: color 0.2s ease;
}
.listItem:hover .listItemTxt {
  color: rgba(255, 255, 255, 0.95);
}
.linkHeaderIcon {
  width: 16px;
  height: 16px;
  cursor: pointer;
  transform: rotate(0deg);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  opacity: 0.5;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.03);
}
.linkHeaderIcon:hover {
  opacity: 1;
}
.linkHeaderIcon.animations {
  animation: run 1.2s cubic-bezier(0.4, 0, 0.2, 1) infinite;
  opacity: 1;
}
@keyframes run {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
/* .linkHeaderIcon:hover {
  opacity: 0.7;

  transform: rotate(180deg);
} */
.linkIcon {
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid rgba(255, 255, 255, 0.15);
  border-radius: 4px;
  padding: 4px;
  opacity: 0;
  transition: all 0.2s ease-in-out;
}
.moreBtn {
  line-height: 38px;
  padding: 0 12px;
  color: rgba(255, 255, 255, 0.8);
  font-size: 12px;
  margin-left: auto;
  cursor: pointer;
}
.moreBtn:hover {
  color: #fff;
}
