.block {
  display: flex;
  line-height: 32px;
  gap: 10px;
  width: 136px;
}
.blockTitle {
  display: flex;
  align-items: center;
  flex-shrink: 0;
  flex-grow: 0;
  justify-content: flex-end;
  width: 60px;
}
.resultBlock {
  position: relative;
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.07);
  padding: 0 8px;
  height: 32px;
  width: 100px;
  flex-grow: 0;
  flex-shrink: 0;
  cursor: pointer;
}
.ICON {
  position: absolute;
  top: 0;
  bottom: 0;
  right: 10px;
  margin: auto;
}
.text {
  width: 70px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
