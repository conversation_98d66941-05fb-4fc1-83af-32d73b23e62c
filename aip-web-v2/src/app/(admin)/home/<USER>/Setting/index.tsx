"use client";
import { Col, Dropdown, Form, Row, Select, Tooltip } from "antd";
import { ChevronDown, CircleAlert } from "lucide-react";
import { FC, ReactNode, useCallback, useEffect, useRef, useState } from "react";
import {
  useWorkflowStore,
  useUserInfoStore,
  useGlobalConfigStore,
} from "@/store/store";
import styles from "./style.module.css";
import {
  styleItems,
  languageItems,
  legibilityItems,
  timeItems,
  enTimeItems,
} from "@/utils/setting";
import { LT } from "@/typing/types";

const { Option } = Select;
interface NoteProps {
  title: string;
  txt: string | ReactNode;
}
/** 注意 */
const Note: FC<NoteProps> = ({ title, txt }) => {
  return (
    <>
      {title}
      <Tooltip placement="top" title={txt} className="ml-1">
        <CircleAlert size={14} color="#BCBCBC" />
      </Tooltip>
    </>
  );
};
/** 初始化信息 */
const initSetConfig = {
  language: { key: "zh" as LT, label: "中文", text: "中文" },
  doc_length: { key: 270, label: "60s", text: "60s" },
  style: { key: "", label: "默认", text: "默认" },
  read_score: { key: 0, label: "默认", text: "默认" },
};

const AreaSetting = () => {
  const { homeSetting, updateHomeByPid, updateHomeSetting } =
    useGlobalConfigStore((state) => state);
  const pid = useUserInfoStore((state) => state.currentIpUser.id);
  const { startParams, setTaskKnowledgeParams } = useWorkflowStore(
    (state) => state
  );
  // const settingValRef = useRef(initSetConfig);
  const [settingVal, setSettingVal] = useState(initSetConfig);
  // 语言改变事件
  const languageEvent = (e: any) => {
    const item = languageItems.filter((item) => item.key === e.key)[0];
    let newConfig = JSON.parse(JSON.stringify(settingVal));
    newConfig = {
      ...newConfig,
      doc_length:
        e.key === "zh"
          ? { key: 270, label: "60s", text: "60s" }
          : { key: 136, label: "60s", text: "60s" },
      language: item,
    };
    // 更新数据到全局
    updateHomeSetting({ pid: `${pid}`, key: "language", value: item });
    updateHomeSetting({
      pid: `${pid}`,
      key: "doc_length",
      value: newConfig.doc_length,
    });
    setSettingVal(newConfig);
  };
  // 时长切换事件
  const timeEvent = (e: any) => {
    let item = timeItems.filter((item) => item.key === e.key / 1)[0];
    let newConfig = JSON.parse(JSON.stringify(settingVal));
    newConfig = {
      ...newConfig,
      doc_length: item,
    };
    // 更新数据到全局
    updateHomeSetting({ pid: `${pid}`, key: "doc_length", value: item });
    setSettingVal(newConfig);
  };
  // 英文时长
  const enTimeEvent = (e: any) => {
    // const config: any = JSON.parse(JSON.stringify(settingVal));
    let item = enTimeItems.filter((item) => item.key === e.key / 1)[0];
    let newConfig = JSON.parse(JSON.stringify(settingVal));
    newConfig = {
      ...newConfig,
      doc_length: item,
    };
    // 更新数据到全局
    updateHomeSetting({ pid: `${pid}`, key: "doc_length", value: item });
    setSettingVal(newConfig);
  };
  // 风格
  const styleEvent = (e: any) => {
    let item = styleItems.filter((item) => item.key === e.key)[0];
    let newConfig = JSON.parse(JSON.stringify(settingVal));
    newConfig = {
      ...newConfig,
      style: item,
    };
    // 更新数据到全局
    updateHomeSetting({ pid: `${pid}`, key: "style", value: item });
    setSettingVal(newConfig);
  };
  // 易读性
  const easyReadEvent = (e: any) => {
    let item = legibilityItems.filter((item) => item.key === e.key / 1)[0];
    let newConfig = JSON.parse(JSON.stringify(settingVal));
    newConfig = {
      ...newConfig,
      read_score: item,
    };
    // 更新数据到全局
    updateHomeSetting({ pid: `${pid}`, key: "read_score", value: item });
    setSettingVal(newConfig);
  };
  /** 初始化参数 */
  // useEffect(() => {
  // const { language, style, read_score, doc_length } = startParams;
  // let languageItem =
  //   languageItems.filter((item) => item.key === language)[0] ??
  //   settingVal.language;
  // let docItem;
  // // 判断如果是英文的话使用下拉
  // if (languageItem.key === "zh") {
  //   docItem =
  //     timeItems.filter((item) => item.key === doc_length)[0] ??
  //     settingVal.doc_length;
  // } else {
  //   docItem =
  //     enTimeItems.filter((item) => item.key === doc_length)[0] ??
  //     settingVal.doc_length;
  // }
  // let styleItem =
  //   styleItems.filter((item) => item.key === style)[0] ?? settingVal.style;
  // let easyReadItem =
  //   legibilityItems.filter((item) => item.key === read_score)[0] ??
  //   settingVal.read_score;
  // const newConfig = {
  //   doc_length: {
  //     key: docItem.key,
  //     label: docItem.label,
  //     text: docItem.text,
  //   },
  //   language: {
  //     key: languageItem.key,
  //     label: languageItem.label,
  //     text: languageItem.text,
  //   },
  //   style: {
  //     key: styleItem.key,
  //     label: styleItem.label,
  //     text: styleItem.text,
  //   },
  //   read_score: {
  //     key: easyReadItem.key,
  //     label: easyReadItem.label,
  //     text: easyReadItem.text,
  //   },
  // };
  // setSettingVal(newConfig);
  // settingValRef.current = newConfig;
  // return () => {
  //   setTaskKnowledgeParams({
  //     ...startParams,
  //     pid: pid,
  //     doc_length: settingValRef.current.doc_length.key,
  //     style: settingValRef.current.style.key,
  //     read_score: settingValRef.current.read_score.key,
  //     language: settingValRef.current.language.key as LT,
  //   });
  // };
  // }, []);
  useEffect(() => {
    if (homeSetting) {
      // 获取配置信息数据
      const data = homeSetting[`${pid}`];
      // 存在就使用已经配置的数据
      if (data) {
        if (data.doc_length && data.language && data.style && data.read_score) {
          setSettingVal({
            doc_length: data.doc_length,
            language: data.language,
            style: data.style,
            read_score: data.read_score,
          });
        }
        // 不存在就使用默认数据
      } else {
        // 初始化数据
        setSettingVal({
          language: { key: "zh", label: "中文", text: "中文" },
          doc_length: { key: 270, label: "60s", text: "60s" },
          style: { key: "", label: "默认", text: "默认" },
          read_score: { key: 0, label: "默认", text: "默认" },
        });
        // 更新数据到配置环境
        updateHomeByPid({
          pid: `${pid}`,
          value: {
            language: { key: "zh", label: "中文", text: "中文" },
            doc_length: { key: 270, label: "60s", text: "60s" },
            style: { key: "", label: "默认", text: "默认" },
            read_score: { key: 0, label: "默认", text: "默认" },
          },
        });
      }
    }
  }, [pid]);
  return (
    <Row className="w-[360px]" gutter={[6, 12]}>
      <Col className="gutter-row" span={12}>
        <div className={styles.block}>
          <label className={styles.blockTitle}>
            <Note
              title="语言"
              txt={
                <div style={{ fontSize: 12 }}>
                  按指定语言生成口播稿文案、标题、标签
                </div>
              }
            />
          </label>
          <Dropdown
            menu={{
              items: languageItems.map((item) => ({
                ...item,
                onClick: (e) => {
                  languageEvent(e);
                },
              })),
            }}
            trigger={["click"]}
            placement={"bottom"}
          >
            <div className={styles.resultBlock}>
              <div className={styles.text}>{settingVal.language.text}</div>
              <ChevronDown size={14} className={styles.ICON} />
            </div>
          </Dropdown>
        </div>
      </Col>
      <Col className="gutter-row" span={12}>
        <div className={styles.block}>
          <label className={styles.blockTitle}>
            <Note
              title="时长"
              txt={
                <div style={{ fontSize: 12 }}>按指定大约时长生成音频、视频</div>
              }
            />
          </label>
          {settingVal.language.key === "zh" ? (
            <Dropdown
              menu={{
                items: timeItems.map((item) => ({
                  ...item,
                  onClick: (e) => {
                    timeEvent(e);
                  },
                })),
              }}
              trigger={["click"]}
              placement={"bottom"}
            >
              <div className={styles.resultBlock}>
                <div className={styles.text}>{settingVal.doc_length.text}</div>
                <ChevronDown size={14} className={styles.ICON} />
              </div>
            </Dropdown>
          ) : (
            <Dropdown
              menu={{
                items: enTimeItems.map((item) => ({
                  ...item,
                  onClick: (e) => {
                    enTimeEvent(e);
                  },
                })),
              }}
              trigger={["click"]}
              placement={"bottom"}
            >
              <div className={styles.resultBlock}>
                <div className={styles.text}>{settingVal.doc_length.text}</div>
                <ChevronDown size={14} className={styles.ICON} />
              </div>
            </Dropdown>
          )}
        </div>
      </Col>
      <Col className="gutter-row" span={12}>
        <div className={styles.block}>
          <label className={styles.blockTitle}>
            <Note
              title="风格"
              txt={
                <div style={{ fontSize: 12 }}>
                  干货分享：专业、干练，带点权威感
                  <br />
                  情绪共鸣：亲切、煽情，带点戏剧化
                  <br />
                  幽默搞笑：轻松、戏谑，偶尔带点“沙雕”感
                  <br />
                  悬念引导：吊胃口、紧凑，带点神秘感
                  <br />
                  喊话种草：热情、急切，带点“推销员”气势
                  <br />
                  剧情沉浸：叙述性强，带点表演感
                </div>
              }
            />
          </label>
          <Dropdown
            menu={{
              items: styleItems.map((item) => ({
                ...item,
                onClick: (e) => {
                  styleEvent(e);
                },
              })),
            }}
            trigger={["click"]}
            placement={"bottom"}
          >
            <div className={styles.resultBlock}>
              <div className={styles.text}>{settingVal.style.text}</div>
              <ChevronDown size={14} className={styles.ICON} />
            </div>
          </Dropdown>
        </div>
      </Col>
      <Col className="gutter-row" span={12}>
        <div className={styles.block}>
          <label className={styles.blockTitle}>
            <Note
              title="易读性"
              txt={
                <div style={{ fontSize: 12 }}>
                  简单易懂 (Flesch指数:
                  80-100)：适用于入门科普、生活小技巧、大众娱乐等
                  <br />
                  日常对话 (Flesch指数: 70-80)：
                  适用于生活分享、产品测评、轻松话题等 <br />
                  知识分享 (Flesch指数:
                  60-70)：适用于深度解析、详细教程、行业见解等
                  <br />
                  专业内容 (Flesch指数:
                  30-60)：适用于专业知识讲解、学术内容、行业深度等
                </div>
              }
            />
          </label>
          <Dropdown
            menu={{
              items: legibilityItems.map((item) => ({
                ...item,
                onClick: (e) => {
                  easyReadEvent(e);
                },
              })),
            }}
            trigger={["click"]}
            placement={"bottom"}
          >
            <div className={styles.resultBlock}>
              <div className={styles.text}>{settingVal.read_score.text}</div>
              <ChevronDown size={14} className={styles.ICON} />
            </div>
          </Dropdown>
        </div>
      </Col>
    </Row>
  );
};
export default AreaSetting;
