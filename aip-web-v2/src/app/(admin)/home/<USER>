"use client";
// import AntdProviderLayout from "@/components/AntdProviderLayout";
import { Button, ConfigProvider, theme } from "antd";
import { AntdRegistry } from "@ant-design/nextjs-registry";
// import AntdProvider from "@/components/AntdProvider";
import HomeTitle from "./HomeTitle";
import HomeTextArea from "./HomeTextArea";
import HomeNavs from "./HomeNavs";
// import useStore from "@/store/useLoginStore";
import styles from "./style.module.css";
import { useEffect } from "react";
import { ApartmentOutlined } from "@ant-design/icons";

const HomePage = () => {
  useEffect(() => {
    localStorage.removeItem("initWorkflow");
    return () => {
      localStorage.removeItem("initWorkflow");
    };
  });
  return (
    <ConfigProvider
      theme={{
        algorithm: theme.darkAlgorithm,
        token: {
          controlItemBgActive: "transparent",
          controlItemBgActiveHover: "transparent",
          controlItemBgHover: "transparent",
        },
        components: {
          Splitter: { splitBarSize: 2 },
        },
      }}
    >
      <AntdRegistry>
        <div className="overflow-y-auto h-screen relative flex items-center justify-center">
          <div className={styles.organizationButton}>
            <Button
              variant="outlined"
              icon={<ApartmentOutlined />}
              onClick={() => {
                window.open(`${window.location.origin}/app/organization`);
              }}
            >
              组织架构
            </Button>
          </div>
          <div className={`${styles.container} w-full max-w-[800px] px-4`}>
            <HomeTitle />
            <HomeTextArea />
            <HomeNavs />
          </div>
        </div>
      </AntdRegistry>
    </ConfigProvider>
  );
};
export default HomePage;
