"use client";
import React, { ReactNode, useEffect, useRef, useState } from "react";
import {
  Avatar,
  Button,
  ConfigProvider,
  Dropdown,
  Flex,
  Popover,
  Space,
  Tooltip,
} from "antd";
import AvatarList from "@/components/AvatarList";
import type { MenuProps, GetProp, ButtonProps } from "antd";
import { Attachments, AttachmentsProps, Sender } from "@ant-design/x";
import { differenceInHours } from "date-fns";
import { styleMap } from "@/utils/setting";
import AreaSetting from "./Setting";
import {
  CloudUploadOutlined,
  DeploymentUnitOutlined,
  LinkOutlined,
  PaperClipOutlined,
} from "@ant-design/icons";
import {
  NotebookPen,
  AudioWaveform,
  TextSelect,
  Wand,
  Clock4,
  Globe,
  LandPlot,
  Settings2,
  Share2,
  LibraryBig,
} from "lucide-react";
import { useRouter } from "next/navigation";
import Image from "next/image";
import { getUploadInfo, getPersonalKnowledgeGraph } from "@/service/fetchData";
import { GetToken } from "@/service/config";
import {
  useUserInfoStore,
  useWorkflowStore,
  useGlobalConfigStore,
  useConfigStore,
} from "@/store/store";
import ImageFile from "@/assets/images";
import styles from "./style.module.css";
import { promptConfig } from "@/utils/setting";
import IconSvg from "@/assets/svg";

const { HomeZHCNSvg, HomeCNZHSvg } = IconSvg;

const { Pdf, Doc, Url, Ppt } = ImageFile;
// const menuItemStyles: React.CSSProperties = {
//   color: "rgba(255, 255, 255, 0.85)",
//   padding: "8px 12px",
//   borderRadius: "4px",
//   fontFamily: "PingFang SC, -apple-system, BlinkMacSystemFont, sans-serif",
// };
// // 添加下拉菜单样式配置
// const dropdownStyles: React.CSSProperties = {
//   backgroundColor: "#2a2a2a",
//   padding: "4px",
//   borderRadius: "6px",
//   border: "1px solid rgba(255, 255, 255, 0.1)",
//   boxShadow: "0 6px 16px rgba(0, 0, 0, 0.3)",
// };
const filesList = [
  {
    uid: "2",
    name: "AIPGPT产品介绍.pptx",
    size: "36 k",
    query: "根据AIPGPT产品介绍.pptx，生成一个短视频",
    img: Ppt,
    file: {
      id: 174,
      file_name: "AIPGPT产品介绍.pptx",
      file_name_uuid: "7b9cc08ace5511ef92f03a05baecfa0c.pptx",
      file_type: "pptx",
      file_size: 1433322,
    },
  },
  {
    uid: "1",
    name: "AIPGPT产品介绍.pdf",
    size: "23 k",
    query: "根据AIPGPT产品介绍.pdf，生成一个短视频",
    img: Pdf,
    file: {
      id: 173,
      file_name: "AIPGPT操作手册.pdf",
      file_name_uuid: "7b77dea0ce5511ef92f03a05baecfa0c.pdf",
      file_type: "pdf",
      file_size: 1045052,
    },
  },
  {
    uid: "3",
    name: "https://mp.weixin.qq.com/s/Mo2b0fdU6iPY2Yw8x1wQ5Q",
    size: "微信",
    query: "",
    img: Url,
  },
];
// const UrlCard = ({ item, file }: { item: any; file: (file: any) => void }) => {
//   const startParams = useWorkflowStore((state) => state.startParams);
//   const setTaskKnowledgeParams = useWorkflowStore(
//     (state) => state.setTaskKnowledgeParams
//   );
//   return (
//     <div
//       className={styles.urlCard}
//       onClick={() => {
//         file(item.file);
//         setTaskKnowledgeParams({
//           ...startParams,
//           query: item.query,
//         });
//       }}
//     >
//       <div className={styles.urlCardImg}>
//         <Image src={item.img} alt={item.name} width={25.2} height={32} />
//       </div>
//       <div className={styles.urlCardInfo}>
//         <div className={styles.urlCardTxt}>{item.name ?? item.url}</div>
//         <div className={styles.urlCardSubTxt}>{item.size}</div>
//       </div>
//     </div>
//   );
// };
const BtnPrimaryCss = {
  borderColor: "rgba(64, 123, 255, 0.15)",
  background: "rgba(64, 123, 255, 0.15)",
  color: "#407BFF",
  height: 26,
};
const BtnDefaultCss = {
  borderRadius: 20,
  borderColor: "rgba(64, 123, 255, 0.15)",
  color: "#A3A3A4",
  height: 26,
};
const HomeTextArea = () => {
  const tokenRef = useRef<string>(null);
  const senderRef = useRef<HTMLDivElement>(null);

  const [open, setOpen] = useState(false);
  const [submitDisabled, setSubmitDisabled] = useState(true);
  const [showPKG, setShowPKG] = useState(false);
  const [fileItems, setFileItems] = useState<
    GetProp<AttachmentsProps, "items">
  >([]);

  const router = useRouter();
  // hooks store
  const { homeSetting, updateHomeSetting, updateHomeByPid } =
    useGlobalConfigStore((state) => state);
  const updateNotificationInformation = useConfigStore(
    (state) => state.updateNotificationInformation
  );
  const {
    id: pid,
    remain_point,
    expire_time,
  } = useUserInfoStore((state) => state.currentIpUser);
  // const currentIpUser = useUserInfoStore((state) => state.currentIpUser);
  const { startParams, setTaskKnowledgeParams } = useWorkflowStore(
    (state) => state
  );

  /**
   * sender 头部信息
   */
  const senderHeader = () => {
    return (
      <Sender.Header
        open={open}
        title="上传文件"
        style={{ color: "#666", fontWeight: "bold" }}
        onOpenChange={setOpen}
        styles={{
          content: {
            padding: 0,
          },
        }}
      >
        <Attachments
          name="files"
          accept=".pdf,.doc,.docx"
          style={{ color: "#666", fontWeight: "normal" }}
          action={getUploadInfo()}
          headers={{
            Authorization: `Bearer ${tokenRef.current}`,
          }}
          data={{
            pid: pid,
            emb_type: 1,
          }}
          beforeUpload={(file, fileList) => {
            console.log("file:", file, fileList);
            return true;
          }}
          items={fileItems}
          onChange={(info) => {
            if (info.file.status !== "uploading") {
              console.log(info.file, info.fileList);
            }
            if (info.file.status === "done") {
              console.log("done");
            } else if (info.file.status === "error") {
              console.log("error");
            }
            setFileItems(info.fileList);
          }}
          multiple={false}
          placeholder={(type) =>
            type === "drop"
              ? {
                  title: "点击或拖拽到此上传",
                }
              : {
                  icon: <CloudUploadOutlined style={{ color: "gray" }} />,
                  description: "点击或拖拽到此上传",
                }
          }
          getDropContainer={() => senderRef.current}
        />
      </Sender.Header>
    );
  };
  /**
   * sender 底部操作
   */
  const senderActions = (
    _: React.ReactNode,
    info: {
      components: {
        SendButton: React.ComponentType<ButtonProps>;
        ClearButton: React.ComponentType<ButtonProps>;
        LoadingButton: React.ComponentType<ButtonProps>;
      };
    }
  ) => {
    const { SendButton } = info.components;
    return (
      <div
        className={styles.actionsContainer}
        onClick={(e) => {
          e.stopPropagation();
        }}
      >
        <Popover
          content={() => <AreaSetting />}
          title="设置"
          trigger="click"
          // open={false}
          destroyTooltipOnHide={true}
        >
          <Tooltip placement="bottomLeft" title={"设置"}>
            <Settings2 color="#A3A3A4" size={24} className="cursor-pointer" />
          </Tooltip>
        </Popover>
        <SendButton
          style={{
            background:
              startParams.query || fileItems.length > 0 ? undefined : "#555",
          }}
          disabled={
            submitDisabled && (startParams.query || fileItems.length > 0)
              ? false
              : true
          }
        />
      </div>
    );
  };
  /**
   * prefix 之前操作
   */
  const prefixActions = () => {
    const pidConfig = homeSetting[pid] ?? {
      is_person: 0,
      is_knowledge: 0,
      is_search: 0,
      is_rag: 0,
    };

    return (
      <Space>
        <Button
          shape="round"
          icon={<LandPlot size={12} />}
          size="small"
          variant="outlined"
          style={pidConfig.is_person === 1 ? BtnPrimaryCss : BtnDefaultCss}
          onClick={() => {
            const v = pidConfig.is_person === 0 ? 1 : 0;
            // 更新配置
            updateHomeSetting({
              pid: `${pid}`,
              key: "is_person",
              value: v,
            });
          }}
        >
          结合人设
        </Button>
        <Button
          shape="round"
          icon={<LibraryBig size={12} />}
          size="small"
          variant="outlined"
          style={pidConfig.is_rag === 1 ? BtnPrimaryCss : BtnDefaultCss}
          onClick={() => {
            console;
            const v = pidConfig.is_rag === 0 ? 1 : 0;
            // 更新配置
            updateHomeSetting({
              pid: `${pid}`,
              key: "is_rag",
              value: v,
            });
          }}
        >
          结合知识库
        </Button>
        {showPKG && (
          <Button
            shape="round"
            icon={<Share2 size={12} />}
            size="small"
            variant="outlined"
            style={pidConfig.is_knowledge === 1 ? BtnPrimaryCss : BtnDefaultCss}
            onClick={() => {
              const v = pidConfig.is_knowledge === 0 ? 1 : 0;
              updateHomeSetting({
                pid: `${pid}`,
                key: "is_knowledge",
                value: v,
              });
            }}
          >
            知识图谱
          </Button>
        )}

        <Button
          shape="round"
          icon={<Globe size={12} />}
          size="small"
          variant="outlined"
          style={pidConfig.is_search === 1 ? BtnPrimaryCss : BtnDefaultCss}
          onClick={() => {
            const v = pidConfig.is_search === 0 ? 1 : 0;
            updateHomeSetting({
              pid: `${pid}`,
              key: "is_search",
              value: v,
            });
          }}
        >
          联网搜索
        </Button>
      </Space>
    );
  };
  const updateQuery = (val: string) => {
    setTaskKnowledgeParams({
      ...startParams,
      query: val,
    });
  };
  /**
   * 提交生成口播稿
   */
  const handleSubmit = (query: string | undefined) => {
    let ids;
    // 判断时间是否过期
    if (differenceInHours(new Date(expire_time), new Date()) <= 0) {
      updateNotificationInformation({
        key: Math.random(),
        type: "error",
        message: "IP过期",
        description: "您的IP已经过期，请及时续费",
      });
      return;
    }
    // 判断点数、过期时间
    // if (remain_point <= 0) {
    //   updateNotificationInformation({
    //     key: Math.random(),
    //     type: "error",
    //     message: "点数不足",
    //     description: "您的剩余点数已用完，请及时充值",
    //   });
    //   return;
    // }
    if (fileItems.length > 0) {
      ids = fileItems
        .map((item) => {
          if (item?.response?.code === 200) {
            const data = item.response.data[0];
            if (data) {
              return data.id;
            }
            return "";
          } else {
            return "";
          }
        })
        .filter((item) => item)
        .join(",");
    }
    // 更新风格映射
    const styleVal = styleMap[startParams.style as string] ?? "";
    const params = {
      ...startParams,
      query: query ?? startParams.query,
      pid: pid,
      task_knowledge_ids: ids,
      style: styleVal,
    };
    setTaskKnowledgeParams(params);
    // 设置首次进入工作流
    sessionStorage.setItem("WF_IS_RELOAD", "1");
    setSubmitDisabled(false);
    router.push(`/workflow/detail/query`);
  };
  /** 请求图谱状态 */
  const getPKG = () => {
    getPersonalKnowledgeGraph(pid)
      .then((res) => {
        if (res.code === 200 && res.data.knowledge_graph === true) {
          setShowPKG(true);
          // 更新知识库配置信息
          updateHomeSetting({
            pid: `${pid}`,
            key: "is_knowledge",
            value: 1,
          });
        } else {
          setShowPKG(false);
          // 失败更新为0
          updateHomeSetting({
            pid: `${pid}`,
            key: "is_knowledge",
            value: 0,
          });
        }
      })
      .catch(() => {
        setShowPKG(false);
        // 失败更新为0
        updateHomeSetting({
          pid: `${pid}`,
          key: "is_knowledge",
          value: 0,
        });
      });
  };
  /**
   * 点击文档选择 todo
   */
  // const handleSwitchFile = (file: any) => {
  //   if (file) {
  //     setFileItems([
  //       ...fileItems,
  //       // {
  //       //   uid: "-1",
  //       //   name: "yyy.png",
  //       //   status: "done",
  //       //   url: "https://zos.alipayobjects.com/rmsportal/jkjgkEfvpUPVyRjUImniVslZfWPnJuuZ.png",
  //       // },
  //     ]);
  //   }
  // };
  /**
   * 默认进来更新配置数据
   */
  useEffect(() => {
    if (pid) {
      const settingConfig = homeSetting[pid];
      console.log("初始化配置");
      // 如果没有配置信息，初始化配置信息
      if (settingConfig) {
      } else {
        updateHomeByPid({
          pid: `${pid}`,
          value: {
            language: { key: "zh", label: "中文", text: "中文" },
            doc_length: { key: 270, label: "60s", text: "60s" },
            style: { key: "", label: "默认", text: "默认" },
            read_score: { key: 0, label: "默认", text: "默认" },
            is_knowledge: 0,
            is_person: 1,
            is_search: 0,
            is_rag: 0,
          },
        });
      }
      getPKG();
    }
  }, [pid]);
  // todo
  useEffect(() => {
    tokenRef.current = GetToken();
  }, []);
  // render
  return (
    <Flex vertical align="flex-start" className="w-full">
      <Sender
        submitType="enter"
        classNames={{
          input: "styles.antdInput",
        }}
        value={startParams.query}
        header={senderHeader()}
        style={{ background: "#222227" }}
        styles={{
          input: { minHeight: "60px", marginBottom: "36px" },
          prefix: { position: "absolute", bottom: "12px" },
          actions: { position: "absolute", bottom: "12px", right: "12px" },
        }}
        placeholder="输入你的需求..."
        actions={senderActions}
        prefix={prefixActions()}
        onChange={(val: any) => {
          setTaskKnowledgeParams({ ...startParams, query: val });
        }}
        onSubmit={() => {
          handleSubmit(undefined);
        }}
      />
      <div className="mt-3">
        <Space wrap>
          {promptConfig.map((btn, index) => {
            return (
              <Button
                key={btn.key}
                onClick={() => {
                  if (btn.key === "audio" || btn.key === "audio_synthesis") {
                    updateHomeSetting({
                      pid: `${pid}`,
                      key: "is_person",
                      value: 0,
                    });
                    updateHomeSetting({
                      pid: `${pid}`,
                      key: "is_knowledge",
                      value: 0,
                    });
                    updateHomeSetting({
                      pid: `${pid}`,
                      key: "is_search",
                      value: 0,
                    });
                  } else if (
                    btn.key === "seeting_writing" ||
                    btn.key === "seeting_xs"
                  ) {
                    updateHomeSetting({
                      pid: `${pid}`,
                      key: "is_person",
                      value: 1,
                    });
                    updateHomeSetting({
                      pid: `${pid}`,
                      key: "is_knowledge",
                      value: 0,
                    });
                    updateHomeSetting({
                      pid: `${pid}`,
                      key: "is_search",
                      value: 0,
                    });
                  }
                  updateQuery(btn.formatterTxt);
                }}
              >
                <AvatarList avatar={btn.avatar} label={btn.label} />
              </Button>
            );
          })}
        </Space>
      </div>
      {/* <div className="w-full" style={{ display: "none" }}>
        <div className={styles.fileTitle}>从文档、网址生成短视频</div>
        <div className={styles.cardContent}>
          {filesList.map((item) => {
            return (
              <UrlCard
                item={item}
                key={item.uid}
                file={() => handleSwitchFile}
              />
            );
          })}
        </div>
      </div> */}
    </Flex>
  );
};
export default HomeTextArea;
