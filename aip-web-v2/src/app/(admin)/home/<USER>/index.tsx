import Image from "next/image";
import { Button, Col, Row, Skeleton, Space } from "antd";
import DotLoading from "@/components/DotLoading";
import IconSvg from "@/assets/svg";
import ImageFile from "@/assets/images";
import styles from "./style.module.css";
import { getHotList } from "@/service/fetchData";
import { useUserInfoStore } from "@/store/store";
import { useEffect, useState } from "react";
import { useWorkflowStore, useGlobalConfigStore } from "@/store/store";
const { Douyin, Haokan, TouTiao, Bing, KuaiShou } = ImageFile;
export const HOTICON = (platform_type: string) => {
  switch (platform_type) {
    //抖音
    case "1":
      return Douyin;

    //好看
    case "2":
      return Haokan;

    //快手
    case "3":
      return KuaiShou;

    //必应
    case "4":
      return Bing;

    //头条
    case "5":
      return TouTiao;
  }
};
interface IHotListItem {
  hot_list_name: string;
  hot_title: string;
  href: string;
  platform_type: string;
}

// 抽离骨架屏组件
const SkeletonList = () => (
  <>
    {/* {[...Array(6)].map((_, index) => (
      <Col span={12} key={index}>
        <div className={styles.listItem}>
          <Skeleton.Avatar
            active
            size="small"
            className="mr-[10px]"
            style={
              {
                backgroundColor: "#1a1a1a",
                "--skeleton-color": "#1a1a1a",
                "--skeleton-to-color": "#232323",
              } as React.CSSProperties
            }
          />
          <Skeleton.Input
            active={true}
            size="small"
            block={true}
            style={
              {
                backgroundColor: "#1a1a1a",
                "--skeleton-color": "#1a1a1a",
                "--skeleton-to-color": "#232323",
                margin: 0,
              } as React.CSSProperties
            }
          />
        </div>
      </Col>
    ))} */}
  </>
);

const HomeNavs = () => {
  const [hotList, setHotList] = useState<IHotListItem[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isFirstLoad, setIsFirstLoad] = useState(true);
  const pid = useUserInfoStore((state) => state.currentIpUser.id);
  const updateHomeSetting = useGlobalConfigStore(
    (state) => state.updateHomeSetting
  );
  const setTaskKnowledgeParams = useWorkflowStore(
    (state) => state.setTaskKnowledgeParams
  );
  const startParams = useWorkflowStore((state) => state.startParams);
  const { HomeCircleSvg, HomeArrowUpRightSvg } = IconSvg;

  const getHotListEve = async () => {
    try {
      setIsLoading(true);
      if (!pid) {
        console.warn("No user ID available");
        return;
      }

      // 创建两个 Promise：一个用于数据获取，一个用于最小延迟
      const [response] = await Promise.all([
        getHotList(pid),
        // 添加最小 500ms 的延迟
        new Promise((resolve) => setTimeout(resolve, 500)),
      ]);

      if (response?.data) {
        setHotList(response.data);
      } else {
        console.error("Invalid response format:", response);
      }
    } catch (error) {
      console.error("Failed to fetch hot list:", error);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (pid) {
      setIsLoading(true);
      Promise.all([
        getHotList(pid),
        // 首次加载使用较短的延迟
        new Promise((resolve) => setTimeout(resolve, 300)),
      ])
        .then(([res]) => {
          if (res?.data) {
            setHotList(res.data);
          }
        })
        .finally(() => {
          setIsFirstLoad(false);
          setIsLoading(false);
        });
    }
  }, [pid]);

  return (
    <section>
      <div className={styles.linkHeader}>
        <span>为你推荐的热点</span>
        <HomeCircleSvg
          className={`${styles.linkHeaderIcon} ${
            isLoading || isFirstLoad ? styles.animations : ""
          }`}
          onClick={getHotListEve}
        />
        {/* <div className={styles.moreBtn}>更多</div> */}
      </div>
      <div>
        <Row gutter={[10, 10]}>
          {isFirstLoad ? (
            <div className="min-h-[146px] w-full">
              <DotLoading loadingTxt="正在努力为您搜索中......" />
            </div>
          ) : (
            // <SkeletonList />
            hotList.map((item: IHotListItem, index: number) => (
              <Col span={12} key={index}>
                <div className={styles.listItem}>
                  <span
                    className={styles.txtBlock}
                    onClick={() => {
                      // 选择热点 关闭人设、知识图谱，自动联网搜索设置
                      updateHomeSetting({
                        pid: `${pid}`,
                        key: "is_person",
                        value: 0,
                      });
                      updateHomeSetting({
                        pid: `${pid}`,
                        key: "is_knowledge",
                        value: 0,
                      });
                      updateHomeSetting({
                        pid: `${pid}`,
                        key: "is_search",
                        value: 1,
                      });
                      setTaskKnowledgeParams({
                        ...startParams,
                        query: `根据${item.hot_title}的热点，生成一个短视频`,
                      });
                    }}
                  >
                    <Image
                      onClick={() => {
                        setTaskKnowledgeParams({
                          ...startParams,
                          query: `根据${item.hot_title}的热点，生成一个短视频`,
                        });
                      }}
                      src={HOTICON(item.platform_type) || ""}
                      alt="douyin"
                      className="size-5 mr-[10px]"
                    />
                    <span className={styles.listItemTxt}>{item.hot_title}</span>
                  </span>
                  <span
                    className={styles.linkIcon}
                    onClick={() => window.open(item.href, "_blank")}
                  >
                    <HomeArrowUpRightSvg />
                  </span>
                </div>
              </Col>
            ))
          )}
        </Row>
      </div>
    </section>
  );
};
export default HomeNavs;
