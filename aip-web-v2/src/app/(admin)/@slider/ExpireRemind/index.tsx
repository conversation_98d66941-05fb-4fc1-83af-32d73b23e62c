import { useEffect, useRef, useState } from "react";
import { Modal, Checkbox, Button, App } from "antd";
import { format, differenceInDays, differenceInHours } from "date-fns";
import { useUserInfoStore } from "@/store/store";
import { InfoCircleOutlined } from "@ant-design/icons";
import { secondsToTime } from "@/utils/helper";

const ExpireRemind = () => {
  const expireRef = useRef({
    expireType: "订阅过期提醒",
    expireMessage: "",
  });
  const instanceRef = useRef<any>(null);
  const todayStateRef = useRef(false);
  const [modal, contextHolder] = Modal.useModal();
  const { expire_time, remain_point, id } = useUserInfoStore(
    (state) => state.currentIpUser
  );

  const getNotificationId = () => {
    return `expire-remind-${id}`;
  };
  const waitEvent = () => {
    if (todayStateRef.current) {
      const key = getNotificationId();
      localStorage.setItem(
        key,
        JSON.stringify({
          timestamp: format(new Date(), "yyyy-MM-dd"),
          hidden: true,
        })
      );
    } else {
      const key = getNotificationId();
      localStorage.setItem(
        key,
        JSON.stringify({
          timestamp: "",
          hidden: true,
        })
      );
    }
  };
  const todayHiddenEvent = (val: any) => {
    todayStateRef.current = val.target.checked;
  };
  const shouldShowNotification = () => {
    const stored = localStorage.getItem(getNotificationId());
    if (!stored) return true;

    try {
      const { timestamp, hidden } = JSON.parse(stored);
      const today = format(new Date(), "yyyy-MM-dd");
      return timestamp !== today;
    } catch (error) {
      console.error("Failed to parse notification storage:", error);
      return true;
    }
  };
  const createModal = () => {
    instanceRef.current = modal.warning({
      title: (
        <span style={{ color: "#cc3333" }}>{expireRef.current.expireType}</span>
      ),
      okText: "稍后处理",
      onOk: () => {
        waitEvent();
      },
      content: (
        <div className="mt-[6px]">
          <p>{expireRef.current.expireMessage}</p>
          <p>
            <Checkbox onChange={todayHiddenEvent}>今天不再显示</Checkbox>
          </p>
        </div>
      ),
      icon: <InfoCircleOutlined style={{ color: "#cc3333" }} />,
    });
  };
  const timeMessage = (expireTime: string, remainPoint: number) => {
    const s = shouldShowNotification();
    if (!s) {
      return;
    }
    const today = new Date();
    const expireDate = new Date(expireTime);
    const hoursRemaining = differenceInHours(expireDate, today);
    const daysRemaining = Math.floor(hoursRemaining / 24);
    const remainingHours = hoursRemaining % 24;

    if (hoursRemaining <= 0) {
      expireRef.current.expireType = "订阅过期提醒";
      expireRef.current.expireMessage = "您的订阅已经过期，请及时续费";
      createModal();
      return;
    }
    if (hoursRemaining <= 168) {
      expireRef.current.expireType = "订阅过期提醒";
      expireRef.current.expireMessage =
        daysRemaining > 0
          ? `该IP将在${daysRemaining} 天 ${remainingHours}小时后过期，请及时分配点数`
          : `该IP将在${remainingHours}小时后过期，请及时分配点数`;
      createModal();
      return;
    }
    if (remainPoint <= 0) {
      expireRef.current.expireType = "点数不足提醒";
      expireRef.current.expireMessage = "您的剩余点数已用完，请及时充值";
      createModal();
      return;
    }
    if (remainPoint <= 500) {
      expireRef.current.expireType = "点数不足提醒";
      const timeStr = secondsToTime(remainPoint || 0);
      const [hours, minutes, seconds] = timeStr.split(":").map(Number);
      const timeParts = [];
      if (hours > 0) timeParts.push(`${hours}小时`);
      if (minutes > 0) timeParts.push(`${minutes}分钟`);
      if (seconds > 0) timeParts.push(`${seconds}秒`);
      const formattedTime = timeParts.join("");
      expireRef.current.expireMessage = `该IP剩余 ${remainPoint} 点（还可以累计生成${formattedTime}视频），请及时分配点数`;
      createModal();
      return;
    }
  };
  useEffect(() => {
    if (expire_time !== undefined && remain_point !== undefined) {
      timeMessage(expire_time, remain_point);
    }
  }, [expire_time, remain_point]);
  return <>{contextHolder}</>;
};
export default ExpireRemind;
