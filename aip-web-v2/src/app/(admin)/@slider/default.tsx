"use client";
import { <PERSON><PERSON>, Tooltip } from "@nextui-org/react";
import ExpireRemind from "./ExpireRemind";
import SlideHeader from "./SlideHeader";
import SlideMenu from "./SlideMenu";
import styles from "./style.module.css";
import IconSvg from "@/assets/svg";
import { Dispatch, FC, SetStateAction, useState } from "react";
interface SliderProps {
  setCollapse: Dispatch<SetStateAction<boolean>>;
  collapse: boolean;
}
const SliderLine: FC<SliderProps> = ({ setCollapse, collapse }) => {
  const { SideNavRight } = IconSvg;
  return (
    <div className={styles.scrollContent}>
      <div className={styles.scrollLine}></div>
      <div className={styles.collapseIcon}>
        <SideNavRight
          className={`${styles.collapseIconItem} ${
            collapse ? styles.rotateIcon : ""
          }`}
          onClick={() => setCollapse(!collapse)}
        />
      </div>
    </div>
  );
};
const SliderContainer = () => {
  const [collapse, setCollapse] = useState(false);
  return (
    <>
      <section
        className={`${styles.sliderbar} ${
          collapse ? styles.sliderbarCollapse : ""
        }`}
      >
        <div className={styles.sliderContent}>
          <SlideHeader collapse={collapse} />
          <SlideMenu collapse={collapse} />
        </div>
        <SliderLine collapse={collapse} setCollapse={setCollapse} />
        <ExpireRemind/>
      </section>
    </>
  );
};

export default SliderContainer;
