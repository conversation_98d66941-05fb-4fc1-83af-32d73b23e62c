.sliderbar {
  position: relative;
  display: flex;
  flex-direction: row;
  min-width: 238px;
  height: 100%;
  flex-grow: 0;
  flex-shrink: 0;
  border-right: 1px solid #3f3f46;
  background: #09090b;
  transition: all 0.2s ease;
}
.rotateIcon {
  transform: rotate(180deg);
  transition: transform 0.3s ease;
}
.sliderContent {
  position: absolute;
  z-index: 12;
  padding: 10px 7px 10px 10px;
  height: 100%;
  width: calc(100% - 3px);
  top: 0;
  left: 0;
  flex-grow: 1;
  background: #09090b;
}

.scrollContent {
  position: relative;
  z-index: 11;
  height: 100%;
  width: 100%;
  top: 0;
  left: 0;
}

.scrollLine {
  position: relative;
  width: 100%;
  height: 20px;
  transform: scaleY(10000);
  overflow: scroll;
  resize: horizontal;
  min-width: 238px;
  max-width: 430px;
  opacity: 0;
}
.collapseIcon {
  display: flex;
  cursor: pointer;
  position: absolute;
  height: 100%;
  width: 20px;
  right: -25px;
  top: 0;
  align-items: center;
}
.collapseIconItem {
  visibility: hidden;
  opacity: 0;
}
.scrollLine::-webkit-resizer {
  background-color: #3f3f46;
}

.scrollLine:hover {
  opacity: 1;
}
.scrollLine:hover + .collapseIcon .collapseIconItem,
.collapseIcon:hover .collapseIconItem {
  visibility: visible;
  opacity: 1;
}

.sliderbarCollapse {
  width: 60px;
  min-width: 60px;
}
.sliderbarCollapse .scrollLine {
  display: none;
}
