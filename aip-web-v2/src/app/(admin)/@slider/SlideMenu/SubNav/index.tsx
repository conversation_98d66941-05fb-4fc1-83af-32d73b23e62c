"use client";
import { getLatesTask } from "@/service/fetchData";
import styles from "./style.module.css";
import { useUserInfoStore, useWorkflowStore } from "@/store/store";
import { GetToken } from "@/service/config";
import { useEffect, useRef, useState } from "react";
import { useRouter } from "next/navigation";
import { CurrentIpInformation } from "@/typing/types";
import { Loader } from "lucide-react";

const SubNav = () => {
  const [list, setList] = useState([]);
  const timerRef = useRef<any>(null);
  const currentIpUserRef = useRef<CurrentIpInformation>(
    {} as CurrentIpInformation
  );
  const { currentIpUser, updateCurrentIpUser } = useUserInfoStore(
    (state) => state
  );
  const taskId = useWorkflowStore((state) => state.taskId);
  const [td, setTd] = useState(0);
  const router = useRouter();
  const getLatesList = async () => {
    const res = await getLatesTask({ pid: currentIpUserRef.current?.id });
    if (res.code === 200) {
      // todo
      if (Array.isArray(res.data)) {
        setList(res.data || []);
      } else {
        setList(res.data.works || []);
        // 如果点数 和 时间没有发生变更则不更新用户数据
        console.log(
          res.data,
          currentIpUserRef.current,
          res.data?.video_point !== currentIpUserRef.current?.remain_point ||
            res.data?.expire_time !== currentIpUserRef.current?.expire_time
        );
        if (
          res.data?.video_point !== currentIpUserRef.current?.remain_point ||
          res.data?.expire_time !== currentIpUserRef.current?.expire_time
        ) {
          updateCurrentIpUser({
            ...currentIpUserRef.current,
            remain_point: res.data?.video_point,
            expire_time: res.data?.expire_time,
          });
        }
      }
    } else {
      setList([]);
    }
    timerRef.current = setTimeout(() => {
      if (timerRef.current) {
        clearTimeout(timerRef.current);
        timerRef.current = null;
        getLatesList();
      }
    }, 5000);
  };
  useEffect(() => {
    setTd(taskId);
  }, [taskId]);
  useEffect(() => {
    if (currentIpUser?.id) {
      currentIpUserRef.current = currentIpUser;
      if (GetToken()) {
        getLatesList();
      }
      return () => {
        timerRef.current = null;
        clearTimeout(timerRef.current);
      };
    }
  }, [currentIpUser?.id]);
  return (
    <div className={styles.subMenuContent}>
      {list.map((item: any) => {
        return (
          <div
            className={`${styles.label} ${
              td === item.id ? styles.labelActive : ""
            } ${styles.hoverEffect}`} // 添加hoverEffect样式
            key={item.id}
            onClick={() => {
              setTd(item.id);
              router.push(`/workflow/detail/${item.id}`);
            }}
          >
            {item.title}
          </div>
        );
      })}
    </div>
  );
};

export default SubNav;
