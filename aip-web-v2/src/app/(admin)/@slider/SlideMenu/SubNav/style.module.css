.subMenuContent {
  position: relative;
  width: 100%;
  overflow: hidden;
}

.subMenuContent::after {
  content: "";
  position: absolute;
  z-index: 10;
  top: 0;
  right: 0;
  width: 30%;
  height: 100%;
  background-image: linear-gradient(to right, rgba(9, 9, 11, 0), #09090b);
  pointer-events: none;
}

.label {
  position: relative;
  font-size: 12px;
  font-style: normal;
  font-weight: 500;
  line-height: 30px;
  white-space: nowrap;
  cursor: pointer;
  /* 继承全局字体设置 */
  font-family: inherit;
  cursor: pointer;
  /* 优化文本渲染 */
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
.hoverEffect {
  padding: 0 0;
  transition: padding 0.2s ease, color 0.2s ease;
}
.hoverEffect:hover {
  color: #fff;
  cursor: pointer;
  padding: 0 8px;
}
.labelActive {
  background-color: rgba(255, 255, 255, 0.25);
  border-radius: 5px;
  color: #fff;
  padding: 0 8px;
}
.navState {
  position: absolute;
  z-index: 1;
  top: 0;
  left: -36px;
}
