"use client";
import { FC, useEffect, useState } from "react";
// import IconSvg from "@/assets/svg";
import { Tooltip } from "@nextui-org/tooltip";
import SubNav from "./SubNav";
import { useRouter, usePathname } from "next/navigation";
import styles from "./style.module.css";
import {
  HousePlus,
  Workflow,
  SquarePlay,
  SquareLibrary,
  Package,
  LandPlot,
} from "lucide-react";
import { useIPPersonalStore, useUserInfoStore } from "@/store/store";
import { App } from "antd";

const MenuArr = [
  {
    icon: HousePlus,
    titleName: "首页",
    key: "home",
    path: "/home",
    // collapsed: collapse,
    activePath: ["/home"],
  },
  {
    icon: Workflow,
    titleName: "工作流",
    key: "workflow/list",
    path: "/workflow/list",
    // collapsed: collapse,
    activePath: ["/workflow/list", "/workflow/detail"],
  },
  {
    icon: SquarePlay,
    titleName: "作品",
    key: "works",
    path: "/works",
    // collapsed: collapse,
    activePath: ["/works"],
  },
  {
    icon: LandPlot,
    titleName: "IP人设",
    key: "ipPersonal",
    path: "/ipPersonal",
    // collapsed: collapse,
    activePath: ["/ipPersonal"],
  },
  {
    icon: SquareLibrary,
    titleName: "知识库",
    key: "knowledgeBase",
    path: "/knowledgeBase",
    // collapsed: collapse,
    activePath: ["/knowledgeBase"],
  },
  {
    icon: Package,
    titleName: "私有模型",
    key: "privateModel",
    path: "/privateModel",
    // collapsed: collapse,
    activePath: ["/privateModel"],
  },
];

interface AIPMenuItemProps {
  icon: any;
  titleName: string;
  key: string;
  path: string;
  collapsed?: boolean;
  activePath?: string[];
  chlidren?: AIPMenuItemProps[];
}
const AIPMenuItem: FC<{ item: AIPMenuItemProps; colapse?: boolean }> = ({
  item,
  colapse,
}) => {
  const router = useRouter();
  const pathname = usePathname();
  const { handlePageLeave } = useIPPersonalStore();

  const pathnameSrarch = (ph: string) => {
    let d = ph;
    if (pathname.includes("/workflow/detail")) {
      d = "/workflow/detail";
    }
    return item.activePath?.includes(d);
  };

  const goto = async (pathUrl: string) => {
    // 如果在 IP 人设页面，且有 handlePageLeave 函数
    if (pathname == "/ipPersonal" && handlePageLeave) {
      const canLeave = await handlePageLeave(pathUrl);
      if (!canLeave) return;
    }
    // 不管是否在 IP 人设页面，都应该执行路由跳转
    router.push(pathUrl);
  };
  const defaultItem = () => {
    return (
      <>
        {!colapse && (
          <div className={styles.sliderMain}>
            <div
              className={`${styles.menuItem} ${
                pathnameSrarch(pathname) ? styles.active : styles.hoverEffect
              }`}
              onClick={() => goto(item.path)}
            >
              <div className={styles.aipIcon}>
                <item.icon className="size-5" />
              </div>
              <div>
                <span>{item.titleName}</span>
              </div>
            </div>
            {item.path === "/workflow/list" && (
              <div className={styles.subMenuItem}>{<SubNav />}</div>
            )}
          </div>
        )}
        {colapse && (
          <div className={styles.sliderMain}>
            <div
              className={`${styles.menuItem} ${
                pathnameSrarch(pathname) ? styles.active : styles.hoverEffect
              }`}
              onClick={() => goto(item.path)}
            >
              <Tooltip content={item.titleName} placement="right" offset={20}>
                <div className={styles.aipIcon}>
                  <item.icon className="size-5" />
                </div>
              </Tooltip>
            </div>
          </div>
        )}
      </>
    );
  };
  return <>{defaultItem()}</>;
};

const AIPSlideMenu = ({ collapse }: { collapse: boolean }) => {
  const [menuList, setMenuList] = useState<AIPMenuItemProps[]>([]);
  useEffect(() => {
    setMenuList(MenuArr);
  }, []);
  return (
    <div className="mt-6">
      {menuList.map((item, index) => {
        return <AIPMenuItem item={item} key={index} colapse={collapse} />;
      })}
    </div>
  );
};

export default AIPSlideMenu;
