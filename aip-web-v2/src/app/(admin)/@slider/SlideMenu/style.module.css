.sliderMain {
  height: 100%;
  flex: 1 1 auto;
  overflow: auto;
}
.menuItem {
  display: flex;
  padding: 15px 10px;
  align-items: center;
  gap: 5px;
  color: #a1a1aa;
  /* 继承全局字体设置 */
  font-family: inherit;
  font-size: 16px;
  font-style: normal;
  font-weight: 600;
  line-height: 20px;
  cursor: pointer;
  /* 优化文本渲染 */
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
.menuItem:hover {
  border-radius: 6px;
  background: rgba(39, 39, 42, 0.6);
}
.menuItem.active {
  color: white;
}
.aipIcon {
  width: 20px;
}
.subMenuItem {
  color: #a1a1aa;
  padding-left: 28px;
  /* 继承全局字体设置 */
  font-family: inherit;
}
