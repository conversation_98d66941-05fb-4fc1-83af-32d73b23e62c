import { useUserInfoStore } from "@/store/store";
import { Avatar } from "@nextui-org/react";
import { Check } from "lucide-react";
import styles from "./style.module.css";
import { CurrentIpInformation } from "@/typing/types";
import { useRouter } from "next/navigation";
import { getLatesTask } from "@/service/fetchData";

// 账户下用户列
const AccountList = () => {
  const router = useRouter();
  const updateCurrentIpUser = useUserInfoStore(
    (state) => state.updateCurrentIpUser
  );
  const currentIpUser = useUserInfoStore((state) => state.currentIpUser);
  const currentIpList = useUserInfoStore((state) => state.currentIpList);
  const chooseIp = (currentInformation: CurrentIpInformation) => {
    localStorage.setItem("initWorkflow", "false");
    getLatesTask({ pid: currentInformation.id })
      .then((res) => {
        if (res.code == 200) {
          if (res.data?.expire_time) {
            updateCurrentIpUser({
              ...currentInformation,
              remain_point: res.data?.video_point,
              expire_time: res.data?.expire_time,
            });
          } else {
            updateCurrentIpUser({
              ...currentInformation,
              remain_point: res.data?.video_point,
            });
          }
        }
      })
      .finally(() => {
        router.replace("/home");
      });
  };
  return (
    <>
      {currentIpList?.map((item: any) => {
        return (
          <div
            className={styles.accountItem}
            key={item.id}
            onClick={() => {
              // 判断当前用户 非 当前选择用户 不切换
              if (item.id !== currentIpUser.id) {
                chooseIp(item);
              }
            }}
          >
            <Avatar src="" className="w-6 h-6 text-tiny shrink-0 grow-0" />
            <div className={styles.label}>{item.ip_name}</div>
            <Check
              className="size-4 stroke-[#00C800] shrink-0 grow-0"
              strokeWidth={3}
              style={{ opacity: item?.id === currentIpUser?.id ? 1 : 0 }}
            />
          </div>
        );
      })}
    </>
  );
};
export default AccountList;
