.accountItem {
  display: flex;
  gap: 6px;
  padding: 13px;
  align-items: center;
}
.accountItem .label {
  flex-grow: 1;
  flex-shrink: 1;
  /* 继承全局字体设置 */
  font-family: inherit;
  font-size: 12px;
  line-height: 20px;
  font-weight: 500;
  color: #ecedee;
  text-wrap: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.accountItem:hover {
  background: rgba(0, 0, 0, 0.2);
  cursor: pointer;
}
.title {
  color: rgba(255, 255, 255, 0.9);
  /* 继承全局字体设置 */
  font-family: inherit;
  font-size: 14px;
  line-height: 18px;
  font-weight: 600;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  /* 增强文本渲染效果 */
  text-rendering: optimizeLegibility;
}
