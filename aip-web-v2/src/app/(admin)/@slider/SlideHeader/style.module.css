.container {
  position: relative;
  width: 100%;
  min-height: 60px;
  user-select: none;
}

.main {
  position: absolute;
  z-index: 20;
  width: 100%;
  min-height: 60px;
  border-radius: 6px;
  background: #09090b;
}

.header {
  display: flex;
  align-items: center;
  padding: 10px;
  cursor: pointer;
  gap: 10px;
  width: 100%;
}

.info {
  flex-shrink: 1;
  flex-grow: 1;
  width: calc(100% - 84px);
}

.loginTxt {
  color: rgba(255, 255, 255, 0.9);
  /* 继承全局字体设置 */
  font-family: inherit;
  font-size: 16px;
  line-height: 18px;
  font-weight: 600;
  width: 100%;
  letter-spacing: 0.02em; /* 轻微调整字距提升可读性 */
}

.title {
  color: rgba(255, 255, 255, 0.9);
  /* 继承全局字体设置 */
  font-family: inherit;
  font-size: 14px;
  line-height: 18px;
  font-weight: 600;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  /* 增强文本渲染效果 */
  text-rendering: optimizeLegibility;
}

.subTitle {
  display: flex;
  align-items: center;
  margin-top: 6px;
  gap: 3px;
}

.subTitleText {
  color: rgba(255, 255, 255, 0.9);
  /* 继承全局字体设置 */
  font-family: inherit;
  font-size: 12px;
  line-height: 14px;
  font-weight: 500;
}

.more {
  display: flex;
  justify-content: center;
  align-items: center;
  flex: 0 0 auto;
  width: 16px;
  cursor: pointer;
}

.more:hover {
  opacity: 0.7;
}

.listContent {
  overflow: hidden;
  transition: height 0.2s ease;
}

.listItem {
  color: #a1a1aa;
  padding: 16px 13px;
  display: flex;
  align-items: center;
  gap: 8px;
  border-top: 1px solid rgba(229, 231, 235, 0.08);
}

.listItem > span {
  flex: 1 1 auto;
  /* 继承全局字体设置 */
  font-family: inherit;
  font-size: 14px;
  font-weight: 700;
  line-height: 20px;
}

.accountList {
  background-color: #26262c;
  padding: 0px;
  border-top: 1px solid rgba(229, 231, 235, 0.08);
}

.accountItem {
  display: flex;
  gap: 6px;
  padding: 13px;
  align-items: center;
}

.accountItem .label {
  flex-grow: 1;
  flex-shrink: 1;
  /* 继承全局字体设置 */
  font-family: inherit;
  font-size: 12px;
  line-height: 20px;
  font-weight: 500;
  color: #ecedee;
  text-wrap: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.accountItem:hover {
  background: rgba(0, 0, 0, 0.2);
  cursor: pointer;
}

.listItem:hover {
  background: rgba(0, 0, 0, 0.2);
  cursor: pointer;
}
