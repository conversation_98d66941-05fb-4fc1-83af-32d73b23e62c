"use client";
import { useEffect, useRef, useState } from "react";
// import { Avatar } from "@nextui-org/react";
import { ChevronDown, Loader, CirclePlus, Crown, LogOut } from "lucide-react";
// import IconSvg from "@/assets/svg";
import IpManageModal from "@/components/IpManageModal";
import AddIPModal from "@/components/AddIPModal";
import AccountList from "./AccountList";
import LoginHead from "./LoginHead";
import NoLogin from "./NoLogin";
import useUser from "@/hooks/useUserinfo";
import useIp from "@/hooks/useIp";
import styles from "./style.module.css";
import { useLoginStore, useUserInfoStore } from "@/store/store";
// const { SideNavSDSvg } = IconSvg;

// IP 数据
// interface IpUser {
//   id: string;
//   ip_name: string;
//   remain_point?: number;
// }
// 头像接口
// interface HeaderProps {
//   collapse: boolean;
//   moreStatue: boolean;
//   setMoreStatue: (value: boolean) => void;
//   isLoading?: boolean;
//   currentIpUser?: IpUser;
//   eleRef: React.RefObject<HTMLDivElement>;
// }

// HeaderLayout 组件拆分出来
// const HeaderLayout = ({
//   collapse,
//   children,
// }: {
//   collapse: boolean;
//   children: React.ReactNode;
// }) => (
//   <div
//     className={`${styles.header} ${
//       collapse ? "" : "bg-[#14141a] rounded-[6px]"
//     }`}
//     style={{
//       padding: collapse ? "10px 0" : "10px",
//       background: collapse ? "transparent" : undefined,
//     }}
//   >
//     <Avatar size="md" className="shrink-0 grow-0" />
//     {!collapse && children}
//   </div>
// );

// 添加用户头部信息组件
// const UserHeader = ({
//   collapse,
//   moreStatue,
//   setMoreStatue,
//   isLoading,
//   currentIpUser,
//   eleRef,
// }: HeaderProps) => {
//   return (
//     <div ref={eleRef} onClick={() => !collapse && setMoreStatue(!moreStatue)}>
//       <HeaderLayout collapse={collapse}>
//         <>
//           <div className={styles.info}>
//             <div className={styles.title}>{currentIpUser?.ip_name}</div>
//             <div className={styles.subTitle}>
//               <IconSvg.SideNavSDSvg className="size-[14px]" />
//               <span className={styles.subTitleText}>
//                 {currentIpUser?.remain_point || 0}
//               </span>
//             </div>
//           </div>
//           <div className={styles.more}>
//             {isLoading ? (
//               <Loader
//                 className="size-4 text-gray-500 animate-spin"
//                 strokeWidth={2}
//               />
//             ) : (
//               <ChevronDown
//                 className={`size-4 text-gray-500 transition-transform duration-300 ${
//                   moreStatue ? "rotate-180" : ""
//                 }`}
//                 strokeWidth={3}
//               />
//             )}
//           </div>
//         </>
//       </HeaderLayout>
//     </div>
//   );
// };

const AIPSlideHeader = ({ collapse }: { collapse: boolean }) => {
  const { LogoutEve } = useUser();
  const { setCurrentIpUser } = useIp();

  const [moreStatue, setMoreStatue] = useState(false);
  const [IpManageOpen, setIpManageOpen] = useState(false);
  const [addIpManageOpen, setAddIpManageOpen] = useState(false);
  const isLogin = useLoginStore((state) => state.isLogin);
  const currentIpUser = useUserInfoStore((state) => state.currentIpUser);
  const currentIpList = useUserInfoStore((state) => state.currentIpList);

  // 修改chooseIp函数
  const chooseIp = (item: any) => {
    setCurrentIpUser(item);
  };

  useEffect(() => {
    // 登录状态下，如果没有用户状态下，默认显示第一个
    if (isLogin && currentIpList?.length > 0 && !currentIpUser?.ip_name) {
      console.log("login--------------");
      chooseIp(currentIpList[0]);
    }
  }, [isLogin]);
  return (
    <section className={styles.container}>
      <div
        className={styles.main}
        style={
          collapse
            ? {
                background: "transparent",
              }
            : {
                background: "#27272a",
              }
        }
      >
        {isLogin ? (
          <LoginHead
            moreStatue={moreStatue}
            setMoreStatue={setMoreStatue}
            collapse={collapse}
          />
        ) : (
          <NoLogin moreStatue={moreStatue} collapse={collapse} />
        )}

        <ul
          className={styles.listContent}
          style={{
            height: moreStatue ? "auto" : "0px",
          }}
        >
          <li className={styles.accountList}>
            <AccountList />
          </li>
          {currentIpList?.length <= 0 && (
            <li className={styles.listItem}>
              <span>个人版</span>
              <CirclePlus color="#fff" size="16" />
            </li>
          )}

          <li className={styles.listItem} onClick={() => setIpManageOpen(true)}>
            <span>IP管理</span>
            <CirclePlus color="#fff" size="16" />
          </li>
          {currentIpList?.length > 0 && (
            <li className={styles.listItem}>
              <span>团队版</span>
              <Crown color="#fff" size="16" />
            </li>
          )}

          <li
            className={styles.listItem}
            onClick={() => {
              setMoreStatue(false);
              LogoutEve();
            }}
          >
            <span>退出登录</span>
            <LogOut color="#fff" size="16" />
          </li>
        </ul>
      </div>

      <IpManageModal isOpen={IpManageOpen} setOpen={setIpManageOpen} />
      <AddIPModal isOpen={addIpManageOpen} setOpen={setAddIpManageOpen} />
    </section>
  );
};
export default AIPSlideHeader;
