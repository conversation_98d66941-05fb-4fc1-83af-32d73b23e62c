import { Avatar } from "@nextui-org/react";
import { ChevronDown } from "lucide-react";
import styles from "./style.module.css";
import { FC } from "react";
interface LoginNavProps {
  moreStatue: boolean;
  collapse: boolean;
}
const NoLogin: FC<LoginNavProps> = ({ moreStatue, collapse }) => {
  return (
    <div
      className={`${styles.header} ${
        moreStatue ? "" : "bg-[#14141a] rounded-[6px]"
      }`}
      style={
        collapse
          ? {
              padding: "10px 0",
            }
          : {
              padding: "10px",
            }
      }
    >
      <Avatar size="md" className="shrink-0 grow-0" />
      {!collapse && (
        <>
          <div className={styles.info}>
            <div className={styles.title}>立即登录</div>
          </div>
          <div className={styles.more}>
            <ChevronDown
              className={`size-4 text-gray-500 transition-transform duration-300 -rotate-90`}
              strokeWidth={3}
            />
          </div>
        </>
      )}
    </div>
  );
};
export default NoLogin;
