import { Dispatch, FC, SetStateAction, useEffect, useRef } from "react";
import { useUserInfoStore } from "@/store/store";
import { secondsToTime } from "@/utils/helper";
import { ChevronDown, Loader } from "lucide-react";
import styles from "./style.module.css";
import IconSvg from "@/assets/svg";
import { Avatar } from "@nextui-org/react";
import { differenceInHours } from "date-fns";
import { Tooltip } from "antd";

const { SideNavSDSvg, SideClockSvg } = IconSvg;
interface LoginHeadProps {
  collapse: boolean;
  moreStatue: boolean;
  setMoreStatue: Dispatch<SetStateAction<boolean>>;
}
const LoginHead: FC<LoginHeadProps> = ({
  moreStatue,
  setMoreStatue,
  collapse,
}) => {
  const currentIpUser = useUserInfoStore((state) => state.currentIpUser);
  const today = new Date();
  const expireDate = new Date(currentIpUser?.expire_time || 0);
  const hoursRemaining = differenceInHours(expireDate, today);
  const daysRemaining = Math.floor(hoursRemaining / 24);
  const remainingHours = hoursRemaining % 24;

  const eleRef = useRef<HTMLDivElement>(null); // 获取元素
  // function secondsToTime(secs: number) {
  //   var date = new Date(null);
  //   date.setSeconds(secs);
  //   return date.toISOString().substr(11, 8); // "HH:MM:SS"
  // }
  // 控制点击其他地方收缩下拉
  useEffect(() => {
    const eve = function (e: any) {
      if (eleRef.current) {
        const rect = eleRef.current?.getBoundingClientRect();
        const x = e.clientX;
        const y = e.clientY;
        if (
          rect?.left <= x &&
          x <= rect?.right &&
          rect.top <= y &&
          y <= rect?.bottom
        ) {
          return;
        }
      }
      setMoreStatue(false);
    };
    document.addEventListener("click", eve);
    return () => {
      document.removeEventListener("click", eve);
    };
  }, []);
  return (
    <div
      className={`${styles.header} ${
        moreStatue ? "" : "bg-[#14141a] rounded-[6px]"
      }`}
      style={
        collapse
          ? { padding: "10px 0", background: "transparent" }
          : { padding: "10px" }
      }
      ref={eleRef}
      onClick={() => {
        if (collapse) return;
        setMoreStatue(true);
      }}
    >
      <Avatar size="md" className="shrink-0 grow-0" />
      {!collapse && (
        <>
          <div className={styles.info}>
            <div className={styles.title}>
              {currentIpUser?.ip_name || "选择IP"}
            </div>
            <Tooltip
              placement="right"
              title={`该点数用于生成视频时消耗：\n 3600点=3600s=60分钟`}
            >
              <div className={styles.subTitle}>
                <SideNavSDSvg className="size-[14px]" />
                <span className={styles.subTitleText}>
                  {currentIpUser?.remain_point || 0} 点
                </span>
              </div>
              <div className={styles.subTitle}>
                <SideClockSvg className="size-[14px]" />
                <span className={styles.subTitleText}>
                  {daysRemaining > 0 ? daysRemaining : 0}天
                  {remainingHours > 0 ? remainingHours : 0}小时
                </span>
              </div>
            </Tooltip>
          </div>
          <div className={styles.more}>
            <ChevronDown
              className={`size-4 text-gray-500 transition-transform duration-300 ${
                moreStatue ? "rotate-180" : ""
              }`}
              strokeWidth={3}
            />
            {/* {isLoading ? (
              <Loader
                className="size-4 text-gray-500 animate-spin"
                strokeWidth={2}
              />
            ) : (
              <ChevronDown
                className={`size-4 text-gray-500 transition-transform duration-300 ${
                  moreStatue ? "rotate-180" : ""
                }`}
                strokeWidth={3}
              />
            )} */}
          </div>
        </>
      )}
    </div>
  );
};
export default LoginHead;
