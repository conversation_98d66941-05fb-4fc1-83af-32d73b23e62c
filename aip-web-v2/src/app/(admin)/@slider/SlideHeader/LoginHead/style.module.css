.header {
  display: flex;
  align-items: center;
  padding: 10px;
  cursor: pointer;
  gap: 10px;
  width: 100%;
}
.info {
  flex-shrink: 1;
  flex-grow: 1;
  width: calc(100% - 84px);
}
.title {
  color: rgba(255, 255, 255, 0.9);
  /* 继承全局字体设置 */
  font-family: inherit;
  font-size: 14px;
  line-height: 18px;
  font-weight: 600;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  /* 增强文本渲染效果 */
  text-rendering: optimizeLegibility;
}
.subTitle {
  display: flex;
  align-items: center;
  margin-top: 6px;
  gap: 3px;
  font-size: 12px;
}

.subTitleText {
  color: rgba(255, 255, 255, 0.9);
  /* 继承全局字体设置 */
  font-family: inherit;
  line-height: 14px;
  font-weight: 500;
}
.more {
  display: flex;
  align-items: center;
  width: 16px;
  cursor: pointer;
}

.more:hover {
  opacity: 0.7;
}
