"use client";
import { useEffect, useState } from "react";
import { Button } from "@nextui-org/react";
import { useRouter } from "next/navigation";
import { IWorkData } from "../../../typing/Works";
import SearchFilter from "./components/SearchFilter";
import VideoCard from "./components/VideoCard";
import { useUserInfoStore } from "../../../store/store";
import { getWork, deleteWork } from "../../../service/fetchData";

interface WorksState {
  query: string;
  pid: number;
  date: {
    start: string | undefined;
    end: string | undefined;
  };
}

export default function WorksPage() {
  const currentIpUser = useUserInfoStore((state: any) => state.currentIpUser);
  const [workList, setWorkList] = useState<IWorkData[]>([]);
  const [filteredWorkList, setFilteredWorkList] = useState<IWorkData[]>([]);
  const [params, setParams] = useState<WorksState>({
    pid: currentIpUser.id,
    date: {
      start: undefined,
      end: undefined,
    },
    query: "",
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const router = useRouter();

  // 过滤视频类型的作品
  useEffect(() => {
    setFilteredWorkList(workList.filter((item) => item.work_type === 3));
  }, [workList]);

  const deleteWorkEvent = async (id: number) => {
    try {
      // 乐观更新：立即从本地状态移除
      setWorkList((prev) => prev.filter((item) => item.id !== id));

      // 后端删除
      const res = await deleteWork(id);
      if (res?.code !== 200) {
        throw new Error("删除失败");
      }
    } catch (error) {
      // 如果删除失败，恢复原始数据并显示错误
      getFallAllData();
      setError(error instanceof Error ? error.message : "删除失败");
      // 3秒后清除错误信息
      setTimeout(() => setError(null), 3000);
    }
  };

  const getFallAllData = async () => {
    setLoading(true);
    try {
      const scope = {
        pid: currentIpUser.id,
        date_from: params.date?.start,
        date_to: params.date?.end,
        search_term: params.query,
      };
      const res = await getWork(scope);
      if (res?.code === 200) {
        setWorkList(res.data);
      }
    } catch (error) {
      console.error("Failed to fetch works:", error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (currentIpUser.id) {
      getFallAllData();
    }
  }, [currentIpUser.id, params.date, params.query]);

  const handleGoCreate = () => {
    router.push("/home");
  };

  return (
    <div className="min-h-screen bg-[#18181B]">
      {/* 搜索和筛选 */}
      <SearchFilter
        query={params?.query}
        date={params?.date}
        onChangeQuery={(query) => setParams({ ...params, query })}
        onSelectDate={(date) => setParams({ ...params, date })}
      />

      <main className="container mx-auto px-6 py-8">
        {loading ? (
          <div className="grid grid-cols-[repeat(auto-fit,minmax(240px,1fr))] gap-6 animate-pulse">
            {[...Array(8)].map((_, i) => (
              <div key={i} className="aspect-video bg-white/5 rounded-lg"></div>
            ))}
          </div>
        ) : workList?.length <= 0 ? (
          <div className="flex flex-col items-center justify-center py-20">
            <p className="text-white/60 text-lg mb-4">暂无任何作品</p>
            <Button
              className="bg-blue-500 hover:bg-blue-600 text-white"
              onPress={handleGoCreate}
            >
              去创建
            </Button>
          </div>
        ) : (
          <>
            {error && (
              <div className="fixed top-4 right-4 bg-red-500 text-white px-4 py-2 rounded shadow-lg z-50 animate-fade-in">
                {error}
              </div>
            )}
            <div className="grid grid-cols-[repeat(auto-fit,minmax(240px,1fr))] gap-6 auto-rows-fr relative isolation-auto">
              {filteredWorkList.map((item) => (
                <div className=" max-w-[360px]" key={item.id}>
                  <VideoCard
                    item={item}
                    onDelete={() => deleteWorkEvent(item.id)}
                  />
                </div>
              ))}
            </div>
          </>
        )}
      </main>
    </div>
  );
}
