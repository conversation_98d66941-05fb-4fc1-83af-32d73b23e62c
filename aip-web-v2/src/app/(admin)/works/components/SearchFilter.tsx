import { FC } from 'react';
import { Input, DatePicker, ConfigProvider, theme, Popover } from 'antd';
import dayjs from 'dayjs';
import 'dayjs/locale/zh-cn';
import locale from 'antd/locale/zh_CN';
import { X } from 'lucide-react';
import { SearchIcon, CalendarDays } from 'lucide-react';

const { RangePicker } = DatePicker;

interface SearchFilterProps {
  query: string;
  date: {
    start: string | undefined;
    end: string | undefined;
  };
  onChangeQuery: (query: string) => void;
  onSelectDate: (date: { start: string | undefined; end: string | undefined }) => void;
}

const darkThemeConfig = {
  algorithm: theme.darkAlgorithm,
  token: {
    colorBgElevated: '#333333',
    boxShadow: '0 4px 12px rgba(0, 0, 0, 0.5)',
    borderRadius: 8,
    colorBorder: 'transparent',
    colorBgContainer: '#27272A'
  }
};

export const SearchFilter: FC<SearchFilterProps> = ({
  query,
  date,
  onChangeQuery,
  onSelectDate,
}) => {
  const renderDateContent = () => (
    <ConfigProvider theme={darkThemeConfig}>
      <div className="min-w-[360px]">
        <div className="flex flex-wrap gap-2 mb-4">
          <button
            onClick={() => {
              const today = dayjs();
              onSelectDate({
                start: today.format('YYYY-MM-DD'),
                end: today.format('YYYY-MM-DD'),
              });
            }}
            className="px-3 py-1.5 rounded bg-[#323237] text-white/80 text-sm hover:bg-[#3F3F46] transition-colors"
          >
            今天
          </button>
          <button
            onClick={() => {
              const yesterday = dayjs().subtract(1, 'day');
              onSelectDate({
                start: yesterday.format('YYYY-MM-DD'),
                end: yesterday.format('YYYY-MM-DD'),
              });
            }}
            className="px-3 py-1.5 rounded bg-[#323237] text-white/80 text-sm hover:bg-[#3F3F46] transition-colors"
          >
            昨天
          </button>
          <button
            onClick={() => {
              const end = dayjs();
              const start = dayjs().subtract(6, 'day');
              onSelectDate({
                start: start.format('YYYY-MM-DD'),
                end: end.format('YYYY-MM-DD'),
              });
            }}
            className="px-3 py-1.5 rounded bg-[#323237] text-white/80 text-sm hover:bg-[#3F3F46] transition-colors"
          >
            最近7天
          </button>
          <button
            onClick={() => {
              const end = dayjs();
              const start = dayjs().subtract(29, 'day');
              onSelectDate({
                start: start.format('YYYY-MM-DD'),
                end: end.format('YYYY-MM-DD'),
              });
            }}
            className="px-3 py-1.5 rounded bg-[#323237] text-white/80 text-sm hover:bg-[#3F3F46] transition-colors"
          >
            最近30天
          </button>
        </div>
        <RangePicker
          onChange={(dates) => {
            if (dates) {
              onSelectDate({
                start: dates[0]?.format('YYYY-MM-DD'),
                end: dates[1]?.format('YYYY-MM-DD'),
              });
            } else {
              onSelectDate({
                start: undefined,
                end: undefined,
              });
            }
          }}
          className="w-full"
          placeholder={['开始日期', '结束日期']}
          value={[
            date.start ? dayjs(date.start) : null,
            date.end ? dayjs(date.end) : null
          ]}
          format="YYYY-MM-DD"
          allowClear
        />
      </div>
    </ConfigProvider>
  );

  return (
    <div className="sticky top-0 z-10 w-full bg-[#18181B]/80 backdrop-blur-sm py-4 px-6 flex flex-col md:flex-row gap-4 items-center justify-between border-b border-white/10">
      <div className="w-full md:w-96">
        <ConfigProvider theme={darkThemeConfig}>
          <div className="relative">
            <Input
              value={query}
              onChange={(e) => onChangeQuery(e.target.value)}
              placeholder="搜索作品..."
              className="w-full hover:bg-[#323237] focus:bg-[#323237] placeholder:text-white/50 placeholder:text-sm"
              style={{
                height: '40px',
                lineHeight: '40px',
                background: '#27272A',
                border: 'none',
                outline: 'none',
                boxShadow: 'none',
                borderRadius: '9999px',
                paddingLeft: '44px',
                fontSize: '14px',
                color: '#fff'
              }}
            />
            <div className="absolute left-4 top-1/2 -translate-y-1/2 pointer-events-none">
              <SearchIcon className="w-5 h-5 text-white/50" />
            </div>
          </div>
        </ConfigProvider>
      </div>
      
      <div className="flex items-center gap-2">
        <ConfigProvider theme={darkThemeConfig}>
          <Popover
            content={renderDateContent()}
            trigger="click"
            placement="bottomRight"
            arrow={false}
          >
            <button className="flex items-center gap-2 px-4 py-2 rounded-full bg-[#27272A] text-white/80 hover:bg-[#323237] transition-colors">
              <CalendarDays className="w-4 h-4" />
              <span className="text-sm font-medium">
                {date.start ? `${date.start} ~ ${date.end}` : '日期筛选'}
              </span>
            </button>
          </Popover>
        </ConfigProvider>
        {(date.start || date.end) && (
          <button
            onClick={() => onSelectDate({ start: undefined, end: undefined })}
            className="p-2 rounded-full bg-[#27272A] text-white/80 hover:bg-[#323237] transition-colors"
          >
            <X className="w-4 h-4" />
          </button>
        )}
      </div>
    </div>
  );
};

export default SearchFilter;