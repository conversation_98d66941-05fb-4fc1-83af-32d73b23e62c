import { FC, useState, useEffect, lazy, Suspense } from "react";
import { MoreHorizontal, Download, Trash2, Ban } from "lucide-react";
import { Dropdown, message } from "antd";
import { motion, AnimatePresence } from "framer-motion";
import { IWorkData } from "../../../../typing/Works";
import { formatDistance } from "date-fns";
import { zhCN } from "date-fns/locale";
import VideoModal from "./VideoModal";

// 懒加载ReactPlayer组件
const ReactPlayer = lazy(() => import("react-player"));

// 下载文件函数
const downloadFile = async (url: string, filename: string): Promise<void> => {
  try {
    const response = await fetch(url);
    if (!response.ok) throw new Error("下载失败");

    const blob = await response.blob();
    const objectUrl = URL.createObjectURL(blob);

    const link = document.createElement("a");
    link.href = objectUrl;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    // 清理URL对象
    URL.revokeObjectURL(objectUrl);
    message.success("下载成功");
  } catch (error) {
    console.error("Download failed:", error);
    message.error("下载失败，请稍后重试");
  }
};

interface VideoCardProps {
  item: IWorkData;
  onDelete: () => void;
}

const VideoCard: FC<VideoCardProps> = ({ item, onDelete }) => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isHovered, setIsHovered] = useState(false);
  const [duration, setDuration] = useState<string>("");
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [isVisible, setIsVisible] = useState(true);
  const [isDownloading, setIsDownloading] = useState(false);
  const [isPreviewPlaying, setIsPreviewPlaying] = useState(false);

  // 使用useCallback优化函数
  const handleDelete = async () => {
    setIsDropdownOpen(false);
    setIsVisible(false);
    setTimeout(() => {
      onDelete();
    }, 300);
  };

  const handleDownload = async () => {
    if (isDownloading) return;
    setIsDownloading(true);
    try {
      await downloadFile(item.file_url, `${item.title}.mp4`);
    } finally {
      setIsDownloading(false);
      setIsDropdownOpen(false);
    }
  };

  // 清理副作用
  useEffect(() => {
    return () => {
      // 组件卸载时清理可能的内存泄漏
      if (isModalOpen) setIsModalOpen(false);
      if (isDropdownOpen) setIsDropdownOpen(false);
    };
  }, []);

  const timeAgo = item.created_at
    ? formatDistance(new Date(item.created_at), new Date(), {
        addSuffix: true,
        locale: zhCN,
      })
    : "";

  const handleDuration = (duration: number) => {
    const minutes = Math.floor(duration / 60);
    const seconds = Math.floor(duration % 60);
    setDuration(
      `${minutes.toString().padStart(2, "0")}:${seconds
        .toString()
        .padStart(2, "0")}`
    );
  };

  useEffect(() => {
    if (isModalOpen) {
      setIsPreviewPlaying(false);
    }
  }, [isModalOpen]);

  const handleMouseEnter = () => {
    if (!isModalOpen) {
      setIsHovered(true);
      setIsPreviewPlaying(true);
    }
  };

  const handleMouseLeave = () => {
    setIsHovered(false);
    setIsPreviewPlaying(false);
  };

  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          initial={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.8 }}
          transition={{ duration: 0.3, ease: "easeOut" }}
          className="group relative bg-black/20 rounded-lg border border-white/10"
          onMouseEnter={handleMouseEnter}
          onMouseLeave={handleMouseLeave}
        >
          <div
            className="aspect-square relative cursor-pointer overflow-hidden rounded-t-lg"
            onClick={() => setIsModalOpen(true)}
          >
            <div className="absolute inset-0 bg-black/20 transition-opacity duration-300 group-hover:opacity-0 z-[1]" />
            {item.file_review_pic ? (
              <img
                src={item.file_review_pic}
                alt={item.title}
                className="w-full h-full object-cover transition-all duration-300 group-hover:scale-105 group-hover:brightness-100 brightness-90"
              />
            ) : item.file_url ? (
              <div className="w-full h-full">
                <Suspense
                  fallback={
                    <div className="w-full h-full bg-[#27272A] flex items-center justify-center">
                      <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-white/30"></div>
                    </div>
                  }
                >
                  <ReactPlayer
                    url={item.file_url}
                    width="100%"
                    height="100%"
                    playing={isPreviewPlaying}
                    muted={true}
                    controls={false}
                    playIcon={<></>}
                    onDuration={handleDuration}
                    onError={() => {
                      setIsPreviewPlaying(false);
                    }}
                    config={{
                      file: {
                        attributes: {
                          crossOrigin: "anonymous",
                        },
                        forceVideo: true,
                      },
                    }}
                    stopOnUnmount={true}
                  />
                </Suspense>
              </div>
            ) : (
              <div className="w-full h-full bg-[#27272A] flex flex-col items-center justify-center text-[#555555]">
                <Ban className="w-12 h-12 mb-2" />
                <span className="text-sm">无法预览</span>
              </div>
            )}

            {/* 视频时长 */}
            {duration && (
              <div className="absolute bottom-2 right-2 px-2 py-1 bg-black/80 rounded text-xs text-white">
                {duration}
              </div>
            )}
          </div>

          {/* 视频信息 */}
          <div className="p-4">
            <h3 className="text-white font-medium line-clamp-2 text-sm">
              {item.title}
            </h3>
            <div className="mt-2 flex items-center justify-between text-xs text-white/60">
              <span>{timeAgo}</span>
              <div className="relative">
                <Dropdown
                  open={isDropdownOpen}
                  onOpenChange={setIsDropdownOpen}
                  dropdownRender={() => (
                    <div className="bg-[#27272A] rounded-lg shadow-xl border border-white/10 p-1 min-w-[120px]">
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          handleDownload();
                        }}
                        disabled={isDownloading}
                        className="w-full px-4 py-2 text-sm text-white/80 hover:bg-white/5 text-left flex items-center gap-2 rounded disabled:opacity-50 disabled:cursor-not-allowed"
                        aria-label="下载视频"
                      >
                        {isDownloading ? (
                          <>
                            <div className="w-4 h-4 border-2 border-white/80 border-t-transparent rounded-full animate-spin" />
                            <span>下载中...</span>
                          </>
                        ) : (
                          <>
                            <Download className="w-4 h-4" />
                            <span>下载视频</span>
                          </>
                        )}
                      </button>
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          handleDelete();
                        }}
                        className="w-full px-4 py-2 text-sm text-red-500 hover:bg-white/5 text-left flex items-center gap-2 rounded"
                      >
                        <Trash2 className="w-4 h-4" />
                        <span>删除视频</span>
                      </button>
                    </div>
                  )}
                  placement="bottomRight"
                  trigger={["click"]}
                  overlayStyle={{ padding: 0 }}
                >
                  <button className="p-1 hover:bg-white/10 rounded-full transition-all">
                    <MoreHorizontal className="w-5 h-5 text-white/80" />
                  </button>
                </Dropdown>
              </div>
            </div>
          </div>

          <VideoModal
            isOpen={isModalOpen}
            onClose={() => {
              setIsModalOpen(false);
              setIsPreviewPlaying(false);
            }}
            url={item.file_url}
            title={item.title}
          />
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default VideoCard;
