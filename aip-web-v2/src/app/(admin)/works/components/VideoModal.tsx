import { FC, useRef, useState, useEffect } from "react";
import ReactPlayer from "react-player";
import { Modal } from "antd";
import { X } from "lucide-react";

interface VideoModalProps {
  isOpen: boolean;
  onClose: () => void;
  url: string;
  title: string;
}

const VideoModal: FC<VideoModalProps> = ({ isOpen, onClose, url, title }) => {
  const playerRef = useRef<ReactPlayer>(null);
  const [isPlaying, setIsPlaying] = useState(false);

  // 优化关闭处理
  const handleClose = () => {
    setIsPlaying(false);
    stopAndResetVideo();
    onClose();
  };

  // 改进视频停止和重置逻辑
  const stopAndResetVideo = () => {
    if (playerRef.current) {
      const player = playerRef.current.getInternalPlayer();
      if (player) {
        try {
          player.pause();
          player.currentTime = 0;
          // 清除视频源
          if (player.src) {
            player.removeAttribute('src');
            player.load();
          }
        } catch (error) {
          console.error('Error resetting video:', error);
        }
      }
    }
  };

  useEffect(() => {
    if (!isOpen) {
      setIsPlaying(false);
      stopAndResetVideo();
    } else {
      // 先确保停止所有视频
      stopAndResetVideo();
      
      // 使用较长的延迟确保之前的视频完全停止
      const timer = setTimeout(() => {
        requestAnimationFrame(() => {
          setIsPlaying(true);
        });
      }, 300); // 增加到300ms的延迟

      return () => {
        clearTimeout(timer);
      };
    }
  }, [isOpen]);

  // 组件卸载时清理
  useEffect(() => {
    return () => {
      stopAndResetVideo();
    };
  }, []);

  return (
    <Modal
      open={isOpen}
      onCancel={handleClose}
      afterClose={stopAndResetVideo}
      destroyOnClose={true}
      footer={null}
      width="100%"
      style={{ maxWidth: "64rem", top: 20 }}
      className="video-modal"
      closable={true}
      styles={{
        mask: { backgroundColor: "rgba(0, 0, 0, 0.9)" },
        body: { padding: 0 },
        content: { backgroundColor: "#18181B" },
        header: {
          backgroundColor: "#18181B",
          borderBottom: "none",
          paddingBottom: 0,
        },
      }}
      closeIcon={<X className="text-white hover:text-white/80" />}
    >
      <div>
        <div className="aspect-video w-full">
          <ReactPlayer
            ref={playerRef}
            url={url}
            width="100%"
            height="100%"
            playing={isPlaying}
            controls
            playsinline
            onPlay={() => setIsPlaying(true)}
            onPause={() => setIsPlaying(false)}
            onEnded={() => setIsPlaying(false)}
            onError={() => setIsPlaying(false)}
            config={{
              file: {
                attributes: {
                  crossOrigin: "anonymous",
                },
                forceVideo: true,
                forceAudio: true,
              },
            }}
            stopOnUnmount={true}
          />
        </div>
      </div>
      <div className="p-6">
        <h3 className="text-lg font-medium text-white">{title}</h3>
      </div>
    </Modal>
  );
};

export default VideoModal;
