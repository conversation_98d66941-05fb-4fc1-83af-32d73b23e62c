"use client";
import { NextUIProvider } from "@nextui-org/react";
import { ThemeProvider as NextThemesProvider } from "next-themes";
import { ConfigProvider, App, theme } from 'antd';

export function Providers({ children }: { children: React.ReactNode }) {
  return (
    <NextUIProvider>
      <NextThemesProvider attribute="class" defaultTheme="dark">
        <ConfigProvider
          theme={{
            algorithm: theme.darkAlgorithm,
            token: {
              colorBgBase: '#111111',
              colorTextBase: 'rgba(255, 255, 255, 0.9)',
              colorBorder: 'rgba(255, 255, 255, 0.1)',
              colorPrimary: 'rgb(0, 111, 238)',
              borderRadius: 8,
            },
            components: {
              Modal: {
                colorPrimary: '#111111',
                // contentBg: '#111111',
                // headerBg: '#111111',
                // titleColor: 'rgba(255, 255, 255, 0.9)',
                // titleFontSize: 16,
                algorithm: true, // 启用算法
              },
              Button: {
                primaryColor: '#ffffff',
                defaultBg: 'transparent',
                defaultColor: 'rgba(255, 255, 255, 0.7)',
                defaultBorderColor: 'rgba(255, 255, 255, 0.1)',
              }
            }
          }}
        >
          <App>
            {children}
          </App>
        </ConfigProvider>
      </NextThemesProvider>
    </NextUIProvider>
  );
}
