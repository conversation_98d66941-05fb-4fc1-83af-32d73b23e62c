FROM node:20.15-alpine

RUN apk add git openssh-client

RUN npm config set registry https://registry.npmmirror.com && \
    npm install -g pnpm --fetch-timeout=100000

WORKDIR /xinhou-agent-web/app/

COPY package.json pnpm-lock.yaml ./

RUN git config --global url."https://".insteadOf git:// && \
    git config --global url."https://github.com/".insteadOf **************: && \
    pnpm install
COPY . .
RUN npm run build:dev

EXPOSE 8080
ENV PORT=8080
CMD ["npm","run","start"]