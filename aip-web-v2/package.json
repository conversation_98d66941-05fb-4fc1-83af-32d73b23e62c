{"name": "xinhou-aipgpt-web", "version": "0.1.0", "private": true, "scripts": {"dev": "cross-env NODE_ENV=development next dev", "dev:uat": "cross-env NODE_ENV=uat next dev", "dev:prod": "cross-env NODE_ENV=production next dev", "build:prod": "cross-env NODE_ENV=production  next build", "build:uat": "cross-env NODE_ENV=uat next build", "build:dev": "cross-env NODE_ENV=test next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@ai-sdk/openai": "^1.0.11", "@ant-design/icons": "^5.5.2", "@ant-design/nextjs-registry": "^1.0.2", "@ant-design/x": "^1.0.4", "@internationalized/date": "^3.6.0", "@nextui-org/react": "^2.6.10", "@nextui-org/theme": "^2.4.3", "@svgr/webpack": "^8.1.0", "ahooks": "^3.8.4", "ai": "^4.0.22", "antd": "^5.22.7", "clsx": "^2.1.1", "cross-env": "^7.0.3", "date-fns": "^4.1.0", "dayjs": "^1.11.13", "framer-motion": "^11.13.5", "immer": "^10.1.1", "json5": "^2.2.3", "lodash": "^4.17.21", "lodash-es": "^4.17.21", "lucide-react": "^0.468.0", "markdown-it": "^14.1.0", "moment": "^2.30.1", "next": "15.1.0", "next-themes": "^0.4.4", "rc-virtual-list": "^3.18.4", "react-force-graph-3d": "^1.26.1", "react-intersection-observer": "^9.16.0", "react-markdown": "^9.0.3", "react-photo-view": "^1.2.6", "react-player": "^2.16.0", "rehype-raw": "^7.0.0", "rehype-sanitize": "^6.0.0", "remark-gfm": "^4.0.0", "simple-mind-map": "^0.12.2", "simple-mind-map-plugin-themes": "^1.0.0", "sonner": "^1.7.1", "swiper": "^11.2.0", "tailwind-merge": "^2.5.5", "three-spritetext": "^1.9.5", "usehooks-ts": "^3.1.0", "uuid": "^11.0.3", "zod": "^3.24.1", "zustand": "^5.0.2"}, "devDependencies": {"@ant-design/v5-patch-for-react-19": "^1.0.3", "@eslint/eslintrc": "^3", "@types/lodash": "^4.17.14", "@types/lodash-es": "^4.17.12", "@types/markdown-it": "^14.1.2", "@types/node": "^20", "@types/react": "^19.0.0", "@types/react-dom": "^19.0.0", "code-inspector-plugin": "^0.20.10", "eslint": "^9", "eslint-config-next": "15.1.0", "postcss": "^8", "react": "^19.0.0", "react-dom": "^19.0.0", "tailwindcss": "^3.4.1", "typescript": "^5"}}