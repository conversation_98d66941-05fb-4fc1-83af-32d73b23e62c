{"pid": 1013, "task_id": 4532, "query": "西红柿炒鸡蛋", "agent_uuid": null, "is_pass": 0, "audio_url": null, "video_url": null, "audio_model_id": null, "video_model_id": null, "workflow": {"id": 2, "workflow_name": "无向版", "workflow_code": "2", "description": "1", "agents": [{"id": 33, "agent_name_cn": "选题专家", "agent_name_en": "choice_expert", "agent_code": "24", "agent_type": 6, "agent_role": 5, "agent_style": "TITLE_CHOOSE", "influence_scope": null, "prompt_cn": "{\r\n    \"expert_role\": \"选题专家\",\r\n    \"task\": \"根据主题生成符合社交媒体传播趋势的精简选题\",\r\n    \"workflow_position\": {\r\n        \"trigger\": \"仅接收来自意图识别专家的任务分配\"\r\n    },\r\n    \"few_shot\": [\r\n        {\r\n            \"input\": {\r\n                \"topic\": \"职场提升\"\r\n            },\r\n            \"output\": [\r\n                  {\"order_cn\": \"选题1\", \"title\": \"3招让你工作效率翻倍！\"},\r\n                  {\"order_cn\": \"选题2\", \"title\": \"职场沟通：这些坑千万别踩！\"},\r\n                  {\"order_cn\": \"选题3\", \"title\": \"压力山大？试试这招解压法！\"},\r\n                  {\"order_cn\": \"选题4\", \"title\": \"职业规划：5年后的你在哪？\"},\r\n                  {\"order_cn\": \"选题5\", \"title\": \"职场新人必看：这些错误别犯！\"},\r\n                  {\"order_cn\": \"选题6\", \"title\": \"领导力提升：从这3点开始！\"},\r\n                  {\"order_cn\": \"选题7\", \"title\": \"职场学习：碎片时间也能高效！\"},\r\n                  {\"order_cn\": \"选题8\", \"title\": \"冲突处理：一招化解职场矛盾！\"},\r\n                  {\"order_cn\": \"选题9\", \"title\": \"人脉搭建：3步搞定职场社交！\"},\r\n                  {\"order_cn\": \"选题10\", \"title\": \"创新思维：让工作更有趣！\"}\r\n                ]\r\n        }\r\n    ],\r\n    \"rules\": [\r\n        \"只处理来自意图识别专家的任务\",\r\n        \"输出必须是列表套json格式 例如[{'order_cn':'','title':''}]\",\r\n        \"每个选题不超过20字\",\r\n        \"使用数字、悬念等吸引眼球\",\r\n        \"包含实用技巧或解决方案\",\r\n        \"适合抖音、小红书等平台传播\",\r\n        \"语言风格轻松有趣\",\r\n        \"以第一人称生成\",\r\n        \"禁止出现博主姓名或昵称\",\r\n        \"仅返回List对象，不包含任何解释\"\r\n    ],\r\n    \"instruction\": \"作为选题专家,仅接收和处理来自意图识别专家的规范化任务,生成符合要求的精简选题。\"\r\n}\r\n", "prompt_en": null, "status": 1, "llm_name": "openai", "model_name": "gpt-4o", "api_url": "https://api.gptsapi.net/v1", "api_key": "sk-F5ef23a49e5be896aece6861c68de33774da3bd8233K69zk", "api_config": null, "description": "生成选题", "tool_ids": "", "tools": []}, {"id": 42, "agent_name_cn": "数据搜索专家", "agent_name_en": "data_rinse", "agent_code": "33", "agent_type": 0, "agent_role": 4, "agent_style": "DATA", "influence_scope": "", "prompt_cn": "你是一个数据搜索专家，你只会调用工具不会回答用户需求，你需要调用工具tool_search_network搜索数据", "prompt_en": null, "status": 1, "llm_name": "openai", "model_name": "gpt-4o", "api_url": "https://api.gptsapi.net/v1", "api_key": "sk-F5ef23a49e5be896aece6861c68de33774da3bd8233K69zk", "api_config": null, "description": "进行数据搜索", "tool_ids": "8", "tools": [{"id": 8, "tool_name": "知识搜索", "tool_function": "tool_search_network"}]}, {"id": 41, "agent_name_cn": "数据清洗专家", "agent_name_en": "data_search", "agent_code": "32", "agent_type": 0, "agent_role": 4, "agent_style": "DATA_LOADING_2", "influence_scope": "", "prompt_cn": "你是一个数据清洗专家", "prompt_en": null, "status": 1, "llm_name": "openai", "model_name": "gpt-4o", "api_url": "https://api.gptsapi.net/v1", "api_key": "sk-F5ef23a49e5be896aece6861c68de33774da3bd8233K69zk", "api_config": null, "description": "进行数清洗", "tool_ids": "", "tools": []}, {"id": 40, "agent_name_cn": "数据分析专家", "agent_name_en": "data_analyse", "agent_code": "31", "agent_type": 3, "agent_role": 4, "agent_style": "DATA_LOADING_1", "influence_scope": "[41,42]", "prompt_cn": "你是一个数据分析专家", "prompt_en": null, "status": 1, "llm_name": "openai", "model_name": "gpt-4o", "api_url": "https://api.gptsapi.net/v1", "api_key": "sk-F5ef23a49e5be896aece6861c68de33774da3bd8233K69zk", "api_config": null, "description": "进行数据分析。", "tool_ids": "", "tools": []}, {"id": 39, "agent_name_cn": "视频作品专家", "agent_name_en": "video_works", "agent_code": "30", "agent_type": 0, "agent_role": 4, "agent_style": "VIDEO_3", "influence_scope": "", "prompt_cn": "你是一个视频作品专家，你只会调用工具不会回答用户需求，你需要调用工具tool_video_create制作视频", "prompt_en": null, "status": 1, "llm_name": "openai", "model_name": "gpt-4o", "api_url": "https://api.gptsapi.net/v1", "api_key": "sk-F5ef23a49e5be896aece6861c68de33774da3bd8233K69zk", "api_config": null, "description": "展示生成的视频作品。", "tool_ids": "6", "tools": [{"id": 6, "tool_name": "视频结果", "tool_function": "tool_video_result"}]}, {"id": 38, "agent_name_cn": "视频等待专家", "agent_name_en": "video_await", "agent_code": "29", "agent_type": 0, "agent_role": 4, "agent_style": "VIDEO_2", "influence_scope": "", "prompt_cn": "你是一个视频等待专家", "prompt_en": null, "status": 1, "llm_name": "openai", "model_name": "gpt-4o", "api_url": "https://api.gptsapi.net/v1", "api_key": "sk-F5ef23a49e5be896aece6861c68de33774da3bd8233K69zk", "api_config": null, "description": "为用户生成视频。", "tool_ids": "5", "tools": [{"id": 5, "tool_name": "视频制作", "tool_function": "tool_video_create"}]}, {"id": 37, "agent_name_cn": "视频展开专家", "agent_name_en": "video_detail", "agent_code": "28", "agent_type": 5, "agent_role": 4, "agent_style": "VIDEO_1", "influence_scope": "[38,39]", "prompt_cn": "你是一个视频展开专家，你只会调用工具不会回答用户需求，你需要调用工具tool_video_info获取视频详细信息", "prompt_en": null, "status": 1, "llm_name": "openai", "model_name": "gpt-4o", "api_url": "https://api.gptsapi.net/v1", "api_key": "sk-F5ef23a49e5be896aece6861c68de33774da3bd8233K69zk", "api_config": null, "description": "提供用户视频模板选择列表。", "tool_ids": "4", "tools": [{"id": 4, "tool_name": "视频列表", "tool_function": "tool_video_info"}]}, {"id": 36, "agent_name_cn": "音频作品专家", "agent_name_en": "audio_works", "agent_code": "27", "agent_type": 0, "agent_role": 5, "agent_style": "VOICE_3", "influence_scope": "", "prompt_cn": "你是一个音频作品专家,你只会调用工具不会回答用户需求，你需要调用工具tool_audio_create制作音频数据", "prompt_en": null, "status": 1, "llm_name": "openai", "model_name": "gpt-4o", "api_url": "https://api.gptsapi.net/v1", "api_key": "sk-F5ef23a49e5be896aece6861c68de33774da3bd8233K69zk", "api_config": null, "description": "", "tool_ids": "2", "tools": [{"id": 2, "tool_name": "音频制作", "tool_function": "tool_audio_create"}]}, {"id": 35, "agent_name_cn": "音频生成专家", "agent_name_en": "audio_await", "agent_code": "26", "agent_type": 0, "agent_role": 4, "agent_style": "VOICE_2", "influence_scope": "", "prompt_cn": "你是一个生成专家", "prompt_en": null, "status": 1, "llm_name": "openai", "model_name": "gpt-4o", "api_url": "https://api.gptsapi.net/v1", "api_key": "sk-F5ef23a49e5be896aece6861c68de33774da3bd8233K69zk", "api_config": null, "description": "", "tool_ids": "2", "tools": [{"id": 2, "tool_name": "音频制作", "tool_function": "tool_audio_create"}]}, {"id": 34, "agent_name_cn": "音频展开专家", "agent_name_en": "audio_detail", "agent_code": "25", "agent_type": 5, "agent_role": 4, "agent_style": "VOICE_1", "influence_scope": "[35,36]", "prompt_cn": "你是一个音频展开专家，,你只会调用工具不会回答用户需求，你需要调用工具tool_audio_info获取用户音频的详细信息", "prompt_en": null, "status": 1, "llm_name": "openai", "model_name": "gpt-4o", "api_url": "https://api.gptsapi.net/v1", "api_key": "sk-F5ef23a49e5be896aece6861c68de33774da3bd8233K69zk", "api_config": null, "description": "提供用户音频模板选择列表。", "tool_ids": "1", "tools": [{"id": 1, "tool_name": "音频列表", "tool_function": "tool_audio_info"}]}, {"id": 23, "agent_name_cn": "意图识别", "agent_name_en": "intention_expert", "agent_code": "14", "agent_type": 1, "agent_role": 1, "agent_style": "COMMAND", "influence_scope": "", "prompt_cn": "", "prompt_en": "intention_expert", "status": 1, "llm_name": "openai", "model_name": "claude-3-5-sonnet-20241022", "api_url": "https://api.gpt2share.com/v1", "api_key": "sk-Qoz2RQ8GTfs7rhDeLb88ho8qua03UVLtiMwOGtam9hxVTwlz", "api_config": null, "description": "分析用户意图并安排agent工作。", "tool_ids": "", "tools": []}, {"id": 32, "agent_name_cn": "用户", "agent_name_en": "user_expert", "agent_code": "23", "agent_type": 9, "agent_role": 9, "agent_style": "USER", "influence_scope": null, "prompt_cn": "你是一个用户agent，接受用户的输入", "prompt_en": "user_expert", "status": 1, "llm_name": "openai", "model_name": "gpt-4o", "api_url": "https://api.gptsapi.net/v1", "api_key": "sk-F5ef23a49e5be896aece6861c68de33774da3bd8233K69zk", "api_config": null, "description": "IP博主本人。", "tool_ids": "", "tools": []}, {"id": 31, "agent_name_cn": "标题专家", "agent_name_en": "title_expert", "agent_code": "22", "agent_type": 2, "agent_role": 5, "agent_style": "USUALLY", "influence_scope": "", "prompt_cn": "你是一个专业的标题创作专家。你需要为文章创作既能准确概括内容，又富有吸引力的标题。请注重标题的新颖性、概括性和传播效果。\n\n你只输出一句话标题，确保它能在社交媒体上传播。顺便输出社交媒体标签，用#符号表示", "prompt_en": "title_expert", "status": 1, "llm_name": "openai", "model_name": "gpt-4o", "api_url": "https://api.gptsapi.net/v1", "api_key": "sk-F5ef23a49e5be896aece6861c68de33774da3bd8233K69zk", "api_config": null, "description": "标题创作以及社交媒体标签。", "tool_ids": "", "tools": []}, {"id": 30, "agent_name_cn": "风格润色", "agent_name_en": "style_expert", "agent_code": "21", "agent_type": 4, "agent_role": 4, "agent_style": "EMBELLISH", "influence_scope": "", "prompt_cn": "你是一个文案风格润色专家，你需要根据writer_expert的文案内容进行改写，生成新的文案，同时讲生成的文案输出，限制字数300字", "prompt_en": "style_expert", "status": 1, "llm_name": "openai", "model_name": "gpt-4o", "api_url": "https://api.gptsapi.net/v1", "api_key": "sk-F5ef23a49e5be896aece6861c68de33774da3bd8233K69zk", "api_config": null, "description": "文案风格润色。", "tool_ids": "", "tools": []}, {"id": 29, "agent_name_cn": "文案写手", "agent_name_en": "writer_expert", "agent_code": "20", "agent_type": 2, "agent_role": 4, "agent_style": "WRITER", "influence_scope": "[30]", "prompt_cn": "{\r\n    \"expert_role\": \"文案创作专家\",\r\n    \"task\": {\r\n      \"objective\": \"根据用户输入的主题和要求，创作高质量文案内容\",\r\n      \"output_rules\": [\r\n        \"确保内容原创、可读性强、表达效果佳\",\r\n        \"文案结构清晰，逻辑连贯\",\r\n        \"语言风格符合目标受众\",\r\n        \"避免语法错误和用词不当\"\r\n      ]\r\n    },\r\n    \"output_format\": \"一篇文案\",\r\n    \"examples\": [\r\n      {\r\n        \"input\": \"为新产品撰写推广文案\",\r\n        \"output\": \"这款智能手表集健康监测、运动追踪、智能提醒于一体，采用先进传感器技术，24小时守护您的健康。时尚外观设计，适合各种场合佩戴。\"\r\n      },\r\n      {\r\n        \"input\": \"撰写品牌故事文案\",\r\n        \"output\": \"从一间小小的工作室起步，我们始终坚持用匠心打造每一件产品。十年间，我们不断创新，只为给用户带来更好的体验。\"\r\n      }\r\n    ]\r\n}", "prompt_en": "writer_expert", "status": 1, "llm_name": "openai", "model_name": "gpt-4o", "api_url": "https://api.gptsapi.net/v1", "api_key": "sk-F5ef23a49e5be896aece6861c68de33774da3bd8233K69zk", "api_config": null, "description": "内容文案创作。", "tool_ids": "", "tools": []}, {"id": 28, "agent_name_cn": "开头专家", "agent_name_en": "outset_expert", "agent_code": "19", "agent_type": 2, "agent_role": 4, "agent_style": "USUALLY", "influence_scope": "[29]", "prompt_cn": "你是一个专业的文章开头专家。你要创作引人入胜的开篇内容，快速抓住读者注意力，并为后续内容做好铺垫。请确保开头既吸引人又自然流畅。", "prompt_en": "outset_expert", "status": 1, "llm_name": "openai", "model_name": "gpt-4o", "api_url": "https://api.gptsapi.net/v1", "api_key": "sk-F5ef23a49e5be896aece6861c68de33774da3bd8233K69zk", "api_config": null, "description": "给内容提供一个很棒的开头。", "tool_ids": "", "tools": []}, {"id": 27, "agent_name_cn": "钩子专家", "agent_name_en": "hook_expert", "agent_code": "18", "agent_type": 2, "agent_role": 4, "agent_style": "USUALLY", "influence_scope": "[28]", "prompt_cn": "你是一个专业的内容钩子专家。你需要设计吸引人的内容亮点和引人入胜的表达方式，让读者产生强烈的阅读兴趣。请注重创意性和吸引力。\n\n注意你只输出文章的钩子部分，钩子的用途是吸引点赞评论或关注，它更像是非常隐蔽的为自己打广告。", "prompt_en": "hook_expert", "status": 1, "llm_name": "openai", "model_name": "gpt-4o", "api_url": "https://api.gptsapi.net/v1", "api_key": "sk-F5ef23a49e5be896aece6861c68de33774da3bd8233K69zk", "api_config": null, "description": "非常隐蔽的为自己打广告。", "tool_ids": "", "tools": []}, {"id": 26, "agent_name_cn": "观点专家", "agent_name_en": "viewpoint_expert", "agent_code": "17", "agent_type": 2, "agent_role": 4, "agent_style": "USUALLY", "influence_scope": "[27]", "prompt_cn": "你是一个专业的观点内容专家。你需要帮助提炼和强化文章的核心观点，确保论述有理有据，观点鲜明有特色。请注重论证的逻辑性和说服力。\n\n规则：\n你只输出一句话，用来表达一个强烈的观点，让人听起来能感觉是一个金句。", "prompt_en": "viewpoint_expert", "status": 1, "llm_name": "openai", "model_name": "gpt-4o", "api_url": "https://api.gptsapi.net/v1", "api_key": "sk-F5ef23a49e5be896aece6861c68de33774da3bd8233K69zk", "api_config": null, "description": "提供一个独特的观点。", "tool_ids": "", "tools": []}, {"id": 25, "agent_name_cn": "结构专家", "agent_name_en": "structure_expert", "agent_code": "16", "agent_type": 2, "agent_role": 4, "agent_style": "STRUCT", "influence_scope": "[26]", "prompt_cn": "{\r\n    \"expert_role\": \"结构专家\",\r\n    \"task\": \"根据主题创造合适的文案结构并解释原因,只解释原因，不回答用户的实际问题\",\r\n    \"few_shot\": [\r\n      {\r\n        \"topic\": \"减肥健身\",\r\n        \"output\": \"# 痛点·方案·案例·行动\\n\\n选择理由:减肥话题最需要解决用户的焦虑,通过真实案例增加说服力,最后给出可执行建议\"\r\n      },\r\n      {\r\n        \"topic\": \"职场提升\", \r\n        \"output\": \"# 反常识·方法·验证·互动\\n\\n选择理由:打破职场人固有认知,提供实用方法,用数据支撑,引导分享经验\"\r\n      }\r\n    ],\r\n    \"output_format\": \"# [文案结构]\\n\\n选择理由:[理由说明]\",\r\n    \"instruction\": \"请根据用户主题,创造一个合适的文案结构(不超过20字),并用markdown格式输出结构和选择理由\"\r\n  }", "prompt_en": "structure_expert", "status": 1, "llm_name": "openai", "model_name": "gpt-4o", "api_url": "https://api.gptsapi.net/v1", "api_key": "sk-F5ef23a49e5be896aece6861c68de33774da3bd8233K69zk", "api_config": null, "description": "提供丰富的内容结构。", "tool_ids": "", "tools": []}]}}