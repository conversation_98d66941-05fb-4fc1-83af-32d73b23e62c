# !/usr/bin/python3
# -*- coding: utf-8 -*-

"""
IP信息表控制器类
----------------------------------------------------
@Project :   xinhou-aip-agents
@File    :   MultiAgentChatController.py

@Modify Time      <AUTHOR>    @Desciption
------------      -------    --------    -----------
2025/1/20 22:04  daniel     v1.0.0     None
"""
import json

import redis
from sqlalchemy.orm import Session
from fastapi import APIRouter, Depends
from fastapi.responses import StreamingResponse
from loguru import logger
from xinhou_openai_framework.core.cache.RedisConnectionPool import RedisConnectionPool
from xinhou_openai_framework.core.db.DatabaseManager import DatabaseManager
from xinhou_openai_framework.core.reponse.R import R

from apps.api.schema.MultiAgentChatSchema import MultiAgentChatRequestSchema, MakeQuerySchema, LocalQuerySchema, \
    AgentsContents, AgentsStopSchema, LocalUpdateSchema
from common.service.AgentHistoryService import AgentHistoryService
from common.service.IpInfoService import IpInfoService
from common.service.MultiAgentChatService import MultiAgentChatService
from common.service.ReadLocalService import get_local_agent_info
from common.service.WorkflowService import WorkflowService
from common.service.PromptConfigService import PromptConfigService
from common.entity.AgentHistoryData import AgentHistoryData
from common.entity.AgentIpData import IpInfoData

api = APIRouter()
chat_service = MultiAgentChatService()
query_service = MakeQuerySchema()


@api.post('/agent/chat', tags=["chat", "v1"],
          summary="[v1]多 Agent 聊天",
          description="多 Agent 聊天")
async def chat(search: MultiAgentChatRequestSchema,
               redis_client: redis.Redis = Depends(RedisConnectionPool().get_pool),
               db: Session = Depends(DatabaseManager().get_session)):
    # logger.info("[find][request]:{}".format(search.model_dump()))
    # query的拼接
    ip_info = IpInfoService(db).find_by_id(IpInfoData(**{"id": search.pid}))  # noqa
    search = query_service.make_query(search, ip_info.ip_name)
    response_stream = chat_service.start_chat(search=search, redis_client=redis_client, db=db)
    return StreamingResponse(response_stream, media_type="text/event-stream")


@api.post('/agent/workflow/debug', tags=["chat", "v1"],
          summary="[v1]工作流服务调试接口",
          description="用于调试重构后的工作流服务")
async def workflow_debug(search: MultiAgentChatRequestSchema,
                         redis_client: redis.Redis = Depends(RedisConnectionPool().get_pool),
                         db: Session = Depends(DatabaseManager().get_session)):
    # logger.info("[workflow_debug][request]:{}".format(search.model_dump()))
    # query的拼接
    search = query_service.make_query(search, "ip_name")
    workflow_service = WorkflowService()
    response_stream = workflow_service.start_chat(search=search, redis_client=redis_client, db=db)
    return StreamingResponse(response_stream, media_type="text/event-stream")


@api.post('/agent/workflow/debug/sync', tags=["chat", "v1"],
          summary="[v1]工作流服务同步调试接口",
          description="用于同步调试重构后的工作流服务")
async def workflow_debug_sync(search: MultiAgentChatRequestSchema,
                              redis_client: redis.Redis = Depends(RedisConnectionPool().get_pool),
                              db: Session = Depends(DatabaseManager().get_session)):
    # logger.info("[workflow_debug_sync][request]:{}".format(search.model_dump()))
    search = query_service.make_query(search, "ip_name")
    workflow_service = WorkflowService()
    result = await workflow_service.debug_chat(search=search, redis_client=redis_client, db=db)
    return result


@api.post('/agent/prompt/save', tags=["debug"],
          summary="保存 prompt 配置",
          description="保存 agent 的 prompt 配置到本地文件")
async def save_prompt(agent_type: str, prompt_config: dict):
    prompt_service = PromptConfigService()
    success = prompt_service.save_prompt(agent_type, prompt_config)
    return {"success": success}


@api.get('/agent/prompt/get/{agent_type}', tags=["debug"],
         summary="获取 prompt 配置",
         description="获取 agent 的 prompt 配置")
async def get_prompt(agent_type: str, use_local: bool = True):
    prompt_service = PromptConfigService()
    prompt = prompt_service.get_prompt(agent_type, use_local)
    return prompt


@api.post('/local/agent/chat', tags=["debug"],
          summary="本地获取prompt调试",
          description="获取 agent 的 prompt 配置")
async def get_local_info(query: LocalQuerySchema,
                         redis_client: redis.Redis = Depends(RedisConnectionPool().get_pool),
                         db: Session = Depends(DatabaseManager().get_session)):
    search = get_local_agent_info()
    search = MultiAgentChatRequestSchema.model_validate(search)
    search.pid = query.pid
    search.query = query.query
    search.task_id = query.task_id
    search.is_pass = query.is_pass
    search.doc_length = query.doc_length
    search.agent_uuid = query.agent_uuid
    ip_info = IpInfoService(db).find_by_id(IpInfoData(**{"id": search.pid}))  # noqa
    search = query_service.make_query(search, ip_info.ip_name)
    response_stream = chat_service.start_chat(search=search, redis_client=redis_client, db=db)
    return StreamingResponse(response_stream, media_type="text/event-stream")


@api.post("/agents/stop", tags=["口播稿", "v1"],
          summary="停止任务生成",
          description="停止指定任务的成过程")
async def stop_task(
        request: AgentsStopSchema,
        redis_client: Session = Depends(RedisConnectionPool().get_pool),
):
    try:
        if not request.task_id:
            return R.ID_NOT_FOUND()

        # 构造停止标记的key
        stop_key = AgentsContents.SPEECH_STOP_LABEL.format(task_id=request.task_id)

        # 设置停止标记，有效期1小时
        await redis_client.lpush(stop_key, "true")

        logger.info(f"已设置任务 {request.task_id} 的停止标记")
        return R.SUCCESS({"message": f"已发送停止信号给任务 {request.task_id}"})

    except Exception as e:
        logger.error(f"设置任务停止标记失败: {str(e)}")
        return R.SERVER_ERROR()


@api.post("/update", tags=["口播稿", "v1"],
          summary="停止任务生成",
          description="停止指定任务的成过程")
def update_info(query: LocalUpdateSchema,
                db: Session = Depends(DatabaseManager().get_session)
                ):
    AgentHistoryService(db).update(
        AgentHistoryData(id=query.id, history=json.dumps(query.content, ensure_ascii=False)))

    return R.SUCCESS({"message": "更新成功"})
