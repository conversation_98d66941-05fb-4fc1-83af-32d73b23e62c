# @Time : 2024/11/28 下午1:11 

# <AUTHOR> daniel

# @File : TestApiSchema.py 

# @Software: PyCharm
from typing import Optional

from pydantic import BaseModel, Field


class TestApiSchema(BaseModel):
    """
    测试接口入参
    """
    query: str = Field(..., title="用户需求 query", description="Required field")


class GraphSchema(BaseModel):
    """
    接受用户参数
    """
    query: str = Field(..., title="用户需求 query", description="Required field")
    tid: str = Field(..., title="任务id", description="Required field")