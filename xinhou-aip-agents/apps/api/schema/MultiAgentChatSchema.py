"""多 Agent 聊天相关的 Schema 定义"""

from typing import List, Optional, Annotated
from pydantic import BaseModel, Field, ConfigDict


class ToolInfo(BaseModel):
    id: int = Field(..., title="工作流ID")
    tool_name: str = Field(..., title="工作流名称")
    tool_function: str = Field(..., title="工作流编码")
    model_config = ConfigDict(
        from_attributes=True,
        protected_namespaces=(),
        arbitrary_types_allowed=True
    )


class AgentInfo(BaseModel):
    """Agent 信息"""
    id: int = Field(..., title="Agent ID")
    agent_name_cn: str = Field(..., title="Agent中文名称")
    agent_name_en: str = Field(..., title="Agent英文名称")
    agent_code: str = Field(..., title="Agent编码")
    agent_type: int = Field(..., title="Agent类型")
    agent_role: int = Field(..., title="Agent角色")
    influence_scope: Optional[str] = Field(None, title="影响范围")
    prompt_cn: Optional[str] = Field(None, title="中文prompt")
    prompt_en: Optional[str] = Field(None, title="英文prompt")
    status: int = Field(1, title="状态:1=正常,2=禁用")
    llm_name: Optional[str] = Field(None, title="LLM名称")
    model_name: Optional[str] = Field(None, title="模型名称")
    api_url: Optional[str] = Field(None, title="API URL")
    api_key: Optional[str] = Field(None, title="API Key")
    agent_action: Optional[str] = Field(None, title="输出功能样式")
    api_config: Optional[str] = Field(None, title="API配置")
    description: str = Field(..., title="agent描述")
    agent_style: str = Field(..., title="agent样式")
    tools_id: Optional[str] = Field(None, title="影响范围")
    tools: List[ToolInfo] = Field(default_factory=list, title="Agent列表")

    model_config = ConfigDict(
        from_attributes=True,
        protected_namespaces=(),
        arbitrary_types_allowed=True
    )


class WorkflowInfo(BaseModel):
    """工作流信息"""
    id: int = Field(..., title="工作流ID")
    workflow_name: str = Field(..., title="工作流名称")
    workflow_code: str = Field(..., title="工作流编码")
    description: str = Field(..., title="工作流描述")
    agents: List[AgentInfo] = Field(default_factory=list, title="Agent列表")
    agent_uuid: Optional[str] = Field(default=None, title="音视频组的专属参数")

    model_config = ConfigDict(
        from_attributes=True,
        protected_namespaces=(),
        arbitrary_types_allowed=True
    )


class MultiAgentChatRequestSchema(BaseModel):
    """多 Agent 聊天接口入参"""
    query: str = Field(..., title="用户需求query", description="Required field")
    pid: int = Field(..., title="ipid", description="Required field")
    task_id: int = Field(..., title="任务id", description="Required field")
    workflow: WorkflowInfo = Field(..., title="工作流数据", description="Required field")
    agent_uuid: Optional[str] = Field(default=None, title="音视频组的专属参数")
    doc_length: Optional[int] = Field(default=None, title="文案字数")
    audio_url: Optional[str] = Field(None, title="音频模型地址")
    language: Optional[str] = Field(None, title="生成文案的语种")
    tmp_query: Optional[str] = Field(None, title="后台拼接参数query")
    video_url: Optional[str] = Field(None, title="视频模型地址")
    audio_model_id: Optional[int] = Field(None, title="音频模型ID")
    video_model_id: Optional[int] = Field(None, title="视频模型ID")
    is_pass: Optional[int] = Field(0, title="是否跳过意图识别")
    voice_is_upload: Optional[int] = Field(0, title="是否上传用户音频")
    video_model_pic_url: Optional[str] = Field(None, title="视频封面图")
    voice_upload_url: Optional[str] = Field(None, title="用户上传的音频url")
    ip_name: Optional[str] = Field(None, title="用户的中文名称")
    is_person: Optional[int] = Field(0, title="是否使用人设")
    is_search: Optional[int] = Field(0, title="是否使用搜索")
    is_knowledge: Optional[int] = Field(1, title="是否使用知识图谱")
    style: Optional[str] = Field(0, title="风格润色的类型")
    read_score: Optional[int] = Field(0, title="文案的易读性")
    err_cause: Optional[int] = Field(None, title="错误日志")
    content: Optional[dict] = Field({}, title="错误的回调url")

    model_config = ConfigDict(
        from_attributes=True,
        protected_namespaces=(),
        arbitrary_types_allowed=True,
        json_schema_extra={
            "example": {
                "query": "给我生成快乐星球的口播稿",
                "pid": 18,
                "task_id": 3767,
                "workflow": {
                    "id": 2,
                    "workflow_name": "无向版",
                    "workflow_code": "2",
                    "description": "工作流描述",
                    "agents": []
                }
            }
        }
    )


class MakeQuerySchema(object):

    @staticmethod
    def make_query(search: MultiAgentChatRequestSchema, ip_name):
        if search.audio_model_id:
            new_query = f'用户ipname为：{ip_name},pid为：{search.pid},task_id为：{search.task_id},voice_id为：{search.audio_model_id}\n用户的query为：{search.query}'
        elif search.video_model_id:
            new_query = (
                f'用户ipname为：{ip_name},pid为：{search.pid},task_id为：{search.task_id},video_model_id为：{search.video_model_id},video_url为：{search.video_url},audio_url为：{search.audio_url}\n'
                f'用户的query为：{search.query}')
        else:
            new_query = f'用户ipname为：{ip_name},pid为：{search.pid},task_id为：{search.task_id}\n用户的query为：{search.query}'
        search.tmp_query = new_query
        for i in search.workflow.agents:
            if i.agent_name_en == "intention_expert":
                i.prompt_cn = i.prompt_cn.replace("<ipname>", str(ip_name))
            if search.doc_length:
                if i.agent_name_en in ["writer_expert", "style_expert", "title_expert", "choice_expert"]:
                    i.prompt_cn = i.prompt_cn.replace("<word_count>", str(search.doc_length)).replace(
                        "<max+word_count>", str(int(search.doc_length) + 30)).replace("<language>",
                                                                                      str(search.language))
            if search.read_score:
                if i.agent_name_en in ["writer_expert", "style_expert"]:
                    i.prompt_cn = i.prompt_cn.replace("<language_style>", str(search.style)).replace("<read_score>",
                                                                                                     str(search.read_score))

        return search


class LocalQuerySchema(BaseModel):
    """多 Agent 聊天接口入参"""
    query: str = Field(..., title="用户需求query", description="Required field")
    pid: int = Field(..., title="ipid", description="Required field")
    task_id: int = Field(..., title="任务id", description="Required field")
    is_pass: Optional[int] = Field(0, title="是否跳过意图识别")
    audio_model_id: Optional[int] = Field(None, title="音频模型ID")
    doc_length: Optional[int] = Field(default=None, title="文案字数")
    agent_uuid: Optional[str] = Field(default=None, title="音视频组的专属参数")


class AgentsContents:
    """
    口播稿常量类
    """

    SPEECH_CREATE_DATA = "aip_agent_data:"

    SPEECH_STOP_LABEL = "aip_agent_stop:{task_id}"


class AgentsStopSchema(BaseModel):
    """文本训练入参模型"""
    task_id: int = Field(default=..., title="口播稿任务id")


class LocalUpdateSchema(BaseModel):
    """多 Agent 聊天接口入参"""
    id: int = Field(..., title="用户需求query", description="Required field")
    content: list = Field(..., title="ipid", description="Required field")
