# @Time : 2024/12/17 下午4:43 

# <AUTHOR> daniel

# @File : UndigraphApiSchema.py 

# @Software: PyCharm
# @Time : 2024/12/9 下午8:48

# <AUTHOR> daniel

# @File : FlexoManuscriptApiSchema.py

# @Software: PyCharm
import ast
from pydantic import BaseModel, Field
from typing import List
from typing_extensions import TypedDict


class InfoDict(TypedDict):
    agents: List


class UndirectedGraphSchema(BaseModel):
    """
    测试接口入参
    """
    query: str = Field(..., title="用户需求 query", description="Required field")
    pid: int = Field(..., title="用户id", description="Required field")
    task_id: int = Field(..., title="任务id", description="Required field")
    workflow: InfoDict = Field(..., title="任务信息", description="Required field")


def check_parameter(content):
    try:
        for info in content.workflow["agents"]:
            if info["influence_scope"]:
                info["influence_scope"] = ast.literal_eval(info["influence_scope"])
        return content
    except Exception as e:
        assert e, "An error occurred while processing the influence_scope field"
