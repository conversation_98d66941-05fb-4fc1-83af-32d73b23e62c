# @Time : 2024/11/28 下午1:11

# <AUTHOR> daniel

# @File : TestApiSchema.py

# @Software: PyCharm
from typing import List, Optional
from typing_extensions import TypedDict

from pydantic import BaseModel, Field


class InfoDict(TypedDict):
    agents: List


class GraphSchema(BaseModel):
    """
    测试接口入参
    """

    query: str = Field(..., title="用户需求 query", description="Required field")
    pid: int = Field(..., title="用户id", description="Required field")
    task_id: int = Field(..., title="任务id", description="Required field")
    workflow: InfoDict = Field(..., title="任务信息", description="Required field")
