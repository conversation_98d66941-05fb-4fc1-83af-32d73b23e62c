# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
OpenAI文档训练&QA功能描述（Milvu<PERSON>）
----------------------------------------------------
@Project :   xinhou-openai-embedding
@File    :   OpenaiSummaryConsumer.py
@Contact :   <EMAIL>

@Modify Time      <AUTHOR>    @Desciption
------------      -------    --------    -----------
2023/12/28 10:47   shenpeng   1.0         None
"""
import json
import asyncio
import aiohttp

from loguru import logger
from xinhou_openai_framework.core.cache.RedisConnectionPool import RedisConnectionPool
from xinhou_openai_framework.core.db.DatabaseManager import DatabaseManager
from xinhou_openai_framework.core.queue.consumer.RedisConsumerSupport import ConsumerServiceSupport

from apps.api.schema.MultiAgentChatSchema import MultiAgentChatRequestSchema, MakeQuerySchema
from common.entity.AgentWorkData import AgentTasksData
from common.service import ReidsStreamService
from common.service.AgentWorkService import AgentTasksService
from common.service.IpInfoService import IpInfoService
from common.entity.AgentIpData import IpInfoData
from common.service.RedisMultiAgentService import MultiAgentChatService

chat_service = MultiAgentChatService()
query_service = MakeQuerySchema()


class AipAgentConsumer(ConsumerServiceSupport):

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.redis_pool = None

    async def initialize(self):
        # 在消费者初始化时获取 Redis 连接池
        self.redis_pool = await RedisConnectionPool().get_pool()

    async def process_message(self, message: MultiAgentChatRequestSchema):
        """
        业务逻辑可以通过继承RedisConsumer重写process用于接收消息及执行业务逻辑
        """
        # 这里实现具体的消息处理逻辑
        logger.info(f"[AipAgentConsumer]消费者开始执行业务逻辑")
        # 可以在这里添加自定义的消息处理逻辑
        logger.info(f"执行消息主键[{message.task_id},用户输入{message.query}]")
        # 执行业务逻辑
        # summary_content = await
        try:
            if self.redis_pool is None:
                await self.initialize()
            await test_make_text(message, self.redis_pool)
        except Exception as e:
            logger.error(f"处理消息时发生错误: {e}")
            raise e
        logger.info(f"[AipAgentConsumer]消费者结束执行业务逻辑")

    async def handler(self):
        try:
            while True:
                lock_acquired = await self.acquire_lock()
                if lock_acquired:
                    message = await self.redis.rpop(self.queue_name)
                    if message:
                        await self.release_lock()
                        success = False
                        msg = MultiAgentChatRequestSchema(**json.loads(message))
                        await self.release_lock()
                        while not success:
                            try:
                                await asyncio.gather(
                                    self.process_message(msg),
                                    self.redis.lrem(self.queue_name, 0, message)  # 确认消息已被消费，从队列中删除已处理的消息
                                )
                                success = True
                            except Exception as e:
                                logger.error(
                                    f"消费者处理[{msg.task_id}]消息时发生异常，异常原因:{str(e)},开始调用回调接口")
                                msg.err_cause = str(e)
                                await self.redis.lpush(f"{self.queue_name}_fail", msg.model_dump_json())
                                logger.info(f'消费者已将消息转移到[{self.queue_name}_fail]失败队列中！')
                                # 检查callback_url是否存在
                                callback_url = msg.content.get('callback_url')
                                if callback_url:
                                    # 创建HTTP的POST请求
                                    async with aiohttp.ClientSession() as session:
                                        payload = {"process_key": msg.task_id,
                                                   "process_result": str(msg.content), "process_error": msg.err_cause,
                                                   "process_success": False}
                                        async with session.post(callback_url, json=payload) as response:
                                            # 你可以添加代码处理响应，如检查HTTP状态码，解析返回的JSON数据等
                                            print(await response.text())
                                else:
                                    logger.info("没有提供回调URL，跳过回调")
                                break

                await self.release_lock()
                await asyncio.sleep(2)
        #
        except asyncio.CancelledError:
            await self.release_lock()
        except Exception as e:
            logger.error("消费者接收消息时发生异常")
            logger.error(e)
            raise e
        finally:
            await self.release_lock()

    @staticmethod
    async def process_listener():
        """
        消费者监听器
        """
        logger.info("启动消费者[AipAgentConsumer]监听器")
        redis_pool = await RedisConnectionPool().get_pool()
        await AipAgentConsumer(
            **{"redis_pool": redis_pool, "queue_name": "aip_speech_queue",
               "lock_name": 'aip_queue_lock'}
        ).handler()


def get_replace_language(input_user, search):
    language_info = {"zh": "中文", "en": "英文", "es": "西班牙语", "fr": "法语", "de": "德语", "ja": "日语"}
    if search.language:
        result = language_info.get(search.language, "中文")
    else:
        result = language_info.get(input_user.language, "中文")
    return result


def get_replace_read_score(input_user, search):
    score_info = {
        0: "",
        1: " (Flesch指数: 80-100)适合受众：普通大众，包括青少年和非母语人士特点：短句、简单词汇、清晰直白适用内容：入门科普、生活小技巧、大众娱乐",
        2: "(Flesch指数: 70-80)适合受众：一般成年人特点：中等句长、类似朋友间聊天的语气适用内容：生活分享、产品测评、轻松话题",
        3: " (Flesch指数: 60-70)适合受众：有一定知识基础的观众特点：稍长句子、专业词汇适度出现适用内容：深度解析、详细教程、行业见解",
        4: "专业内容 (Flesch指数: 30-60)适合受众：行业专家、专业人士特点：复杂句式、专业术语、深度分析适用内容：专业知识讲解、学术内容、行业深度",
    }
    if search.read_score:
        search.read_score = score_info.get(int(search.read_score), "")
    return search


def get_replace_style_info(input_user, search):
    search.read_score = input_user.read_score
    search.read_score = get_replace_read_score(input_user, search).read_score
    search.style = input_user.style
    return search


async def test_make_text(search: MultiAgentChatRequestSchema, redis_client):
    db_manager = DatabaseManager.get_instance()
    session_generator = db_manager.get_session()
    db = next(session_generator)
    try:
        ip_info = IpInfoService(db).find_by_id(IpInfoData(**{"id": search.pid}))  # noqa
        search.ip_name = ip_info.ip_name
        input_user = AgentTasksService(db=db).find_by_id(AgentTasksData(id=search.task_id))  # noqa
        if input_user and not search.doc_length:
            search.doc_length = input_user.doc_length
        search.language = get_replace_language(input_user, search)
        if input_user and not search.style and not search.read_score:
            search = get_replace_style_info(input_user, search)
        else:
            search = get_replace_read_score(input_user, search)
        search = query_service.make_query(search, ip_info.ip_name)
        # logger.info(f"参数\n{search}\n")
        manager = ReidsStreamService.MultiAgentStreamManager()
        tmp_task_agents = []
        tmp_task = False
        all_agents_key = f'aip_task:{search.task_id}_agents'
        AgentTasksService(db).update(AgentTasksData(id=search.task_id, progress=1))
        if await redis_client.exists(all_agents_key):
            tmp_task = True
            tmp_task_agents = await redis_client.lrange(all_agents_key, 0, -1)
        for again_num in range(4):
            try:
                await chat_service.start_chat(search=search, redis_client=redis_client, db=db, redis_manager=manager)
                break
            except Exception as error_e:
                if await redis_client.exists(all_agents_key):
                    await redis_client.delete(all_agents_key)
                if tmp_task:
                    str_task_agents = [agent.decode('utf-8') for agent in tmp_task_agents]
                    for agent_id in str_task_agents:
                        await redis_client.rpush(all_agents_key, agent_id)
                logger.error(f'当前任务id{search.task_id}第{again_num}重试失败,失败原因{error_e}')
                if again_num == 3:
                    raise error_e
    except Exception as e:
        await manager.stream_input_content(search.task_id, 999, str('生成完毕'), redis_client)
        await manager.stored_add_agent(search.task_id, {'status': 'completed', 'stream_id': 999},
                                             redis_client)
        _ = await manager.add_agent(search.task_id, {'status': 'completed', 'agent_id': 999}, redis_client)
        AgentTasksService(db).update(AgentTasksData(id=search.task_id, progress=3))
        logger.info(f"{search.task_id}写入结束标记位置agent999")
        db.rollback()
        logger.error(f"处理消息时发生错误: {str(e)}")
        raise
    finally:
        db.close()
