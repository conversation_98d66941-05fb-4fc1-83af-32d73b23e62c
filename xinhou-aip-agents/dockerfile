FROM python:3.10-slim
# 设置时区
RUN ln -sf /usr/share/zoneinfo/Asia/Shanghai /etc/localtime && echo "Asia/Shanghai" > /etc/timezone
WORKDIR /home/<USER>/aip-agents
COPY requirements.txt .
RUN pip config set global.index-url https://pypi.org/simple && \
              pip install --upgrade pip && \
              pip install --no-cache-dir -r requirements.txt -i https://mirrors.aliyun.com/pypi/simple && \
              pip install xinhou-openai-framework==20250317002 && \
              rm -rf /root/.cache/pip/*
COPY . .

# 复制特定文件
COPY chat_models.py /usr/local/lib/python3.10/site-packages/langchain_core/language_models/chat_models.py
COPY utils.py /usr/local/lib/python3.10/site-packages/langchain_core/messages/utils.py
CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8080", "--workers", "5", "--limit-concurrency", "20","--loop", "asyncio", "--proxy-headers"]
