# @Time : 2024/12/23 下午4:51

# <AUTHOR> daniel

# @File : test_tool.py

# @Software: PyCharm

from langchain_openai import ChatOpenAI
from langgraph.prebuilt import create_react_agent

from langchain_core.messages import (
    HumanMessage,
    SystemMessage,
    ToolMessage
)
from langgraph.prebuilt import ToolNode
from langchain_core.tools import tool


@tool(return_direct=True)
def get_word_length(word: str) -> int:
    """返回单词的长度"""
    return len(word)


@tool(return_direct=True)
def replace_eng(word: str) -> str:
    """将输入的中文翻译成英文"""
    return "抱歉我不会翻译"


# 创建工具清单
tools = [get_word_length, replace_eng]
# tool_node = ToolNode(tools=tools)

# 创建语言模型实例
llm = ChatOpenAI(
    temperature=0,
    api_key='sk-X8QR18KsVO4f3RRZE4FcBb3c8fDe4f08Aa3dD8C4202f7396',
    base_url='https://api.gpt2share.com/v1',
    model="gpt-4o"
)

# 创建 agent
graph = create_react_agent(llm, tools=tools)

# 构建消息
message = [
    SystemMessage(
        content="你只会调用工具不会回答用户需求，你的任务就是将工具结果返回给用户"
    ),
    HumanMessage(content="调用工具, 将 您好世界，翻译成英文"),
]
inputs = {"messages": message}

# 执行
info = graph.stream(inputs, stream_mode="values")

for s in info:
    message = s["messages"][-1]
    if isinstance(message, tuple):
        print(message)
    elif isinstance(message, ToolMessage):
        print("工具结果")
        print(message.content)
        break
    else:
        message.pretty_print()
