# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
功能描述
----------------------------------------------------
@Project :   aip-agents
@File    :   main.py
@Contact :
"""
import asyncio
import os
import uvicorn
from starlette.middleware.cors import CORSMiddleware
from xinhou_openai_framework.core.context.model.AppContext import AppContext
from xinhou_openai_framework.core.context.model.SystemContext import ctx
from xinhou_openai_framework.core.init.AppManager import AppManager

from dotenv import load_dotenv, find_dotenv

_ = load_dotenv(find_dotenv())

app = AppManager.create_app()
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)
from common.queue.listener.QueueListenerHandler import QueueListenerHandler

QueueListenerHandler.init_listeners(app, ctx.__getattr__("context"))

if __name__ == '__main__':
    context: AppContext = ctx.__getattr__("context")  # 全局变量
    os.environ["LANGCHAIN_API_KEY"] = context.llm_config.lang_smith.LANGCHAIN_API_KEY
    os.environ["LANGCHAIN_ENDPOINT"] = context.llm_config.lang_smith.LANGCHAIN_ENDPOINT
    os.environ["LANGCHAIN_PROJECT"] = context.llm_config.lang_smith.LANGCHAIN_PROJECT
    uvicorn.run(
        app="main:app",
        reload=context.application.server.reload,
        host=context.application.server.host,
        port=context.application.server.post
    )
