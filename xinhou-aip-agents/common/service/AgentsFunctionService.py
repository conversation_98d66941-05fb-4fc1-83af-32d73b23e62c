# @Time : 2024/12/22 下午5:42
import time
from typing import Annotated

# <AUTHOR> daniel

# @File : AgentsFunctionService.py

# @Software: PyCharm
import threading
import logging
import json
from typing import Dict, Callable, List
from common.remote.AudioFunctionService import AudioRemoteService
from common.remote.CozeFunctionService import CoZeFunctionService
from common.remote.VideoFunctionService import VideoRemoteService
from common.remote.DataFunctionService import DataRemoteService
from langchain_core.tools import tool

TOOL_FUNCTIONS: Dict[str, Callable] = {}


def register_tool(func: Callable) -> Callable:
    """工具函数注册装饰器"""
    TOOL_FUNCTIONS[func.__name__] = func
    return func


@tool(return_direct=True)
@register_tool
def tool_audio_info(pid: Annotated[str, "pid"]):
    """
        通过pid获取用户音频展开的详细列表信息
    :param pid: 用户的pid
    :return: 返回消息
    """
    response_data = {"pid": pid}
    logging.info(f'查询audio_info，参数{response_data}')
    return AudioRemoteService().audio_info(json_data=response_data)


def extract_mindmap_data(data_str):
    # 解析JSON字符串
    try:
        data = json.loads(data_str)
    except json.JSONDecodeError:
        # 如果不是有效的JSON，尝试评估为Python字典
        data = eval(data_str)

    # 递归函数来提取节点文本和其子节点
    def extract_node(node):
        text = node['data']['text']
        result = {'text': text}

        if 'children' in node and node['children']:
            children = []
            for child in node['children']:
                children.append(extract_node(child))

            if children:  # 只有当children非空时才添加到结果中
                result['children'] = children

        return result

    # 提取根节点及其所有子节点
    result = extract_node(data)
    return result


def tool_person_info(pid: Annotated[int, "pid"]):
    """
        通过pid获取用户的详细信息
    :param pid: 用户的pid
    :return: 返回消息
    """
    response_data = {"pid": int(pid)}
    person_info = DataRemoteService().get_new_person_info(json_data=response_data)
    if person_info:
        data = extract_mindmap_data(person_info)
        info = json.dumps(data, ensure_ascii=False, indent=2)
    else:
        info = {"text": "未获取到用户人设", "children": []}
    return info


@tool(return_direct=True)
@register_tool
def tool_audio_create(task_id: Annotated[int, "任务的task_id"],
                      pid: Annotated[int, "用户的pid"],
                      voice_id: Annotated[int, "音频的id"]):
    """
        创建音频任务，发起制作请求
        :param
            pid: 用户的pid
            task_id: 任务id
            voice_id: 音频id
        :return: 返回消息

    """
    response_data = {"task_id": task_id, "pid": pid, "voice_id": voice_id}
    logging.info(f'发起audio_create，参数{response_data}')
    return AudioRemoteService().audio_create(json_data=response_data)


# @tool(return_direct=True)
# @register_tool
# def tool_audio_await(task_id):
#     """
#             通过pid获取用户列表详情
#         :param pid: 用户的pid
#         :return: 返回消息
#         """
#     response_data = {"task_id": task_id}
#     AudioRemoteService().audio_await(task_id)
#     return AudioRemoteService().audio_await(json_data=response_data)


@tool(return_direct=True)
@register_tool
def tool_audio_result(task_id: Annotated[str, "任务的task_id"], ):
    """
            通过pid获取用户列表详情
        :param task_id: 用户的pid
        :return: 返回消息
        """
    response_data = {"task_id": task_id}
    # AudioRemoteService().audio_await(task_id)
    AudioRemoteService().audio_await(json_data=response_data)
    # response_data = {"task_id": task_id}
    return AudioRemoteService().audio_result(json_data=response_data)


@tool(return_direct=True)
@register_tool
# Video tools
def tool_video_info(pid: Annotated[str, "用户的pid"], ):
    """
        通过pid获取用户视频列表详情

        Args:
            pid (str): 用户的pid

        Returns:
            Dict[str, Any]: 用户列表详情

        Raises:
            ValueError: 当API调用失败时抛出
    """
    try:
        response_data = {"pid": pid, "status": 1}
        result = VideoRemoteService().video_info(json_data=response_data)
        if result is None:
            raise ValueError("获取视频信息失败：空响应")
        return result
    except Exception as e:
        raise ValueError(f"获取视频信息失败：{str(e)}")


@tool(return_direct=True)
@register_tool
def tool_video_result(audio_url: Annotated[str, "音频地址"],
                      task_id: Annotated[str, "任务的task_id"],
                      pid: Annotated[str, "用户id"],
                      video_url: Annotated[str, "视频地址"],
                      style_uuid: Annotated[str, "uuid16位"],
                      video_model_id: Annotated[str, "视频的模型id"], ):
    """
         通过用户数据创建视频制作任务
        :param
            pid: 用户的pid
            audio_url: 音频地址
            task_id: 任务id
            video_url: 视频地址
            video_model_id: 视频模型id
            uuid: 16位的uuid
        :return: 返回消息
    """
    response_data = {"task_id": task_id, "pid": pid, "video_url": video_url, "video_model_id": video_model_id,
                     "audio_url": audio_url, "uuid": style_uuid}
    logging.info(f'发起video_result，参数{response_data}')
    response = VideoRemoteService().video_create(json_data=response_data)
    return "response"


@tool(return_direct=True)
@register_tool
# Data tools
def tool_search_rag(task_id: Annotated[str, "任务id"],
                    pid: Annotated[str, "用户id"],
                    query: Annotated[str, "用户的需求"],
                    ):
    """
            通过pid获取用户的个人人设以及获取用户的知识库信息
        :param
            pid: 用户的pid
            task_id: 任务id
            query: 用户的需求
        :return: 返回消息
        """
    response_data = {"pid": int(pid)}
    person_info = DataRemoteService().get_person_info(json_data=response_data)
    response_data = {"query": query, "task_id": task_id, "pid": pid, "limit": 3}
    person_knowledge = DataRemoteService().search_rag(json_data=response_data)
    result_data = {
        "person_info": person_info,
        "person_knowledge": person_knowledge
    }
    return result_data


def make_kg_info(task_id, data):
    """
    发起制作图谱请求
    """
    json_data = {
        "redis_key": task_id,
        "text": data
    }
    try:
        DataRemoteService().create_knowledge(json_data=json_data)
    except Exception as e:
        logging.info('图谱报错', e)


@tool(return_direct=True)
@register_tool
def tool_create_knowledge(task_id: Annotated[str, "任务id"],
                          data: Annotated[str, "参考素材"]):
    """
        发起图谱制作
        :param
            task_id: 任务id
            data: 参考数据
        :return: 返回消息
        """
    kg_data = {
        "user_data": "",
        "benchmarking_data": "",
        "search_data": [data],
        "hot_data": []
    }
    # 生成知识图谱
    request_thread = threading.Thread(target=make_kg_info, args=(task_id, kg_data))
    request_thread.daemon = True
    request_thread.start()
    logging.info(f'发起制作图谱成功：{task_id}文案为{kg_data}')


@tool(return_direct=True)
@register_tool
def tool_search_knowledge(task_id: Annotated[str, "任务id"]):
    """
         通过task_id获取图谱三元组信息
        :param task_id: 用户的pid
        :return: 返回消息
        """
    response_data = {"redis_key": task_id}
    return DataRemoteService().search_knowledge(json_data=response_data)


def process_text_with_longblob(text: str) -> str:
    """使用 LONGBLOB 处理文本并转换为 UTF-8"""
    try:
        # 1. 转换为二进制 (模拟 LONGBLOB 存储)
        binary_data = text.encode('utf-8')

        # 2. 从二进制转回 UTF-8 字符串
        utf8_text = binary_data.decode('utf-8', errors='replace')

        # 3. 逐字符处理
        filtered_chars = []
        for char in utf8_text:
            if char in '\n\t\r' or ord(char) >= 32:
                filtered_chars.append(char)
            else:
                # 记录被过滤的字符
                logging.debug(f"Filtering character: {repr(char)}")

        return ''.join(filtered_chars)
    except Exception as e:
        logging.error(f"Text processing error: {str(e)}")
        return ''


@tool(return_direct=True)
@register_tool
def tool_search_network(question: Annotated[str, "用户需求"],
                        task_id: Annotated[int, '任务id']):
    """
        获取全网搜索数据
        :param
            question: 用户的问题
            task_id 任务id
        :return: 返回消息
        """
    response_data = {"content": question, "task_id": task_id}
    tool_start = time.time()
    logging.info(f'发起ai搜索，参数{response_data}')
    content = DataRemoteService().search_network(json_data=response_data)
    tool_end = time.time()
    logging.info(f"调取远程搜索耗时{tool_end - tool_start}秒")
    text = [i["graph_content"] for i in content["results"] if i["graph_content"] and i["graph_content"].strip()][0]
    result_text = {'markdown_list': text}
    link_data = [{"title": i["title"], "href": i["href"], "favicon": i["favicon"], } for i in content["results"]]
    return {"content": result_text, "link_info": link_data, "image_urls": content.get("image_urls", "")}


@tool(return_direct=True)
@register_tool
def tool_spider_data(url: Annotated[str, "参考网址"]):
    """
         通过task_id获取图谱三元组信息
        :param url: 参考网址
        :return: 返回消息
        """
    payload = {
        "bot_id": "7469721207349788722",
        "message": url,
    }
    return CoZeFunctionService().get_spider_data(json_data=payload)


# 工具函数获取方法
def get_tool_functions(tool_names: List[str]) -> List[Callable]:
    """
    根据工具名称列表获取对应的工具函数
    
    Args:
        tool_names: 工具函数名称列表
        
    Returns:
        List[Callable]: 工具函数对象列表
    """
    return [TOOL_FUNCTIONS[name] for name in tool_names if name in TOOL_FUNCTIONS]


def analyse_kg_data(documents: list):
    all_triplets = []

    for doc in documents:
        # 获取文本内容
        content = doc.get("page_content", "")

        # 查找Triplets部分
        if 'Triplets:' in content:
            # 提取Triplets行
            triplets_line = content.split('\n\n')[0].replace('Triplets:', '').strip()

            # 按|分割多个三元组
            triplet_strings = triplets_line.split('|')

            # 解析每个三元组
            for triplet_str in triplet_strings:
                # 跳过空字符串
                if not triplet_str.strip():
                    continue

                # 分解三元组
                try:
                    # 处理格式: "头实体 -- 关系 --> 尾实体"
                    head = triplet_str.split(' -- ')[0].strip()
                    relation = triplet_str.split(' -- ')[1].split(' --> ')[0].strip()
                    tail = triplet_str.split(' --> ')[1].strip()

                    # 添加到结果列表
                    all_triplets.append({
                        'head': head,
                        'relation': relation,
                        'tail': tail
                    })
                except IndexError:
                    print(f"Warning: Could not parse triplet: {triplet_str}")
                    continue
    try:
        nodes = []
        links = []
        node_ids = {}
        node_counter = 1  # 用于生成唯一的节点ID
        # 处理所有的头尾节点
        for item in all_triplets:
            head = item["head"]
            tail = item["tail"]
            relation = item["relation"]
            
            # 添加头节点
            if head not in node_ids:
                node_id = f"id{node_counter}"
                node_ids[head] = node_id
                nodes.append({"id": node_id, "name": head, "value": head})
                node_counter += 1
            
            # 添加尾节点
            if tail not in node_ids:
                node_id = f"id{node_counter}"
                node_ids[tail] = node_id
                nodes.append({"id": node_id, "name": tail, "value": tail})
                node_counter += 1
            
            # 添加链接
            links.append({
                "source": node_ids[head],
                "target": node_ids[tail],
                "value": relation
            })

        # 创建最终的数据结构
        result = {
            "nodes": nodes,
            "links": links
        }
    except Exception as e:
        result = {"nodes": [], "links": []}
    return result
