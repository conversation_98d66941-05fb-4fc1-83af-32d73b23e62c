import asyncio
import json
import logging
import re
import time
import uuid
from datetime import datetime
from typing import List, Any, Optional, AsyncGenerator, Dict

from langchain_core.messages import (
    HumanMessage,
    SystemMessage,
    ToolMessage,
    AIMessage
)
from langgraph.prebuilt import create_react_agent
from redis import Redis
from sqlalchemy.orm import Session
from tenacity import retry, stop_after_attempt, wait_exponential

from apps.api.schema.MultiAgentChatSchema import MultiAgentChatRequestSchema, AgentInfo
from common.remote.AudioFunctionService import AudioRemoteService
from common.remote.VideoFunctionService import VideoRemoteService
from common.service.AgentHistoryService import AgentHistoryService
from common.service.AgentWorkService import WorkService, AgentTasksService
from common.service.AgentsFunctionService import get_tool_functions
from common.service.LLMService import LLMService
from common.contents.AgentsContents import AgentsContents
from common.entity.AgentHistoryData import AgentHistoryData
from common.entity.AgentWorkData import WorkData, AgentTasksData
from common.service.ReidsStreamService import MultiAgentStreamManager


# from common.service.PydanticAiAgent import XhPydanticAi


class MultiAgentChatService:
    """
    多 Agent 聊天服务
    """

    def __init__(self):
        """
        初始化服务
        """
        self.llm_service = LLMService()
        self.other_agents = []
        # self.pydantic_agent = XhPydanticAi()

    @staticmethod
    def agents_class(agents: List[AgentInfo]):
        """
            格式化所有的agents
        """
        system_agents: List[AgentInfo] = []
        other_agents: List[AgentInfo] = []
        query_agents: List[AgentInfo] = []

        for agent in agents:
            if agent.agent_type == 1:  # 系统 Agent
                system_agents.append(agent)
            elif agent.agent_type in [0, 2, 3, 4, 5, 6]:  # 用户 Agent
                other_agents.append(agent)
            else:  # Query Agent
                query_agents.append(agent)
        return system_agents, other_agents, query_agents

    # 开始多 Agent 聊天
    @staticmethod
    def extract_workflow_ids(text: str) -> List[int]:
        """
        从文本中提取工作流 ID

        Args:
            text: 包含工作流信息的文本

        Returns:
            List[int]: 工作流 ID 列表

        Example:
            text = '{"workflow": ["37", "25", "29", "30"], "current_stage": "37"}'
            result = [37, 25, 29, 30]
        """
        try:
            # 首先尝试解析 JSON 格式
            json_pattern = r'"workflow"\s*:\s*\[(.*?)\]'
            match = re.search(json_pattern, text)

            if match:
                # 提取数字并转换为整数列表
                numbers_str = match.group(1)
                # 移除引号并分割字符串
                workflow_ids = [int(num.strip(' "\'')) for num in numbers_str.split(',') if num.strip(' "\'')]
                return workflow_ids

            # 如果没找到 JSON 格式，尝试其他格式
            workflow_pattern = r'workflows\s*=\s*\[([\d,\s]+)\]'
            match = re.search(workflow_pattern, text)

            if not match:
                # 如果还是没找到，尝试直接查找方括号内的数字
                bracket_pattern = r'\[([\d,\s]+)\]'
                match = re.search(bracket_pattern, text)

            if match:
                numbers_str = match.group(1)
                workflow_ids = [int(num.strip()) for num in numbers_str.split(',') if num.strip()]
                return workflow_ids

            logging.warning(f"未能从文本中提取到工作流ID: {text}")
            return []

        except Exception as e:
            logging.error(f"提取工作流ID时发生错误: {str(e)}")
            return []

    @staticmethod
    def get_history_messages(history):
        history_messages = []
        if history:
            for h in history:
                # 处理 DATA 类型的消息
                if h.get("agent_style") == "DATA" and h.get("content"):
                    try:
                        content = h.get("content", "")
                        agent_type = h.get("agent_type", "")
                        if content and agent_type != 3 and agent_type != 6:
                            content_json = json.loads(content)
                            # 检查 content_json 的类型
                            if isinstance(content_json, dict):
                                actual_content = content_json.get("content", {}).get("markdown_list", "")
                            elif isinstance(content_json, list):
                                actual_content = json.dumps(content_json, ensure_ascii=False)
                            else:
                                actual_content = str(content_json)
                        else:
                            actual_content = ""
                        # 添加用户消息，使用解析后的 markdown_list 作为内容
                        if actual_content:
                            history_messages.append({
                                "role": "user" if h.get("agent_type") == 9 else "assistant",
                                "agent_name": h.get("agent_name_en", "assistant"),
                                "content": actual_content
                            })
                    except Exception as e:
                        logging.error(str(e))
                else:
                    if h.get("agent_style") not in ["DATA_LOADING_1", "DATA_LOADING_2", "DATA", "TITLE_CHOOSE"]:
                        now_content = h.get("content", "")
                        if now_content:
                            # 其他类型消息保持不变
                            history_messages.append({
                                "role": "user" if h.get("agent_type") == 9 else "assistant",
                                "agent_name": h.get("agent_name_en", "assistant"),
                                "content": h.get("content", "")
                            })
        return history_messages

    @retry(
        stop=stop_after_attempt(3),  # 最多重试3次
        wait=wait_exponential(multiplier=1, min=2, max=10),  # 指数退避，等待时间在2-10秒之间
        retry_error_callback=lambda retry_state: None  # 重试失败后返回None
    )
    async def analyze_intention(self, intention_agent: AgentInfo, user_agents: List[AgentInfo], history,
                                task_id) -> \
            AsyncGenerator[tuple[str, Optional[AgentInfo]], None]:
        """
        分析用户意图，选择合适的处理 Agent（带重试机制）

        Returns:
            AsyncGenerator[tuple[str, Optional[AgentInfo]]]:
                - 流式返回 (content, None) 用于实时显示
                - 最后返回 (last_content, selected_agent) 包含选中的 Agent
        """
        try:
            # 构建描述
            agent_descriptions = [
                {
                    "id": i.id,
                    "agent_name": i.agent_name_en,
                    "agent_name_cn": i.agent_name_cn,
                    "description": i.description
                }
                for i in user_agents
                if i.agent_type != 0
            ]

            # 继续需求分析的prompt
            try:
                data = json.loads(intention_agent.prompt_cn)
            except:
                raise ValueError("需求分析的prompt不是一个json格式")

            intention_prompt = {
                "instructions": data,
                "available_agents": agent_descriptions
            }

            intention_prompt_result = json.dumps(intention_prompt, ensure_ascii=False)
            history_messages = self.get_history_messages(history)
            tmp_prompt = [
                {"role": "system", "content": intention_prompt_result},
                *history_messages
            ]
            llm_start = time.time()
            logging.info(f"已向大模型发送请求，{intention_agent.api_url}")
            # logging.info(f"{intention_agent.agent_name_cn}发送大模型的prompt为{self.safe_string(str(tmp_prompt))}")
            completion = await self.llm_service.chat_completion(
                api_key=intention_agent.api_key,
                base_url=intention_agent.api_url,
                model=intention_agent.model_name,
                messages=tmp_prompt,
                max_tokens=8192,
                task_id=task_id,
                temperature=0.1,
                chat_name=intention_agent.agent_name_en,
                stream=True  # 确保开启流式返回
            )
            llm_end = time.time()
            logging.info(f"调取大模型耗时{llm_end - llm_start}秒")
            content = ""
            async for chunk in completion:
                if chunk and chunk.choices and chunk.choices[0].delta.content:
                    chunk_content = chunk.choices[0].delta.content
                    content += chunk_content
                    # 流式返回当前内容，但还没有选定 Agent
                    yield chunk_content, None
            yield "", content

        except Exception as e:
            logging.error(f"意图识别失败: {str(e)}")
            raise  # 抛出异常以触发重试

    @staticmethod
    def get_history_info(db: Session, task_id: int) -> tuple[list, str]:
        """
            查询历史数据是否存在
            Args:
                db: 数据库
                task_id: 任务id
            Returns
                tuple[list, str]
        """
        task_info = AgentHistoryService(db).find_by_task_id(task_id)
        if task_info:
            history_id = task_info[0].id
            history_info = json.loads(task_info[0].history.decode('utf-8'))
        else:
            history_id = ""
            history_info = []
        return history_info, history_id

    @staticmethod
    def get_work_info(db: Session, task_id: int):
        """
            查询历史数据是否存在
            Args:
                db: 数据库
                task_id: 任务id
            Returns:
                Optional[AgentInfo]: None
                """
        task_info = WorkService(db).find_by_task_id(task_id)
        if task_info:
            writer_id = task_info[0].id
        else:
            writer_id = ""
        return writer_id

    @staticmethod
    def get_now_time():
        timestamp = time.time()

        # 将时间戳转换为datetime对象
        dt_object = datetime.fromtimestamp(timestamp)
        return dt_object.strftime('%Y-%m-%d %H:%M:%S')

    def get_new_workflow(self, history_info):
        """
        从历史记录中获取最后一个意图识别agent的workflow信息
        当agent_type不等于5时，排除倒数第二条消息的agent_id

        Args:
            history_info (list): 历史消息记录列表

        Returns:
            list: workflow ID列表，如果没找到则返回空列表
        """
        # 找到所有意图识别agent的消息
        intention_messages = [
            msg for msg in history_info
            if msg.get('agent_type') == 1 and msg.get('agent_role') == 1
        ]

        # 如果没有找到任何符合条件的消息，返回空列表
        if not intention_messages:
            return []
        try:
            # 获取workflow数据
            for msg in reversed(intention_messages):
                content = msg.get('content', '')
                workflow_ids = self.extract_workflow_ids(content)
                if workflow_ids:
                    # 获取最后一条消息的信息
                    if len(history_info) >= 2:
                        last_agent_id = history_info[-2].get('agent_id')  # 获取倒数第二条消息的agent_id
                        last_agent_type = history_info[-2].get('agent_type')  # 获取倒数第二条消息的agent_type

                        # 在workflow中找到last_agent_id的位置
                        try:
                            current_index = workflow_ids.index(last_agent_id)

                            # 如果是agent_type=5，从当前agent_id开始截取（包含当前）
                            if last_agent_type == 5:
                                return workflow_ids[current_index:]

                            # 其他情况，从下一个agent_id开始截取（不包含当前）
                            return workflow_ids[current_index + 1:]

                        except ValueError:
                            # 如果在workflow中找不到last_agent_id，返回空
                            return []

                    return workflow_ids
            return []

        except Exception as e:
            logging.error(f"从意图识别消息中提取workflow失败: {str(e)}")
            return []

    @staticmethod
    def get_text_workflow(content, history):
        """
        获取新的workflow_ids
        content:意图识别内容
        history:agent输出历史
        """
        try:
            # 1. 从当前内容中提取JSON
            json_pattern = r'\{[^}]+\}'
            match = re.search(json_pattern, content)

            if not match:
                logging.warning(f"未找到有效的 JSON 格式: {content}")
                return []

            json_str = match.group(0)
            current_data = json.loads(json_str)

            # 2. 如果当前JSON只包含next字段，查找历史workflow
            if 'next' in current_data and 'workflow' not in current_data:
                next_id = current_data['next']

                # 从历史记录中查找最近的带workflow的消息
                intention_messages = [
                    msg for msg in history
                    if msg.get('agent_id') == 23 and msg.get('agent_type') == 1
                ]

                for msg in reversed(intention_messages):
                    match = re.search(json_pattern, msg.get('content', ''))
                    if match:
                        history_json = json.loads(match.group(0))
                        if 'workflow' in history_json:
                            workflow = history_json['workflow']
                            try:
                                # 找到next_id在workflow中的位置
                                next_index = workflow.index(next_id)
                                # 返回next_id位置之后的所有元素（包含next_id）
                                workflow_ids = [int(id_str) for id_str in workflow[next_index:]]
                                return workflow_ids
                            except ValueError:
                                logging.warning(f"在workflow中未找到next_id: {next_id}")
                                continue

            # 3. 如果当前JSON包含workflow字段，直接使用
            if 'workflow' in current_data:
                workflow_ids = [int(id_str) for id_str in current_data['workflow']]
                return workflow_ids

            logging.warning("未找到有效的workflow字段")
            return []

        except json.JSONDecodeError as e:
            logging.error(f"JSON 解析失败: {str(e)}")
            return []
        except Exception as e:
            logging.error(f"处理 workflow 失败: {str(e)}")
            return []

    @staticmethod
    def save_workflow_title(db, task_id, text):
        AgentTasksService(db=db).update(
            AgentTasksData(id=task_id, title=text))

    def get_save_title(self, db, task_id, history):
        intention = [msg for msg in history
                     if msg.get('agent_type') == 1 and msg.get('agent_role') == 1]
        if len(intention) == 1:
            try:
                content = intention[0]["content"]
                json_pattern = r'\{[^}]+\}'
                match = re.search(json_pattern, content)
                json_str = match.group(0)
                current_data = json.loads(json_str)
                title_name = current_data.get("taskname", "")
                if title_name:
                    self.save_workflow_title(db, task_id, title_name)
                logging.info(f"获取到的title为：{title_name}")
            except json.JSONDecodeError:
                logging.info(f"需求分析未能正确返回数据")

    async def start_chat(self, search: MultiAgentChatRequestSchema, redis_client: Redis, db: Session):
        """
        开始多 Agent 聊天
        """
        # 步骤1: 解析入参，将 agents 分为意图识别agent 用户agent 和 其他
        system_agents, other_agents, query_agents = self.agents_class(search.workflow.agents)
        self.other_agents = other_agents

        # 步骤2: 获取意图识别 Agent 并进行意图分析
        intention_agent = system_agents[0]
        # 查询用户历史输入并将用户输入传入历史记录
        many_history, history_id = self.get_history_info(db, search.task_id)
        user_messages = self.get_save_messages(
            agents=query_agents[0],
            task_id=search.task_id,
            pid=search.pid,
            tmp_uuid=str(uuid.uuid1()),
            agent_uuid="",
            content=search.query,
            work_id="",
            stop=False
        )
        # 增加用户的消息到redis
        many_history.append(user_messages)
        history_id = self.save_messages_info(db, many_history, search.task_id, search.pid, history_id)
        # 获取作品的历史
        writer_history_id = self.get_work_info(db, search.task_id)
        # 在 start_chat 方法中的调用部分
        intention_content = ""
        intention_uuid = str(uuid.uuid1())
        if search.is_pass == 0:
            async for content, result_content in self.analyze_intention(intention_agent, other_agents, many_history,
                                                                        search.task_id):
                if result_content:
                    intention_content = result_content
                else:
                    now_time = self.get_now_time()
                    yield f"data: {json.dumps({'content': content, 'agent_name_cn': intention_agent.agent_name_cn, 'agent_name_en': intention_agent.agent_name_en, 'agent_id': intention_agent.id, 'agent_style': intention_agent.agent_style, 'task_id': search.task_id, 'created_at': now_time, 'uuid': intention_uuid, 'agent_uuid': '', 'agent_action': intention_agent.agent_action}, ensure_ascii=False)}\n\n"

            # 保存意图的消息
            intention_messages = self.get_save_messages(
                agents=intention_agent,
                task_id=search.task_id,
                pid=search.pid,
                tmp_uuid=intention_uuid,
                agent_uuid="",
                content=intention_content,
                work_id="",
                stop=False
            )
            many_history.append(intention_messages)
            history_id = self.save_messages_info(db, many_history, search.task_id, search.pid, history_id)
            self.get_save_title(db, search.task_id, many_history)
            # 获取并且拼接workflow列表
            selected_agent = self.get_text_workflow(intention_content, many_history)
        else:
            # 读取数据库
            history_info, _ = self.get_history_info(db, search.task_id)
            selected_agent = self.get_new_workflow(history_info)
        # selected_agent = [37]
        logging.info(f"意图识别的内容:\n{intention_content}")
        logging.info(f"需求分析的WorkFlowId: {selected_agent}")
        # 开始执行工作流
        async for current_agent, response, tmp_uid, agent_uuid, work_id in self.process_agent_workflow(
                search=search,
                workflow_ids=selected_agent,
                writer_id=writer_history_id,
                user_agents=other_agents,
                query=search.tmp_query,
                task_id=search.task_id,
                redis_client=redis_client,
                db=db,
                pid=search.pid,
                many_history=many_history,
                history_id=history_id
        ):
            # 添加时间
            time_now = self.get_now_time()
            # 修改需要增加到返回字段上面
            info = json.dumps(
                {'content': response, 'agent_name_cn': current_agent.agent_name_cn, 'agent_id': current_agent.id,
                 'agent_style': current_agent.agent_style, 'task_id': search.task_id, 'created_at': time_now,
                 'agent_name_en': current_agent.agent_name_en,
                 'uuid': tmp_uid, 'agent_uuid': agent_uuid, 'work_id': work_id, 'agent_action': current_agent.agent_action}, ensure_ascii=False)
            # logging.info(info)
            yield f"data: {info}\n\n"
            await asyncio.sleep(0.01)

    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=2, max=10),
        retry_error_callback=lambda retry_state: None)
    async def process_query(self, query: str, agent: AgentInfo, history: list, task_id) -> AsyncGenerator[str, None]:
        try:
            history_messages = self.get_history_messages(history)
            tmp_prompt = [
                {"role": "system", "content": agent.prompt_cn},
                *history_messages  # 添加用户的查询内容
            ]

            # logging.info(f"{agent.agent_name_cn}发送大模型的prompt为{self.safe_string(str(tmp_prompt))}")
            llm_start = time.time()
            logging.info(f"已向大模型发送请求，{agent.api_url}")

            completion = await self.llm_service.chat_completion(
                api_key=agent.api_key,
                base_url=agent.api_url,
                model=agent.model_name,
                messages=tmp_prompt,
                temperature=0.7,
                max_tokens=8192,
                task_id=task_id,
                chat_name=agent.agent_name_en,
                stream=True
            )

            buffer_size = 5
            content_found = False
            buffer_count = 0
            has_content = False  # 添加标志来追踪是否有任何内容被生成

            async for chunk in completion:
                if not content_found:
                    if chunk and chunk.choices and chunk.choices[0].delta.content:
                        content = chunk.choices[0].delta.content.strip()
                        if content:
                            content_found = True
                            has_content = True
                            yield content
                    elif buffer_count >= buffer_size:
                        logging.warning(f"{agent.agent_name_cn}响应为空，触发重试")
                        raise ValueError("Empty response from LLM")
                    buffer_count += 1
                else:
                    if chunk and chunk.choices and chunk.choices[0].delta.content:
                        has_content = True
                        yield chunk.choices[0].delta.content
                    if chunk and chunk.choices and chunk.choices[0].finish_reason == 'stop':
                        break

            # 确保在有内容的情况下发送 [DONE] 信号
            if has_content:
                yield "[DONE]"
            else:
                logging.warning(f"{agent.agent_name_cn}没有生成任何内容")
                raise ValueError("No content generated")

            llm_end = time.time()
            logging.info(f"调取大模型耗时{llm_end - llm_start}秒")

        except Exception as e:
            logging.error(f"处理查询失败: {str(e)}")
            raise  # 重新抛出异常以触发重试

    @staticmethod
    def save_messages_info(db: Session, result_data: list, task_id: int, pid: int, history_id):
        """
        将agent的输出进行存库
        Args:
            result_data: agent消息
            db: 数据库
            task_id: 任务id
            pid: 用户id，
            history_id: 当前数据数据库的id
        Returns:
            Optional[AgentInfo]: None
        """
        if len(result_data) > 1:
            AgentHistoryService(db).update(
                AgentHistoryData(id=history_id, history=json.dumps(result_data, ensure_ascii=False)))
        else:
            res_data = AgentHistoryData(
                task_id=str(task_id),
                pid=str(pid),
                history=json.dumps(result_data, ensure_ascii=False),
                remark='Agents制作',
                status=1
            )
            AgentHistoryService(db).save(res_data)
            task_info = AgentHistoryService(db).find_by_task_id(task_id)
            history_id = task_info[0].id
        return history_id

    @staticmethod
    def save_writer_info(db: Session, result_data: str, task_id: int, pid: int):
        save_work_info = WorkData(
            task_id=task_id,
            work_type=1,
            content=result_data,
            status=1,
            pid=pid,
            remark="Agents制造",
            title="",
        )
        info = WorkService(db).save(save_work_info)
        return info.id

    def get_info_index(self, current_agent, many_history):
        tool_scope = []
        if current_agent.influence_scope:
            tool_scope.append(current_agent.id)
            tool_scope.extend(eval(current_agent.influence_scope))
        else:
            tool_scope.append(current_agent.id)

        agent_count_tmp = {}

        # 遍历数据列表
        for item in many_history:
            # 获取当前项的agent_id
            agent_id = item['agent_id']
            # 如果agent_id已经在字典中，增加它的计数
            if agent_id in agent_count_tmp:
                agent_count_tmp[agent_id] += 1
            # 如果agent_id不在字典中，初始化它的计数为1
            else:
                agent_count_tmp[agent_id] = 1
        tmp_agents_info = {}
        for key, value in agent_count_tmp.items():
            tmp_agents_info[str(key)] = value
        tmp_two_id = tmp_agents_info.get(str(tool_scope[1]), 0)
        tmp_three_id = tmp_agents_info.get(str(tool_scope[2]), 0)
        two_id = int(tmp_two_id) + int(tmp_three_id)
        one_id = tmp_agents_info.get(str(tool_scope[0]), 0)

        wheel_two = self.custom_division(two_id, 2)
        remainder_two = two_id % 2
        if remainder_two == 0:
            if wheel_two == one_id:
                current_index = 0
            else:
                current_index = 1
        else:
            current_index = 1
        return tool_scope, current_index

    @staticmethod
    def custom_division(a, b):
        """
            寻找最新的小标状态
        Args:
            a: 展开专家的次数
            b: 生成专家的次数
        """
        # 执行除法运算
        result = a / b
        # 如果结果是整数，则直接返回结果
        if result.is_integer():
            return int(result)
        # 如果结果不是整数，根据被除数返回不同的值
        elif a < b:
            return 0
        elif a == b:
            return 1
        else:
            # 对于大于除数的被除数，向上取整
            return (a // b) + 1 if (a % b) else (a // b)

    def get_save_messages(self, agents, task_id, pid, tmp_uuid, agent_uuid, content, work_id, stop=False):
        """
        保存所有的数据
        """
        messages = {
            "task_id": task_id,
            "pid": pid,
            "user_stop": stop,
            "agent_id": agents.id,
            "agent_style": agents.agent_style,
            "agent_action": agents.agent_action,
            "agent_name_cn": agents.agent_name_cn,
            "agent_name_en": agents.agent_name_en,
            "model_name": agents.model_name,
            "created_at": self.get_now_time(),
            "uuid": tmp_uuid,
            "agent_uuid": agent_uuid,
            "agent_type": agents.agent_type,
            "agent_role": agents.agent_role,
            "work_id": work_id,
            "content": content
        }
        return messages

    async def process_agent_workflow(
            self,
            workflow_ids: List,
            user_agents: List[AgentInfo],
            writer_id,
            query: str,
            task_id: int,
            redis_client: Redis,
            db: Session,
            pid: int,
            many_history: list,
            history_id,
            search: MultiAgentChatRequestSchema
    ) -> AsyncGenerator[tuple[AgentInfo, Any], None]:
        tmp_history_id = history_id

        for workflow_id in workflow_ids:
            # 寻找当前的agent
            current_agent = next((agent for agent in user_agents if agent.id == int(workflow_id)), None)
            logging.info(f"当前agent名称: {current_agent.agent_name_cn}")
            tmp_uuid = str(uuid.uuid1())
            try:
                agent_uuid = search.agent_uuid or ""

                if current_agent.agent_type == 5:  # Tool Agent
                    # tool_scope = [37, 39]
                    agent_uuid = search.agent_uuid or tmp_uuid
                    # 逻辑获取下一个工具发言者后续 可以考虑用大模型替换
                    tool_scope, current_index = self.get_info_index(current_agent, many_history)
                    logging.info(f"最终的 current_index: {current_index}")
                    logging.info(f"当前agent名称: {current_agent.agent_name_cn}")
                    if current_index == 0:
                        current_agent = next((agent for agent in user_agents if agent.id == tool_scope[0]), None)
                        # 音频 视频分开调用
                        response = ""
                        if current_agent.agent_style == "VOICE_1":
                            audio_data = AudioRemoteService().audio_info(json_data={"pid": pid})
                            response = json.dumps(audio_data, ensure_ascii=False)
                        elif current_agent.agent_style == "VIDEO_1":
                            video_data = VideoRemoteService().video_info(json_data={"pid": pid, "status":1})
                            response = json.dumps(video_data, ensure_ascii=False)
                        elif current_agent.agent_style == "VOICE_UPLOAD":
                            response = ""
                        tmp_messages_one = self.get_save_messages(
                            agents=current_agent,
                            task_id=task_id,
                            pid=pid,
                            tmp_uuid=tmp_uuid,
                            agent_uuid=tmp_uuid,
                            content=str(response),
                            work_id=writer_id,
                            stop=False
                        )
                        many_history.append(tmp_messages_one)
                        history_id = self.save_messages_info(db, many_history, task_id, pid, tmp_history_id)
                        tmp_history_id = history_id
                        yield current_agent, response, tmp_uuid, tmp_uuid, ""
                    else:
                        status_uuid = str(uuid.uuid1())
                        current_agent = next((agent for agent in user_agents if agent.id == int(tool_scope[1])), None)
                        logging.info(f"当前agent名称: {current_agent.agent_name_cn}")
                        tmp_response = ""
                        if current_agent.agent_style == "VIDEO_2":
                            video_loading = {"preview_url": search.video_model_pic_url}
                            tmp_response = json.dumps(video_loading, ensure_ascii=False)
                        loading_info = self.get_save_messages(
                            agents=current_agent,
                            task_id=task_id,
                            pid=pid,
                            tmp_uuid=status_uuid,
                            agent_uuid=agent_uuid,
                            content=str(tmp_response),
                            work_id=writer_id,
                            stop=False
                        )
                        many_history.append(loading_info)
                        history_id = self.save_messages_info(db, many_history, task_id, pid, tmp_history_id)
                        tmp_history_id = history_id
                        yield current_agent, tmp_response, status_uuid, agent_uuid, ""

                        # 切换style类型
                        style_uuid = str(uuid.uuid1())
                        current_agent = next((agent for agent in user_agents if agent.id == int(tool_scope[-1])), None)
                        # 音视频结果分开发送
                        if search.voice_is_upload:
                            result_response = json.dumps({"audio_url": search.voice_upload_url},ensure_ascii=False)
                            result_info = self.get_save_messages(
                                agents=current_agent,
                                task_id=task_id,
                                pid=pid,
                                tmp_uuid=style_uuid,
                                agent_uuid=agent_uuid,
                                content=str(result_response),
                                work_id=writer_id,
                                stop=False
                            )
                            many_history.append(result_info)
                            history_id = self.save_messages_info(db, many_history, task_id, pid, tmp_history_id)
                            tmp_history_id = history_id
                            yield current_agent, result_response, style_uuid, agent_uuid, ""
                        elif current_agent.agent_style == "VOICE_3":
                            voice_index = next(
                                (index for index, value in enumerate(many_history[::-1]) if
                                 value["agent_style"] == "VOICE_1"),
                                None)
                            user_info = next(
                                (value['content'] for index, value in enumerate(many_history[::-1][voice_index:]) if
                                 value["agent_style"] == "USER"),
                                None)
                            tmp_style_info = next(
                                (value['content'] for index, value in enumerate(many_history[::-1][voice_index:]) if
                                  value["agent_name_en"] == "style_expert"),
                                "None")
                            if len(user_info) >= 80 and tmp_style_info == "None":
                                response_data = {"task_id": task_id, "pid": pid, "voice_id": search.audio_model_id,
                                                 "content": user_info}
                                response_tmp = json.dumps(response_data, ensure_ascii=False)
                                response_data = json.loads(response_tmp)
                            else:
                                response_data = {"task_id": task_id, "pid": pid, "voice_id": search.audio_model_id}

                            logging.info(f'发起audio_create，参数{response_data}')
                            audio_result = AudioRemoteService().audio_create(json_data=response_data)
                            result_response = json.dumps(audio_result, ensure_ascii=False)
                            result_info = self.get_save_messages(
                                agents=current_agent,
                                task_id=task_id,
                                pid=pid,
                                tmp_uuid=style_uuid,
                                agent_uuid=agent_uuid,
                                content=str(result_response),
                                work_id=writer_id,
                                stop=False
                            )
                            many_history.append(result_info)
                            history_id = self.save_messages_info(db, many_history, task_id, pid, tmp_history_id)
                            tmp_history_id = history_id
                            yield current_agent, result_response, style_uuid, agent_uuid, ""
                        elif current_agent.agent_style == "VIDEO_3":
                            audio_info = next((json.loads(i["content"]) for i in many_history[::-1] if i["agent_style"] == "VOICE_3"), None)
                            audio_url = audio_info.get("new_audio_url", "") or audio_info.get("audio_url", "")
                            response_data = {"task_id": task_id, "pid": pid, "video_url": search.video_url,
                                             "video_model_id": search.video_model_id,
                                             "audio_url": audio_url, "uuid": style_uuid}
                            logging.info(f'发起video_result，参数{response_data}')
                            response = VideoRemoteService().video_create(json_data=response_data)
                            result_response = json.dumps({
                                "video_url": "",
                                "preview_url": search.video_model_pic_url
                            }, ensure_ascii=False)
                            result_info = self.get_save_messages(
                                agents=current_agent,
                                task_id=task_id,
                                pid=pid,
                                tmp_uuid=style_uuid,
                                agent_uuid=agent_uuid,
                                content=str(result_response),
                                work_id=writer_id,
                                stop=False
                            )
                            many_history.append(result_info)
                            history_id = self.save_messages_info(db, many_history, task_id, pid, tmp_history_id)
                            tmp_history_id = history_id
                            logging.info(f"当前下标为：{current_index}")
                            yield current_agent, result_response, style_uuid, agent_uuid, ""
                    await asyncio.sleep(0.05)
                    if current_index == 0:
                        break
                elif current_agent.agent_type == 3:
                    search_group_uuid = str(uuid.uuid1())
                    tool_scope = []
                    if current_agent.influence_scope:
                        tool_scope.append(current_agent.id)
                        tool_scope.extend(eval(current_agent.influence_scope))
                    else:
                        tool_scope.append(current_agent.id)
                    for index, i in enumerate(tool_scope):
                        search_tmp_uuid = str(uuid.uuid1())
                        current_agent = next((agent for agent in user_agents if agent.id == int(i)), None)
                        logging.info(f"当前agent名称: {current_agent.agent_name_cn}")
                        if index == len(tool_scope) - 1:
                            response = await self.process_tool_agent(query, current_agent, many_history, task_id)
                        else:
                            response = ""
                        tmp_messages_two = self.get_save_messages(
                            agents=current_agent,
                            task_id=task_id,
                            pid=pid,
                            tmp_uuid=search_tmp_uuid,
                            agent_uuid=search_group_uuid,
                            content=response,
                            work_id=writer_id
                        )
                        many_history.append(tmp_messages_two)
                        history_id = self.save_messages_info(db, many_history, task_id, pid, tmp_history_id)
                        tmp_history_id = history_id
                        yield current_agent, response, search_tmp_uuid, search_group_uuid, ""
                elif current_agent.agent_style == 7:
                    # 上传音频进行选择
                    tool_scope = []
                    if current_agent.influence_scope:
                        tool_scope.append(current_agent.id)
                        tool_scope.extend(eval(current_agent.influence_scope))
                    else:
                        tool_scope.append(current_agent.id)
                # elif current_agent.agent_type == 6:
                #     # 利用pydantic_ai实现对输出的控制
                #     tmp_info = []
                #     pydantic_history_messages = self.get_history_messages(many_history)
                #     async for response in self.pydantic_agent.get_title_info(current_agent, pydantic_history_messages):
                #         tmp_info.append(response)
                #         yield current_agent, response, tmp_uuid, "", ""
                #     tmp_messages_one = self.get_save_messages(
                #         agents=current_agent,
                #         task_id=task_id,
                #         pid=pid,
                #         tmp_uuid=tmp_uuid,
                #         agent_uuid=agent_uuid,
                #         content=tmp_info[-1],
                #         work_id=writer_id,
                #         stop=False
                #     )
                #     many_history.append(tmp_messages_one)
                #     history_id = self.save_messages_info(db, many_history, task_id, pid, tmp_history_id)
                #     tmp_history_id = history_id
                #     break
                else:
                    tmp_info = ""
                    is_succeed = 0
                    # 第一部分：收集所有响应
                    async for response in self.process_query(query, current_agent, many_history, task_id):
                        if response != "[DONE]":
                            tmp_info += response
                        tmp_redis_key = f"{AgentsContents.AIP_STOP_INFO}{task_id}"
                        await asyncio.sleep(0.05)

                        # 处理用户中断
                        if await redis_client.exists(tmp_redis_key):
                            await redis_client.delete(tmp_redis_key)
                            if current_agent.agent_type == 4:
                                res_work_id = self.save_writer_info(db, tmp_info, task_id, pid)
                                writer_id = res_work_id
                                # 先发送正常的响应
                                yield current_agent, response, tmp_uuid, "", ""
                                # 等待一下再发送存库结果
                                await asyncio.sleep(0.2)
                                yield current_agent, "", tmp_uuid, "", writer_id
                            tmp_messages_one = self.get_save_messages(
                                agents=current_agent,
                                task_id=task_id,
                                pid=pid,
                                tmp_uuid=tmp_uuid,
                                agent_uuid=agent_uuid,
                                content=tmp_info,
                                stop=True,
                                work_id=writer_id
                            )
                            many_history.append(tmp_messages_one)
                            history_id = self.save_messages_info(db, many_history, task_id, pid, tmp_history_id)
                            tmp_history_id = history_id
                            logging.info(f"用户主动打断当前agent{current_agent.agent_name_cn}")
                            return

                        if response == "[DONE]":
                            is_succeed = 1
                            break
                        # 发送正常的流式响应
                        yield current_agent, response, tmp_uuid, "", ""
                    await asyncio.sleep(2)
                    # 第二部分：处理完整响应
                    logging.info(f"当前agent名称: {current_agent.agent_name_cn}\n输出信息为：\n'{tmp_info}'")
                    while True:
                        if is_succeed:
                            if current_agent.agent_type == 4:
                                # 文案写手存库
                                res_work_id = self.save_writer_info(db, tmp_info, task_id, pid)
                                writer_id = res_work_id
                                # 发送存库结果
                                yield current_agent, "", tmp_uuid, "", res_work_id
                                break
                            else:
                                yield current_agent, "", tmp_uuid, "", ""
                                break
                    # 更新历史记录
                    tmp_messages_one = self.get_save_messages(
                        agents=current_agent,
                        task_id=task_id,
                        pid=pid,
                        tmp_uuid=tmp_uuid,
                        agent_uuid=agent_uuid,
                        content=tmp_info,
                        work_id=writer_id,
                        stop=False
                    )
                    many_history.append(tmp_messages_one)
                    history_id = self.save_messages_info(db, many_history, task_id, pid, tmp_history_id)
                    tmp_history_id = history_id

                    if current_agent.agent_type == 6:
                        # 选题专家暂停
                        break
            except Exception as e:
                logging.error(f"处理Agent {current_agent.agent_name_cn} 失败: {str(e)}")
                yield current_agent, {
                    'content': f"工作流处理失败: {str(e)}",
                    'name': current_agent.agent_name_cn,
                    'tid': task_id
                }, tmp_uuid, "", ""

    @staticmethod
    def safe_string(text):
        try:
            # 1. 转换为二进制 (模拟 LONGBLOB 存储)
            binary_data = text.encode('utf-8')

            # 2. 从二进制转回 UTF-8 字符串
            utf8_text = binary_data.decode('utf-8', errors='replace')

            # 3. 逐字符处理
            filtered_chars = []
            for char in utf8_text:
                if char in '\n\t\r' or ord(char) >= 32:
                    filtered_chars.append(char)
                else:
                    # 记录被过滤的字符
                    logging.debug(f"Filtering character: {repr(char)}")

            return ''.join(filtered_chars)
        except Exception as e:
            logging.error(f"Text processing error: {str(e)}")
            return ''

    @retry(
        stop=stop_after_attempt(5),  # 最多重试3次
        wait=wait_exponential(multiplier=1, min=5, max=30),  # 指数退避等待
        retry_error_callback=lambda retry_state: None)
    async def create_tool_agent(self, query: str, tool_agent: AgentInfo, history, task_id, intact, style_uuid):
        """
        分析用户意图，选择合适的处理 Agent（带重试机制）
        """
        try:
            tool_names = [tool.tool_function for tool in tool_agent.tools]
            tool_prompt = "\n".join([
                f"{info['agent_name_cn']}: {info['content']}"
                for info in history
            ])
            # system_message = tool_agent.prompt_cn
            system_mes = f'用户历史记录如下\n{tool_prompt}'

            # 获取实际的工具函数列表
            tools_info = get_tool_functions(tool_names)
            logging.info(f"{tool_agent.agent_name_cn}调用工具为: {tool_names}")

            llm = await self.llm_service.create_graph_llm(
                api_key=tool_agent.api_key,
                base_url=tool_agent.api_url,
                model_name=tool_agent.model_name,
            )
            # 注册工具
            # llm.bind_tools(tools_info)
            # llm.invoke().tool_calls
            graph = create_react_agent(llm, tools=tools_info)
            if uuid:
                query = f'用户的style_uuid为：{style_uuid}\n' + query
            message = [
                SystemMessage(content=tool_agent.prompt_cn),
                AIMessage(content=system_mes),
                HumanMessage(content=query)]
            inputs = {"messages": message}
            # graph.invoke(inputs).tool
            llm_start = time.time()
            logging.info(f"已向大模型发送请求，{tool_agent.api_url}")
            info = graph.invoke(inputs, {"metadata": {"session_id": task_id}})
            llm_end = time.time()
            logging.info(f"调取大模型耗时{llm_end - llm_start}秒")
            if not intact:
                for s in info["messages"]:
                    message = s
                    if isinstance(message, ToolMessage):
                        message_result = message.content
                        if message_result:
                            message_content = self.safe_string(str(message_result))
                            logging.info(f"{tool_agent.agent_name_cn}工具结果: {str(message_content)}")
                            if message_result == "response":
                                return message_result
                            else:
                                message_result = json.loads(message_result)
                                return message_result
                        else:
                            logging.error(f"{tool_agent.agent_name_cn}工具结果为空")
                            return None  # 触发重试
            else:
                result = info["messages"][-1]
                if result:
                    return result
                else:
                    return None
        except Exception as e:
            logging.error(f"{tool_agent.agent_name_cn}工具调用失败:{str(e)}")
            raise  # 抛出异常以触发重试
        logging.error(f"{tool_agent.agent_name_cn}工具结果为空")
        return None

    async def process_tool_agent(self, query: str, tool_agent: AgentInfo, history: List, task_id: int,
                                 intact: bool = False, style_uuid: str = None):
        """
        处理工具 Agent 的请求
        Args:
            tool_agent: 工具 Agent 信息
            query: 用户的问题
            history: 历史信息
            task_id: 任务id
            intact: react执行到工具是否结束
            style_uuid: agent的uuid
        Returns:
            Dict[str, Any]: 工具执行结果
        """
        try:
            logging.info(f"工具调用开始{tool_agent.agent_name_cn}")
            info = await self.create_tool_agent(query, tool_agent, history, task_id, intact, style_uuid)
            if not info or info == '' or info == "null":
                return '[]'
            elif info == "response":
                return ""
            if not isinstance(info, str):
                json_info = json.dumps(info, ensure_ascii=False)
            else:
                json_info = info
            return json_info

        except Exception as e:
            logging.error(f"工具 Agent 处理失败: {str(e)}")
            raise
