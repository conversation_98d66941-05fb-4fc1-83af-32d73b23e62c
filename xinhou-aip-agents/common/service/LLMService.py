import asyncio
import json
from typing import AsyncIterator
from typing import Optional, Dict, Any, List

import aiohttp
from langchain_openai import ChatOpenAI
from langsmith.wrappers import wrap_openai
from openai import AsyncOpenAI

from common.contents.AgentsContents import AgentsContents


class LLMService:
    def __init__(self):
        pass

    async def create_client(self, api_key: str, base_url: str) -> AsyncOpenAI:
        """
        创建 OpenAI 异步客户端
        """
        return AsyncOpenAI(
            api_key=api_key,
            base_url=base_url
        )

    def chat_R1_completion(self, api_key, messages):
        from openai import OpenAI
        client = OpenAI(api_key=api_key, base_url="https://api.deepseek.com")
        response = client.chat.completions.create(
            model="deepseek-reasoner",
            messages=messages,
            stream=True
        )
        return response

    async def chat_completion(
            self,
            api_key: str,
            base_url: str,
            model: str,
            task_id: int,
            messages: List[Dict[str, str]],
            temperature: float = 0.7,
            max_tokens: int = 8192,
            stream: bool = False,
            chat_name: str = 'Chat'
    ) -> Any:
        """
        执行 LLM 聊天补全
        """
        client = await self.create_client(api_key, base_url)
        client = wrap_openai(client, chat_name=chat_name,
                             tracing_extra={"metadata": {"session_id": task_id}, "tags": [str(task_id)]})

        completion = await client.chat.completions.create(
            model=model,
            messages=messages,
            temperature=temperature,
            max_tokens=max_tokens,
            stream=stream
        )

        return completion

    @staticmethod
    async def create_graph_llm(api_key: str, base_url: str, model_name: str,
                               temperature: Optional[float] = 0.7) -> ChatOpenAI:
        """
        创建 LLM 实例
        Args:
            api_key: API密钥
            base_url: API基础URL
            model_name: 模型名称
            temperature: 温度参数，默认0.7
        Returns:
            ChatOpenAI: LLM实例
        """
        return ChatOpenAI(
            temperature=temperature,
            api_key=api_key,
            base_url=base_url,
            model=model_name
        )

    @staticmethod
    async def raw_chat_completion(
            redis_manager,
            task_id,
            agent,
            redis_client,
            api_key: str,
            messages: List[Dict[str, str]],
            model: str = "deepseek-ai/DeepSeek-R1",
            stream: bool = True,
            base_url: str = "https://api.gpt2share.com",

    ):
        """
        使用原生HTTP请求调用大模型API
        Args:
            api_key: API密钥
            messages: 消息列表
            model: 模型名称
            stream: 是否使用流式响应
            base_url: API基础URL
            temperature: 温度参数
            max_tokens: 最大token数
        Yields:
            str: 模型响应内容
        """
        headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json",
            "Accept": "*/*",
            "User-Agent": "Apifox/1.0.0 (https://apifox.com)",
        }

        payload = {
            "model": model,
            "messages": messages,
            "stream": stream,
        }
        is_stop = False
        result_content = ""
        async with aiohttp.ClientSession() as session:
            async with session.post(
                    f"{base_url}/chat/completions",
                    headers=headers,
                    json=payload
            ) as response:
                if stream:
                    async for line in response.content:
                        if line:
                            line = line.decode('utf-8').strip()
                            if line.startswith('data: '):
                                data = line[6:]
                                if data != '[DONE]':
                                    try:
                                        chunk = json.loads(data)
                                        if chunk.get('choices') and chunk['choices'][0].get('delta', {}).get('content'):
                                            chunk_content = chunk['choices'][0]['delta']['content']
                                            result_content += chunk_content
                                            await redis_manager.stream_input_content(
                                                task_id,
                                                agent.id,
                                                chunk_content,  # 发送当前chunk
                                                redis_client
                                            )
                                            tmp_redis_key = f"{AgentsContents.AIP_STOP_INFO}{task_id}"
                                            await asyncio.sleep(0.05)
                                            if await redis_client.exists(tmp_redis_key):
                                                await redis_client.delete(tmp_redis_key)
                                                is_stop = True
                                                break
                                    except json.JSONDecodeError:
                                        continue
        if not result_content:
            await redis_manager.stream_input_content(
                task_id,
                agent.id,
                "服务器繁忙，请稍后重试~",  # 发送当前chunk
                redis_client
            )
        return result_content, is_stop

    @staticmethod
    async def claude_chat_completion(
            redis_manager,
            task_id,
            agent,
            redis_client,
            api_key: str,
            messages: List[Dict[str, str]],
            model: str = "deepseek-ai/DeepSeek-R1",
            stream: bool = True,
            base_url: str = "https://api.gpt2share.com",

    ):
        """
        使用原生HTTP请求调用大模型API
        Args:
            api_key: API密钥
            messages: 消息列表
            model: 模型名称
            stream: 是否使用流式响应
            base_url: API基础URL
            temperature: 温度参数
            max_tokens: 最大token数
        Yields:
            str: 模型响应内容
        """
        headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json",
            "Accept": "*/*",
            "User-Agent": "Apifox/1.0.0 (https://apifox.com)",
        }

        payload = {
            "model": model,
            "messages": messages,
            "stream": stream,
        }
        flag_bool = True
        is_stop = False
        result_content = "<think>"
        await redis_manager.stream_input_content(
            task_id,
            agent.id,
            "<think>",  # 发送当前chunk
            redis_client
        )
        async with aiohttp.ClientSession() as session:
            async with session.post(
                    f"{base_url}/chat/completions",
                    headers=headers,
                    json=payload
            ) as response:
                if stream:
                    async for line in response.content:
                        if line:
                            line = line.decode('utf-8').strip()
                            if line.startswith('data: '):
                                data = line[6:]
                                if data != '[DONE]':
                                    try:
                                        chunk = json.loads(data)
                                        if chunk.get('choices') and chunk['choices'][0].get('delta', {}).get('content') or chunk['choices'][0].get('delta', {}).get('reasoning_content'):
                                            flag_index = chunk['choices'][0]['index']
                                            if not flag_index:
                                                chunk_content = chunk['choices'][0]['delta']['reasoning_content']
                                                result_content += chunk_content
                                                await redis_manager.stream_input_content(
                                                    task_id,
                                                    agent.id,
                                                    chunk_content,  # 发送当前chunk
                                                    redis_client
                                                )
                                            elif flag_index and flag_bool:
                                                await redis_manager.stream_input_content(
                                                    task_id,
                                                    agent.id,
                                                    "</think>",  # 发送当前chunk
                                                    redis_client
                                                )
                                                result_content += "</think>"
                                                flag_bool = False
                                                chunk_content = chunk['choices'][0]['delta']['content']
                                                result_content += chunk_content
                                                await redis_manager.stream_input_content(
                                                    task_id,
                                                    agent.id,
                                                    chunk_content,  # 发送当前chunk
                                                    redis_client
                                                )
                                            else:
                                                chunk_content = chunk['choices'][0]['delta']['content']
                                                result_content += chunk_content
                                                await redis_manager.stream_input_content(
                                                    task_id,
                                                    agent.id,
                                                    chunk_content,  # 发送当前chunk
                                                    redis_client
                                                )
                                            tmp_redis_key = f"{AgentsContents.AIP_STOP_INFO}{task_id}"
                                            await asyncio.sleep(0.05)
                                            if await redis_client.exists(tmp_redis_key):
                                                await redis_client.delete(tmp_redis_key)
                                                is_stop = True
                                                break
                                    except json.JSONDecodeError:
                                        continue
        if not result_content:
            await redis_manager.stream_input_content(
                task_id,
                agent.id,
                "服务器繁忙，请稍后重试~",  # 发送当前chunk
                redis_client
            )
        return result_content, is_stop
