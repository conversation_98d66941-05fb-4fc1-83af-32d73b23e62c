# -*- coding: utf-8 -*-
import asyncio
import json
import logging
import random
import time
import uuid
import re
from datetime import datetime
from typing import List, Optional, Any, AsyncGenerator, Dict

from langchain_core.messages import (
    HumanMessage,
    SystemMessage,
    ToolMessage,
    AIMessage
)
from langgraph.prebuilt import create_react_agent
from redis import Redis
from sqlalchemy.orm import Session
from tenacity import retry, stop_after_attempt, wait_exponential

from apps.api.schema.MultiAgentChatSchema import AgentInfo, MultiAgentChatRequestSchema
from common.service.AgentHistoryService import AgentHistoryService
from common.service.AgentWorkService import WorkService
from common.service.AgentsFunctionService import get_tool_functions
from common.service.LLMService import LLMService
from common.contents.AgentsContents import AgentsContents
from common.entity.AgentHistoryData import AgentHistoryData
from common.entity.AgentWorkData import WorkData
from common.service.PromptConfigService import PromptConfigService


class WorkflowService:
    """工作流服务类"""

    def __init__(self):
        self.llm_service = LLMService()
        self.debug_logs = []  # 用于收集调试日志
        self.current_workflow = []  # 存储当前工作流
        self.prompt_service = PromptConfigService()  # 添加 prompt 服务

    def add_debug_log(self, message: str):
        """添加调试日志"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]
        log_entry = f"[{timestamp}] {message}"
        self.debug_logs.append(log_entry)
        logging.info(log_entry)

    async def debug_chat(self, search: MultiAgentChatRequestSchema, redis_client: Redis, db: Session) -> Dict:
        """
        同步版本的聊天处理，用于调试
        Returns:
            Dict: {
                'workflow': List[str],  # 工作流程记录
                'messages': List[Dict],  # 消息记录
                'debug_logs': List[str], # 调试日志
                'error': Optional[str]   # 错误信息
            }
        """
        self.debug_logs = []
        messages = []
        workflow = []
        error = None

        try:
            # 1. 解析 agents
            self.add_debug_log("开始解析 agents...")
            system_agents, user_agents, query_agents, tool_agents = self.format_agents(search.workflow.agents)
            self.user_agents = user_agents
            self.tool_agents = tool_agents

            # 2. 获取意图识别 Agent
            self.add_debug_log("获取意图识别 Agent...")
            intention_agent = self.get_intention_agent(system_agents)
            if not intention_agent:
                raise ValueError("未找到意图识别 Agent")

            # 3. 获取历史记录
            self.add_debug_log("获取历史记录...")
            many_history, history_id = self.get_history_info(db, search.task_id)

            # 4. 保存用户输入
            self.add_debug_log("保存用户输入...")
            user_input = self._create_base_message(
                task_id=search.task_id,
                pid=search.pid,
                agent=query_agents[0],
                uuid_str=str(uuid.uuid1()),
                agent_uuid=""
            )
            user_input.update({
                "content": search.query,
                "user_stop": "false"
            })
            many_history.append(user_input)
            messages.append(user_input)

            # 5. 意图分析
            self.add_debug_log("开始意图分析...")
            intention_result = {
                "content": "",
                "selected_agents": [],
                "raw_response": ""
            }

            dispense = []
            dispense.extend(user_agents)
            dispense.extend(tool_agents)

            if search.is_pass == 0:
                async for content, agent, result_content in self.analyze_intention(
                        search.query, intention_agent, dispense, many_history
                ):
                    if result_content:
                        intention_result["raw_response"] = result_content
                    if agent is not None:
                        intention_result["selected_agents"] = agent
                    else:
                        intention_result["content"] += content

                workflow.append({
                    "stage": "intention_analysis",
                    "result": intention_result
                })

                # 保存意图分析结果
                intention_message = self._create_base_message(
                    task_id=search.task_id,
                    pid=search.pid,
                    agent=intention_agent,
                    uuid_str=str(uuid.uuid1()),
                    agent_uuid=""
                )
                intention_message.update({
                    "content": intention_result["raw_response"],
                    "user_stop": "false"
                })
                many_history.append(intention_message)
                messages.append(intention_message)
            else:
                self.add_debug_log("跳过意图分析，使用历史工作流...")
                history_info, _ = self.get_history_info(db, search.task_id)
                intention_result["selected_agents"] = self.get_new_workflow(history_info)

            # 6. 执行工作流
            self.add_debug_log("开始执行工作流...")
            writer_history_id = self.get_work_info(db, search.task_id)

            workflow_results = []
            async for current_agent, response, tmp_uid, agent_uuid, work_id in self.process_agent_workflow(
                    search=search,
                    workflow_ids=intention_result["selected_agents"],
                    writer_id=writer_history_id,
                    user_agents=dispense,
                    query=search.query,
                    task_id=search.task_id,
                    redis_client=redis_client,
                    db=db,
                    pid=search.pid,
                    many_history=many_history,
                    history_id=int(history_id)
            ):
                workflow_results.append({
                    "agent": current_agent.agent_name_cn,
                    "response": response,
                    "uuid": tmp_uid,
                    "agent_uuid": agent_uuid,
                    "work_id": work_id
                })

            workflow.append({
                "stage": "workflow_execution",
                "results": workflow_results
            })

        except Exception as e:
            error_msg = f"处理过程中出现错误: {str(e)}"
            self.add_debug_log(f"错误: {error_msg}")
            error = error_msg
            logging.exception("工作流处理失败")

        return {
            "workflow": workflow,
            "messages": messages,
            "debug_logs": self.debug_logs,
            "error": error
        }

    @staticmethod
    def get_history_messages(history: List[Dict]) -> List[Dict]:
        """获取历史消息"""
        history_messages = []
        if history:
            for h in history:
                if h.get("agent_style") == "DATA" and h.get("content"):
                    try:
                        content_json = json.loads(h.get("content"))
                        actual_content = content_json.get("content", {}).get("markdown_list", "")
                        history_messages.append({
                            "role": "user" if h.get("agent_type") == 1 else "assistant",
                            "agent_name": h.get("agent_name_en", "assistant"),
                            "content": actual_content
                        })
                    except json.JSONDecodeError:
                        history_messages.append({
                            "role": "user" if h.get("agent_type") == 1 else "assistant",
                            "agent_name": h.get("agent_name_en", "assistant"),
                            "content": h.get("content", "")
                        })
                else:
                    history_messages.append({
                        "role": "user" if h.get("agent_type") == 1 else "assistant",
                        "agent_name": h.get("agent_name_en", "assistant"),
                        "content": h.get("content", "")
                    })
        return history_messages

    def get_now_time(self) -> str:
        """获取当前时间字符串"""
        return datetime.fromtimestamp(time.time()).strftime('%Y-%m-%d %H:%M:%S')

    def save_messages_info(self, db: Session, result_data: list, task_id: int, pid: int, history_id: int) -> int:
        """保存消息历史"""
        if len(result_data) > 1:
            AgentHistoryService(db).update(
                AgentHistoryData(id=history_id, history=json.dumps(result_data, ensure_ascii=False)))
        else:
            res_data = AgentHistoryData(
                task_id=str(task_id),
                pid=str(pid),
                history=json.dumps(result_data, ensure_ascii=False),
                remark='Agents制作',
                status=1
            )
            AgentHistoryService(db).save(res_data)
            task_info = AgentHistoryService(db).find_by_task_id(task_id)
            history_id = task_info[0].id
        return history_id

    def save_writer_info(self, db: Session, result_data: str, task_id: int, pid: int, history_id: int) -> int:
        """保存写作内容"""
        save_work_info = WorkData(
            task_id=task_id,
            work_type=1,
            content=result_data,
            status=1,
            pid=pid,
            remark="Agents制造",
            title="",
        )
        info = WorkService(db).save(save_work_info)
        return info.id

    @staticmethod
    def get_info_index(current_agent: AgentInfo, many_history: List[Dict]) -> tuple[List[int], int]:
        """获取工具范围和当前索引"""
        tool_scope = []
        if current_agent.influence_scope:
            tool_scope.append(current_agent.id)
            tool_scope.extend(eval(current_agent.influence_scope))
        else:
            tool_scope.append(current_agent.id)

        agent_count = {}
        for item in many_history:
            agent_id = item['agent_id']
            agent_count[agent_id] = agent_count.get(agent_id, 0) + 1

        tmp_two_id = agent_count.get(tool_scope[1], 0)
        tmp_three_id = agent_count.get(tool_scope[2], 0)
        two_id = tmp_two_id + tmp_three_id
        one_id = agent_count.get(tool_scope[0], 0)

        wheel_two = WorkflowService.custom_division(two_id, 2)
        remainder_two = two_id % 2
        current_index = 0 if remainder_two == 0 and wheel_two == one_id else 1

        return tool_scope, current_index

    @staticmethod
    def custom_division(a: int, b: int) -> int:
        """自定义除法"""
        result = a / b
        if result.is_integer():
            return int(result)
        elif a < b:
            return 0
        elif a == b:
            return 1
        else:
            return (a // b) + 1 if (a % b) else (a // b)

    @staticmethod
    def safe_string(text: str) -> str:
        """安全处理字符串"""
        try:
            binary_data = text.encode('utf-8')
            utf8_text = binary_data.decode('utf-8', errors='replace')
            filtered_chars = []
            for char in utf8_text:
                if char in '\n\t\r' or ord(char) >= 32:
                    filtered_chars.append(char)
                else:
                    logging.debug(f"Filtering character: {repr(char)}")
            return ''.join(filtered_chars)
        except Exception as e:
            logging.error(f"Text processing error: {str(e)}")
            return ''

    @retry(
        stop=stop_after_attempt(5),
        wait=wait_exponential(multiplier=1, min=5, max=30),
        retry_error_callback=lambda retry_state: None)
    async def create_tool_agent(self, query: str, tool_agent: AgentInfo, history: List[Dict]) -> Optional[Dict]:
        """创建并执行工具代理"""
        try:
            tool_names = [tool.tool_function for tool in tool_agent.tools]
            tool_prompt = "\n".join([
                f"{info['agent_name_cn']}: {info['content']}"
                for info in history
            ])
            system_mes = f'用户历史记录如下\n{tool_prompt}'
            tools_info = get_tool_functions(tool_names)
            logging.info(f"{tool_agent.agent_name_cn}调用工具为: {tool_names}")

            llm = await self.llm_service.create_graph_llm(
                api_key=tool_agent.api_key,
                base_url=tool_agent.api_url,
                model_name=tool_agent.model_name,
            )
            graph = create_react_agent(llm, tools=tools_info)
            message = [
                SystemMessage(content=tool_agent.prompt_cn),
                AIMessage(content=system_mes),
                HumanMessage(content=query)]
            inputs = {"messages": message}

            llm_start = time.time()
            logging.info(f"已向大模型发送请求，{tool_agent.api_url}")
            info = graph.stream(inputs, stream_mode="values")
            llm_end = time.time()
            logging.info(f"调取大模型耗时{llm_end - llm_start}秒")

            for s in info:
                message = s["messages"][-1]
                if isinstance(message, ToolMessage):
                    message_result = message.content
                    if message_result:
                        message_content = self.safe_string(str(message_result))
                        logging.info(f"{tool_agent.agent_name_cn}工具结果: {str(message_content)}")
                        return json.loads(message_result) if not isinstance(message_result, dict) else message_result
                    logging.error(f"{tool_agent.agent_name_cn}工具结果为空")
                    return None

            logging.error(f"{tool_agent.agent_name_cn}工具结果为空")
            return None

        except Exception as e:
            logging.error(f"{tool_agent.agent_name_cn}工具调用失败:{str(e)}")
            raise

    async def process_tool_agent(self, query: str, tool_agent: AgentInfo, history: List[Dict]) -> str:
        """处理工具代理"""
        try:
            logging.info(f"工具调用开始{tool_agent.agent_name_cn}")
            info = await self.create_tool_agent(query, tool_agent, history)
            if not info:
                return '[]'
            return json.dumps(info, ensure_ascii=False) if not isinstance(info, str) else info
        except Exception as e:
            logging.error(f"工具 Agent 处理失败: {str(e)}")
            raise

    async def process_query(self, query: str, agent: AgentInfo, history: List[Dict]) -> AsyncGenerator[str, None]:
        """处理查询请求"""
        try:
            history_messages = self.get_history_messages(history)
            tmp_prompt = [
                {"role": "system", "content": agent.prompt_cn},
                *history_messages,
                {"role": "user" if agent.agent_type == 1 else "assistant",
                 "agent_name": agent.agent_name_en,
                 "content": ""}
            ]
            # logging.info(f"{agent.agent_name_cn}发送大模型的prompt为{self.safe_string(str(tmp_prompt))}")
            llm_start = time.time()
            logging.info(f"已向大模型发送请求，{agent.api_url}")
            completion = await self.llm_service.chat_completion(
                api_key=agent.api_key,
                base_url=agent.api_url,
                model=agent.model_name,
                messages=tmp_prompt,
                temperature=0.7,
                max_tokens=8000,
                stream=True
            )
            llm_end = time.time()
            logging.info(f"调取大模型耗时{llm_end - llm_start}秒")

            async for chunk in completion:
                if chunk and chunk.choices and chunk.choices[0].delta.content:
                    yield chunk.choices[0].delta.content
                if chunk and chunk.choices and chunk.choices[0].finish_reason == 'stop':
                    yield "[DONE]"

        except Exception as e:
            logging.error(f"处理查询失败: {str(e)}")
            yield f"处理失败: {str(e)}"

    async def process_agent_workflow(
            self,
            workflow_ids: List[int],
            user_agents: List[AgentInfo],
            writer_id: int,
            query: str,
            task_id: int,
            redis_client: Redis,
            db: Session,
            pid: int,
            many_history: List[Dict],
            history_id: int,
            search: MultiAgentChatRequestSchema
    ) -> AsyncGenerator[tuple[AgentInfo, Any, str, str, str], None]:
        """处理代理工作流"""
        tmp_history_id = history_id

        for workflow_id in workflow_ids:
            current_agent = next((agent for agent in user_agents if agent.id == int(workflow_id)), None)
            logging.info(f"当前agent名称: {current_agent.agent_name_cn}")
            tmp_uuid = str(uuid.uuid1())
            try:
                agent_uuid = search.agent_uuid or ""
                one_messages = self._create_base_message(task_id, pid, current_agent, tmp_uuid, agent_uuid)

                if current_agent.agent_type == 5:  # Tool Agent
                    async for result in self._handle_tool_agent(current_agent, user_agents, query, many_history,
                                                                one_messages, tmp_history_id, task_id, pid, db,
                                                                tmp_uuid, agent_uuid):
                        yield result
                    if current_agent.agent_type == 5:
                        break

                elif current_agent.agent_type == 3:
                    async for result in self._handle_search_agent(current_agent, user_agents, query, many_history,
                                                                  task_id, pid, tmp_history_id, db):
                        yield result

                else:
                    async for result in self._handle_normal_agent(current_agent, query, many_history, one_messages,
                                                                  task_id, redis_client, tmp_uuid, writer_id, pid,
                                                                  tmp_history_id, db):
                        yield result

                    if current_agent.agent_type == 6:
                        break

            except Exception as e:
                logging.error(f"处理Agent {current_agent.agent_name_cn} 失败: {str(e)}")
                yield current_agent, {
                    'content': f"工作流处理失败: {str(e)}",
                    'name': current_agent.agent_name_cn,
                    'tid': task_id
                }, tmp_uuid, "", ""

    def _create_base_message(self, task_id: int, pid: int, agent: AgentInfo,
                             uuid_str: str, agent_uuid: str) -> Dict:
        """创建基础消息"""
        return {
            "task_id": task_id,
            "pid": pid,
            "agent_id": agent.id,
            "agent_style": agent.agent_style,
            "agent_name_cn": agent.agent_name_cn,
            "agent_name_en": agent.agent_name_en,
            "model_name": agent.model_name,
            "created_at": self.get_now_time(),
            "uuid": uuid_str,
            "agent_uuid": agent_uuid,
            "agent_type": agent.agent_type,
            "agent_role": agent.agent_role,
            "work_id": "",
        }

    async def _handle_tool_agent(self, current_agent: AgentInfo, user_agents: List[AgentInfo],
                                 query: str, many_history: List[Dict], one_messages: Dict,
                                 tmp_history_id: int, task_id: int, pid: int, db: Session,
                                 tmp_uuid: str, agent_uuid: str) -> AsyncGenerator[
        tuple[AgentInfo, str, str, str, str], None]:
        """处理工具代理"""
        agent_uuid = agent_uuid or tmp_uuid
        tool_scope, current_index = self.get_info_index(current_agent, many_history)

        if current_index == 0:
            current_agent = next((agent for agent in user_agents if agent.id == tool_scope[0]), None)
            response = await self.process_tool_agent(query, current_agent, many_history)
            one_messages.update({
                "user_stop": "False",
                "content": str(response),
                "agent_uuid": tmp_uuid
            })
            many_history.append(one_messages)
            self.save_messages_info(db, many_history, task_id, pid, tmp_history_id)
            yield current_agent, response, tmp_uuid, agent_uuid, ""
        else:
            # 处理状态消息
            status_uuid = str(uuid.uuid1())
            current_agent = next((agent for agent in user_agents if agent.id == int(tool_scope[1])), None)
            tmp_response = "任务生成中"
            tmp_messages = self._create_base_message(task_id, pid, current_agent, status_uuid, agent_uuid)
            tmp_messages["user_stop"] = False
            tmp_messages["content"] = tmp_response
            many_history.append(tmp_messages)
            self.save_messages_info(db, many_history, task_id, pid, tmp_history_id)
            yield current_agent, tmp_response, status_uuid, agent_uuid, ""

            # 处理样式消息
            style_uuid = str(uuid.uuid1())
            current_agent = next((agent for agent in user_agents if agent.id == int(tool_scope[-1])), None)
            response = await self.process_tool_agent(query, current_agent, many_history)
            one_messages.update({
                "user_stop": "False",
                "content": str(response),
                "agent_name_cn": current_agent.agent_name_cn,
                "agent_name_en": current_agent.agent_name_en,
                "agent_style": current_agent.agent_style,
                "agent_id": current_agent.id,
                "uuid": style_uuid,
                "agent_uuid": agent_uuid
            })
            many_history.append(one_messages)
            self.save_messages_info(db, many_history, task_id, pid, tmp_history_id)
            yield current_agent, response, style_uuid, agent_uuid, ""

    async def _handle_search_agent(self, current_agent: AgentInfo, user_agents: List[AgentInfo],
                                   query: str, many_history: List[Dict], task_id: int, pid: int,
                                   tmp_history_id: int, db: Session) -> AsyncGenerator[
        tuple[AgentInfo, str, str, str, str], None]:
        """处理搜索代理"""
        search_group_uuid = str(uuid.uuid1())
        tool_scope = []
        if current_agent.influence_scope:
            tool_scope.append(current_agent.id)
            tool_scope.extend(eval(current_agent.influence_scope))
        else:
            tool_scope.append(current_agent.id)

        for index, agent_id in enumerate(tool_scope):
            search_tmp_uuid = str(uuid.uuid1())
            current_agent = next((agent for agent in user_agents if agent.id == int(agent_id)), None)
            logging.info(f"当前agent名称: {current_agent.agent_name_cn}")

            response = await self.process_tool_agent(query, current_agent, many_history) if index == len(
                tool_scope) - 1 else ""
            time.sleep(random.randint(1, 5))

            tmp_messages = self._create_base_message(task_id, pid, current_agent, search_tmp_uuid, search_group_uuid)
            tmp_messages.update({
                "user_stop": False,
                "content": response
            })

            many_history.append(tmp_messages)
            self.save_messages_info(db, many_history, task_id, pid, tmp_history_id)
            yield current_agent, response, search_tmp_uuid, search_group_uuid, ""

    async def _handle_normal_agent(self, current_agent: AgentInfo, query: str, many_history: List[Dict],
                                   one_messages: Dict, task_id: int, redis_client: Redis, tmp_uuid: str,
                                   writer_id: int, pid: int, tmp_history_id: int,
                                   db: Session) -> AsyncGenerator[tuple[AgentInfo, str, str, str, str], None]:
        """处理普通代理"""
        tmp_info = ""
        is_succeed = 0

        async for response in self.process_query(query, current_agent, many_history):
            if response != "[DONE]":
                tmp_info += response
            tmp_redis_key = f"{AgentsContents.AIP_STOP_INFO}{task_id}"
            await asyncio.sleep(0.05)

            # 处理用户中断
            if await redis_client.exists(tmp_redis_key):
                await redis_client.delete(tmp_redis_key)
                one_messages["user_stop"] = "true"
                one_messages["content"] = tmp_info
                many_history.append(one_messages)

                if current_agent.agent_type == 4:
                    work_id = self.save_writer_info(db, tmp_info, task_id, pid, writer_id)
                    one_messages["work_id"] = work_id
                    yield current_agent, response, tmp_uuid, "", ""
                    await asyncio.sleep(0.2)
                    yield current_agent, "", tmp_uuid, "", work_id

                self.save_messages_info(db, many_history, task_id, pid, tmp_history_id)
                logging.info(f"用户主动打断当前agent{current_agent.agent_name_cn}")
                return

            if response == "[DONE]":
                is_succeed = 1
                break
            yield current_agent, response, tmp_uuid, "", ""

        await asyncio.sleep(0.05)
        logging.info(f"当前agent名称: {current_agent.agent_name_cn}\n输出信息为：\n'{tmp_info}'")
        one_messages["user_stop"] = "false"
        one_messages["content"] = tmp_info

        if is_succeed:
            if current_agent.agent_type == 4:
                work_id = self.save_writer_info(db, tmp_info, task_id, pid, writer_id)
                one_messages["work_id"] = work_id
                yield current_agent, "", tmp_uuid, "", work_id
            else:
                yield current_agent, "", tmp_uuid, "", ""

        many_history.append(one_messages)
        self.save_messages_info(db, many_history, task_id, pid, tmp_history_id)

    async def start_chat(self, search: MultiAgentChatRequestSchema, redis_client: Redis, db: Session) -> AsyncGenerator[
        str, None]:
        """
        开始多 Agent 聊天
        """
        # 步骤1: 解析入参，将 agents 分为系统 Agent、用户 Agent、Query Agent 和 Tool Agent
        system_agents, user_agents, query_agents, tool_agents = self.format_agents(search.workflow.agents)
        self.user_agents = user_agents
        self.tool_agents = tool_agents  # 保存工具 Agent 列表

        # 步骤2: 获取意图识别 Agent 并进行意图分析
        intention_agent = self.get_intention_agent(system_agents)
        if not intention_agent:
            yield "错误：未找到意图识别 Agent"
            return

        # 查询用户历史输入并将用户输入传入历史记录
        many_history, history_id = self.get_history_info(db, search.task_id)

        # 保存用户输入
        user_input_messages = self._create_base_message(
            task_id=search.task_id,
            pid=search.pid,
            agent=query_agents[0],
            uuid_str=str(uuid.uuid1()),
            agent_uuid=""
        )
        user_input_messages.update({
            "content": search.query,
            "user_stop": "false"
        })

        many_history.append(user_input_messages)
        history_id = self.save_messages_info(db, many_history, search.task_id, search.pid, history_id)

        # 获取作品的历史
        writer_history_id = self.get_work_info(db, search.task_id)

        dispense = []
        dispense.extend(user_agents)
        dispense.extend(tool_agents)
        selected_agent = []

        # 意图分析
        intention_content = ""
        intention_uuid = str(uuid.uuid1())

        if search.is_pass == 0:
            async for content, agent, result_content in self.analyze_intention(search.query, intention_agent, dispense,
                                                                               many_history):
                if result_content:
                    intention_content = result_content
                if agent is not None:
                    selected_agent = agent
                else:
                    now_time = self.get_now_time()
                    yield f"data: {json.dumps({'content': content, 'agent_name_cn': intention_agent.agent_name_cn, 'agent_name_en': intention_agent.agent_name_en, 'agent_id': intention_agent.id, 'agent_style': intention_agent.agent_style, 'task_id': search.task_id, 'created_at': now_time, 'uuid': intention_uuid, 'agent_uuid': ''}, ensure_ascii=False)}\n\n"

            # 保存意图的消息
            intention_messages = self._create_base_message(
                task_id=search.task_id,
                pid=search.pid,
                agent=intention_agent,
                uuid_str=intention_uuid,
                agent_uuid=""
            )
            intention_messages.update({
                "content": intention_content,
                "user_stop": "false"
            })

            many_history.append(intention_messages)
            history_id = self.save_messages_info(db, many_history, search.task_id, search.pid, history_id)
        else:
            # 读取数据库
            history_info, _ = self.get_history_info(db, search.task_id)
            selected_agent = self.get_new_workflow(history_info)

        if not selected_agent:
            yield "WorkFlow初始化失败"
            return

        # 开始执行工作流
        async for current_agent, response, tmp_uid, agent_uuid, work_id in self.process_agent_workflow(
                search=search,
                workflow_ids=selected_agent,
                writer_id=writer_history_id,
                user_agents=dispense,
                query=search.query,
                task_id=search.task_id,
                redis_client=redis_client,
                db=db,
                pid=search.pid,
                many_history=many_history,
                history_id=int(history_id)
        ):
            # 添加时间
            time_now = self.get_now_time()
            # 修改需要增加到返回字段上面
            info = json.dumps(
                {'content': response, 'agent_name_cn': current_agent.agent_name_cn, 'agent_id': current_agent.id,
                 'agent_style': current_agent.agent_style, 'task_id': search.task_id, 'created_at': time_now,
                 'agent_name_en': current_agent.agent_name_en,
                 'uuid': tmp_uid, 'agent_uuid': agent_uuid, 'work_id': work_id}, ensure_ascii=False)
            yield f"data: {info}\n\n"
            await asyncio.sleep(0.01)

    def format_agents(self, agents: List[AgentInfo]) -> tuple[
        List[AgentInfo], List[AgentInfo], List[AgentInfo], List[AgentInfo]]:
        """格式化 agents 为系统 Agent 和用户 Agent 和 Query Agent 和 Tool Agent"""
        system_agents: List[AgentInfo] = []
        user_agents: List[AgentInfo] = []
        query_agents: List[AgentInfo] = []
        tool_agents: List[AgentInfo] = []

        for agent in agents:
            if agent.agent_type == 1:  # 系统 Agent
                system_agents.append(agent)
            elif agent.agent_type in [0, 2, 3, 4, 5, 6]:  # 用户 Agent
                user_agents.append(agent)
            elif agent.agent_type == 9:  # Query Agent
                query_agents.append(agent)
            elif agent.agent_type == 3:  # Tool Agent
                tool_agents.append(agent)
        return system_agents, user_agents, query_agents, tool_agents

    def get_intention_agent(self, system_agents: List[AgentInfo]) -> Optional[AgentInfo]:
        """获取意图识别 Agent"""
        for agent in system_agents:
            if agent.agent_role == 1:  # 意图识别角色
                return agent
        return None

    def get_history_info(self, db: Session, task_id: int) -> tuple[list, str]:
        """查询历史数据"""
        task_info = AgentHistoryService(db).find_by_task_id(task_id)
        if task_info:
            history_id = task_info[0].id
            history_info = json.loads(task_info[0].history.decode('utf-8'))
        else:
            history_id = ""
            history_info = []
        return history_info, history_id

    def get_work_info(self, db: Session, task_id: int) -> str:
        """查询作品历史"""
        task_info = WorkService(db).find_by_task_id(task_id)
        return task_info[0].id if task_info else ""

    async def analyze_intention(self, query: str, intention_agent: AgentInfo, user_agents: List[AgentInfo], history) -> \
            AsyncGenerator[tuple[str, Optional[List[int]], Optional[str]], None]:
        """分析用户意图并管理工作流程"""
        try:
            agent_descriptions = [
                {
                    "id": i.id,
                    "agent_name": i.agent_name_en,
                    "agent_name_cn": i.agent_name_cn,
                    "description": i.description
                }
                for i in user_agents
                if i.agent_type != 0
            ]

            # 尝试从本地加载 prompt
            local_prompt = self.prompt_service.get_prompt("intention_agent", use_local=True)
            if local_prompt:
                data = local_prompt
            else:
                try:
                    data = json.loads(intention_agent.prompt_cn)
                except:
                    raise ValueError("需求分析的prompt不是一个json格式")

            intention_prompt = {
                "instructions": data,
                "available_agents": agent_descriptions
            }

            intention_prompt_result = json.dumps(intention_prompt, ensure_ascii=False)
            history_messages = self.get_history_messages(history)
            tmp_prompt = [
                {"role": "system", "content": intention_prompt_result},
                *history_messages,
                {"role": "user", "content": query}
            ]

            completion = await self.llm_service.chat_completion(
                api_key=intention_agent.api_key,
                base_url=intention_agent.api_url,
                model=intention_agent.model_name,
                messages=tmp_prompt,
                max_tokens=8000,
                stream=True
            )

            content = ""
            async for chunk in completion:
                if chunk and chunk.choices and chunk.choices[0].delta.content:
                    chunk_content = chunk.choices[0].delta.content
                    content += chunk_content
                    yield chunk_content, None, None

            # 记录完整的大模型输出
            logging.info("意图识别输出 >>>")
            logging.info(f"{content}")
            logging.info("<<< 意图识别输出结束")

            # 解析响应
            workflow, next_agent, error = self.parse_workflow_response(content)

            # 如果没有找到 JSON，检查是否是需求澄清的情况
            if error == "响应中未找到JSON格式数据":
                logging.info("✓ 需求澄清回复")
                yield "", None, content
                return

            # 其他错误则需要报告
            if error:
                logging.error(f"✗ 解析失败: {error}")
                raise ValueError(error)

            # 记录解析结果
            if workflow:
                workflow_ids = [int(id_) for id_ in workflow]
                logging.info(f"✓ 工作流: {workflow_ids}")
                logging.info(f"✓ 下一步: {next_agent}")
            else:
                workflow_ids = None
                logging.info("✗ 未获取到工作流")

            yield "", workflow_ids, content

        except Exception as e:
            error_msg = f"意图识别失败: {str(e)}"
            if "未找到JSON格式数据" not in str(e):
                logging.error(f"✗ {error_msg}")
                raise ValueError(error_msg)
            return

    def parse_workflow_response(self, content: str) -> tuple[Optional[List[str]], Optional[str], str]:
        """
        解析意图识别的响应，提取工作流信息
        
        Args:
            content: 意图识别的完整响应文本
            
        Returns:
            tuple[Optional[List[str]], Optional[str], str]:
                - workflow: 工作流数组或 None
                - next: 下一个执行的专家ID或 None
                - error: 错误信息，成功时为空字符串
        """
        try:
            # 1. 提取JSON部分
            json_pattern = r'\{[^{}]*\}'
            matches = list(re.finditer(json_pattern, content))

            if not matches:
                return None, None, "响应中未找到JSON格式数据"

            # 尝试解析最后一个JSON块
            last_json = matches[-1].group()

            try:
                # 2. 解析JSON
                data = json.loads(last_json)

                # 3. 提取和验证数据
                workflow = data.get("workflow")
                next_agent = data.get("next")

                # 4. 数据验证
                if workflow is not None:
                    if not isinstance(workflow, list):
                        return None, None, "workflow 必须是数组格式"
                    if not all(isinstance(id_, str) for id_ in workflow):
                        return None, None, "workflow 中的所有ID必须是字符串格式"
                    self.current_workflow = workflow

                # 验证 next
                if next_agent is None:
                    return None, None, "必须指定 next 字段"
                if not isinstance(next_agent, str):
                    return None, None, "next 必须是字符串格式"

                # 5. 验证 next 是否在 workflow 中
                if workflow is not None and next_agent not in workflow:
                    return None, None, f"next ({next_agent}) 必须在 workflow 数组中"

                return workflow, next_agent, ""

            except json.JSONDecodeError:
                # 如果最后一个JSON解析失败，可能是不完整的，尝试前一个
                if len(matches) > 1:
                    try:
                        prev_json = matches[-2].group()
                        data = json.loads(prev_json)
                        # ... (重复上面的验证逻辑)
                    except:
                        pass
                return None, None, "JSON解析错误"

        except Exception as e:
            return None, None, f"解析响应时发生错误: {str(e)}"

    def get_current_workflow(self) -> List[str]:
        """获取当前存储的工作流"""
        return self.current_workflow

    def validate_agent_id(self, agent_id: str) -> bool:
        """验证agent ID是否在当前工作流中"""
        return agent_id in self.current_workflow
