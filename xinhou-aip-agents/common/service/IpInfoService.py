# @Time : 2025/1/10 下午5:39 

# <AUTHOR> daniel

# @File : IpInfoService.py 

# @Software: PyCharm

from sqlalchemy.orm import Session
from xinhou_openai_framework.core.orm.service.BaseServiceImpl import BaseServiceImpl
from typing import TypeVar

from pydantic import BaseModel

from common.entity.AgentIpData import IpInfoData

M = TypeVar('M', bound=BaseModel)


class IpInfoService(BaseServiceImpl[IpInfoData]):
    """
    Agents历史数据
    """

    def __init__(self, db: Session):
        super(IpInfoService, self).__init__(db, IpInfoData)

    def find_by_ip_name(self, ip_name):
        """
        根据模型查询指定字段数据
        """
        return self.db.query(IpInfoData).filter(IpInfoData.ip_name == ip_name).all()
