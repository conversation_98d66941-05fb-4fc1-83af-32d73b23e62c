# @Time : 2024/12/26 下午5:35 

# <AUTHOR> daniel

# @File : AgentWorkService.py 

# @Software: PyCharm
from sqlalchemy.orm import Session

from xinhou_openai_framework.core.orm.service.BaseServiceImpl import BaseServiceImpl

from common.entity.AgentWorkData import WorkData, AgentTasksData


class WorkService(BaseServiceImpl[WorkData]):
    """
    作品服务类
    """

    def __init__(self, db: Session):
        super(WorkService, self).__init__(db, WorkData)

    def find_by_task_id(self, task_id):
        """
        根据模型查询指定ID数据
        """
        return self.db.query(WorkData).filter(self.cls.task_id == (task_id)).all()


class AgentTasksService(BaseServiceImpl[AgentTasksData]):
    """
    Agents历史数据
    """

    def __init__(self, db: Session):
        super(AgentTasksService, self).__init__(db, AgentTasksData)
