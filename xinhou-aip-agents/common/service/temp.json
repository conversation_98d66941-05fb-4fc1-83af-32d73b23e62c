﻿{
        "pid": 1013,
        "task_id": 4532,
        "query": "",
        "agent_uuid": null,
        "is_pass": 0,
        "audio_url": null,
        "video_url": null,
        "audio_model_id": null,
        "video_model_id": null,
        "workflow": {
            "id": 2,
            "workflow_name": "无向版",
            "workflow_code": "2",
            "description": "1",
            "agents": [
                {
                    "id": 33,
                    "agent_name_cn": "选题专家",
                    "agent_name_en": "choice_expert",
                    "agent_code": "24",
                    "agent_type": 6,
                    "agent_role": 5,
                    "agent_style": "TITLE_CHOOSE",
                    "influence_scope": null,
                    "prompt_cn": "",
                    "prompt_en": null,
                    "status": 1,
                    "llm_name": "openai",
                    "model_name": "gpt-4o",
                    "api_url": "https://api.gptsapi.net/v1",
                    "api_key": "sk-F5ef23a49e5be896aece6861c68de33774da3bd8233K69zk",
                    "api_config": null,
                    "description": "生成选题",
                    "tool_ids": "",
                    "tools": []
                },
                {
                    "id": 42,
                    "agent_name_cn": "数据搜索专家",
                    "agent_name_en": "data_rinse",
                    "agent_code": "33",
                    "agent_type": 0,
                    "agent_role": 4,
                    "agent_style": "DATA",
                    "influence_scope": "",
                    "prompt_cn": "你是一个数据搜索专家，你只会调用工具不会回答用户需求，你需要调用工具tool_search_network搜索数据",
                    "prompt_en": null,
                    "status": 1,
                    "llm_name": "openai",
                    "model_name": "gpt-4o",
                    "api_url": "https://api.gptsapi.net/v1",
                    "api_key": "sk-F5ef23a49e5be896aece6861c68de33774da3bd8233K69zk",
                    "api_config": null,
                    "description": "进行数据搜索",
                    "tool_ids": "8",
                    "tools": [
                        {
                            "id": 8,
                            "tool_name": "知识搜索",
                            "tool_function": "tool_search_network"
                        }
                    ]
                },
                {
                    "id": 41,
                    "agent_name_cn": "数据清洗专家",
                    "agent_name_en": "data_search",
                    "agent_code": "32",
                    "agent_type": 0,
                    "agent_role": 4,
                    "agent_style": "DATA_LOADING_2",
                    "influence_scope": "",
                    "prompt_cn": "你是一个数据清洗专家",
                    "prompt_en": null,
                    "status": 1,
                    "llm_name": "openai",
                    "model_name": "gpt-4o",
                    "api_url": "https://api.gptsapi.net/v1",
                    "api_key": "sk-F5ef23a49e5be896aece6861c68de33774da3bd8233K69zk",
                    "api_config": null,
                    "description": "进行数清洗",
                    "tool_ids": "",
                    "tools": []
                },
                {
                    "id": 40,
                    "agent_name_cn": "数据分析专家",
                    "agent_name_en": "data_analyse",
                    "agent_code": "31",
                    "agent_type": 3,
                    "agent_role": 4,
                    "agent_style": "DATA_LOADING_1",
                    "influence_scope": "[41,42]",
                    "prompt_cn": "你是一个数据分析专家",
                    "prompt_en": null,
                    "status": 1,
                    "llm_name": "openai",
                    "model_name": "gpt-4o",
                    "api_url": "https://api.gptsapi.net/v1",
                    "api_key": "sk-F5ef23a49e5be896aece6861c68de33774da3bd8233K69zk",
                    "api_config": null,
                    "description": "进行数据分析。",
                    "tool_ids": "",
                    "tools": []
                },
                {
                    "id": 39,
                    "agent_name_cn": "视频作品专家",
                    "agent_name_en": "video_works",
                    "agent_code": "30",
                    "agent_type": 0,
                    "agent_role": 4,
                    "agent_style": "VIDEO_3",
                    "influence_scope": "",
                    "prompt_cn": "你是一个视频作品专家，你只会调用工具不会回答用户需求，你需要调用工具tool_video_create制作视频",
                    "prompt_en": null,
                    "status": 1,
                    "llm_name": "openai",
                    "model_name": "gpt-4o",
                    "api_url": "https://api.gptsapi.net/v1",
                    "api_key": "sk-F5ef23a49e5be896aece6861c68de33774da3bd8233K69zk",
                    "api_config": null,
                    "description": "展示生成的视频作品。",
                    "tool_ids": "6",
                    "tools": [
                        {
                            "id": 6,
                            "tool_name": "视频结果",
                            "tool_function": "tool_video_result"
                        }
                    ]
                },
                {
                    "id": 38,
                    "agent_name_cn": "视频等待专家",
                    "agent_name_en": "video_await",
                    "agent_code": "29",
                    "agent_type": 0,
                    "agent_role": 4,
                    "agent_style": "VIDEO_2",
                    "influence_scope": "",
                    "prompt_cn": "你是一个视频等待专家",
                    "prompt_en": null,
                    "status": 1,
                    "llm_name": "openai",
                    "model_name": "gpt-4o",
                    "api_url": "https://api.gptsapi.net/v1",
                    "api_key": "sk-F5ef23a49e5be896aece6861c68de33774da3bd8233K69zk",
                    "api_config": null,
                    "description": "为用户生成视频。",
                    "tool_ids": "5",
                    "tools": [
                        {
                            "id": 5,
                            "tool_name": "视频制作",
                            "tool_function": "tool_video_create"
                        }
                    ]
                },
                {
                    "id": 37,
                    "agent_name_cn": "视频展开专家",
                    "agent_name_en": "video_detail",
                    "agent_code": "28",
                    "agent_type": 5,
                    "agent_role": 4,
                    "agent_style": "VIDEO_1",
                    "influence_scope": "[38,39]",
                    "prompt_cn": "你是一个视频展开专家，你只会调用工具不会回答用户需求，你需要调用工具tool_video_info获取视频详细信息",
                    "prompt_en": null,
                    "status": 1,
                    "llm_name": "openai",
                    "model_name": "gpt-4o",
                    "api_url": "https://api.gptsapi.net/v1",
                    "api_key": "sk-F5ef23a49e5be896aece6861c68de33774da3bd8233K69zk",
                    "api_config": null,
                    "description": "提供用户视频模板选择列表。",
                    "tool_ids": "4",
                    "tools": [
                        {
                            "id": 4,
                            "tool_name": "视频列表",
                            "tool_function": "tool_video_info"
                        }
                    ]
                },
                {
                    "id": 36,
                    "agent_name_cn": "音频作品专家",
                    "agent_name_en": "audio_works",
                    "agent_code": "27",
                    "agent_type": 0,
                    "agent_role": 5,
                    "agent_style": "VOICE_3",
                    "influence_scope": "",
                    "prompt_cn": "你是一个音频作品专家,你只会调用工具不会回答用户需求，你需要调用工具tool_audio_create制作音频数据",
                    "prompt_en": null,
                    "status": 1,
                    "llm_name": "openai",
                    "model_name": "gpt-4o",
                    "api_url": "https://api.gptsapi.net/v1",
                    "api_key": "sk-F5ef23a49e5be896aece6861c68de33774da3bd8233K69zk",
                    "api_config": null,
                    "description": "",
                    "tool_ids": "2",
                    "tools": [
                        {
                            "id": 2,
                            "tool_name": "音频制作",
                            "tool_function": "tool_audio_create"
                        }
                    ]
                },
                {
                    "id": 35,
                    "agent_name_cn": "音频生成专家",
                    "agent_name_en": "audio_await",
                    "agent_code": "26",
                    "agent_type": 0,
                    "agent_role": 4,
                    "agent_style": "VOICE_2",
                    "influence_scope": "",
                    "prompt_cn": "你是一个生成专家",
                    "prompt_en": null,
                    "status": 1,
                    "llm_name": "openai",
                    "model_name": "gpt-4o",
                    "api_url": "https://api.gptsapi.net/v1",
                    "api_key": "sk-F5ef23a49e5be896aece6861c68de33774da3bd8233K69zk",
                    "api_config": null,
                    "description": "",
                    "tool_ids": "2",
                    "tools": [
                        {
                            "id": 2,
                            "tool_name": "音频制作",
                            "tool_function": "tool_audio_create"
                        }
                    ]
                },
                {
                    "id": 34,
                    "agent_name_cn": "音频展开专家",
                    "agent_name_en": "audio_detail",
                    "agent_code": "25",
                    "agent_type": 5,
                    "agent_role": 4,
                    "agent_style": "VOICE_1",
                    "influence_scope": "[35,36]",
                    "prompt_cn": "你是一个音频展开专家，,你只会调用工具不会回答用户需求，你需要调用工具tool_audio_info获取用户音频的详细信息",
                    "prompt_en": null,
                    "status": 1,
                    "llm_name": "openai",
                    "model_name": "gpt-4o",
                    "api_url": "https://api.gptsapi.net/v1",
                    "api_key": "sk-F5ef23a49e5be896aece6861c68de33774da3bd8233K69zk",
                    "api_config": null,
                    "description": "提供用户音频模板选择列表。",
                    "tool_ids": "1",
                    "tools": [
                        {
                            "id": 1,
                            "tool_name": "音频列表",
                            "tool_function": "tool_audio_info"
                        }
                    ]
                },
                {
                    "id": 23,
                    "agent_name_cn": "意图识别",
                    "agent_name_en": "intention_expert",
                    "agent_code": "14",
                    "agent_type": 1,
                    "agent_role": 1,
                    "agent_style": "COMMAND",
                    "influence_scope": "",
                    "prompt_cn": "",
                    "prompt_en": "intention_expert",
                    "status": 1,
                    "llm_name": "openai",
                    "model_name": "o1-mini", 
                    "api_url": "https://api.gpt2share.com/v1",
                    "api_key": "sk-Qoz2RQ8GTfs7rhDeLb88ho8qua03UVLtiMwOGtam9hxVTwlz",
                    "api_config": null,
                    "description": "分析用户意图并安排agent工作。",
                    "tool_ids": "",
                    "tools": []
                },
                {
                    "id": 32,
                    "agent_name_cn": "用户",
                    "agent_name_en": "user",
                    "agent_code": "23",
                    "agent_type": 9,
                    "agent_role": 9,
                    "agent_style": "USER",
                    "influence_scope": null,
                    "prompt_cn": "你是一个用户agent，接受用户的输入",
                    "prompt_en": "user_expert",
                    "status": 1,
                    "llm_name": "openai",
                    "model_name": "gpt-4o",
                    "api_url": "https://api.gptsapi.net/v1",
                    "api_key": "sk-F5ef23a49e5be896aece6861c68de33774da3bd8233K69zk",
                    "api_config": null,
                    "description": "IP博主本人。",
                    "tool_ids": "",
                    "tools": []
                },
                {
                    "id": 31,
                    "agent_name_cn": "标题专家",
                    "agent_name_en": "title_expert",
                    "agent_code": "22",
                    "agent_type": 2,
                    "agent_role": 5,
                    "agent_style": "USUALLY",
                    "influence_scope": "",
                    "prompt_cn": "",
                    "prompt_en": "title_expert",
                    "status": 1,
                    "llm_name": "openai",
                    "model_name": "gpt-4o",
                    "api_url": "https://api.gptsapi.net/v1",
                    "api_key": "sk-F5ef23a49e5be896aece6861c68de33774da3bd8233K69zk",
                    "api_config": null,
                    "description": "标题创作以及社交媒体标签。",
                    "tool_ids": "",
                    "tools": []
                },
                {
                    "id": 30,
                    "agent_name_cn": "风格润色",
                    "agent_name_en": "style_expert",
                    "agent_code": "21",
                    "agent_type": 4,
                    "agent_role": 4,
                    "agent_style": "EMBELLISH",
                    "influence_scope": "",
                    "prompt_cn": "",
                    "prompt_en": "style_expert",
                    "status": 1,
                    "llm_name": "openai",
                    "model_name": "gpt-4o",
                    "api_url": "https://api.gptsapi.net/v1",
                    "api_key": "sk-F5ef23a49e5be896aece6861c68de33774da3bd8233K69zk",
                    "api_config": null,
                    "description": "文案风格润色。",
                    "tool_ids": "",
                    "tools": []
                },
                {
                    "id": 29,
                    "agent_name_cn": "文案写手",
                    "agent_name_en": "writer_expert",
                    "agent_code": "20",
                    "agent_type": 2,
                    "agent_role": 4,
                    "agent_style": "WRITER",
                    "influence_scope": "[30]",
                    "prompt_cn": "",
                    "prompt_en": "writer_expert",
                    "status": 1,
                    "llm_name": "openai",
                    "model_name": "gpt-4o",
                    "api_url": "https://api.gptsapi.net/v1",
                    "api_key": "sk-F5ef23a49e5be896aece6861c68de33774da3bd8233K69zk",
                    "api_config": null,
                    "description": "内容文案创作。",
                    "tool_ids": "",
                    "tools": []
                },
                {
                    "id": 28,
                    "agent_name_cn": "开头专家",
                    "agent_name_en": "outset_expert",
                    "agent_code": "19",
                    "agent_type": 2,
                    "agent_role": 4,
                    "agent_style": "USUALLY",
                    "influence_scope": "[29]",
                    "prompt_cn": "",
                    "prompt_en": "outset_expert",
                    "status": 1,
                    "llm_name": "openai",
                    "model_name": "gpt-4o",
                    "api_url": "https://api.gptsapi.net/v1",
                    "api_key": "sk-F5ef23a49e5be896aece6861c68de33774da3bd8233K69zk",
                    "api_config": null,
                    "description": "给内容提供一个很棒的开头。",
                    "tool_ids": "",
                    "tools": []
                },
                {
                    "id": 27,
                    "agent_name_cn": "钩子专家",
                    "agent_name_en": "hook_expert",
                    "agent_code": "18",
                    "agent_type": 2,
                    "agent_role": 4,
                    "agent_style": "USUALLY",
                    "influence_scope": "[28]",
                    "prompt_cn": "",
                    "prompt_en": "hook_expert",
                    "status": 1,
                    "llm_name": "openai",
                    "model_name": "gpt-4o",
                    "api_url": "https://api.gptsapi.net/v1",
                    "api_key": "sk-F5ef23a49e5be896aece6861c68de33774da3bd8233K69zk",
                    "api_config": null,
                    "description": "非常隐蔽的为自己打广告。",
                    "tool_ids": "",
                    "tools": []
                },
                {
                    "id": 26,
                    "agent_name_cn": "观点专家",
                    "agent_name_en": "viewpoint_expert",
                    "agent_code": "17",
                    "agent_type": 2,
                    "agent_role": 4,
                    "agent_style": "USUALLY",
                    "influence_scope": "[27]",
                    "prompt_cn": "",
                    "prompt_en": "viewpoint_expert",
                    "status": 1,
                    "llm_name": "openai",
                    "model_name": "gpt-4o",
                    "api_url": "https://api.gptsapi.net/v1",
                    "api_key": "sk-F5ef23a49e5be896aece6861c68de33774da3bd8233K69zk",
                    "api_config": null,
                    "description": "提供一个独特的观点。",
                    "tool_ids": "",
                    "tools": []
                },
                {
                    "id": 25,
                    "agent_name_cn": "结构专家",
                    "agent_name_en": "structure_expert",
                    "agent_code": "16",
                    "agent_type": 2,
                    "agent_role": 4,
                    "agent_style": "STRUCT",
                    "influence_scope": "[26]",
                    "prompt_cn": "",
                    "prompt_en": "structure_expert",
                    "status": 1,
                    "llm_name": "openai",
                    "model_name": "gpt-4o",
                    "api_url": "https://api.gptsapi.net/v1",
                    "api_key": "sk-F5ef23a49e5be896aece6861c68de33774da3bd8233K69zk",
                    "api_config": null,
                    "description": "提供丰富的内容结构。",
                    "tool_ids": "",
                    "tools": []
                }
            ]
        }
    }