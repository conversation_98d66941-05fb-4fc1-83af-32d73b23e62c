import json
import os
import logging
from typing import Dict, Optional

class PromptConfigService:
    """Prompt 配置管理服务"""
    
    # Agent 类型到文件名的映射
    AGENT_FILE_MAPPING = {
        "intention_agent": "intention_agent.json",
        # 可以添加其他 agent 的映射
        # "structure_agent": "structure_agent.json",
        # "writer_agent": "writer_agent.json",
    }
    
    def __init__(self, base_path: str = "apps/api/service/prompts/"):
        self.base_path = base_path
        self.prompt_cache: Dict[str, dict] = {}
        
    def get_prompt(self, agent_type: str, use_local: bool = False) -> Optional[dict]:
        """
        获取 prompt 配置
        
        Args:
            agent_type: agent 类型标识（如 'intention_agent'）
            use_local: 是否使用本地配置文件
            
        Returns:
            dict: prompt 配置
        """
        if not use_local:
            return None
            
        try:
            # 获取对应的文件名
            file_name = self.AGENT_FILE_MAPPING.get(agent_type)
            if not file_name:
                logging.warning(f"未找到 agent 类型 '{agent_type}' 对应的文件名映射")
                return None
                
            # 从本地文件加载
            cache_key = f"local_{agent_type}"
            if cache_key not in self.prompt_cache:
                file_path = os.path.join(self.base_path, file_name)
                if os.path.exists(file_path):
                    with open(file_path, 'r', encoding='utf-8') as f:
                        self.prompt_cache[cache_key] = json.load(f)
                        logging.info(f"成功加载 prompt 配置: {file_path}")
                else:
                    logging.warning(f"prompt 配置文件不存在: {file_path}")
                    return None
                    
            return self.prompt_cache[cache_key]
            
        except Exception as e:
            logging.error(f"加载 prompt 配置失败: {str(e)}")
            return None
        
    def save_prompt(self, agent_type: str, prompt_config: dict) -> bool:
        """
        保存 prompt 配置到本地文件
        
        Args:
            agent_type: agent 类型标识（如 'intention_agent'）
            prompt_config: prompt 配置内容
            
        Returns:
            bool: 是否保存成功
        """
        try:
            # 获取对应的文件名
            file_name = self.AGENT_FILE_MAPPING.get(agent_type)
            if not file_name:
                logging.error(f"未找到 agent 类型 '{agent_type}' 对应的文件名映射")
                return False
                
            file_path = os.path.join(self.base_path, file_name)
            
            # 保存配置
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(prompt_config, f, ensure_ascii=False, indent=2)
            
            # 更新缓存
            cache_key = f"local_{agent_type}"
            self.prompt_cache[cache_key] = prompt_config
            
            logging.info(f"成功保存 prompt 配置: {file_path}")
            return True
            
        except Exception as e:
            logging.error(f"保存 prompt 配置失败: {str(e)}")
            return False 