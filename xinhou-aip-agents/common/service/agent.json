{"pid": 1013, "task_id": 3988, "query": "我的pid是1013,我想先查看一下我的所有音频详细信息吗。然后再生成视频其中我的task_id 为3297，voice_id为277我需要根据他来生成音频", "workflow": {"id": 2, "workflow_name": "无向版", "workflow_code": "2", "description": "1", "agents": [{"id": 33, "agent_name_cn": "选题专家", "agent_name_en": "choice_expert", "agent_code": "24", "agent_type": 2, "agent_role": 5, "agent_style": 1, "influence_scope": null, "prompt_cn": "你是一个选题专家,你是一个优秀的问题专家，需要完成的任务是写十个选题，并将选题输出：选题要求如下：选题围绕主题和你了解的信息每个问题20个字以内输出要求必须严格按照JSON格式输出键名统一使用“选题1”、“选题2”的格式每个选题是一个独立且完整的精简问题，字符20字以内保证问题的严谨性，语句的通顺性问题中禁止出现博主的姓名问题中禁止出现博主的昵称问题以第一人称生成，禁止出现称呼不包含任何其他说明文字必须以json格式的结构输出，参考实例如下<“选题一”: “xxx”, “选题二”: “xxx”>输出要求必须将生成好的选题以json的格式输出使用中文回复", "prompt_en": null, "status": 1, "llm_name": "gpt-4o-latest", "model_name": "gpt-4o", "api_url": "https://api.gpt2share.com/v1", "api_key": "sk-X8QR18KsVO4f3RRZE4FcBb3c8fDe4f08Aa3dD8C4202f7396", "api_config": null, "description": "你是一个选题专家,用来生成选题的，并且结果返回", "tool_ids": "", "tools": []}, {"id": 42, "agent_name_cn": "数据清洗专家", "agent_name_en": "data_rinse", "agent_code": "33", "agent_type": 3, "agent_role": 4, "agent_style": 1, "influence_scope": "[25]", "prompt_cn": "你是一个数据清洗专家", "prompt_en": null, "status": 1, "llm_name": "gpt-4o-latest", "model_name": "gpt-4o", "api_url": "https://api.gpt2share.com/v1", "api_key": "sk-X8QR18KsVO4f3RRZE4FcBb3c8fDe4f08Aa3dD8C4202f7396", "api_config": null, "description": "你是一个数据清洗专家", "tool_ids": "9", "tools": [{"id": 9, "tool_name": "知识图搜(图谱)", "tool_function": "tool_search_knowledge"}]}, {"id": 41, "agent_name_cn": "数据搜索专家", "agent_name_en": "data_search", "agent_code": "32", "agent_type": 3, "agent_role": 4, "agent_style": 1, "influence_scope": "[42]", "prompt_cn": "你是一个数据搜索专家,你只会调用工具不会回答用户需求，你的任务就是将工具结果返回给用户，你必须调用自身工具回答用户需求", "prompt_en": null, "status": 1, "llm_name": "gpt-4o-latest", "model_name": "gpt-4o", "api_url": "https://api.gpt2share.com/v1", "api_key": "sk-X8QR18KsVO4f3RRZE4FcBb3c8fDe4f08Aa3dD8C4202f7396", "api_config": null, "description": "你是一个数据搜索专家", "tool_ids": "8", "tools": [{"id": 8, "tool_name": "知识搜索", "tool_function": "tool_search_network"}]}, {"id": 40, "agent_name_cn": "数据分析专家", "agent_name_en": "data_analyse", "agent_code": "31", "agent_type": 3, "agent_role": 4, "agent_style": 1, "influence_scope": "[41]", "prompt_cn": "你是一个数据分析专家", "prompt_en": null, "status": 1, "llm_name": "gpt-4o-latest", "model_name": "gpt-4o", "api_url": "https://api.gpt2share.com/v1", "api_key": "sk-X8QR18KsVO4f3RRZE4FcBb3c8fDe4f08Aa3dD8C4202f7396", "api_config": null, "description": "你是一个数据分析专家", "tool_ids": "7", "tools": [{"id": 7, "tool_name": "知识检索", "tool_function": "tool_search_rag"}]}, {"id": 39, "agent_name_cn": "视频作品专家", "agent_name_en": "video_works", "agent_code": "30", "agent_type": 3, "agent_role": 4, "agent_style": 1, "influence_scope": "[31]", "prompt_cn": "你是一个视频作品专家", "prompt_en": null, "status": 1, "llm_name": "gpt-4o-latest", "model_name": "gpt-4o", "api_url": "https://api.gpt2share.com/v1", "api_key": "sk-X8QR18KsVO4f3RRZE4FcBb3c8fDe4f08Aa3dD8C4202f7396", "api_config": null, "description": "你是一个视频作品专家", "tool_ids": "6", "tools": [{"id": 6, "tool_name": "视频结果", "tool_function": "tool_video_result"}]}, {"id": 38, "agent_name_cn": "视频等待专家", "agent_name_en": "video_await", "agent_code": "29", "agent_type": 3, "agent_role": 4, "agent_style": 1, "influence_scope": "[39]", "prompt_cn": "你是一个视频等待专家", "prompt_en": null, "status": 1, "llm_name": "gpt-4o-latest", "model_name": "gpt-4o", "api_url": "https://api.gpt2share.com/v1", "api_key": "sk-X8QR18KsVO4f3RRZE4FcBb3c8fDe4f08Aa3dD8C4202f7396", "api_config": null, "description": "你是一个视频等待专家", "tool_ids": "5", "tools": [{"id": 5, "tool_name": "视频制作", "tool_function": "tool_video_create"}]}, {"id": 37, "agent_name_cn": "视频展开专家", "agent_name_en": "video_detail", "agent_code": "28", "agent_type": 3, "agent_role": 4, "agent_style": 1, "influence_scope": "[38]", "prompt_cn": "你是一个视频展开专家", "prompt_en": null, "status": 1, "llm_name": "gpt-4o-latest", "model_name": "gpt-4o", "api_url": "https://api.gpt2share.com/v1", "api_key": "sk-X8QR18KsVO4f3RRZE4FcBb3c8fDe4f08Aa3dD8C4202f7396", "api_config": null, "description": "你是一个视频展开专家,用来获取用户视频的详细信息", "tool_ids": "4", "tools": [{"id": 4, "tool_name": "视频列表", "tool_function": "tool_video_info"}]}, {"id": 34, "agent_name_cn": "音频展开专家", "agent_name_en": "audio_detail", "agent_code": "25", "agent_type": 3, "agent_role": 4, "agent_style": 1, "influence_scope": "[35]", "prompt_cn": "你是一个音频展开专家,你只会调用工具不会回答用户需求，你的任务就是将工具结果返回给用户，你必须调用自身工具回答用户需求", "prompt_en": null, "status": 1, "llm_name": "gpt-4o-latest", "model_name": "gpt-4o", "api_url": "https://api.gpt2share.com/v1", "api_key": "sk-X8QR18KsVO4f3RRZE4FcBb3c8fDe4f08Aa3dD8C4202f7396", "api_config": null, "description": "你是一个音频展开专家，你的作用是用来获取用户音频下的详细信息的", "tool_ids": "1", "tools": [{"id": 1, "tool_name": "音频列表", "tool_function": "tool_audio_info"}]}, {"id": 35, "agent_name_cn": "音频生成", "agent_name_en": "audio_detail", "agent_code": "25", "agent_type": 3, "agent_role": 4, "agent_style": 1, "influence_scope": "[36]", "prompt_cn": "你是一个生成专家,你只会调用工具不会回答用户需求，你的任务就是将工具结果返回给用户，你必须调用自身工具回答用户需求", "prompt_en": null, "status": 1, "llm_name": "gpt-4o-latest", "model_name": "gpt-4o", "api_url": "https://api.gpt2share.com/v1", "api_key": "sk-X8QR18KsVO4f3RRZE4FcBb3c8fDe4f08Aa3dD8C4202f7396", "api_config": null, "description": "你是一个音频生成专家，你的作用是生成音频", "tool_ids": "1", "tools": [{"id": 1, "tool_name": "音频列表", "tool_function": "tool_audio_create"}]}, {"id": 23, "agent_name_cn": "意图识别", "agent_name_en": "intention_expert", "agent_code": "14", "agent_type": 1, "agent_role": 1, "agent_style": 1, "influence_scope": "", "prompt_cn": "你是一个专业的意图识别和任务分发专家。你需要：\n\n1. 深入分析用户输入：\n- 理解用户的直接表述和潜在需求\n- 考虑历史对话上下文\n- 识别关键词和情感倾向\n\n2. 任务分类：\n- 新文章创作需求\n- 现有内容修改需求\n- 具体环节优化需求（如标题、结构、观点等）\n- 风格调整需求\n\n3. Agent选择原则：\n- 新文章创作：优先选择结构专家(id:3)\n- 内容修改或优化：根据具体需求选择对应专家\n- 明确针对某个环节：直接选择相应专家\n- 综合性需求：选择路由专家(id:2)进行后续分发\n\n4. 输出要求：\n- 给出明确的意图判断理由\n- 说明选择特定agent的依据\n- 提供后续处理建议\n\n请基于以上原则，准确识别用户意图并选择最合适的起始agent并且只返回纯数字ID。用户需要获取视频列表的时候，返回视频agentid", "prompt_en": "intention_expert", "status": 1, "llm_name": "openai", "model_name": "claude-3-5-sonnet-20241022", "api_url": "https://api.gpt2share.com/v1", "api_key": "sk-Qoz2RQ8GTfs7rhDeLb88ho8qua03UVLtiMwOGtam9hxVTwlz", "api_config": null, "description": "你是一个专业的意图识别和任务分发专家。你需要：\n\n1. 深入分析用户输入：\n- 理解用户的直接表述和潜在需求\n- 考虑历史对话上下文\n- 识别关键词和情感倾向\n\n2. 任务分类：\n- 新文章创作需求\n- 现有内容修改需求\n- 具体环节优化需求（如标题、结构、观点等）\n- 风格调整需求\n\n3. Agent选择原则：\n- 新文章创作：优先选择结构专家(id:3)\n- 内容修改或优化：根据具体需求选择对应专家\n- 明确针对某个环节：直接选择相应专家\n- 综合性需求：选择路由专家(id:2)进行后续分发\n\n4. 输出要求：\n- 给出明确的意图判断理由\n- 说明选择特定agent的依据\n- 提供后续处理建议\n\n请基于以上原则，准确识别用户意图并选择最合适的起始agent并且只返回纯数字ID。用户需要获取视频列表的时候，返回视频agentid", "tool_ids": "", "tools": []}, {"id": 32, "agent_name_cn": "用户", "agent_name_en": " user_expert", "agent_code": "23", "agent_type": 9, "agent_role": 9, "agent_style": 1, "influence_scope": null, "prompt_cn": "你是一个用户agent，接受用户的输入", "prompt_en": "user_expert", "status": 1, "llm_name": "gpt-4o-latest", "model_name": "gpt-4o", "api_url": "https://api.gpt2share.com/v1", "api_key": "sk-X8QR18KsVO4f3RRZE4FcBb3c8fDe4f08Aa3dD8C4202f7396", "api_config": null, "description": "User expert", "tool_ids": "", "tools": []}, {"id": 31, "agent_name_cn": "标题专家", "agent_name_en": "title_expert", "agent_code": "22", "agent_type": 2, "agent_role": 5, "agent_style": 1, "influence_scope": "", "prompt_cn": "你是一个专业的标题创作专家。你需要为文章创作既能准确概括内容，又富有吸引力的标题。请注重标题的新颖性、概括性和传播效果。\n\n你只输出一句话标题，确保它能在社交媒体上传播。顺便输出社交媒体标签，用#符号表示", "prompt_en": "title_expert", "status": 1, "llm_name": "gpt-4o-latest", "model_name": "gpt-4o", "api_url": "https://api.gpt2share.com/v1", "api_key": "sk-X8QR18KsVO4f3RRZE4FcBb3c8fDe4f08Aa3dD8C4202f7396", "api_config": null, "description": "你是一个专业的标题创作专家。你需要为文章创作既能准确概括内容，又富有吸引力的标题。请注重标题的新颖性、概括性和传播效果。\n\n你只输出一句话标题，确保它能在社交媒体上传播。顺便输出社交媒体标签，用#符号表示", "tool_ids": "", "tools": []}, {"id": 30, "agent_name_cn": "风格润色", "agent_name_en": "style_expert", "agent_code": "21", "agent_type": 2, "agent_role": 4, "agent_style": 3, "influence_scope": "[31,34]", "prompt_cn": "你是一个专业的文案风格润色专家。你需要根据目标受众和传播场景，对文案进行风格优化和语言润色，使内容更加优美流畅，风格更加统一。", "prompt_en": "style_expert", "status": 1, "llm_name": "gpt-4o-latest", "model_name": "gpt-4o", "api_url": "https://api.gpt2share.com/v1", "api_key": "sk-X8QR18KsVO4f3RRZE4FcBb3c8fDe4f08Aa3dD8C4202f7396", "api_config": null, "description": "你是一个专业的文案风格润色专家。你需要根据目标受众和传播场景，对文案进行风格优化和语言润色，使内容更加优美流畅，风格更加统一。", "tool_ids": "", "tools": []}, {"id": 29, "agent_name_cn": "文案写手", "agent_name_en": "writer_expert", "agent_code": "20", "agent_type": 2, "agent_role": 4, "agent_style": 3, "influence_scope": "[30]", "prompt_cn": "你是一个专业的文案创作专家。你需要根据给定的主题和要求，创作出高质量的文案内容。请注重内容的原创性、可读性和表达效果。", "prompt_en": "writer_expert", "status": 1, "llm_name": "gpt-4o-latest", "model_name": "gpt-4o", "api_url": "https://api.gpt2share.com/v1", "api_key": "sk-X8QR18KsVO4f3RRZE4FcBb3c8fDe4f08Aa3dD8C4202f7396", "api_config": null, "description": "你是一个专业的文案创作专家。你需要根据给定的主题和要求，创作出高质量的文案内容。请注重内容的原创性、可读性和表达效果。", "tool_ids": "", "tools": []}, {"id": 28, "agent_name_cn": "开头专家", "agent_name_en": "outset_expert", "agent_code": "19", "agent_type": 2, "agent_role": 4, "agent_style": 1, "influence_scope": "[29]", "prompt_cn": "你是一个专业的文章开头专家。你要创作引人入胜的开篇内容，快速抓住读者注意力，并为后续内容做好铺垫。请确保开头既吸引人又自然流畅。", "prompt_en": "outset_expert", "status": 1, "llm_name": "gpt-4o-latest", "model_name": "gpt-4o", "api_url": "https://api.gpt2share.com/v1", "api_key": "sk-X8QR18KsVO4f3RRZE4FcBb3c8fDe4f08Aa3dD8C4202f7396", "api_config": null, "description": "你是一个专业的文章开头专家。你要创作引人入胜的开篇内容，快速抓住读者注意力，并为后续内容做好铺垫。请确保开头既吸引人又自然流畅。", "tool_ids": "", "tools": []}, {"id": 27, "agent_name_cn": "钩子专家", "agent_name_en": "hook_expert", "agent_code": "18", "agent_type": 2, "agent_role": 4, "agent_style": 1, "influence_scope": "[28]", "prompt_cn": "你是一个专业的内容钩子专家。你需要设计吸引人的内容亮点和引人入胜的表达方式，让读者产生强烈的阅读兴趣。请注重创意性和吸引力。\n\n注意你只输出文章的钩子部分，钩子的用途是吸引点赞评论或关注，它更像是非常隐蔽的为自己打广告。", "prompt_en": "hook_expert", "status": 1, "llm_name": "gpt-4o-latest", "model_name": "gpt-4o", "api_url": "https://api.gpt2share.com/v1", "api_key": "sk-X8QR18KsVO4f3RRZE4FcBb3c8fDe4f08Aa3dD8C4202f7396", "api_config": null, "description": "你是一个专业的内容钩子专家。你需要设计吸引人的内容亮点和引人入胜的表达方式，让读者产生强烈的阅读兴趣。请注重创意性和吸引力。\n\n注意你只输出文章的钩子部分，钩子的用途是吸引点赞评论或关注，它更像是非常隐蔽的为自己打广告。", "tool_ids": "", "tools": []}, {"id": 26, "agent_name_cn": "观点专家", "agent_name_en": "viewpoint_expert", "agent_code": "17", "agent_type": 2, "agent_role": 4, "agent_style": 1, "influence_scope": "[27]", "prompt_cn": "你是一个专业的观点内容专家。你需要帮助提炼和强化文章的核心观点，确保论述有理有据，观点鲜明有特色。请注重论证的逻辑性和说服力。\n\n规则：\n你只输出一句话，用来表达一个强烈的观点，让人听起来能感觉是一个金句。", "prompt_en": "viewpoint_expert", "status": 1, "llm_name": "gpt-4o-latest", "model_name": "gpt-4o", "api_url": "https://api.gpt2share.com/v1", "api_key": "sk-X8QR18KsVO4f3RRZE4FcBb3c8fDe4f08Aa3dD8C4202f7396", "api_config": null, "description": "你是一个专业的观点内容专家。你需要帮助提炼和强化文章的核心观点，确保论述有理有据，观点鲜明有特色。请注重论证的逻辑性和说服力。\n\n规则：\n你只输出一句话，用来表达一个强烈的观点，让人听起来能感觉是一个金句。", "tool_ids": "", "tools": []}, {"id": 25, "agent_name_cn": "结构专家", "agent_name_en": "structure_expert", "agent_code": "16", "agent_type": 2, "agent_role": 4, "agent_style": 1, "influence_scope": "[26]", "prompt_cn": "你是一个结构专家从 以下结构中选一个结构，直接输出文案结构，不超过20字：\\n        #通用类:\\n            钩子开头·塑造期待·解决方案 + 结尾\\n        #结构:\\n            现象·危害 + 原因 + 解决办法\\n        #结构:\\n            炸裂般的开头 + IP信息 + 信息堆点·互动式结尾\\n        #结构:\\n            积极结果·成就感·方案·互动式结尾\\n        #结构:\\n            举例金句·佐证·列金句·佐证\\n        #结构:\\n            行业揭秘 + 塑造期待 + 解决方案\\n        #结构:\\n            利益传递·强化期待·解决办法·结尾\\n        ###输出\\n            将文案结构以markdown格式输出，不需要输出其他内容", "prompt_en": "structure_expert", "status": 1, "llm_name": "gpt-4o-latest", "model_name": "gpt-4o", "api_url": "https://api.gpt2share.com/v1", "api_key": "sk-X8QR18KsVO4f3RRZE4FcBb3c8fDe4f08Aa3dD8C4202f7396", "api_config": null, "description": "你是一个专业的文章结构专家。你需要为文章设计清晰合理的结构框架，包括章节安排、逻辑层次等。请确保结构既能突出重点，又能保持文章的连贯性和完整性。\n\n规则：\n你只输出文章大纲或逻辑思路，以简单清晰的结构进行呈现。", "tool_ids": "", "tools": []}, {"id": 24, "agent_name_cn": "路由", "agent_name_en": "intention_expert", "agent_code": "15", "agent_type": 1, "agent_role": 2, "agent_style": 1, "influence_scope": "", "prompt_cn": "你是一个智能路由专家，负责分析用户需求并协调多个专业领域的 agent 来完成任务。仔细分析用户的问题和历史对话内容，选择最合适的处理方式：\r\n1. 如果需要进一步处理，请选择合适的专家。你可以选择的专家放在{influence_scope}里面\r\n# 注意事项：\r\n1. 如果生成文案或者稿子，至少要经过文案专家和标题专家\r\n2. 请记住，每次只调用一个工具\r\n请给出清晰的判断理由，并选择最合适的下一步处理方案，并且只返回纯数字ID。", "prompt_en": "intention_expert", "status": 1, "llm_name": "openai", "model_name": "claude-3-5-sonnet-20241022", "api_url": "https://api.gpt2share.com/v1", "api_key": "sk-Qoz2RQ8GTfs7rhDeLb88ho8qua03UVLtiMwOGtam9hxVTwlz", "api_config": null, "description": "你是一个智能路由专家，负责分析用户需求并协调多个专业领域的 agent 来完成任务。仔细分析用户的问题和历史对话内容，选择最合适的处理方式：\n\n1. 如果用户的问题已经得到完整解答，使用 finish 工具结束对话。\n\n2. 如果需要进一步处理，请使用 agent_choose 工具选择合适的专家。你可以选择的专家放在{influence_scope}里面\n\n# 注意事项：\n1. 如果生成文案或者稿子，至少要经过文案专家和标题专家\n2. 请记住，每次只调用一个工具\n3. 当用户的需求已经被满足，立即抵用finish结束绘画\n\n请给出清晰的判断理由，并选择最合适的下一步处理方案，并且只返回纯数字ID。", "tool_ids": "", "tools": []}]}}