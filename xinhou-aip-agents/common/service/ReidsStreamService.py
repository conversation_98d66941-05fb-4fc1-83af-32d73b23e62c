#!/usr/bin/env python
# -*- encoding: utf-8 -*-
'''
@File    :   ReidsStreamService.py
@Time    :   2024/08/15 17:29:31
'''
import time
from datetime import datetime


class MultiAgentStreamManager:
    @staticmethod
    def create_task(task_id, r):
        task_key = f'aip_task:{task_id}'
        r.hset(task_key, 'created_at', datetime.now().isoformat())
        r.hset(task_key, 'is_end', 'false')
        r.zadd('tasks_active', {task_id: datetime.now().timestamp()})

    @staticmethod
    async def add_agent(task_id, agent_data, r):
        # 给列表增加元素
        task_key = f'aip_task:{task_id}'
        info = await r.lrange(f'{task_key}_agents', 0, -1)
        agent_id = f"{len(info) - 1}_{agent_data['agent_id']}"
        agent_key = f'{task_key}_agent_{agent_id}'

        agent_data_str = {k: str(v) for k, v in agent_data.items()}
        # await r.sadd(f'{task_key}_agents', agent_id)
        await r.hmset_dict(agent_key, agent_data_str)
        return agent_key

    @staticmethod
    async def stored_add_agent(task_id, agent_data, r):
        # 向agent列表增加元素
        task_key = f'aip_task:{task_id}'
        info = await r.lrange(f'{task_key}_agents', 0, -1)
        # if len(info)==1:
        #     agent_id = f"{len(info)}_{agent_data['stream_id']}"
        # else:
        agent_id = f"{len(info)}_{agent_data['stream_id']}"
        # agent_key = f'{task_key}_agent_{agent_id}'

        # agent_data_str = {k: str(v) for k, v in agent_data.items()}
        await r.rpush(f'{task_key}_agents', agent_id)
        # await r.hmset_dict(agent_key, agent_data_str)

    @staticmethod
    async def complete_task(task_id, r):
        task_key = f'aip_task:{task_id}'
        await r.hset(task_key, 'status', 'completed')
        await r.hset(task_key, 'completed_at', datetime.now().isoformat())
        await r.zrem('tasks_active', task_id)
        await r.zadd('tasks_completed', datetime.now().timestamp(), task_id)

    @staticmethod
    def stream_output_content(task_id, r):
        task_key = f'aip_task:{task_id}'
        agent_ids = r.smembers(f'{task_key}_agents')

        for agent_id in agent_ids:
            agent_key = f'{task_key}_agent_{agent_id.decode()}'
            content_key = f'{agent_key}_content'

            offset = 0
            while True:
                chunk = r.getrange(content_key, offset, offset + 1023)
                if not chunk:
                    if r.hget(task_key, 'status') == b'completed':
                        break
                    time.sleep(0.1)
                    continue

                offset += len(chunk)
                yield chunk.decode('utf-8')

    @staticmethod
    async def stream_input_content(task_id, agent_id, content_generator, r):
        # 将文案写入redis
        task_key = f'aip_task:{task_id}'
        info = await r.lrange(f'{task_key}_agents', 0, -1)
        if agent_id == 999:
            agent_key = f'aip_task:{task_id}_agent_{len(info)}_{agent_id}'
        else:
            agent_key = f'aip_task:{task_id}_agent_{len(info) - 1}_{agent_id}'
        content_key = f'{agent_key}_content'
        await r.append(content_key, content_generator)
        await r.hset(agent_key, 'last_updated', datetime.now().isoformat())
        await r.zadd('tasks_active', datetime.now().timestamp(), task_id)
