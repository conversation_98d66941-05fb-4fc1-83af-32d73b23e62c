# @Time : 2025/2/6 下午1:45
# <AUTHOR> daniel

# @File : PydanticAiAgent.py 

# @Software: PyCharm
import json
import logging
import asyncio
from typing import List

from pydantic import ValidationError, BaseModel
from pydantic_ai import Agent
from pydantic_ai.models.openai import OpenAIModel

from common.contents.AgentsContents import AgentsContents


class UserProfile(BaseModel):
    order_cn: str
    title: str
    because: str


class XhPydanticAi(object):
    def __init__(self):
        """
        初始化服务
        """
        self.type_info = List[UserProfile]

    async def get_title_info(self, current_agent, history, redis_client, redis_manager, task_id):
        model = OpenAIModel(current_agent.model_name, api_key=current_agent.api_key,
                            base_url=current_agent.api_url)
        agent = Agent(model, result_type=self.type_info, system_prompt=current_agent.prompt_cn)

        user_input = json.dumps(history, ensure_ascii=False)
        temp_info = []
        # user_input = "根据8米长鲸鱼尸体搁浅海南一沙滩的热点，生成一个短视频"
        is_stop = False
        previous_res = []
        async with agent.run_stream(user_input) as result:
            async for message, last in result.stream_structured(debounce_by=0.01):
                try:
                    profile = await result.validate_structured_result(
                        message,
                        allow_partial=not last,
                    )
                    if profile:
                        current_res = [{"order_cn": i.order_cn, "title": i.title, "because": i.because} for i in
                                       profile]
                        temp_info.append(current_res)
                        # 检查现有项的更新
                        for i in range(min(len(current_res), len(previous_res))):
                            if current_res[i] != previous_res[i]:
                                return_data = json.dumps([current_res[i]], ensure_ascii=False)
                                await redis_manager.stream_input_content(
                                    task_id,
                                    current_agent.id,
                                    return_data + '\n',  # 发送当前chunk
                                    redis_client
                                )
                                logging.info(f"选题{i + 1}更新:{return_data}")
                        # 检查新增项
                        if len(current_res) > len(previous_res):
                            new_items = current_res[len(previous_res):]
                            new_return_data = json.dumps(new_items, ensure_ascii=False)
                            await redis_manager.stream_input_content(
                                task_id,
                                current_agent.id,
                                new_return_data + '\n',  # 发送当前chunk
                                redis_client
                            )
                            logging.info(f"新增选题:{new_return_data}")
                        previous_res = current_res  # 更新上一次的内容
                        tmp_redis_key = f"{AgentsContents.AIP_STOP_INFO}{task_id}"
                        if await redis_client.exists(tmp_redis_key):
                            await redis_client.delete(tmp_redis_key)
                            logging.info(f"用户主动打断当前agent{current_agent.agent_name_cn}")
                            is_stop = True
                            break
                        # yield result_data
                        await asyncio.sleep(0.1)
                except ValidationError:
                    continue
        temp_info = json.dumps(temp_info[-1], ensure_ascii=False)
        return temp_info, is_stop
