# @Time : 2024/12/19 下午4:22 

# <AUTHOR> daniel

# @File : AgentHistoryService.py 

# @Software: PyCharm
from sqlalchemy import and_
from sqlalchemy.orm import Session
from xinhou_openai_framework.core.orm.service.BaseServiceImpl import BaseServiceImpl
from typing import TypeVar
from sqlalchemy import func, update
from pydantic import BaseModel
from common.entity.AgentHistoryData import AgentHistoryData

M = TypeVar('M', bound=BaseModel)


class AgentHistoryService(BaseServiceImpl[AgentHistoryData]):
    """
    Agents历史数据
    """

    def __init__(self, db: Session):
        super(AgentHistoryService, self).__init__(db, AgentHistoryData)

    def find_by_task_id(self, task_id):
        """
        根据模型查询指定ID数据
        """
        return self.db.query(AgentHistoryData).filter(self.cls.task_id == (task_id)).all()

    def update_json_field(self, task_id: int, update_value: dict):
        """
        根据 task_id 更新 data 字段，将 value 追加到 JSON 数组中
        """
        # 构造更新语句
        stmt = (
            update(self.cls)
            .where(self.cls.task_id == task_id)
            .values(history=func.JSON_ARRAY_APPEND(self.cls.data, '$', update_value))
        )
        # 执行更新操作
        result = self.db.execute(stmt)
        self.db.commit()
        # 返回受影响的行数
        return result.rowcount
