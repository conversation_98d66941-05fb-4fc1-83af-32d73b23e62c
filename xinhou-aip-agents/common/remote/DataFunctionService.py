# @Time : 2024/12/22 下午5:42 

# <AUTHOR> daniel

# @File : DataFunctionService.py 

# @Software: PyCharm
import json
from xinhou_openai_framework.core.context.model.AppContext import AppContext
from xinhou_openai_framework.core.context.model.SystemContext import ctx
from xinhou_openai_framework.core.decorator.HttpClientDecorator import http_client

from common.remote.VideoFunctionService import xh_http_client


class DataRemoteService:
    """
    AgentsPlatform 远程服务类，用于调用远程服务。
    """
    context: AppContext = ctx.__getattr__("context")  # 全局变量

    @staticmethod
    @http_client(context.aip.remote.admin, path="/rag/v1/hybrid_search", method="POST",
                 timeout=60)
    def search_rag(response_data, **kwargs):
        if response_data:
            if response_data['code'] == 200:  # 假设 200 表示成功
                return response_data['data']
            else:
                # 处理错误情况
                raise ValueError(response_data['msg'])
        else:
            raise Exception

    @staticmethod
    @http_client(context.aip.remote.knowledge, path="/knowledge/v1/spo_result", method="POST",
                 timeout=60)
    def create_knowledge(response_data, **kwargs):
        if response_data:
            if response_data['code'] == 200:  # 假设 200 表示成功
                return response_data['data']
            else:
                # 处理错误情况
                raise ValueError(response_data['msg'])
        else:
            raise Exception

    @staticmethod
    @http_client(context.aip.remote.admin, path="/api/knowledge/spo_search", method="POST",
                 timeout=60)
    def search_knowledge(response_data, **kwargs):
        if response_data:
            if response_data['code'] == 200:  # 假设 200 表示成功
                return response_data['data']
            else:
                # 处理错误情况
                raise ValueError(response_data['msg'])
        else:
            raise Exception

    @staticmethod
    @http_client(context.aip.remote.search, path="/ai/summary_search", method="POST",
                 timeout=60)
    def search_network(response_data, **kwargs):
        if response_data:
            if response_data['code'] == 200:  # 假设 200 表示成功
                return response_data['data']
            else:
                # 处理错误情况
                return ""
        else:
            return ""

    @staticmethod
    @xh_http_client(base_url=context.aip.remote.admin,
                    path="/admin/ip_prompt/detail/{pid}",
                    method="GET",
                    timeout=60)
    def get_person_info(response_data, **kwargs):
        if response_data:
            basic_information = ""
            data = json.loads(response_data['data']['json'])
            for i in data:
                if i["填写内容"] and i["填写内容"] != "无":
                    if i["IP策划字段"] in ['个人简介', 'IP超级故事', "IP自己的故事", "IP商业化及变现建议",
                                           "IP专属口头禅",
                                           "IP差异化设计--IP专属口头禅，押韵slogan，在短视频开头或者结尾会说，突破个人品牌，加强用户记忆。例如，关注李十一，生意更容易。（10个）"]:
                        one_info = str(i['填写内容']).replace("\n", "")
                        try:
                            basic_information += f"{i['IP策划字段']}:{one_info}\n"
                        except:
                            basic_information += f"{i['标题']}:{one_info}\n"
            return basic_information
        else:
            print("No response data received.")

    @staticmethod
    @http_client(context.aip.remote.admin, path="/ai_search/v1/query_detail", method="POST",
                 timeout=60)
    def get_link(response_data, **kwargs):
        if response_data:
            if response_data['code'] == 200:  # 假设 200 表示成功
                return response_data['data']
            else:
                # 处理错误情况
                return ""
        else:
            raise ""

    @staticmethod
    @xh_http_client(base_url=context.aip.remote.admin,
                    path="/admin/ip_prompt/detail/{pid}",
                    method="GET",
                    timeout=60)
    def get_new_person_info(response_data, **kwargs):
        result = ""
        if response_data:
            result = response_data['data']['json']
            return result
        else:
            return result
