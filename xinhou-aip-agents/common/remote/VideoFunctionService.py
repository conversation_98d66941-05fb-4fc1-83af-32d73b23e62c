# @Time : 2024/12/22 下午5:38 

# <AUTHOR> daniel

# @File : VideoFunctionService.py

# @Software: PyCharm
from xinhou_openai_framework.core.context.model.AppContext import AppContext
from xinhou_openai_framework.core.context.model.SystemContext import ctx
from xinhou_openai_framework.core.decorator.HttpClientDecorator import http_client
import requests
import logging
from typing import Dict, Any, Callable
from functools import wraps


def xh_http_client(base_url: str, path: str, method: str = "POST", timeout: int = 60):
    def decorator(func: Callable):
        @wraps(func)
        def wrapper(*args, **kwargs):
            try:
                # 获取请求参数
                json_data = kwargs.get('json_data', {})

                # 构建 URL
                url = f"{base_url}{path}"

                # 如果是 GET 请求且有 task_id 参数
                if method.upper() == "GET" and "task_id" in json_data:
                    # 替换 URL 中的占位符
                    url = url.format(task_id=json_data['task_id'])
                    logging.debug(f"Making GET request to: {url}")

                    # 发送 GET 请求
                    response = requests.get(
                        url,
                        timeout=timeout,
                        headers={
                            "Content-Type": "application/json",
                        }
                    )
                elif method.upper() == "GET" and "pid" in json_data:
                    url = url.format(pid=json_data['pid'])
                    logging.debug(f"Making GET request to: {url}")

                    # 发送 GET 请求
                    response = requests.get(
                        url,
                        timeout=timeout,
                        headers={
                            "Content-Type": "application/json",
                        }
                    )
                else:
                    # 其他类型的请求
                    response = requests.request(
                        method=method,
                        url=url,
                        json=json_data,
                        timeout=timeout
                    )

                # 检查响应状态
                response.raise_for_status()

                # 解析响应数据
                response_data = response.json()

                # 调用原始函数处理响应
                return func(response_data)

            except requests.exceptions.RequestException as e:
                logging.error(f"HTTP request failed: {str(e)}")
                raise
            except Exception as e:
                logging.error(f"Unexpected error: {str(e)}")
                raise

        return wrapper

    return decorator


class VideoRemoteService:
    """
    AgentsPlatform 远程服务类，用于调用远程服务。
    """
    context: AppContext = ctx.__getattr__("context")  # 全局变量

    @staticmethod
    @http_client(context.aip.remote.admin, path="/admin/media/model/findAll", method="POST",
                 timeout=60)
    def video_info(response_data, **kwargs):
        if response_data:
            if response_data['code'] == 200:  # 假设 200 表示成功
                return response_data['data']
            else:
                # 处理错误情况
                raise ValueError(response_data['msg'])
        else:
            raise Exception

    @staticmethod
    @http_client(context.aip.remote.admin, path="/admin/voice/task/createWithAudio", method="POST",
                 timeout=60)
    def video_create(response_data, **kwargs):
        if response_data:
            if response_data['code'] == 200:  # 假设 200 表示成功
                return response_data['data']
            else:
                # 处理错误情况
                raise ValueError(response_data['msg'])
        else:
            raise Exception

    @staticmethod
    @xh_http_client(base_url=context.aip.remote.admin,
                    path="/admin/task/status/{task_id}",
                    method="GET",
                    timeout=60)
    def video_result(response_data):
        if response_data:
            if response_data['code'] == 200:  # 假设 200 表示成功
                return response_data['data']
            else:
                # 处理错误情况
                raise ValueError(response_data['msg'])
        else:
            raise Exception
