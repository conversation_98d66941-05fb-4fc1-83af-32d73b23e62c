# @Time : 2025/2/10 下午3:44 

# <AUTHOR> daniel

# @File : CoziFunctionService.py 

# @Software: PyCharm
import json

from xinhou_openai_framework.core.context.model.AppContext import AppContext
from xinhou_openai_framework.core.context.model.SystemContext import ctx
from xinhou_openai_framework.core.decorator.HttpClientDecorator import http_client

context: AppContext = ctx.__getattr__("context")


class CoZeFunctionService:

    @staticmethod
    @http_client(base_url=context.aip.remote.admin, path="/admin/coze/chat", method="POST",
                 timeout=60)
    def get_spider_data(response_data, **kwargs):
        """
           获取参考文本的内容
        """
        content = ""
        title = ""
        if response_data:
            # 在这里处理业务逻辑，可以通过 res_data 获取响应数据
            try:
                data = json.loads(response_data['data'])['output']
                content = data['content']
                title = data['title']
            except:
                data = {}
            print("Received response data:", response_data)
            return {"content": content, "title": title}
        else:
            print("No response data received.")
