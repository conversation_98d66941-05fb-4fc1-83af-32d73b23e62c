# @Software: PyCharm
# @Time : 2025/3/14 下午1:37
# <AUTHOR> daniel
# @File : knowledge_service.py
# @Software: PyCharm

from xinhou_openai_framework.core.decorator.HttpClientDecorator import http_client
from xinhou_openai_framework.core.context.model.AppContext import AppContext
from xinhou_openai_framework.core.context.model.SystemContext import ctx

context: AppContext = ctx.__getattr__("context")


class KnowledgeService:
    context: AppContext = ctx.__getattr__("context")  # 全局变量

    @staticmethod
    @http_client(context.graph_knowledge.data_url, path="/graph/info", method="POST",
                 timeout=60)
    def get_graph_info(response_data, **kwargs):
        if response_data:
            if response_data['code'] == 200:  # 假设 200 表示成功
                return response_data['data']
            else:
                # 处理错误情况
                raise ValueError(response_data['msg'])
        else:
            raise Exception
