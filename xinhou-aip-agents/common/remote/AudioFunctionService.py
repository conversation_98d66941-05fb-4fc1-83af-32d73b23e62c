# @Time : 2024/12/22 下午5:07 

# <AUTHOR> daniel

# @File : AudioFunctionService.py

# @Software: PyCharm
from xinhou_openai_framework.core.context.model.AppContext import AppContext
from xinhou_openai_framework.core.context.model.SystemContext import ctx
from xinhou_openai_framework.core.decorator.HttpClientDecorator import http_client

PATH = "/admin/voice/model/findAll"


class AudioRemoteService:
    """
    AgentsPlatform 远程服务类，用于调用远程服务。
    """
    context: AppContext = ctx.__getattr__("context")  # 全局变量

    @staticmethod
    @http_client(context.aip.remote.admin, path="/admin/voice/model/findAll", method="POST",
                 timeout=60)
    def audio_info(response_data, **kwargs):
        if response_data:
            if response_data['code'] == 200:  # 假设 200 表示成功
                return response_data['data']
            else:
                # 处理错误情况
                raise ValueError(response_data['msg'])
        else:
            raise Exception

    @staticmethod
    @http_client(context.aip.remote.admin, path="/audio/fish/generate/v2", method="POST",
                 timeout=300)
    def audio_create(response_data, **kwargs):
        if response_data:
            if response_data['code'] == 200:  # 假设 200 表示成功
                return response_data['data']
            else:
                # 处理错误情况
                return ""
        else:
            return ""
