# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
全局应用初始化代理类
----------------------------------------------------
@Project :   xinhou-openai-embedding
@File    :   InitializeHandler.py
@Contact :   <EMAIL>

@Modify Time      <AUTHOR>    @Desciption
------------      -------    --------    -----------
2023/1/19 13:43   shenpeng   1.0         None
"""
import asyncio

from loguru import logger
from xinhou_openai_framework.core.context.model.AppContext import AppContext

from apps.admin.queue.consumer.MakeAgentTaskConsumer import AipAgentConsumer


async def start_queue_listener(listener_name):
    # 获取全局符号表中对应的方法
    listener_func = globals().get(listener_name)

    # 检查方法是否存在并且可调用
    if listener_func and callable(listener_func):
        # 如果存在且可调用，则创建一个异步任务来执行该方法
        asyncio.create_task(listener_func())
    else:
        logger.info(f"queue listener function [{listener_name}] not found or not callable.")


class QueueListenerHandler:
    """
    应用初始化处理类
    """

    @staticmethod
    def init_listeners(app, context: AppContext):
        @app.on_event("startup")
        async def startup_queue_listeners():
            # 启动异步任务，在后台运行 Redis 消费者队列监听 【通过类方法方式启动监听器】
            asyncio.create_task(AipAgentConsumer.process_listener())

        # # 遍历字符串列表，依次调用方法【通过函数方式启动监听器】
        # listeners = ['openai_summary_consumer_listener']
        # for listener in listeners:
        #     await start_queue_listener(listener)
