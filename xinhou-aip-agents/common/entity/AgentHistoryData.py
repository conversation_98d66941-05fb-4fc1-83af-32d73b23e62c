# @Time : 2024/12/19 下午4:23 

# <AUTHOR> daniel

# @File : AgentHistoryData.py 

# @Software: PyCharm
from sqlalchemy import Column, Integer, String, Text
from sqlalchemy.dialects.mysql import LONGTEXT
from xinhou_openai_framework.core.orm.entity.BaseEntity import BaseEntity
from typing import List, Optional, Annotated
from pydantic import BaseModel, Field, ConfigDict

class AgentHistoryData(BaseEntity):
    # 数据表名&字段
    __tablename__ = 't_agents_history'

    task_id = Column(String(30), comment='口播稿的任务id')
    pid = Column(String(30), comment='博主名称')
    status = Column(Integer, default=1, comment='口播稿生成状态1:制作中,2:完成')
    history = Column(LONGTEXT, comment='每个agent的完成记录')
    remark = Column(String(500), comment='备注')


class AgentHistoryOneData(BaseModel):
        """Agent 信息"""
        agent_name_cn: str = Field(..., title="Agent中文名称")
        agent_name_en: str = Field(..., title="Agent英文名称")
        content: str = Field(..., title="Agent输出")
        model_name: str = Field(..., title="Agent模型")
