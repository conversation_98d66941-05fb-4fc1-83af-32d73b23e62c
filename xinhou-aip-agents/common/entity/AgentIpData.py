# @Time : 2025/1/10 下午5:40 

# <AUTHOR> daniel

# @File : AgentIpData.py 

# @Software: PyCharm
from sqlalchemy import Column, Integer, String
from xinhou_openai_framework.core.orm.entity.BaseEntity import BaseEntity


class IpInfoData(BaseEntity):
    # 数据表名&字段
    __tablename__ = 't_ip'
    uid = Column(String(30), comment='用户ID')
    ip_name = Column(String(30), comment='IP名称')
    avatar = Column(String(100), comment='头像路径')
    status = Column(Integer, default=1, comment='帐号状态:1=正常,2=禁用')
    del_flag = Column(Integer, default=1, comment='删除标志:1=代表存在,2=代表删除')
    remark = Column(String(500), comment='备注')
