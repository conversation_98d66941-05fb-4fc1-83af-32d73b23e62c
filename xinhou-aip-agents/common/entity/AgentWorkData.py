# @Time : 2024/12/26 下午5:34 

# <AUTHOR> daniel

# @File : AgentWorkData.py 

# @Software: PyCharm

from sqlalchemy import Column, Integer, String, Text
from xinhou_openai_framework.core.orm.entity.BaseEntity import BaseEntity


class WorkData(BaseEntity):
    __tablename__ = 't_work'
    task_id = Column(Integer, nullable=False, comment='关联的任务id')
    work_type = Column(Integer, nullable=False, comment='作品类型:1=文本,2=音频,3=视频')
    title = Column(Text, comment='作品标题(用于文本类型)')
    content = Column(Text, comment='作品内容(用于文本类型)')
    file_id = Column(Integer, comment='关联的文件id(用于音频和视频类型)')
    status = Column(Integer, default=1, comment='作品状态:1=正常,2=待审核,3=已删除')
    remark = Column(String(500), comment='备注')
    pid = Column(Integer, nullable=False, comment='ipid')


class AgentTasksData(BaseEntity):
    # 数据表名&字段
    __tablename__ = 't_task'
    pid = Column(Integer, comment='IPID')
    status = Column(Integer, default=1, comment='帐号状态:1=正常,2=禁用')
    del_flag = Column(Integer, default=1, comment='删除标志:1=代表存在,2=代表删除')
    progress = Column(Integer, default=0, comment='任务进度（0:进行中, 1:生成中, 2:口播噶成功, 3:口播稿失败）')
    title = Column(String(500), comment='用户输入')
    remark = Column(String(500), comment='备注')
    doc_length = Column(Integer, comment='口播稿长度')
    language = Column(String(500), comment='prompt语言版本')
    is_person = Column(Integer, comment='是否使用人设')
    is_search = Column(Integer, comment='是否使用搜索')
    read_score = Column(Integer, comment='易读指数')
    style = Column(String(500), comment='风格润色')
