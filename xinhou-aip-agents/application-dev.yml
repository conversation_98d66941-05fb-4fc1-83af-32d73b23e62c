framework:
  datasource:
    url: mysql+pymysql://**************:33307/xinhou_aip_db?charset=utf8mb4
    username: root
    password: qweasdzxc123
    pool:
      pool_pre_ping: True
      pool_size: 20
      pool_timeout: 120
      echo: True
  redis:
    host: **************
    port: 36379
    password: qweasdzxc123
    timeout: 10000ms
    database: 8
  cache:
    type: redis
  logging:
    path:
    level: debug
  SECRET_KEY: D26RckVyJhin8U0EeTyQ7dOd6SO9jFmkcox0RFuQZ9M
  ALGORITHM: HS256
  ACCESS_TOKEN_EXPIRE_MINUTES: 30

model:
  bge:
    name: sbert
    path: /opt/disk1/models/baai/bge-m3
    device: cuda
  bge_reranker:
    path: /opt/disk1/models/baai/bge-reranker-v2-m3
  analysis:
    model_path: /data/models/.EasyOCR/model
    user_path: /data/models/.EasyOCR/user_network
kb_config:
  script_chunk_size: 100
  script_overlap_size: 0
  person_chunk_size: 250
  person_overlap_size: 50
  hot_chunk_size: 100
  hot_overlap_size: 0
  rerank_flag: True
  additional_num_for_rerank: 20
  vector_search_score_threshold: 1.1
  milvus_insert_batch_size: 10
  embedding_batch_size: 200
  embedding_line_max_len: 1000
file:
  bge:
    path: "/opt/disk1/upload/multi_agents"
  ocr:
    path: "static/cache_images"
  oss:
    path: "static/processed"
aliyun_oss:
  endpoint: https://oss-cn-shanghai.aliyuncs.com
  access_key_id: LTAI5tEXLTanjHcjZnZpDVG9
  access_key_secret: ******************************
  bucket_name: wechat-luobo
aliyun_acs:
  endpoint: green-cip.cn-shanghai.aliyuncs.com
  region_id: cn-shanghai
  access_key_id: LTAI5tEXLTanjHcjZnZpDVG9
  access_key_secret: ******************************
aliyun_tw:
  endpoint: https://oss-cn-shanghai.aliyuncs.com
  region_id: cn-shanghai
  access_key_id: LTAI5t5d9RvgYRxpJw4Rf8kP
  access_key_secret: ******************************
  bucket_name: wechat-luobo
docs:
  swagger-ui:
    path: /swagger-ui.html
    enabled: true
  enabled: true
  packages-to-scan: apps/**
  paths-to-match: /**
milvus:
  host: "**************"
  port: 19530
  db_name: "xinhou_sp"
  collection_name: "xinhou_sp"
  batch_size: 10
  dim: 1024
kms:
  url: https://test-vault.chatonai.com:8200
  role_id: a29bbf47-ffad-1bf4-3db1-1b53e28e890f
  secret_id: 6544dcbd-6121-3991-8998-5b360c625bc9
openai:
  chatgpt:
    base_url:
      - https://openai-service.chatonai.com/v1





