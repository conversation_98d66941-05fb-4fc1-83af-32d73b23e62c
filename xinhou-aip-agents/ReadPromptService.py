# @Time : 2025/1/9 下午6:40

# <AUTHOR> daniel

# @File : ReadPromptService.py

# @Software: PyCharm
import os
import json

if __name__ == '__main__':
    workflow_path = r'prompt_data/workflow.json'
    prompt_path = r'prompt_data/agent_prompt'

    with open(workflow_path, 'r', encoding='utf-8') as f:
        workflow_data = json.load(f)
    json_files = [f'{prompt_path}/{f}' for f in os.listdir(prompt_path) if f.endswith('.json')]
    for tmp_prompt_path in json_files:
        current_agent = os.path.basename(tmp_prompt_path).replace('.json', '').replace('.txt', '')
        for agent in workflow_data['workflow']['agents']:
            if agent['agent_name_en'] == current_agent:
                if tmp_prompt_path.endswith('.txt'):
                    with open(tmp_prompt_path, 'r', encoding='utf-8') as f:
                        prompt_content = f.read()
                        agent['prompt_cn'] = prompt_content
                else:
                    with open(tmp_prompt_path, 'r', encoding='utf-8') as f:
                        prompt_content = json.load(f)
                        agent['prompt_cn'] = prompt_content
    print(workflow_data)