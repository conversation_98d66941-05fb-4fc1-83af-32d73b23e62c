# Xinhou OpenAI Framework 框架 V1版本 使用手册

## 介绍

本项目基于 FastAPI 框架开发的后台管理系统，该版本已升级到Python 3.10版本，旨在为多语言开发者（Java、Kotlin、Python、PHP、Rust）提供一个统一的后台管理系统的模板，屏蔽个语言的编程差异，尽可能的简化快速构建微服务系统。



## 主要版本

- [x] Marmot Boot Admin 基于springboot+jpa+mysql+redis+mongodb框架版本（MVC模式）
- [x] Marmot Boot Admin DDD 基于springboot+jpa+mysql+redis+mongodb框架版本（DDD模式）
- [x] Marmot Kotlin Admin DDD 基于kotlin+springboot+jpa+mysql+redis+mongodb框架版本（DDD模式）
- [x] Marmot Gloovy Admin 基于gloovy+springboot+jpa+mysql+redis+mongodb框架版本（MVC模式）
- [x] Marmot Django Admin 基于Python语言 django+mysql+redis框架版本
- [x] Marmot Flask Admin 基于Python语言 flask+mysql+redis框架版本
- [x] Marmot FastAPI Admin 基于Python语言 fastapi+mysql+redis框架版本
- [x] Marmot FastAPI Admin 基于Python语言fastapi+pysql+redis框架版本
- [x] Marmot Think Admin 基于PHP语言 ThinkPHP+Mysql框架版本
- [x] Marmot Rust Admin 基于rust语言 actix-web + sqlx 框架版本
- [ ] Marmot Rust Admin 基于rust语言 actix-web + rbatis+pgsql 框架版本


## 更新情况

### 2024.02.23 开发需求

- [P0]新增Nacos注册中心&配置中心支持【完成】

  - 新增框架启动时初始化配置中心获取配置；
  - 新增初始化阶段支持 服务注册到Nacos，并提供异步心跳通信；
  - 新增Nacos服务调用类，支持注册服务的远程调用；实现重试、超时、降级等功能；

- [P0]修改Context上下文解析，支持Nacos部分，并取消自定义模型接收；实现动态解析对象属性；【完成】

- [P0]新增HTTP工具注解，支持接口直接注解远程调用【完成】

- [P0]新增全局常量设置类AppContents【完成】

- [P0]优化manager管理器加载流程【完成】

- [P0]新增setup包生成工具【完成】

  - 加入打包工具setup.py

  - 加入py私服包管理

  - 新增deploy.sh 打包部署工具脚本，已支持 build、rebuild、clean、upload、deploy等命令

    

## 内置功能

- [x] 应用分层管理：系统框架支持多apps应用场景聚合、微服务场景、单项目场景业务；同时支持通用业务逻辑common共享；
  - [x] 支持多模块apps应用集成调用；
  - [x] 支持Controller & ParamsModel 自定义接口模型；
  - [x] 支持Common通用业务逻辑开发，支持MVC模式；
  - [x] 支持注解Remote远程调用，同时实现重试、降级、超时异常处理；
  - [x] 支持统一返回泛型&业务逻辑调用ResModel泛型支持；
  - [x] Swagger接口文档编写，支持入参出参泛型；
- [x] 框架提供YML配置文件管理：支持YML配置，实现逻辑与springboot方式相似；
- [x] Nacos集成管理：使用Nacos的注册中心&配置中心，同时实现微服务注册&心跳&调用、支持接口的重试、降级、超时的处理；
- [x] 全局自动化路由管理：系统框架可以自动扫描项目下的apps下所有Controller接口；
- [x] 全局上下文管理：提供系统级别的全局上下文支持；
- [x] 全局ORM支持：框架支持ORM面向对象的持久化操作，只需要简单继承即可实现CRUD操作；同时支持事务处理；
- [x] 全局操作日志：系统提供日志配置&自动生成。
- [x] 全局异常处理支持：提供了全局的异常处理、业务异常处理、自定义异常处理；同时全局拦截异常&验证错误，并提供统一错误码；
- [x] 全局装饰器：提供全局装饰器，可以使接口、函数等实现 AOP 支持；
- [x] 全局数据库&连接池管理：支持MySQL、PgSQL、Oracle等数据库，并内置连接池；
- [x] 全局缓存&连接池管理：系统初始化支持配置缓存&连接池，也支持Utils调用；
- [x] 全局消息队列支持：通过使用Redis队列，实现消息队列同时支持 生产者&消费者 通过继承实现逻辑调用；同时支持消费失败补偿机制；
- [x] 文件上传：图片、表格、文档等文件上传下载 & OSS 上传存储 示例；
- [x] Excel文件：excel&cvs数据文件导入导出下载示例；
- [x] Word文件：文档的模板生成及下载示例；
- [x] PDF文件：模板生成PDF及下载示例；
- [x] 代码生成：提供代码自动生成，支持Entity、Dao、Service、Controller 及 前端页面模板生成；
- [x] docker&k8s：支持docker镜像生成 & k8s部署 及 docker镜像&docer-compose部署；
- [x] 支持服务器代理访问；

## 软件架构

~~~
marmot-framework
├─apps  # 应用目录
│  ├─admin  # 后台管理模块
│  │  ├─ config # 配置（可以省略）
│  │  ├─ controller # 控制器
│  │  └─ schema # 参数模型
│  ├─api # API接口模块
│  │  ├─ config # 配置（可以省略）
│  │  ├─ controller # 控制器
│  │  └─ schema # 参数模型
│  ├─monitor # 监控接口模块
│  │  ├─ config # 配置（可以省略）
│  │  ├─ controller # 控制器
│  │  └─ schema # 参数模型
├─backend # 后台虚拟环境
│  ├─venv # 虚拟环境
├─common  # 通用业务层
│  ├─ dao   # 数据访问层（可以省略）
│  ├─ model # 模型层
│  ├─ service # 业务逻辑层
│  └─ remote # 远程调用层
├─deploy  # 系统部署模块
│  ├─ docker  # docker-compose部署脚本
│  ├─ k3s # k3s部署脚本
│  └─ k8s # k8s部署脚本
├─docs  # 文档
├─extends # 系统扩展模块
│  ├─core # 核心模块
│  │  ├─ cache # 缓存执行器
│  │  ├─ common # 通用功能控制层（健康检查，自动生成）
│  │  ├─ config # 配置执行器
│  │  ├─ context # 上下文执行器
│  │  ├─ db # 数据库执行器
│  │  ├─ exception  # 统一异常处理
│  │  ├─ init # 统一初始化执行器
│  │  ├─ logger # 日志执行器
│  │  ├─ middleware # 中间件执行器
│  │  ├─ orm # 基础ORM抽象层
│  │  │  ├─ dao # 基础DAO抽象层
│  │  │  ├─ entity # 基础Entity抽象层
│  │  │  ├─ service # 基础Service抽象层
│  │  │  └─ tools # 基础ORM工具层
│  │  ├─ queue # 消息队列抽象层
│  │  ├─ remote # 远程调用抽象层
│  │  ├─ reponse # 抽象响应对象模型
│  │  ├─ router # 自动扫描路由执行器
│  │  ├─ session  # Session会话执行器
│  │  └─ templates # 模板执行器
│  ├─ pages  # 分页模块
│  └─ utils # 工具模块
├─logs  # 日志文件
├─script  # 脚本文件
│  └─ sql # 数据库脚本
├─static  # 全局静态文件
├─templates # 全局模板文件
│  ├─ code  # 自动生成代码模板
│  └─ default # 默认模板页面文件
├─.gitignore  # git忽略文件配置
├─application.yml # 基础配置文件
├─application-dev.yml # 开发环境配置文件
├─application-test.yml  # 测试环境配置文件
├─application-uat.yml # UAT环境配置文件
├─application-prod.yml  # 生产环境配置文件
├─dockerfile  # docker文件
├─main.py # 启动入口文件
├─manager.py  # 启动管理器文件
├─readme.md # 说明文档
└─requirement # 依赖文件
~~~



## 运行环境

> 注：项目框架主要依赖FastAPI，使用具体依赖包查看requirements.txt文件

| 名称        | 版本    | 备注                                       |
| ----------- | ------- | ------------------------------------------ |
| Python      | 3.10    |                                            |
| FastAPI     | 0.109.0 |                                            |
| Redis       | 7.0     |                                            |
| MySQL       | 8.0     |                                            |
| Nacos 2.0.2 | 2.0.2   | 后续版本会支持Nacos的注册中心&配置中心服务 |



## Object Deploy 项目部署

### PyCharm启动项目

> 注：IDE导入项目后，需要配置环境的虚拟空间。支持使用2种解析器的配置启动：1、在当前项目下创建解析器；2、使用Conda配置的解析器；建议使用当前项目解析器，便于区分和查看当前项目包加载情况和项目环境隔离；

~~~shell
# 注：本地需要安装Python 3.10版本
# 更新pip库 & 导入依赖包
(venv) MacBook-Pro-3:marmot-framework shenpeng$ pip install --upgrade pip
(venv) MacBook-Pro-3:marmot-framework shenpeng$ pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple

# 将依赖包写入requirements.txt（加入自定义包后将包导出写入文件）
(venv) MacBook-Pro-3:marmot-framework shenpeng$ pip freeze >requirements.txt
~~~



### Pip私服配置

> 注：需要在当前用户的目录下创建 .pip文件夹，并创建pip.conf配置文件，同时将配置写入文件；私服账号密码由运维提供；

~~~shell
(venv) MacBook-Pro-3:marmot-framework shenpeng$ mkdir ~/.pip
(venv) MacBook-Pro-3:marmot-framework shenpeng$ cd ~/.pip
(venv) MacBook-Pro-3:marmot-framework shenpeng$ vi pip.conf # 将配置内容复制粘贴到pip.conf文件
[global]
index-url = https://mirrors.aliyun.com/pypi/simple/
trusted-host = mirrors.aliyun.com
[install]
extra-index-url = https://<EMAIL>:<EMAIL>/repository/pip3/simple
trusted-host = nexus.xinhouai.com

(venv) shenpeng@Mac-Studio xinhou-openai-admin % pip install --upgrade pip # 通过更新pip版本测试结果
Looking in indexes: https://mirrors.aliyun.com/pypi/simple/, https://sp_hrz%40qq.com:****@nexus.xinhouai.com/repository/pip3/simple
Requirement already satisfied: pip in ./backend/venv/lib/python3.9/site-packages (23.3.1)
Collecting pip
  Downloading https://nexus.xinhouai.com/repository/pip3/packages/pip/23.3.2/pip-23.3.2-py3-none-any.whl (2.1 MB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 2.1/2.1 MB 617.0 kB/s eta 0:00:00
Installing collected packages: pip
  Attempting uninstall: pip
    Found existing installation: pip 23.3.1
    Uninstalling pip-23.3.1:
      Successfully uninstalled pip-23.3.1
Successfully installed pip-23.3.2

~~~



### Docker & K8S 部署

> 注：本地部署docker & k8s可以使用Docker Desktop，建议使用代理访问外网。

~~~shell
# 使用uvicorn 启动fastapi，通过 docker 构建 image && 发布k8s
(venv) MacBook-Pro-3:marmot-framework shenpeng$ uvicorn main:app --reload --host 0.0.0.0 --port 8000  # 本地启动服务(测试系统是否能正常运行)
(venv) MacBook-Pro-3:marmot-framework shenpeng$ docker build -t python/marmot-framework:v1.0.0 .  # 构建image
(venv) MacBook-Pro-3:marmot-framework shenpeng$ docker images python/* # 查看image是否已构建
(venv) MacBook-Pro-3:marmot-framework shenpeng$ docker run -it --rm -p 8000:8000 python/marmot-framework:v1.0.0 # 尝试使用docker启动容器验证是否正常
(venv) MacBook-Pro-3:marmot-framework shenpeng$ docker ps --filter name=fastapi # 查看容器情况
(venv) MacBook-Pro-3:marmot-framework shenpeng$ docker rm cf73f7fa1970	# 删除容器 容器ID：cf73f7fa1970

# 导入导出image镜像
(venv) MacBook-Pro-3:marmot-framework shenpeng$ docker save python/marmot-framework -o marmot-framework.tar
(venv) MacBook-Pro-3:marmot-framework shenpeng$ docker load -i marmot-framework.tar


(venv) MacBook-Pro-3:marmot-framework shenpeng$ kubectl apply -f deploy/k8s-marmot-framework.yml # 部署k8s (如果构建的image有变化名称或版本号，需要修改对应的修改镜像名称)


~~~




## 开发实战

开发阶段需要了解以下几方面的内容：Controller访问控制器、Schema参数模型层、Common通用业务层、Service业务逻辑层、Model数据模型层；结构方面主要和Java的MVC三层结构基本保持一致；已完成了Controller、Service、Dao、Entity抽象、支持面向对象的ORM和执行SQL语句；同时已加入了事务的支持；对于请求响应的结构进行了基本的封装，支持泛型参数；支持全局异常处理、全局中间件注册、全局路由自动扫描注册、全局缓存控制、全局日志、WebSocket支持、Stream流式响应、MQ消息队列、Nacos配置中心&注册中心；

### Controller 控制层

控制层包含请求函数方法、请求body、header等其中body参数需要注意，如果是list对象，则需要使用Object来接收并设置List[Object]作为对象的属性才能接收；同时Swagger配置需要特别注意，在请求入参配置字段后，需要指定response_model=ResModel[PageResultHelper[ResMenuDetailSchema]],响应参数。

~~~python
# !/usr/bin/python3
# -*- coding: utf-8 -*-

"""
菜单权限表控制器类
----------------------------------------------------
@Project :   marmot-framework
@File    :   Menu.py
@Contact :   <EMAIL>

@Modify Time      <AUTHOR>    @Desciption
------------      -------    --------    -----------
2023/04/16 22:04  peng.shen   v1.0.0     None
"""

from fastapi import APIRouter, Depends
from fastapi.params import Path
from loguru import logger
from sqlalchemy.orm import Session

from apps.admin.schema.MenuSchema import ReqMenuFindSchema, ReqMenuSaveSchema, ReqMenuUpdateSchema, ResMenuDetailSchema
from common.entity.Menu import Menu
from common.service.MenuService import MenuService
from extends.core.db.DatabaseManager import DatabaseManager
from extends.core.exception.GlobalExceptionType import ParameterException
from extends.core.reponse.R import R
from extends.core.reponse.ResModel import ResModel
from extends.pages.PageHelper import PageHelper, PageResultHelper

api = APIRouter()


@api.post('/admin/menu/find', tags=["admin", "menu"],
          response_model=ResModel[PageResultHelper[ResMenuDetailSchema]],
          summary="查询菜单权限表信息带分页接口",
          description="通过参数模型传递条件查询")
async def find(search: PageHelper[ReqMenuFindSchema], db: Session = Depends(DatabaseManager().get_session)):
  logger.info("[body]:{}".format(search.model_dump_json()))
  return R.SUCCESS(MenuService(db).find_by(search))


@api.post('/admin/menu/findAll', tags=["admin", "menu"],
          response_model=ResModel[PageResultHelper[ResMenuDetailSchema]],
          summary="查询菜单权限表信息接口",
          description="通过参数模型传递条件查询")
async def find_all(search: PageHelper[ReqMenuFindSchema], db: Session = Depends(DatabaseManager().get_session)):
  logger.info("[body]:{}".format(search.model_dump_json()))
  return R.SUCCESS(MenuService(db).find_all(search))


@api.post("/admin/menu/save", tags=["admin", "menu"],
          response_model=ResModel[ResMenuDetailSchema],
          summary="保存菜单权限表信息接口",
          description="通过参数模型保存数据")
async def save(model: ReqMenuSaveSchema, db: Session = Depends(DatabaseManager().get_session)):
  logger.info("[body]:{}".format(model.model_dump_json()))
  return R.SUCCESS(MenuService(db).save(Menu(**model.model_dump(exclude_unset=True))))


@api.get("/admin/menu/delete/{id}", tags=["admin", "menu"],
         response_model=ResModel,
         summary="删除菜单权限表信息接口",
         description="根据ID删除数据")
async def delete(id: int = Path(title="ID不能为空"), db: Session = Depends(DatabaseManager().get_session)):
  logger.info("[params]:{}".format({"id": id}))
  if id is None:
    raise ParameterException()
  MenuService(db).delete(Menu(id=id))
  return R.SUCCESS()


@api.post("/admin/menu/update", tags=["admin", "menu"],
          response_model=ResModel[ResMenuDetailSchema],
          summary="更新菜单权限表信息接口",
          description="根据ID更新数据")
async def update(model: ReqMenuUpdateSchema, db: Session = Depends(DatabaseManager().get_session)):
  logger.info("[body]:{}".format(model.model_dump_json()))
  if model.id is None:
    raise ParameterException()
  return R.SUCCESS(MenuService(db).update(Menu(**model.model_dump(exclude_unset=True))))


@api.get("/admin/menu/detail/{id}", tags=["admin", "menu"],
         response_model=ResModel[ResMenuDetailSchema],
         summary="获取菜单权限表详情接口",
         description="根据ID获取数据")
async def detail(id: int = Path(title="ID不能为空"), db: Session = Depends(DatabaseManager().get_session)):
  logger.info("[params]:{}".format({"id": id}))
  if id is None:
    raise ParameterException()
  return R.SUCCESS(MenuService(db).find_by_id(Menu(id=id)))


~~~

### Model 参数模型层

~~~python
# !/usr/bin/python3
# -*- coding: utf-8 -*-

"""
菜单权限表模型类
----------------------------------------------------
@Project :   marmot-framework
@File    :   Menu.py
@Contact :   <EMAIL>

@Modify Time      <AUTHOR>    @Desciption
------------      -------    --------    -----------
2023/04/16 22:04  peng.shen   v1.0.0     None
"""

from datetime import datetime
from typing import Optional

from pydantic import BaseModel, Field


class ReqMenuFindSchema(BaseModel):
    """
    菜单权限表 查询入参参数&校验模型
    """

    id: Optional[int] = Field(default=None, title="菜单ID")
    menu_name: Optional[str] = Field(default=None, title="菜单名称")
    parent_id: Optional[int] = Field(default=None, title="父菜单ID:join=t_menu,index=1")
    order_num: Optional[int] = Field(default=None, title="显示顺序")
    url: Optional[str] = Field(default=None, title="请求地址")
    menu_type: Optional[int] = Field(default=None, title="菜单类型:1=目录,2=菜单,3=按钮")
    perms: Optional[str] = Field(default=None, title="权限标识")
    level: Optional[int] = Field(default=None, title="菜单层级")
    icon: Optional[str] = Field(default=None, title="菜单图标")
    is_frame: Optional[int] = Field(default=None, title="是否外链")
    visible: Optional[int] = Field(default=None, title="菜单状态:1=显示,2=隐藏")
    create_by: Optional[str] = Field(default=None, title="创建者")
    created_at: Optional[datetime] = Field(default=None, title="创建时间")
    update_by: Optional[str] = Field(default=None, title="更新者")
    updated_at: Optional[datetime] = Field(default=None, title="更新时间")
    remark: Optional[str] = Field(default=None, title="备注")


class ReqMenuSaveSchema(BaseModel):
    """
    菜单权限表 保存入参参数&校验模型
    """

    menu_name: Optional[str] = Field(title="菜单名称")
    parent_id: Optional[int] = Field(default='0', title="父菜单ID:join=t_menu,index=1")
    order_num: Optional[int] = Field(default='0', title="显示顺序")
    url: Optional[str] = Field(default='#', title="请求地址")
    menu_type: Optional[int] = Field(title="菜单类型:1=目录,2=菜单,3=按钮")
    perms: Optional[str] = Field(title="权限标识")
    level: Optional[int] = Field(default='0', title="菜单层级")
    icon: Optional[str] = Field(default='#', title="菜单图标")
    is_frame: Optional[int] = Field(default='2', title="是否外链")
    visible: Optional[int] = Field(default='1', title="菜单状态:1=显示,2=隐藏")
    create_by: Optional[str] = Field(default=None, title="创建者")
    update_by: Optional[str] = Field(default=None, title="更新者")
    remark: Optional[str] = Field(default=None, title="备注")


class ReqMenuUpdateSchema(BaseModel):
    """
    菜单权限表 更新入参参数&校验模型
    """
    id: int = Field(title="菜单ID")
    menu_name: Optional[str] = Field(default=None, title="菜单名称")
    parent_id: Optional[int] = Field(default=None, title="父菜单ID:join=t_menu,index=1")
    order_num: Optional[int] = Field(default=None, title="显示顺序")
    url: Optional[str] = Field(default=None, title="请求地址")
    menu_type: Optional[int] = Field(default=None, title="菜单类型:1=目录,2=菜单,3=按钮")
    perms: Optional[str] = Field(default=None, title="权限标识")
    level: Optional[int] = Field(default=None, title="菜单层级")
    icon: Optional[str] = Field(default=None, title="菜单图标")
    is_frame: Optional[int] = Field(default=None, title="是否外链")
    visible: Optional[int] = Field(default=None, title="菜单状态:1=显示,2=隐藏")
    create_by: Optional[str] = Field(default=None, title="创建者")
    update_by: Optional[str] = Field(default=None, title="更新者")
    remark: Optional[str] = Field(default=None, title="备注")


class ResMenuDetailSchema(BaseModel):
    """
    菜单权限表 查询出参参数&校验模型
    """

    id: Optional[int] = Field(default=None, title="菜单ID")
    menu_name: Optional[str] = Field(default=None, title="菜单名称")
    parent_id: Optional[int] = Field(default=None, title="父菜单ID:join=t_menu,index=1")
    order_num: Optional[int] = Field(default=None, title="显示顺序")
    url: Optional[str] = Field(default=None, title="请求地址")
    menu_type: Optional[int] = Field(default=None, title="菜单类型:1=目录,2=菜单,3=按钮")
    perms: Optional[str] = Field(default=None, title="权限标识")
    level: Optional[int] = Field(default=None, title="菜单层级")
    icon: Optional[str] = Field(default=None, title="菜单图标")
    is_frame: Optional[int] = Field(default=None, title="是否外链")
    visible: Optional[int] = Field(default=None, title="菜单状态:1=显示,2=隐藏")
    create_by: Optional[str] = Field(default=None, title="创建者")
    created_at: Optional[datetime] = Field(default=None, title="创建时间")
    update_by: Optional[str] = Field(default=None, title="更新者")
    updated_at: Optional[datetime] = Field(default=None, title="更新时间")
    remark: Optional[str] = Field(default=None, title="备注")

~~~



### Common 通用层

通用层是项目框架划分的逻辑层，主要负责将 业务逻辑层、数据模型层、远程调用层等统一规划调用的层级；可以理解为java中的Business层

#### Service 业务逻辑层

> 业务逻辑层 - 顾名思义核心业务逻辑可以在这一层完成；与Java项目中的Service的作用一致；该层的类统一继承自BaseServiceImpl实现类，并需要给出具体的泛型模型；已支持基本的CURD操作；

~~~python
# !/usr/bin/python3
# -*- coding: utf-8 -*-

"""
菜单权限表服务类
----------------------------------------------------
@Project :   marmot-framework
@File    :   Menu.py
@Contact :   <EMAIL>

@Modify Time      <AUTHOR>    @Desciption
------------      -------    --------    -----------
2023/04/16 22:04  peng.shen   v1.0.0     None
"""

from sqlalchemy.orm import Session
from common.entity.Menu import Menu
from extends.core.orm.service.BaseServiceImpl import BaseServiceImpl
from extends.pages.PageHelper import PageHelper
from extends.pages.Paginate import Paginate


class MenuService(BaseServiceImpl[Menu]):
  """
  用户服务类
  """

  def __init__(self, db: Session):
    super(MenuService, self).__init__(db, Menu)

~~~



#### Entity 实体模型层

> 数据模型层统一继承BaseEntity实体类，支持基本的实体CURD操作，可以直接通过链式调用；

~~~python
# !/usr/bin/python3
# -*- coding: utf-8 -*-

"""
菜单权限表模型类
----------------------------------------------------
@Project :   marmot-framework
@File    :   Menu.py
@Contact :   <EMAIL>

@Modify Time      <AUTHOR>    @Desciption
------------      -------    --------    -----------
2023/04/16 22:04  peng.shen   v1.0.0     None
"""

from sqlalchemy import ForeignKey
from sqlalchemy import func

from sqlalchemy import Column, Integer, String, Text, Numeric, TIMESTAMP
from extends.core.orm.entity.BaseEntity import BaseEntity


class Menu(BaseEntity):
    # 数据表名&字段
    __tablename__ = 't_menu'

    menu_name = Column(String(50), comment="菜单名称")
    parent_id = Column(Integer, default='0', comment="父菜单ID:join=t_menu,index=1")
    order_num = Column(Integer, default='0', comment="显示顺序")
    url = Column(String(200), default='#', comment="请求地址")
    menu_type = Column(Integer, comment="菜单类型:1=目录,2=菜单,3=按钮")
    perms = Column(String(100), comment="权限标识")
    level = Column(Integer, default='0', comment="菜单层级")
    icon = Column(String(100), default='#', comment="菜单图标")
    is_frame = Column(Integer, default='2', comment="是否外链")
    remark = Column(String(500), comment="备注")
    visible = Column(Integer, default='1', comment="菜单状态:1=显示,2=隐藏")

~~~

#### Remote 远程调用层

> 远程调用支持 Http&Https方式；当可以通过注解@HttpClient定义接口方法或函数调用；

- @HttpClient 注解使用样例

~~~python
class OpenaiRemoteService:
    """
    OpenAI 远程服务类，用于调用远程服务。
    """

    @staticmethod
    @http_client("http://127.0.0.1:8000", path="/test/summary/callback", json_data={
        "summary_process_key": "1",
        "summary_process_result": "测试"
    }, headers={
        "tenant_id": '888888',
        "classify_type": 'role',
        "classify_id": '888888',
        "platform_code": 'pt'
    }, method="POST", retry_times=3, retry_delay=1, retry_backoff=2)
    def health(response_data, **kwargs):
        """
        健康检查函数。

        Args:
            response_data (dict): 响应数据。
            **kwargs: 关键字参数。

        Returns:
            None
        """
        if response_data:
            # 在这里处理业务逻辑，可以通过 res_data 获取响应数据
            print("Received response data:", response_data)
        else:
            print("No response data received.")


def main():
    # 调用方式默认使用@http_client注解时通过header 和json_data等参数设置
    OpenaiRemoteService.health()

    # 调用方式二
    OpenaiRemoteService.health(json_data={
        "summary_process_key": "2",
        "summary_process_result": "测试"
    }, headers={
        "tenant_id": '888888',
        "classify_type": 'role',
        "classify_id": '888888',
        "platform_code": 'pt'
    }, method="POST")


if __name__ == "__main__":
    main()
~~~


### ORM - CRUD操作

>框架中的持久化操作，统一使用SQLAlchemy完成；已完成了Service、Dao抽象层的封装；该框架与Java中的JPA&Hibernate使用上相似；

~~~python
# 原生sql语句操作
sql = 'select * from user'
result = db.session.execute(sql)

# 查询全部
User.query.all()
# 主键查询
User.query.get(1)
# 条件查询
User.query.filter_by(User.username='name')
# 多条件查询
from sqlalchemy import and_
User.query.filter_by(and_(User.username =='name',User.password=='passwd'))
# 多表联查 inner join
db.session.query(UserRole, User, Role) \
            .join(User, UserRole.user_id == User.id) \
            .join(Role, UserRole.role_id == Role.id) \
            .filter(UserRole.user_id == id).all()
# 多表联查 left join
db.session.query(UserRole, User, Role) \
            .join(User, UserRole.user_id == User.id, isouter=True) \
            .join(Role, UserRole.role_id == Role.id, isouter=True) \
            .filter(UserRole.user_id == id).all()

# 比较查询
User.query.filter(User.id.__lt__(5)) # 小于5
User.query.filter(User.id.__le__(5)) # 小于等于5
User.query.filter(User.id.__gt__(5)) # 大于5
User.query.filter(User.id.__ge__(5)) # 大于等于5
# in查询
User.query.filter(User.username.in_('A','B','C','D'))
# 排序
User.query.order_by('age') # 按年龄排序，默认升序，在前面加-号为降序'-age'
# 限制查询
User.query.filter(age=18).offset(2).limit(3)  # 跳过二条开始查询，限制输出3条

# 增加
use = User(id,username,password)
db.session.add(use)
db.session.commit() 

# 删除
User.query.filter_by(User.username='name').delete()

# 修改
User.query.filter_by(User.username='name').update({'password':'newdata'})
~~~



### Request & Response 请求与响应

#### 请求

在Http请求中需要对入参做Schema模型，便于每个请求都有面向业务的参数；同时支持嵌套泛型对象search: PageHelper[ReqMenuFindSchema]，具体模型使用可以参考MenuController代码内容。

~~~python
# !/usr/bin/python3
# -*- coding: utf-8 -*-

"""
菜单权限表模型类
----------------------------------------------------
@Project :   marmot-framework
@File    :   Menu.py
@Contact :   <EMAIL>

@Modify Time      <AUTHOR>    @Desciption
------------      -------    --------    -----------
2023/04/16 22:04  peng.shen   v1.0.0     None
"""

from datetime import datetime
from typing import Optional

from pydantic import BaseModel, Field


class ReqMenuFindSchema(BaseModel):
    """
    菜单权限表 查询入参参数&校验模型
    """

    id: Optional[int] = Field(default=None, title="菜单ID")
    menu_name: Optional[str] = Field(default=None, title="菜单名称")
    parent_id: Optional[int] = Field(default=None, title="父菜单ID:join=t_menu,index=1")
    order_num: Optional[int] = Field(default=None, title="显示顺序")
    url: Optional[str] = Field(default=None, title="请求地址")
    menu_type: Optional[int] = Field(default=None, title="菜单类型:1=目录,2=菜单,3=按钮")
    perms: Optional[str] = Field(default=None, title="权限标识")
    level: Optional[int] = Field(default=None, title="菜单层级")
    icon: Optional[str] = Field(default=None, title="菜单图标")
    is_frame: Optional[int] = Field(default=None, title="是否外链")
    visible: Optional[int] = Field(default=None, title="菜单状态:1=显示,2=隐藏")
    create_by: Optional[str] = Field(default=None, title="创建者")
    created_at: Optional[datetime] = Field(default=None, title="创建时间")
    update_by: Optional[str] = Field(default=None, title="更新者")
    updated_at: Optional[datetime] = Field(default=None, title="更新时间")
    remark: Optional[str] = Field(default=None, title="备注")


class ReqMenuSaveSchema(BaseModel):
    """
    菜单权限表 保存入参参数&校验模型
    """

    menu_name: Optional[str] = Field(title="菜单名称")
    parent_id: Optional[int] = Field(default='0', title="父菜单ID:join=t_menu,index=1")
    order_num: Optional[int] = Field(default='0', title="显示顺序")
    url: Optional[str] = Field(default='#', title="请求地址")
    menu_type: Optional[int] = Field(title="菜单类型:1=目录,2=菜单,3=按钮")
    perms: Optional[str] = Field(title="权限标识")
    level: Optional[int] = Field(default='0', title="菜单层级")
    icon: Optional[str] = Field(default='#', title="菜单图标")
    is_frame: Optional[int] = Field(default='2', title="是否外链")
    visible: Optional[int] = Field(default='1', title="菜单状态:1=显示,2=隐藏")
    create_by: Optional[str] = Field(default=None, title="创建者")
    update_by: Optional[str] = Field(default=None, title="更新者")
    remark: Optional[str] = Field(default=None, title="备注")


class ReqMenuUpdateSchema(BaseModel):
    """
    菜单权限表 更新入参参数&校验模型
    """
    id: int = Field(title="菜单ID")
    menu_name: Optional[str] = Field(default=None, title="菜单名称")
    parent_id: Optional[int] = Field(default=None, title="父菜单ID:join=t_menu,index=1")
    order_num: Optional[int] = Field(default=None, title="显示顺序")
    url: Optional[str] = Field(default=None, title="请求地址")
    menu_type: Optional[int] = Field(default=None, title="菜单类型:1=目录,2=菜单,3=按钮")
    perms: Optional[str] = Field(default=None, title="权限标识")
    level: Optional[int] = Field(default=None, title="菜单层级")
    icon: Optional[str] = Field(default=None, title="菜单图标")
    is_frame: Optional[int] = Field(default=None, title="是否外链")
    visible: Optional[int] = Field(default=None, title="菜单状态:1=显示,2=隐藏")
    create_by: Optional[str] = Field(default=None, title="创建者")
    update_by: Optional[str] = Field(default=None, title="更新者")
    remark: Optional[str] = Field(default=None, title="备注")


~~~



#### 响应

响应参数支持ResModel通用模型，泛型response_model=ResModel[PageResultHelper[ResMenuDetailSchema]]对象等格式；

~~~python
class ResMenuDetailSchema(BaseModel):
    """
    菜单权限表 查询出参参数&校验模型
    """

    id: Optional[int] = Field(default=None, title="菜单ID")
    menu_name: Optional[str] = Field(default=None, title="菜单名称")
    parent_id: Optional[int] = Field(default=None, title="父菜单ID:join=t_menu,index=1")
    order_num: Optional[int] = Field(default=None, title="显示顺序")
    url: Optional[str] = Field(default=None, title="请求地址")
    menu_type: Optional[int] = Field(default=None, title="菜单类型:1=目录,2=菜单,3=按钮")
    perms: Optional[str] = Field(default=None, title="权限标识")
    level: Optional[int] = Field(default=None, title="菜单层级")
    icon: Optional[str] = Field(default=None, title="菜单图标")
    is_frame: Optional[int] = Field(default=None, title="是否外链")
    visible: Optional[int] = Field(default=None, title="菜单状态:1=显示,2=隐藏")
    create_by: Optional[str] = Field(default=None, title="创建者")
    created_at: Optional[datetime] = Field(default=None, title="创建时间")
    update_by: Optional[str] = Field(default=None, title="更新者")
    updated_at: Optional[datetime] = Field(default=None, title="更新时间")
    remark: Optional[str] = Field(default=None, title="备注")
~~~





### Template & Function 模板与函数注入

~~~python
# !/usr/bin/python3
# -*- coding: utf-8 -*-

"""
{{ table.table_comment }}模型类
----------------------------------------------------
@Project :   {{ table.project_name }}
@File    :   {{ table.file_name }}
@Contact :   {{ table.contact }}

@Modify Time      <AUTHOR>    @Desciption
------------      -------    --------    -----------
{{ table.modify_time }}  {{ table.author }}   {{ table.version }}     {{ table.desciption }}
"""

from sqlalchemy import ForeignKey
from sqlalchemy import func

from sqlalchemy import Column, Integer, String, Text, Numeric, TIMESTAMP
from extends.core.orm.entity.BaseEntity import BaseEntity


class {{ table.class_name }}(BaseEntity):
    # 数据表名&字段
    __tablename__ = '{{ table.table_name }}'
{% for field in table.fields %}{% if not field.column_name in table.ignore_fields %}
    {{ field.column_name }} = Column({{ field.column_type|mapping_column_type }}{{ field.column_default|check_column_default }}, comment="{{ field.column_comment }}"){% endif %}{% endfor %}

~~~

~~~python
# 解析字段-》生成模型 -》service》Controller
env = Environment(loader=FileSystemLoader("templates"))
# 注册自定义函数到模板jinja2环境
env.filters["mapping_column_type"] = mapping_column_type
env.filters["mapping_schema_column_type"] = mapping_schema_column_type
env.filters["check_column_default"] = check_column_default
env.filters["check_schema_column_default"] = check_schema_column_default
~~~



### Exception 异常

全局统一初始化使用GlobalExceptionHandler异常代理类，统一处理GlobalBusinessException、HTTPException、FileNotFoundError等错误。全局异常处理已划分为 验证异常处理 和 系统异常处理；

~~~python
# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
初始化全局异常代理文件
----------------------------------------------------
@Project :   marmot-framework
@File    :   ExceptionHandler.py
@Contact :   <EMAIL>

@Modify Time      <AUTHOR>    @Desciption
------------      -------    --------    -----------
2021/7/12 12:55   shenpeng   1.0         None
"""
from fastapi.exceptions import RequestValidationError
from loguru import logger
from starlette import status
from starlette.exceptions import HTTPException
from starlette.requests import Request
from starlette.responses import JSONResponse

from extends.core.context.model.AppContext import AppContext
from extends.core.exception.CodeEnum import CodeEnum
from extends.core.exception.GlobalBusinessException import GlobalBusinessException
from extends.core.reponse.R import R


class GlobalExceptionHandler:
    """
    全局异常处理
    """

    @staticmethod
    def init_excepts(app, context: AppContext):
        @app.exception_handler(RequestValidationError)
        async def validation_exception_handler(request: Request, exc: RequestValidationError):
            message = []
            for error in exc.errors():
                message.append({"field": ".".join(error.get("loc")), "cause": error.get("msg")})
            return JSONResponse(
                status_code=status.HTTP_200_OK,
                content={"code": CodeEnum.PARAMETER_ERROR.value['code'], "msg": CodeEnum.PARAMETER_ERROR.value['msg'],
                         "data": message}
            )

        @app.exception_handler(Exception)
        async def exception_handler(req: Request, e: Exception):
            """全局处理错误方法"""
            logger.error(e)  # 对错误进行日志记录

            if isinstance(e, GlobalBusinessException):
                # 处理 GlobalBusinessException 异常并返回其自定义消息体
                return JSONResponse(e.get_body())

            if isinstance(e, HTTPException):
                # 处理 FastAPI 内置的 HTTPException 并返回 R.REQUEST_ERROR
                return R.REQUEST_ERROR()

            if isinstance(e, FileNotFoundError):
                # 处理 FileNotFoundError 异常并返回 R.FILE_NO_FOUND
                return R.FILE_NO_FOUND()

            # 默认情况下，返回 R.SERVER_ERROR
            return R.SERVER_ERROR()

~~~



### Middleware 中间件

> 在整体框架中使用注册Handler方式加入执行的中间件，理论上任何功能都可以做成中间件在项目启动时注册；以下功能实现请求计算执行时间统计；

~~~Python
# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
初始化全局Http中间件
----------------------------------------------------
@Project :   marmot-framework  
@File    :   HttpMiddlewareHandler.py
@Contact :   <EMAIL>

@Modify Time      <AUTHOR>    @Desciption
------------      -------    --------    -----------
2023/2/20 17:45   shenpeng   1.0         None
"""
import time
from urllib.request import Request


class HttpMiddlewareHandler:

    @staticmethod
    def init_http_middleware(app, context):
        @app.middleware("http")
        async def add_process_time_header(request: Request, call_next):
            """
            为app增加接口处理耗时的响应头信息
            """
            start_time = time.time()
            response = await call_next(request)
            process_time = time.time() - start_time
            # X- 作为前缀代表专有自定义请求头
            response.headers["X-Process-Time"] = str(process_time)
            return response


~~~





### Router 路由

>  全局自动扫描路由&注册初始化，通过GlobalRouteHandler实现在系统启动时自动扫描apps目录，将所有controller标注的路由统一注册到上下文。在运行时将自动发现并自动注册路由；

自动注册路由规则：

- 控制器的命名规则 TestController.py  业务名称+Controller

- 控制器内部 蓝图命名 controller = Bluelogger.info('home:docker', __name__, url_prefix="/home/<USER>");
      - 蓝图变量统一使用controller变量名
     - 蓝图名称 模块+类名(或文件名) ：'home:docker'
     - 路由前缀（必须添加）
     - 路由注解 统一使用 @controller.route('/index')

- 路由扫描时根据 扫描指定项目(marmot-framework)下的 应用层(apps) 的所有目录下的Controller文件

~~~python
# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
全局路由初始化处理类

自动注册路由规则：
1、控制器的命名规则 ToolController.py  业务名称+Controller
2、控制器内部 路由命名 api = APIRouter()
    a.路由变量统一使用api变量名
    b.路由名称 模块+类名(或文件名) ：'home:docker'
    c.路由前缀（必须添加）
    d.路由注解 统一使用 @controller.route('/index')
3、路由扫描时根据 扫描指定项目(marmot-framework)下的 应用层(apps) 的所有目录下的Controller文件
----------------------------------------------------
@Project :   marmot-framework
@File    :   RouterHandler.py
@Contact :   <EMAIL>

@Modify Time      <AUTHOR>    @Desciption
------------      -------    --------    -----------
2021/7/13 17:11   shenpeng   1.0         None
"""
import importlib
import os

from apps.openai.socket.OpenAiSocket import ws_chat_router
from extends.core.common.controller.CommonController import base_common_api
from extends.core.common.controller.GenerateCodeController import code_generater_api
from extends.utils.PathUtil import PathUtil
from extends.utils.StrUtil import StrUtil


class RouterHandler:
  """
  全局路由初始化处理类

  自动注册路由规则：
  1、控制器的命名规则 ToolController.py  业务名称+Controller
  2、控制器内部 路由命名 api = APIRouter()
      a.路由变量统一使用api变量名
      b.路由名称 模块+类名(或文件名) ：'home:docker'
      c.路由前缀（必须添加）
      d.路由注解 统一使用 @controller.route('/index')
  3、路由扫描时根据 扫描指定项目(marmot-framework)下的 应用层(apps) 的所有目录下的Controller文件
  """

  @staticmethod
  def init_routes(app, context):
    """
    自动扫描 初始化 应用路由路由
    :param app:
    :return:
    """
    app.include_router(base_common_api)  # 注册自动生成代码路由
    app.include_router(code_generater_api)  # 注册自动生成代码路由
    app.include_router(ws_chat_router)  # 注册websocket路由
    path = PathUtil.get_root_path() + "/apps"
    RouterHandler.auto_scan_routes(app, path)  # 自动扫描路由

  @staticmethod
  def auto_scan_routes(app, filePath):
    """
    自动扫描路由注册
    :param app:
    :param filePath:
    :return:
    """
    file_list = os.listdir(filePath)
    for file_name in file_list:  # 循环文件&文件夹类别
      if os.path.isdir(os.path.join(filePath, file_name)):
        RouterHandler.auto_scan_routes(app, os.path.join(filePath, file_name))
      else:
        if file_name.find("Controller.py") > -1:
          clazz_path = StrUtil.sub_str_after(filePath, "apps").replace("/", ".")
          clazz_name = StrUtil.sub_str_before(file_name, ".py")
          clazz_full = clazz_path + "." + clazz_name
          auto_route_module_controller_class = importlib.import_module(clazz_full)
          flag = 'api' in auto_route_module_controller_class.__dict__
          if flag:
            module_controller_route_class = auto_route_module_controller_class.__dict__["api"]
            app.include_router(module_controller_route_class)  # 注册路由
            # logger.info(" * auto scan route name：", clazz_full)

~~~

### Logger日志

全局统一日志处理，日志将自动存入logs目录下的日志文件；

#### 全局配置文件

~~~yml
docs:
  swagger-ui:
    path: /swagger-ui.html
    enabled: true
  enabled: true
  packages-to-scan: apps/**
  paths-to-match: /**
~~~



#### 日志管理器

~~~Python
# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
功能描述
----------------------------------------------------
@Project :   marmot-framework  
@File    :   LoggerManager.py    
@Contact :   <EMAIL>

@Modify Time      <AUTHOR>    @Desciption
------------      -------    --------    -----------
2023/2/23 18:49   shenpeng   1.0         None
"""
import logging
import os
from datetime import datetime
from logging import StreamHandler
from logging.handlers import RotatingFileHandler

from loguru import logger


class LoggerManager:

    @staticmethod
    def init_logger(app, log_path: str = None):
        """
        初始化 logger，将日志同时输出到 console 和指定的文件中。
        """
        logger.remove()
        logger.add(
            sink=StreamHandler(),
            format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}:{function}:{line}</cyan> | <level>{message}</level>",
            colorize=True,
            enqueue=True,
        )
        file_info = log_path + f"/{datetime.now().strftime('%Y-%m-%d')}.log"
        os.makedirs(log_path, exist_ok=True)
        if log_path is not None:
            # 创建日志目录
            logger.add(
                sink=file_info,
                format="{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}",
                rotation="1 day",
                retention="7 days",
                encoding="utf-8",
                compression="zip",
                colorize=True,
                serialize=False,
            )

        sql_logger = logging.getLogger('sqlalchemy')
        if not sql_logger.handlers:
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            sql_rf_handler = RotatingFileHandler(file_info, maxBytes=100000, backupCount=10)
            sql_rf_handler.setLevel(logging.DEBUG)
            sql_rf_handler.setFormatter(formatter)
            sql_logger.addHandler(sql_rf_handler)

~~~



### Cache缓存

框架层使用Redis缓存，提供了连接池&缓存管理器&缓存执行器；在使用的过程中需要做如下步骤：

#### 配置文件

~~~yml
framework:
  redis:
    host: kubernetes.docker.internal
    port: 36379
    password: '' # 密码如果是纯数字，需要加上单引号
    timeout: 10000ms
    database: 8
  cache:
    type: redis # 指定缓存类型
~~~

#### 初始化执行器&管理器

~~~Python
# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
缓存管理器
----------------------------------------------------
@Project :   marmot-framework
@File    :   CacheManager.py
@Contact :   <EMAIL>

@Modify Time      <AUTHOR>    @Desciption
------------      -------    --------    -----------
2023/4/4 17:34   shenpeng   1.0         None
"""
import json

from extends.core.context.model.AppContext import AppContext
from extends.utils.RedisUtil import RedisUtil


class CacheManager:
    _instance = None

    def __init__(self):
        self.__redis = None

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance

    @classmethod
    def get_instance(cls):
        if cls._instance is None:
            cls._instance = cls()
        return cls._instance

    def connect(self, context: AppContext):
        host = context.framework.redis.host
        port = context.framework.redis.port
        db = context.framework.redis.database
        passwd = context.framework.redis.passwd
        self.__redis = RedisUtil(host=host, port=port, db=db, password=passwd)

    def disconnect(self):
        if self.__redis is not None:
            self.__redis = None

    def set(self, key, value, ex=None):
        try:
            value = json.dumps(value)
            if ex:
                self.__redis.setex(key, value, ex)
            else:
                self.__redis.set(key, value)
            return True
        except Exception as e:
            logger.info("Error: ", e)
            return False

    def get(self, key):
        try:
            value = self.__redis.get(key)
            if value:
                value = json.loads(value)
            return value
        except Exception as e:
            logger.info("Error: ", e)
            return None

    def delete(self, *keys):
        try:
            return self.__redis.delete(*keys)
        except Exception as e:
            self.__logger.error(f"删除键值对失败：{e}")
            return 0

    def update(self, key, value):
        try:
            self.__redis.set(key, json.dumps(value))
            return True
        except Exception as e:
            self.__logger.error(f"修改键值对失败：{e}")
            return False

~~~



### WebSocket支持

~~~python
# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
WebSocket功能描述
----------------------------------------------------
@Project :   marmot-framework  
@File    :   OpenAiSocket.py
@Contact :   <EMAIL>

@Modify Time      <AUTHOR>    @Desciption
------------      -------    --------    -----------
2023/4/13 13:50   shenpeng   1.0         None
"""
import openai

from fastapi import WebSocket, Depends, APIRouter

ws_chat_router = APIRouter()


async def get_user_from_db(token: str):
    # 在此处执行认证逻辑，返回用户信息
    logger.info(token)
    return token


async def chat_websocket_auth(websocket: WebSocket, token: str = Depends(get_user_from_db)):
    if not token:
        await websocket.close(code=1008)
    else:
        await websocket.accept()


@ws_chat_router.websocket("/chat")
async def chat_websocket_endpoint(websocket: WebSocket, token: str = "", show_type: int = 0):
    await chat_websocket_auth(websocket, token)
    while True:
        data = await websocket.receive_text()
        content = ""
        response = request.post(url,params) # 请求或调用service分批获取数据
        for r in response:
            if "content" in r.choices[0].delta:
                content = r.choices[0].delta['content']
                logger.info(content, end='')
                answer += content
                if show_type == 0:
                    await websocket.send_text(f"{content}")
        if show_type == 1: # 一次组织全量发送
            await websocket.send_text(f"{content}")

~~~





### Stream流式响应

~~~python
@api.post("/openai/v5/training/stream/chat", tags=["OpenAI", "v5"],
          response_class=StreamingResponse,
          summary="[v5][OpenAI][Stream]进行Chat对话支持多模态接口",
          description="注：此chat接口已支持多模态，图片需要先上传到oss后，传递完整url才可以由LLM识别并提问，向量库v4、v5版本训练数据可以共用；结尾判断条件：===stop [无量义经.txt, 解深密经五卷.txt]")
async def openai_v5_training_stream_chat(
        search: V5ReqTrainingStreamChatSchema,
        tenant_id: Union[int, None] = Header(default=Required, convert_underscores=False,
                                             description="租户ID:为接入方的租户唯一ID"),
        classify_id: Union[int, None] = Header(default=Required, convert_underscores=False,
                                               description="分类ID:根据分类类型的主键ID，该ID为接入方的 自由对话、拟物、拟人的数据ID"),
        classify_type: Union[str, None] = Header(default=Required, convert_underscores=False,
                                                 description="分类类型:all=自由对话,prompt=创作对话,role=角色模拟"),
        platform_code: Union[str, None] = Header(default=Required, convert_underscores=False,
                                                 description="平台编码:pt=孵化平台,wt=群活平台,dt=数字人平台,业务站：zp=招聘,sm=算命")
):
    logger.info("[header]:{}".format({
        "tenant_id": tenant_id,
        "classify_type": classify_type,
        "classify_id": classify_id,
        "platform_code": platform_code
    }))
    logger.info("[body]:{}".format(search.model_dump_json()))
    if tenant_id is None or classify_type is None or classify_id is None or platform_code is None:
        raise GlobalBusinessException(500, "租户ID&分类ID&分类类型&平台编码必须")
    if search.context_history is not None and len(search.context_history) > OpenAiContents.CONTEXT_HISTORY_SIZE:
        raise GlobalBusinessException(500, "历史上下文超出预设阈值")
    vector_info = builder_vector_info(platform_code, tenant_id, classify_type, classify_id)
    context: AppContext = ctx.__getattr__("context")  # 全局变量
    llm_embedding = OpenAIEmbeddings(openai_api_key=random.choice(context.openai.chatgpt.api_keys),
                                     openai_api_base=random.choice(context.openai.chatgpt.base_url))
    docs = []  # 文档集合
    source = []  # 来源集合
    doc_contents = []  # 文档内容集合
    if search.is_search_embedding == 0:  # 是否需要搜索向量库
        try:
            vector_db = Milvus(collection_name=vector_info['collection_name'], embedding_function=llm_embedding,
                               connection_args={"user": vector_info['db_name'], "db_name": vector_info['db_name'],
                                                "host": context.openai.milvus.host, "port": context.openai.milvus.port})
            docs = vector_db.similarity_search_with_score(search.question, k=search.llm_config.n_results)
        except Exception as ex:
            logger.error(ex)
            logger.info(f"没有查询到相关向量数据")

        if docs is not None and len(docs) > 0:
            logger.info(f"[{vector_info['collection_name']}]匹配到[{len(docs)}]条分段数据")
            for doc in docs:
                source.append(doc[0].metadata['source'])
                doc_contents.append(doc[0].page_content)
                logger.info(
                    f"[{vector_info['collection_name']}]匹配到['source':{doc[0].metadata['source']}]源文件，评分[{doc[1]}]")
        else:
            logger.info(f"[向量库信息]:{vector_info}")
            logger.info(f"[{vector_info['collection_name']}]匹配到[{0}]条分段数据")
    prompt_system = (
        f"===上下文信息:【{search.prompt_template}】"
        f"===历史信息:【{search.context_history}】"
        f"===输入文档信息:【{', '.join(doc_contents)}】"
        f"===要求:请结合以上内容回答"
    )
    logger.info(f"[{vector_info['collection_name']}][prompt]:{prompt_system}")

    async def generate_text():
        client = OpenAI(
            api_key=random.choice(context.openai.chatgpt.api_keys),
            base_url=random.choice(context.openai.chatgpt.base_url)
        )
        if search.image_urls is not None and len(search.image_urls) > 0:
            # 加入图片识别多模态
            messages = [
                {"role": "system", "content": prompt_system},
                {"role": "user", "content": [
                    {"type": 'text', "text": search.question}
                ]}
            ]
            # 循环添加 image_url
            for image_url in search.image_urls:
                messages[1]["content"].append({
                    "type": "image_url",
                    "image_url": {
                        "url": image_url,
                    },
                })
        else:
            messages = [
                {"role": "system", "content": prompt_system},
                {"role": "user", "content": search.question}
            ]
        result_text = ""
        try:
            for chunk in client.chat.completions.create(
                    model=search.llm_config.llm_name,
                    messages=messages,
                    max_tokens=search.llm_config.max_tokens if search.llm_config.max_tokens != 0 else 15000,
                    temperature=search.llm_config.temperature,
                    stream=True
            ):
                generated_text = chunk.choices[0].delta.content
                if generated_text is not None and is_keywords(generated_text):
                    continue
                if generated_text:
                    result_text += generated_text
                    if search.steam_wrap is not None and search.steam_wrap != "" and re.search(r'\n', generated_text):
                        generated_text = generated_text.replace('\n', search.steam_wrap)
                    yield f"data: {generated_text}\n\n"
            # 加入关键字触发
            if search.keyword_reply is not None and len(search.keyword_reply) > 0:
                for kw in search.keyword_reply:
                    if re.search(kw, search.question) or re.search(kw, result_text):
                        yield f"data: {search.steam_wrap}{search.steam_wrap}{search.keyword_reply_content}\n\n"
                        break  # 如果找到一个匹配就结束循环
            logger.info(f"{result_text} data: ===stop [{', '.join(source)}]\n\n")
            yield f"data: ===stop [{', '.join(source)}]\n\n"
        except Exception as ex:
            logger.error(ex)
            WxRobotTool.SendGroupMsg(f"OpenAI Chat接口调用异常：{ex}")

    return R.Streaming(generate_text())
~~~



### MQ消息队列

整体框架中使用Redis队列作为MQ消息中间件，已封装了生产者、消费者、消息体、监听器；已支持队列异常做补偿流程；

#### 生产者

~~~python
import asyncio
import json
from typing import List

from loguru import logger

from extends.core.cache.RedisConnectionPool import RedisConnectionPool
from extends.core.exception.GlobalBusinessException import GlobalBusinessException
from extends.core.queue.message.RedisMessageModel import RedisMessageModel
from extends.utils.DateUtil import DateUtil
from extends.utils.IdUtil import IdUtil


class RedisProducerSupport:
    def __init__(self, redis_pool, queue_name='xinhou_queue'):
        self.redis = redis_pool
        self.queue_name = queue_name

    async def add_messages_to_queue(self, messages: List[RedisMessageModel]):
        try:
            serialized_messages = [message.model_dump_json() for message in messages]
            result = await self.redis.lpush(self.queue_name, *serialized_messages)
            logger.info(f'生产者已发送消息到队列[{self.queue_name}]，现有队列中消息数量为[{result}]条')
        except Exception as e:
            logger.error("生产者发送消息时发生异常")
            logger.error(e)
            raise GlobalBusinessException(500, "生产者发送消息时发生异常")

    async def get_all_messages_from_queue(self):
        try:
            all_messages = await self.redis.lrange(f"{self.queue_name}_fail", 0, -1)
            return all_messages
        except Exception as e:
            logger.error(f"生产者获取[{self.queue_name}_fail]失败消息时发生异常")
            logger.error(e)
            raise GlobalBusinessException(500, f"生产者获取[{self.queue_name}_fail]失败消息时发生异常")

    async def remove_message_by_key(self, key):
        try:
            all_messages = await self.redis.lrange(f"{self.queue_name}_fail", 0, -1)
            for message in all_messages:
                msg = json.loads(message)
                if msg.get('key') == key:
                    await self.redis.lrem(f"{self.queue_name}_fail", 0, message)
                    logger.info(f'生产者已删除在队列[{self.queue_name}_fail]中的[{key}]失败消息')
                    break
        except Exception as e:
            logger.error(f"生产者删除[{key}]失败消息时发生异常")
            logger.error(e)
            raise GlobalBusinessException(500, f"生产者删除[{key}]失败消息时发生异常")


class ProducerServiceSupport(RedisProducerSupport):

    @staticmethod
    async def process_push(queue_name, messages: List[RedisMessageModel]):
        redis_pool = await RedisConnectionPool().get_pool()
        redis_producer = ProducerServiceSupport(**{
            "redis_pool": redis_pool,
            "queue_name": queue_name
        })
        await redis_producer.add_messages_to_queue(messages)

    @staticmethod
    async def get_all_fail_messages(queue_name) -> List[RedisMessageModel]:
        redis_pool = await RedisConnectionPool().get_pool()
        all_fail_messages = await ProducerServiceSupport(**{
            "redis_pool": redis_pool,
            "queue_name": queue_name
        }).get_all_messages_from_queue()

        fail_messages = []
        for message in all_fail_messages:
            fail_messages.append(RedisMessageModel(**json.loads(message)))
        return fail_messages

    @staticmethod
    async def remove_by_key(queue_name, key):
        redis_pool = await RedisConnectionPool().get_pool()
        await ProducerServiceSupport(**{
            "redis_pool": redis_pool,
            "queue_name": queue_name
        }).remove_message_by_key(key)


async def producer_process_push():
    redis_pool = await RedisConnectionPool().get_pool()
    cfg = {
        "redis_pool": redis_pool,
        "queue_name": 'xinhou_queue'
    }
    redis_producer = RedisProducerSupport(**cfg)

    search = {
        "question": "请总结问答内容",
        "prompt_template": "请根据上下文内容总结相关数据",
        "history_file_oss_object_key": "/static/public/upload/1.txt",
        "max_length": 500,
        "steam_wrap": "<br/>"
    }

    await redis_producer.add_messages_to_queue([
        RedisMessageModel(key=IdUtil.uuid_32(), content=search, timestamp=DateUtil.get_current_timestamp())
    ])


if __name__ == '__main__':
    loop = asyncio.get_event_loop()
    redis_connection = RedisConnectionPool()
    loop.run_until_complete(redis_connection.initialize_pool('127.0.0.1', 36379))
    # 执行 producer_process_push() 函数 20 次
    for _ in range(20):
        loop.run_until_complete(producer_process_push())
    loop.close()

~~~

#### 消费者

~~~python
# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
功能描述
----------------------------------------------------
@Project :   marmot-framework
@File    :   RedisConsumer.py
@Contact :   <EMAIL>

@Modify Time      <AUTHOR>    @Desciption
------------      -------    --------    -----------
2023/2/23 18:49   shenpeng   1.0         None
"""

import asyncio
import json
from loguru import logger
from extends.core.cache.RedisConnectionPool import RedisConnectionPool
from extends.core.queue.message.RedisMessageModel import RedisMessageModel
from extends.utils.DateUtil import DateUtil


class RedisConsumerSupport:
    def __init__(self, redis_pool, queue_name='default_queue', lock_name='default_queue_lock', retry_interval=5,
                 max_retries=3):
        self.redis = redis_pool
        self.queue_name = queue_name
        self.max_retries = max_retries
        self.retry_interval = retry_interval
        if lock_name is None:
            self.lock_name = f"{queue_name}_lock"
        else:
            self.lock_name = lock_name

    async def acquire_lock(self):
        # 使用带有超时的Redis锁
        acquired = await self.redis.execute('SET', self.lock_name, 'locked', 'NX', 'EX', 30)
        return acquired == 'OK'

    async def release_lock(self):
        await self.redis.delete(self.lock_name)

    async def process_message(self, message):
        # 处理消息的逻辑
        logger.info(f"处理消息：{message}")
        await asyncio.sleep(2)  # 模拟处理消息所需的时间

    async def handler(self):
        try:
            while True:
                lock_acquired = await self.acquire_lock()
                if lock_acquired:
                    message = await self.redis.lpop(self.queue_name)
                    if message:
                        retry_count = 0
                        success = False
                        msg = RedisMessageModel(**json.loads(message))
                        while not success and retry_count < self.max_retries:
                            try:
                                await asyncio.gather(
                                    self.process_message(msg),
                                    self.redis.lrem(self.queue_name, 0, message)  # 确认消息已被消费，从队列中删除已处理的消息
                                )
                                success = True
                            except Exception as e:
                                retry_count += 1
                                logger.error(
                                    f"消费者处理[{msg.key}]消息时发生异常，重试次数{retry_count} of {self.max_retries}")
                                logger.error(e)
                                await asyncio.sleep(self.retry_interval)
                            if not success:
                                # 超过重试次数，可以选择记录日志，发送警告，或者推送到一个“死信”队列等
                                error_msg = f"消费者处理发生异常,已超过最大重试次数: {self.max_retries} 次."
                                logger.error(error_msg)
                                msg.err_cause = error_msg
                                await self.redis.lpush(f"{self.queue_name}_fail", msg.model_dump_json())
                                logger.info(f'消费者已将消息放入[{self.queue_name}_fail]失败队列中！')

                await self.release_lock()
                await asyncio.sleep(5)

                # all_tasks = asyncio.all_tasks()
                # for task in all_tasks:
                #     logger.info(task)

        except asyncio.CancelledError:
            await self.release_lock()
        except Exception as e:
            logger.error("消费者接收消息时发生异常")
            logger.error(e)
        finally:
            await self.release_lock()


class ConsumerServiceSupport(RedisConsumerSupport):
    async def process_message(self, message: RedisMessageModel):
        """
        业务逻辑可以通过继承ConsumerServiceSupport重写process_message用于接收消息及执行业务逻辑
        """
        # 这里实现具体的消息处理逻辑
        logger.info(f"消费者执行业务逻辑: {message.content} : {message.timestamp} ")
        # 可以在这里添加自定义的消息处理逻辑

    @staticmethod
    async def process_listener():
        """
        [启动消息监听器 方法一]业务逻辑可以通过继承ConsumerServiceSupport重写process_listener用于启动消费者监听器
        """
        logger.info("启动消费者[ConsumerServiceSupport]监听")
        redis_pool = await RedisConnectionPool().get_pool()
        await ConsumerServiceSupport(**{
            "redis_pool": redis_pool,
            "queue_name": 'default_queue',
            "lock_name": 'default_queue_lock',
            "retry_interval": 5,
            "max_retries": 3
        }).handler()


async def consumer_process_listener():
    """
    [启动消息监听器 方法二]业务逻辑可以通过consumer_process_listener用于启动消费者监听器
    """
    logger.info("启动消费者[ConsumerServiceSupport]监听")
    redis_pool = await RedisConnectionPool().get_pool()
    await ConsumerServiceSupport(**{
        "redis_pool": redis_pool,
        "queue_name": 'default_queue',
        "lock_name": 'default_queue_lock',
        "retry_interval": 5,
        "max_retries": 3
    }).handler()


if __name__ == '__main__':
    loop = asyncio.get_event_loop()
    redis_connection = RedisConnectionPool()
    loop.run_until_complete(redis_connection.initialize_pool('127.0.0.1', 36379))
    loop.run_until_complete(consumer_process_listener())
    loop.close()

~~~

#### 消息体

~~~python
# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
功能描述
----------------------------------------------------
@Project :   marmot-framework
@File    :   RedisMessager.py
@Contact :   <EMAIL>

@Modify Time      <AUTHOR>    @Desciption
------------      -------    --------    -----------
2023/2/23 18:49   shenpeng   1.0         None
"""
from datetime import datetime
from typing import Optional

from pydantic import BaseModel, Field


class RedisMessageModel(BaseModel):
    key: str = Field(title="消息KEY")
    content: object = Field(title="消息内容")
    timestamp: datetime = Field(title="消息时间戳")
    err_cause: Optional[str] = Field(default=None, title="消息处理错误原因")

~~~

#### 队列监听器

~~~python
# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
全局应用初始化代理类
----------------------------------------------------
@Project :   marmot-framework
@File    :   InitializeHandler.py
@Contact :   <EMAIL>

@Modify Time      <AUTHOR>    @Desciption
------------      -------    --------    -----------
2023/12/28 13:43   shenpeng   1.0         None
"""
import asyncio

from loguru import logger

from extends.core.context.model.AppContext import AppContext


async def start_queue_listener(listener_name):
    # 获取全局符号表中对应的方法
    listener_func = globals().get(listener_name)

    # 检查方法是否存在并且可调用
    if listener_func and callable(listener_func):
        # 如果存在且可调用，则创建一个异步任务来执行该方法
        asyncio.create_task(listener_func())
    else:
        logger.info(f"queue listener function [{listener_name}] not found or not callable.")


class QueueListenerHandler:
    """
    应用初始化处理类
    """

    @staticmethod
    def init_listeners(app, context: AppContext):
        @app.on_event("startup")
        async def startup_queue_listeners():
            # 遍历字符串列表，依次调用方法【通过函数方式启动监听器】
            # TODO 需要通过配置读取监听器
            listeners = ['openai_summary_consumer_listener']
            for listener in listeners:
                await start_queue_listener(listener)

~~~



### Nacos集成

集成Nacos有注册中心&配置中心，支持多环境；配置中心启动需要在application.yml中加入nacos配置，并在项目中加入nacos-sdk-python-customer==0.1.14 包；同时pip更新xinhou-openai-framework框架版本；

#### 配置文件

- application.yml

~~~yml
application:
  name: xinhou-openai-framework
  version: v1.0.0
  author: shenpeng
  email: <EMAIL>
framework:
  profiles:
    active: dev
~~~

- application-dev.yml

~~~yml
framework:
  cloud:
    nacos:
      discovery:
        # 服务注册地址
        server_addr: nacos.xinhouai.com:80
        enabled: true
        username: nacos
        password: nacos
        group: XINHOU_OPENAI_GROUP
        namespace_id: '89d52cdc-adac-49e8-8c3e-7e132f430e7e'
      config:
        # 配置中心地址
        server_addr: nacos.xinhouai.com:80
        enabled: true
        username: nacos
        password: nacos
        group: XINHOU_OPENAI_GROUP
        namespace_id: '89d52cdc-adac-49e8-8c3e-7e132f430e7e'
        # 共享配置
        shared_configs:
          - application-dev.yml
~~~







### Utils工具包

> 涵盖日常使用的工具类，并尽可能将调用与其他语言保持一致；

- AsyncReqUtil
- StrUtil
- DateUtil
- ExcelUtil
- WordUtil
- FileUtil
- JsonUtil
- Md5Util 
- NetUtil
- NumberUtil
- RedisUtil
- ReflectUtil
- ObjUtil
- ObjDictUtil
- QueryUtil
- PathUtil
- ReflectUtil
- ReqUtil
- RuntimeUtil
- ZipUtil
- SessionUtil
- ValidateUtil



## 其他

### 健康检查

GET /health HTTP/1.1
Host: **************:8000

### 代码生成

GET /common/code/generater HTTP/1.1
Host: **************:8000

### 文件上传下载&存储

### Excel处理

### Word处理

### Docker & K8S 监控操作





