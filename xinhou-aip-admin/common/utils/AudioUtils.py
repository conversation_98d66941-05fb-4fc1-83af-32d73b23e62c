import io
import os
import tempfile
from urllib.parse import urlparse

import requests
from mutagen import File
from pydub import AudioSegment


def get_audio_duration(url) -> float:
    try:
        # 下载音频文件
        response = requests.get(url)
        response.raise_for_status()

        # 获取文件扩展名
        file_extension = os.path.splitext(urlparse(url).path)[1].lower()

        if file_extension == '.wav':
            # 使用 pydub 处理 WAV 文件
            audio_file = io.BytesIO(response.content)
            audio = AudioSegment.from_wav(audio_file)
            duration = len(audio) / 1000.0
        else:
            # 使用 mutagen 处理其他格式
            with tempfile.NamedTemporaryFile(delete=False, suffix=file_extension) as temp_file:
                temp_file.write(response.content)
                temp_file_path = temp_file.name

            try:
                audio = File(temp_file_path)
                if audio is None:
                    return f"无法识别音频格式: {url}"
                duration = audio.info.length
            finally:
                # 删除临时文件
                os.unlink(temp_file_path)

        return duration

    except Exception as e:
        return f"处理文件时出错: {url}, 错误: {str(e)}"