# !/usr/bin/python3
# -*- coding: utf-8 -*-
import os
from urllib.parse import urlparse, unquote

import aiofiles
import cv2
import requests
from fastapi import HTTPException
from loguru import logger
from xinhou_openai_framework.core.context.model.AppContext import AppContext
from xinhou_openai_framework.utils.IdUtil import IdUtil
from xinhou_openai_framework.utils.OssUtil import OssUtil
from xinhou_openai_framework.utils.PathUtil import PathUtil
from xinhou_openai_framework.utils.StrUtil import StrUtil


def file_download(context, downloaded_files, file_urls):
    for url in file_urls:
        try:
            # 发起 HTTP GET 请求以下载文件
            response = requests.get(url, stream=True)
            # 确保请求成功
            if response.status_code == 200:
                # 设置文件保存路径
                file_path = f"{context.file.bge.path}/{url.split('/')[-1]}"
                # 以二进制写入模式打开文件
                with open(file_path, 'wb') as f:
                    # 写入文件内容
                    for chunk in response.iter_content(chunk_size=8192):
                        f.write(chunk)
                # 记录保存的文件路径
                downloaded_files.append(file_path)
            else:
                raise HTTPException(status_code=400, detail=f"Failed to download file from {url}")
        except Exception as e:
            # 记录异常，可以选择抛出或处理异常
            raise HTTPException(status_code=500, detail=f"Error downloading file from {url}: {str(e)}")


def clean_url(url: str) -> str:
    # 解析 URL
    parsed_url = urlparse(url)

    # 重建基本 URL，不包括查询参数
    clean_url = f"{parsed_url.scheme}://{parsed_url.netloc}{parsed_url.path}"

    # URL 解码
    decoded_url = unquote(clean_url)

    return decoded_url


async def save_files_v1_oss(files):
    """
    将文件保存到阿里云 OSS 并返回文件信息列表。
    """
    file_infos = []
    for file in files:
        try:
            # 生成文件名
            file_extension = StrUtil.sub_str_after(file.filename, ".", True)
            file_name_uuid = f"{IdUtil.uuid_32()}.{file_extension}"
            local_file_path = f"{PathUtil.get_root_path()}/static/uploads/{file_name_uuid}"
            
            # 确保目录存在
            upload_dir = os.path.dirname(local_file_path)
            PathUtil.not_exists_mkdir(upload_dir)
            
            # 保存文件到本地
            content = await file.read()
            async with aiofiles.open(local_file_path, 'wb') as f:
                await f.write(content)
            
            try:
                # 上传文件到 OSS
                res = await upload_to_oss(local_file_path, f"static/uploads/{file_name_uuid}")
                if not res:
                    raise HTTPException(status_code=500, detail="Failed to upload file to OSS")
                
                file_review_pic = None
                # 处理视频文件
                if file_extension.lower() in ['mp4', 'avi', 'mov', 'wmv']:
                    frame_path = f"{local_file_path}.jpg"
                    if await generate_video_preview(local_file_path, frame_path):
                        frame_key = f"static/uploads/preview_{file_name_uuid}.jpg"
                        jpg_res = await upload_to_oss(frame_path, frame_key)
                        if jpg_res and jpg_res.success:
                            file_review_pic = clean_url(jpg_res.data.signed_url)
                
                file_infos.append({
                    'file_name': file.filename,
                    'file_name_uuid': file_name_uuid,
                    'file_type': file_extension,
                    'file_size': file.size,
                    'file_path': f'static/uploads/{file_name_uuid}',
                    'file_url': clean_url(res.data.signed_url),
                    'file_review_pic': file_review_pic
                })
            finally:
                # 清理本地临时文件
                if os.path.exists(local_file_path):
                    os.remove(local_file_path)
                if os.path.exists(f"{local_file_path}.jpg"):
                    os.remove(f"{local_file_path}.jpg")
                    
        except Exception as e:
            logger.error(f"Error processing file {file.filename}: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Error processing file: {str(e)}")
            
    return file_infos


# 辅助函数
async def generate_video_preview(video_path: str, frame_path: str) -> bool:
    """生成视频预览图"""
    try:
        video = cv2.VideoCapture(video_path)
        if video.isOpened():
            success, frame = video.read()
            if success:
                cv2.imwrite(frame_path, frame)
                video.release()
                return True
        video.release()
        return False
    except Exception as e:
        logger.error(f"Error generating video preview: {str(e)}")
        return False


async def upload_to_oss(file_path: str, oss_key: str):
    """
    上传文件到 OSS
    
    Args:
        file_path: 本地文件路径
        oss_key: OSS存储的键值
        
    Returns:
        OssResult 对象或 None
    """
    try:
        import random
        import time
        
        oss_util = OssUtil()
        # 修改：去掉本地路径前缀，只使用文件名作为 OSS 路径
        clean_oss_key = oss_key.split('static/uploads/')[-1]
        oss_path = f"static/uploads/{clean_oss_key}"
        
        # 添加重试逻辑
        max_retries = 3
        retry_count = 0
        upload_success = False
        
        while retry_count < max_retries and not upload_success:
            try:
                logger.info(f"尝试上传文件到OSS (尝试 {retry_count + 1}/{max_retries}): {oss_path}")
                # 上传文件
                upload_result = oss_util.upload_file(file_path, oss_path)
                if not upload_result:
                    raise Exception("上传文件到OSS返回失败结果")
                
                # 获取文件访问链接
                result = oss_util.get_object(oss_path)
                if not result or not result.success:
                    raise Exception(f"获取OSS对象失败: {result.message if result else '未知错误'}")
                
                upload_success = True
                logger.info(f"文件上传OSS成功: {oss_path}")
                return result
                
            except Exception as retry_error:
                retry_count += 1
                error_msg = f"上传文件到OSS失败 (尝试 {retry_count}/{max_retries})"
                logger.error(error_msg)
                
                if retry_count < max_retries:
                    retry_interval = 2 + (random.random() * 3)  # 2-5秒间随机等待时间
                    logger.info(f"等待 {retry_interval:.2f} 秒后重试上传")
                    time.sleep(retry_interval)
                else:
                    logger.error(f"上传文件到OSS失败，已达最大重试次数: {oss_path}")
                    return None
                    
        return None
    except Exception as e:
        logger.error(f"Error uploading to OSS: {str(e)}, file_path: {file_path}, oss_key: {oss_key}")
        return None


def load_oss_docs_v5(oss_object_key: str, context: AppContext) -> str:
    """
    从阿里云 OSS 加载文档。

    Parameters:
    - oss_object_key: OSS对象的键
    - context: 应用程序上下文

    Returns:
    如果成功，返回文件内容，否则返回None。
    """
    oss_util = OssUtil(
        endpoint=context.aliyun_oss.endpoint,
        access_key_id=context.aliyun_oss.access_key_id,
        access_key_secret=context.aliyun_oss.access_key_secret,
        bucket_name=context.aliyun_oss.bucket_name)
    get_object_result = oss_util.get_object(oss_object_key)
    if get_object_result.success:
        obj = get_object_result.data
        logger.info(f"OSS Object Key: {obj.oss_key}")
        logger.info(f"Signed URL: {obj.signed_url}")
        file_content = read_file(obj.signed_url)
        if file_content:
            return file_content
    else:
        logger.info(get_object_result.message)
    return None


def read_file(url):
    """
    从URL读取文件内容。

    Parameters:
    - url: 文件的URL

    Returns:
    如果成功，返回文件内容，否则返回None。
    """
    try:
        response = requests.Session().get(url)
        response.raise_for_status()  # 检查响应状态码是否为200
        return response.content.decode('utf-8')
    except requests.exceptions.RequestException as e:
        logger.error(f"Error: {e}")
        return None
        
        
async def download_files_to_zip(urls: list[str], file_names: list[str], zip_filename: str) -> str:
    """
    下载多个文件并打包成zip文件
    
    Args:
        urls: 文件URL列表
        file_names: 文件名列表，与URLs一一对应
        zip_filename: 生成的zip文件名
    
    Returns:
        生成的zip文件的本地路径
    """
    import zipfile
    import io
    import time
    
    # 确保目录存在
    temp_dir = f"{PathUtil.get_root_path()}/static/temp"
    PathUtil.not_exists_mkdir(temp_dir)
    
    # 生成唯一的zip文件名
    timestamp = int(time.time())
    zip_path = f"{temp_dir}/{zip_filename}_{timestamp}.zip"
    
    try:
        # 创建一个新的zip文件
        with zipfile.ZipFile(zip_path, 'w') as zip_file:
            for i, url in enumerate(urls):
                if not url:
                    continue
                
                try:
                    # 下载文件
                    response = requests.get(url, stream=True)
                    response.raise_for_status()
                    
                    # 获取文件名(使用提供的文件名或从URL中提取)
                    filename = file_names[i] if i < len(file_names) else url.split('/')[-1]
                    
                    # 将文件内容添加到zip文件中
                    zip_file.writestr(filename, response.content)
                    
                except Exception as e:
                    logger.error(f"下载文件时出错: {url}, 错误: {str(e)}")
                    # 继续处理其他文件
                    
        return zip_path
    
    except Exception as e:
        logger.error(f"创建zip文件时出错: {str(e)}")
        return None
