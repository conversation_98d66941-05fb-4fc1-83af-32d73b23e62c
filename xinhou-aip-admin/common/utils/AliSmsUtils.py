# 创建新文件 SmsUtil.py

import json
import random

from aliyunsdkcore.client import AcsClient  # noqa
from aliyunsdkcore.request import CommonRequest  # noqa
from xinhou_openai_framework.core.context.model.SystemContext import ctx


class AliSmsUtils:
    # 延迟初始化
    _access_key_id = None
    _access_key_secret = None
    _sign_name = None
    _template_code = None
    _client = None

    @classmethod
    def _init_if_needed(cls):
        if cls._client is None:
            context = ctx.__getattr__("context")
            cls._access_key_id = context.aliyun_sms.access_key_id
            cls._access_key_secret = context.aliyun_sms.access_key_secret
            cls._sign_name = context.aliyun_sms.sign_name
            cls._template_code = context.aliyun_sms.template_code
            cls._client = AcsClient(cls._access_key_id, cls._access_key_secret, 'cn-hangzhou')

    @staticmethod
    def generate_code():
        return str(random.randint(1000, 9999))

    @classmethod
    def send_sms(cls, code, phone_number):
        cls._init_if_needed()

        request = CommonRequest()
        request.set_accept_format('json')
        request.set_domain('dysmsapi.aliyuncs.com')
        request.set_method('POST')
        request.set_protocol_type('https')
        request.set_version('2017-05-25')
        request.set_action_name('SendSms')

        request.add_query_param('RegionId', "cn-hangzhou")
        request.add_query_param('PhoneNumbers', phone_number)
        request.add_query_param('SignName', cls._sign_name)
        request.add_query_param('TemplateCode', cls._template_code)
        request.add_query_param('TemplateParam', json.dumps({"code": code}))
        response = cls._client.do_action_with_exception(request)
        return json.loads(response)
