#!/usr/bin/python3
# -*- coding: utf-8 -*-

"""
Redis历史记录同步工具类
----------------------------------------------------
@Project :   xinhou-openai-admin
@File    :   RedisHistoryUtil.py
@Contact :   <EMAIL>

@Modify Time      <AUTHOR>    @Desciption
------------      -------    --------    -----------
2025/02/08 10:00   logic      1.0         None
"""

import asyncio
import json
from typing import List, Dict, Any

from aioredis import Redis
from loguru import logger
from sqlalchemy.orm import Session

from common.entity.AgentsHistory import AgentsHistory


class RedisHistoryUtil:
    @staticmethod
    async def sync_history_to_redis(redis_client: Redis, task_id: int, history_list: List[Dict[str, Any]]) -> None:
        """
        将历史记录同步到Redis
        
        Args:
            redis_client: Redis客户端
            task_id: 任务ID
            history_list: 历史记录列表
        """
        try:
            logger.info(f"开始同步历史记录到Redis，task_id: {task_id}")

            # 使用 SCAN 命令删除旧数据，但保留TITLE_CHOOSE
            pattern = f"aip_task:{task_id}_agent_*"
            cursor = 0
            while True:
                cursor, keys = await redis_client.scan(cursor=cursor, match=pattern, count=100)
                if keys:
                    # 过滤出需要删除的keys
                    keys_to_delete = []
                    for key in keys:
                        try:
                            # 跳过content key
                            if key.endswith(b'_content'):
                                continue
                            
                            # 获取agent_style
                            agent_style = await redis_client.hget(key, 'agent_style')
                            if agent_style:
                                agent_style = agent_style.decode('utf-8')
                                if agent_style != "TITLE_CHOOSE":
                                    keys_to_delete.append(key)
                                    # 同时删除相关的content key
                                    content_key = f"{key.decode('utf-8')}_content"
                                    keys_to_delete.append(content_key)
                        except Exception as e:
                            logger.error(f"检查key {key} 的agent_style失败: {str(e)}")
                            continue

                    if keys_to_delete:
                        try:
                            await redis_client.delete(*keys_to_delete)
                            logger.info(f"已删除非TITLE_CHOOSE的数据，keys: {keys_to_delete}")
                        except Exception as e:
                            logger.error(f"删除keys失败: {str(e)}")
                if cursor == 0:
                    break

            # 存储完整的agents列表
            agents_key = f"aip_task:{task_id}_agents"
            agents_data = []

            # 处理每个agent的数据
            for index, agent_data in enumerate(history_list):
                agent_id = agent_data.get('agent_id')
                title_choose = agent_data.get('agent_style')
                if not agent_id:
                    continue

                base_key = f"aip_task:{task_id}_agent_{index}_{agent_id}"
                logger.info(f"处理agent数据，base_key: {base_key}")
                # 添加到agents列表
                agents_data.append(f"{index}_{agent_id}")

                # 构建agent基础信息
                agent_info = {
                    'task_id': str(agent_data.get('task_id', '')),
                    'pid': str(agent_data.get('pid', '')),
                    'agent_id': str(agent_id),
                    'agent_style': str(agent_data.get('agent_style', '')),
                    'agent_name_cn': str(agent_data.get('agent_name_cn', '')),
                    'agent_name_en': str(agent_data.get('agent_name_en', '')),
                    'model_name': str(agent_data.get('model_name', '')),
                    'created_at': str(agent_data.get('created_at', '')),
                    'uuid': str(agent_data.get('uuid', '')),
                    'agent_uuid': str(agent_data.get('agent_uuid', '')),
                    'agent_type': str(agent_data.get('agent_type', '')),
                    'agent_role': str(agent_data.get('agent_role', '')),
                    'work_id': str(agent_data.get('work_id', '')),
                    'user_stop': str(agent_data.get('user_stop', '')),
                    'agent_action': str(agent_data.get('agent_action', ''))
                }

                try:
                    # 存储agent基础信息
                    await redis_client.hmset_dict(base_key, agent_info)
                    logger.info(f"已存储agent基础信息，key: {base_key}")

                    # 存储content
                    content = agent_data.get('content')
                    if content:
                        content_key = f"{base_key}_content"
                        if isinstance(content, (dict, list)):
                            content = json.dumps(content, ensure_ascii=False)
                        await redis_client.set(content_key, str(content))
                        logger.info(f"已存储content，key: {content_key}")
                except Exception as e:
                    logger.error(f"存储agent数据失败: {str(e)}")
                    continue

            # 存储完整的agents列表
            if agents_data:
                try:
                    # 获取现有的agents列表
                    existing_agents = []
                    try:
                        existing_agents = await redis_client.lrange(agents_key, 0, -1)
                        existing_agents = [agent.decode('utf-8') for agent in existing_agents]
                    except Exception as e:
                        logger.error(f"获取现有agents列表失败: {str(e)}")

                    # 合并现有的和新的agents列表，保持顺序
                    merged_agents = []
                    seen = set()

                    # 先添加现有的agents
                    for agent in existing_agents:
                        if agent not in seen:
                            merged_agents.append(agent)
                            seen.add(agent)

                    # 再添加新的agents
                    for agent in agents_data:
                        if agent not in seen:
                            merged_agents.append(agent)
                            seen.add(agent)

                    # 更新agents列表
                    if merged_agents:
                        await redis_client.delete(agents_key)
                        await redis_client.rpush(agents_key, *merged_agents)
                        logger.info(f"已更新agents列表，key: {agents_key}")
                except Exception as e:
                    logger.error(f"存储agents列表失败: {str(e)}")

            logger.info(f"同步历史记录到Redis完成，task_id: {task_id}")
        except Exception as e:
            logger.error(f"同步历史记录到Redis失败: {str(e)}")
            raise e

    @staticmethod
    async def sync_all_to_redis(redis_client: Redis, db_session: Session, batch_size: int = 100) -> Dict[str, Any]:
        """
        将所有历史记录同步到Redis
        
        Args:
            redis_client: Redis客户端
            db_session: 数据库会话
            batch_size: 每批处理的数量
            
        Returns:
            同步结果统计
        """
        success_count = 0
        failed_tasks = []
        total_count = 0

        try:
            # 分批查询所有历史记录
            offset = 0
            while True:
                # 查询一批数据
                results = db_session.query(AgentsHistory).order_by(
                    AgentsHistory.id
                ).offset(offset).limit(batch_size).all()

                if not results:
                    break

                total_count += len(results)

                # 处理每条记录
                for result in results:
                    try:
                        if not result.history:
                            continue

                        # 解析history字段
                        history_str = result.history.decode('utf-8')
                        if history_str.startswith('"') and history_str.endswith('"'):
                            history_str = history_str[1:-1].encode().decode('unicode_escape')

                        history_list = json.loads(history_str)

                        # 同步到Redis
                        await RedisHistoryUtil.sync_history_to_redis(redis_client, result.task_id, history_list)
                        success_count += 1

                    except Exception as e:
                        logger.error(f"同步任务 {result.task_id} 的历史记录失败: {str(e)}")
                        failed_tasks.append({
                            "task_id": result.task_id,
                            "error": str(e)
                        })

                offset += batch_size
                logger.info(f"已处理 {offset} 条记录")

            return {
                "total": total_count,
                "success": success_count,
                "failed": len(failed_tasks),
                "failed_tasks": failed_tasks
            }

        except Exception as e:
            logger.error(f"全量同步历史记录到Redis失败: {str(e)}")
            raise

    @staticmethod
    async def sync_all_to_redis_concurrent(
            redis_client: Redis,
            db_session: Session,
            batch_size: int = 100,
            max_concurrency: int = 5
    ) -> Dict[str, Any]:
        """
        并发将所有历史记录同步到Redis
        
        Args:
            redis_client: Redis客户端
            db_session: 数据库会话
            batch_size: 每批查询的数量
            max_concurrency: 最大并发数
            
        Returns:
            同步结果统计
        """
        success_count = 0
        failed_tasks = []
        total_count = 0

        try:
            # 获取总记录数
            total_count = db_session.query(AgentsHistory).count()
            logger.info(f"总记录数: {total_count}")

            # 创建信号量控制并发数
            semaphore = asyncio.Semaphore(max_concurrency)

            async def process_batch(batch_records):
                """处理一批记录"""
                async with semaphore:
                    tasks = []
                    for record in batch_records:
                        if not record.history:
                            continue

                        try:
                            # 解析history字段
                            history_str = record.history.decode('utf-8')
                            if history_str.startswith('"') and history_str.endswith('"'):
                                history_str = history_str[1:-1].encode().decode('unicode_escape')

                            history_list = json.loads(history_str)

                            # 创建同步任务，使用V2版本的同步方法
                            task = RedisHistoryUtil.sync_history_to_redis_v2(
                                redis_client,
                                record.task_id,
                                history_list
                            )
                            tasks.append(task)

                        except Exception as e:
                            logger.error(f"处理任务 {record.task_id} 失败: {str(e)}")
                            failed_tasks.append({
                                "task_id": record.task_id,
                                "error": str(e)
                            })

                    if tasks:
                        # 并发执行同步任务
                        results = await asyncio.gather(*tasks, return_exceptions=True)

                        # 统计结果
                        for i, result in enumerate(results):
                            if isinstance(result, Exception):
                                logger.error(f"同步失败: {str(result)}")
                                failed_tasks.append({
                                    "task_id": batch_records[i].task_id,
                                    "error": str(result)
                                })
                            else:
                                nonlocal success_count
                                success_count += 1

            # 分批处理数据
            batch_tasks = []
            offset = 0

            while True:
                # 查询一批数据
                batch_records = db_session.query(AgentsHistory).order_by(
                    AgentsHistory.id
                ).offset(offset).limit(batch_size).all()

                if not batch_records:
                    break

                # 创建批处理任务
                batch_task = process_batch(batch_records)
                batch_tasks.append(batch_task)

                offset += batch_size
                logger.info(f"已创建批处理任务，当前进度: {offset}/{total_count}")

                # 当累积的批处理任务达到最大并发数时，等待它们完成
                if len(batch_tasks) >= max_concurrency:
                    await asyncio.gather(*batch_tasks)
                    batch_tasks = []

            # 等待剩余的批处理任务完成
            if batch_tasks:
                await asyncio.gather(*batch_tasks)

            return {
                "total": total_count,
                "success": success_count,
                "failed": len(failed_tasks),
                "failed_tasks": failed_tasks
            }

        except Exception as e:
            logger.error(f"并发同步历史记录到Redis失败: {str(e)}")
            raise

    @staticmethod
    async def sync_history_to_redis_v2(redis_client: Redis, task_id: int, history_list: List[Dict[str, Any]]) -> None:
        """
        将历史记录同步到Redis（新版本，保留所有agent数据）
        
        Args:
            redis_client: Redis客户端
            task_id: 任务ID
            history_list: 历史记录列表
        """
        try:
            logger.info(f"开始同步历史记录到Redis（V2），task_id: {task_id}")

            # 删除旧数据
            pattern = f"aip_task:{task_id}_agent_*"
            cursor = 0
            while True:
                cursor, keys = await redis_client.scan(cursor=cursor, match=pattern, count=100)
                if keys:
                    try:
                        await redis_client.delete(*keys)
                        logger.info(f"已删除旧数据，keys: {keys}")
                    except Exception as e:
                        logger.error(f"删除keys失败: {str(e)}")
                if cursor == 0:
                    break

            # 删除agents列表
            agents_key = f"aip_task:{task_id}_agents"
            await redis_client.delete(agents_key)

            # 处理每个agent的数据
            agents_data = []
            for index, agent_data in enumerate(history_list):
                # 检查数据格式，判断是否为选题格式
                is_title_format = 'order_cn' in agent_data and 'title' in agent_data and 'because' in agent_data

                if is_title_format:
                    # 处理选题格式数据
                    agent_id = index + 1  # 使用索引作为agent_id
                    base_key = f"aip_task:{task_id}_agent_{index}_{agent_id}"
                    logger.info(f"处理选题数据，base_key: {base_key}")

                    # 构建选题信息
                    agent_info = {
                        'task_id': str(task_id),
                        'agent_id': str(agent_id),
                        'agent_style': 'TITLE_CHOOSE',
                        'order_cn': str(agent_data.get('order_cn', '')),
                        'title': str(agent_data.get('title', '')),
                        'because': str(agent_data.get('because', ''))
                    }
                else:
                    # 处理普通agent数据
                    agent_id = agent_data.get('agent_id')
                    if not agent_id:
                        continue

                    base_key = f"aip_task:{task_id}_agent_{index}_{agent_id}"
                    logger.info(f"处理agent数据，base_key: {base_key}")

                    # 构建agent基础信息
                    agent_info = {
                        'task_id': str(agent_data.get('task_id', '')),
                        'pid': str(agent_data.get('pid', '')),
                        'agent_id': str(agent_id),
                        'agent_style': str(agent_data.get('agent_style', '')),
                        'agent_name_cn': str(agent_data.get('agent_name_cn', '')),
                        'agent_name_en': str(agent_data.get('agent_name_en', '')),
                        'model_name': str(agent_data.get('model_name', '')),
                        'created_at': str(agent_data.get('created_at', '')),
                        'uuid': str(agent_data.get('uuid', '')),
                        'agent_uuid': str(agent_data.get('agent_uuid', '')),
                        'agent_type': str(agent_data.get('agent_type', '')),
                        'agent_role': str(agent_data.get('agent_role', '')),
                        'work_id': str(agent_data.get('work_id', '')),
                        'user_stop': str(agent_data.get('user_stop', '')),
                        'agent_action': str(agent_data.get('agent_action', ''))
                    }

                try:
                    # 存储agent基础信息
                    await redis_client.hmset_dict(base_key, agent_info)
                    logger.info(f"已存储agent基础信息，key: {base_key}")

                    # 存储content
                    content = agent_data.get('content') if not is_title_format else agent_data
                    if content:
                        content_key = f"{base_key}_content"
                        if isinstance(content, (dict, list)):
                            content = json.dumps(content, ensure_ascii=False)
                        await redis_client.set(content_key, str(content))
                        logger.info(f"已存储content，key: {content_key}")

                    # 添加到agents列表
                    agents_data.append(f"{index}_{agent_id}")
                except Exception as e:
                    logger.error(f"存储agent数据失败: {str(e)}")
                    continue

            # 存储agents列表和999标记
            if agents_data:
                try:
                    # 添加999标记相关信息
                    end_index = len(agents_data)  # 使用当前最后一个索引的下一个
                    end_agent_id = "999"
                    end_key = f"aip_task:{task_id}_agent_{end_index}_{end_agent_id}"
                    
                    # 构建999的agent信息
                    import datetime
                    current_time = datetime.datetime.now().strftime("%Y-%m-%dT%H:%M:%S.%f")[:-3]
                    end_agent_info = {
                        'last_updated': current_time,
                        'status': 'completed',
                        'agent_id': '999'
                    }
                    
                    # 存储999的agent信息
                    await redis_client.hmset_dict(end_key, end_agent_info)
                    
                    # 存储999的content
                    end_content_key = f"{end_key}_content"
                    await redis_client.set(end_content_key, "生成完毕")
                    
                    # 将所有agent ID添加到列表中，包括999的两种格式
                    agents_data.append(f"{end_index}_{end_agent_id}")  # 添加索引_999格式
                    await redis_client.rpush(agents_key, *agents_data)
                    logger.info(f"已存储agents列表和999标记，key: {agents_key}")
                except Exception as e:
                    logger.error(f"存储agents列表和999标记失败: {str(e)}")

            logger.info(f"同步历史记录到Redis完成（V2），task_id: {task_id}")
        except Exception as e:
            logger.error(f"同步历史记录到Redis失败（V2）: {str(e)}")
            raise e

    @staticmethod
    async def sync_db_to_redis_v2(redis_client: Redis, db_session: Session, task_id: int):
        """
        将数据库中的历史记录同步到Redis（新版本）
        
        Args:
            redis_client: Redis客户端
            db_session: 数据库会话
            task_id: 任务ID
        """
        try:
            # 从数据库查询历史记录
            result = db_session.query(AgentsHistory).filter(
                AgentsHistory.task_id == task_id
            ).first()

            if not result or not result.history:
                return

            # 解析history字段
            history_str = result.history.decode('utf-8')
            # 处理可能的转义字符
            if history_str.startswith('"') and history_str.endswith('"'):
                history_str = history_str[1:-1].encode().decode('unicode_escape')

            history_list = json.loads(history_str)

            # 同步到Redis
            await RedisHistoryUtil.sync_history_to_redis_v2(redis_client, task_id, history_list)

        except Exception as e:
            logger.error(f"同步历史记录到Redis失败（V2）: {str(e)}")
            raise
