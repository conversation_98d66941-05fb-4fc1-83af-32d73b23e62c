import json
import logging
import random
import time

from volcengine.sms.SmsService import SmsService
from xinhou_openai_framework.core.context.model.SystemContext import ctx


class HuoshanSmsUtils:
    """
    火山引擎短信服务工具类
    
    提供短信验证码发送和验证功能，使用火山引擎SMS服务
    支持自动重试、超时控制和详细日志
    
    主要功能:
    1. send_sms_verify_code: 发送短信验证码
    2. check_sms_verify_code: 验证短信验证码
    3. generate_code: 生成本地验证码
    """
    # 延迟初始化
    _sms_account = None
    _sign = None
    _template_id = None
    _access_key_id = None
    _access_key_secret = None
    _initialized = False
    _logger = logging.getLogger("HuoshanSmsUtils")

    @classmethod
    def _init_if_needed(cls):
        if not cls._initialized:
            try:
                context = ctx.__getattr__("context")
                # 保存认证信息
                cls._access_key_id = context.huoshan.ak
                cls._access_key_secret = context.huoshan.sk
                cls._sms_account = context.huoshan_sms.sms_account
                cls._sign = context.huoshan_sms.sign
                cls._template_id = context.huoshan_sms.template_id
                # 使用日志系统记录初始化信息
                cls._logger.info(f"HuoshanSmsUtils初始化成功: SMS账号={cls._sms_account}")
                cls._initialized = True
            except Exception as e:
                # 从配置获取失败时抛出异常
                cls._logger.error(f"从配置加载火山短信配置失败: {str(e)}")
                raise ValueError(f"火山短信服务配置加载失败: {str(e)}")

    @staticmethod
    def generate_code(length=4):
        """
        生成验证码，默认4位数，与火山引擎短信服务的CodeType=4配置匹配
        
        参数:
            length (int): 验证码长度，默认为4
            
        返回:
            str: 生成的验证码
        """
        if length <= 0:
            raise ValueError("验证码长度必须为正整数")

        min_val = 10 ** (length - 1)
        max_val = (10 ** length) - 1

        return str(random.randint(min_val, max_val))

    @classmethod
    def send_sms_verify_code(cls, verify_code, phone_number, scene="login", retry_count=3, retry_delay=1,
                             timeout=(5, 10)):
        cls._init_if_needed()
        sms_service = SmsService()
        sms_service.set_ak(cls._access_key_id)
        sms_service.set_sk(cls._access_key_secret)
        body = {
            "SmsAccount": cls._sms_account,
            "Sign": cls._sign,
            "TemplateID": cls._template_id,
            "TemplateParam": json.dumps({"code": verify_code}),
            "PhoneNumbers": phone_number,
            "Tag": "login",
        }

        body = json.dumps(body)
        resp = sms_service.send_sms(body)
        print(resp)
        return {"Code": "OK", "Message": "发送成功"}

    @classmethod
    def check_sms_verify_code(cls, phone_number, code, scene="login"):
        """
        验证短信验证码是否正确

        参数:
            phone_number (str): 接收短信的手机号码
            code (str): 用户输入的验证码
            scene (str): 使用场景，默认为"login"

        返回:
            dict: 包含验证结果的字典，格式如下：
                {
                    "Code": "Success" 或错误代码,
                    "Message": 成功或错误信息,
                    "RequestId": 请求ID
                }
        """
        cls._init_if_needed()

        # 初始化火山引擎SMS服务
        sms_service = SmsService()
        sms_service.set_ak(cls._access_key_id)
        sms_service.set_sk(cls._access_key_secret)

        # 准备请求参数
        payload = {
            "SmsAccount": cls._sms_account,
            "PhoneNumber": phone_number,
            "Scene": scene,
            "Code": code,
            "TemplateID": cls._template_id,
            "Sign": cls._sign
        }

        # 转换为JSON字符串
        body = json.dumps(payload)

        try:
            # 隐藏部分手机号用于日志
            masked_phone = f"{phone_number[:3]}****{phone_number[-4:]}"
            cls._logger.info(f"验证火山短信验证码，电话号码: {masked_phone}")

            # 调用火山引擎SDK验证短信验证码
            response = sms_service.check_sms_verify_code(body)

            # 检查响应
            if isinstance(response, dict):
                result = response
            else:
                # 尝试解析JSON响应
                result = json.loads(response) if isinstance(response, str) else {}

            # 检查是否成功
            if "ResponseMetadata" in result and result.get("ResponseMetadata", {}).get("Error") is None:
                cls._logger.info(f"验证码验证成功，电话号码: {masked_phone}")
                return {
                    "Code": "Success",
                    "Message": "验证成功",
                    "RequestId": result.get("ResponseMetadata", {}).get("RequestId", "")
                }
            else:
                # 获取错误信息
                error = result.get("ResponseMetadata", {}).get("Error", {})
                error_code = error.get("Code", "UnknownError")
                error_message = error.get("Message", "Unknown error occurred")

                cls._logger.warning(f"验证码验证失败: {error_code} - {error_message}, 电话号码: {masked_phone}")

                return {
                    "Code": error_code,
                    "Message": error_message,
                    "RequestId": result.get("ResponseMetadata", {}).get("RequestId", "")
                }

        except Exception as e:
            error_message = str(e)
            cls._logger.error(f"验证码验证异常: {error_message}，电话号码: {phone_number[:3]}****{phone_number[-4:]}")

            return {
                "Code": "RequestException",
                "Message": f"验证码验证异常: {error_message}",
                "RequestId": "request-failed"
            }

    @classmethod
    def send_sms(cls, phone_number, scene="login", retry_count=3, retry_delay=1, timeout=(5, 10)):
        """
        发送短信验证码，使用火山引擎SDK

        注意: 建议使用send_sms_verify_code方法，功能相同但名称更准确

        参数:
            phone_number (str): 接收短信的手机号码
            scene (str): 使用场景，默认为"login"
            retry_count (int): 重试次数，默认为3次
            retry_delay (int): 重试延迟基础时间，单位为秒，默认为1秒，实际重试间隔会使用指数退避策略
            timeout (tuple): 请求超时设置，默认为(连接超时5秒, 读取超时10秒)

        返回:
            dict: 包含发送结果的字典，格式如下：
                {
                    "Code": "Success" 或错误代码,
                    "Message": 成功或错误信息,
                    "RequestId": 请求ID,
                    "VerifyCode": 验证码(成功时),
                    "AttemptCount": 尝试次数
                }
        """
        cls._init_if_needed()

        # 初始化火山引擎SMS服务
        sms_service = SmsService()
        sms_service.set_ak(cls._access_key_id)
        sms_service.set_sk(cls._access_key_secret)

        # 准备请求参数
        payload = {
            "SmsAccount": cls._sms_account,
            "Sign": cls._sign,
            "TemplateID": cls._template_id,
            "PhoneNumber": phone_number,
            "Scene": scene,
            "CodeType": 4,  # 4位验证码长度
            "ExpireTime": 300,  # 验证码有效期5分钟
            "TryCount": retry_count  # 在火山引擎平台上的重试次数
        }

        # 转换为JSON字符串
        body = json.dumps(payload)

        # 实现重试逻辑
        for attempt in range(retry_count):
            try:
                cls._logger.info(
                    f"发送火山短信验证码(尝试 {attempt + 1}/{retry_count}), 电话号码: {phone_number[:3]}****{phone_number[-4:]}")

                # 调用火山引擎SDK发送短信
                response = sms_service.send_sms_verify_code(body)

                # 检查响应
                if isinstance(response, dict):
                    result = response
                else:
                    # 尝试解析JSON响应
                    result = json.loads(response) if isinstance(response, str) else {}

                cls._logger.info(f"火山引擎短信发送响应(尝试 {attempt + 1}/{retry_count}): {result}")

                # 检查是否成功
                if "ResponseMetadata" in result and result.get("ResponseMetadata", {}).get("Error") is None:
                    # 成功，提取验证码
                    verify_code = result.get("Result", {}).get("Code", "")

                    cls._logger.info(f"火山短信发送成功，电话号码: {phone_number[:3]}****{phone_number[-4:]}")
                    return {
                        "Code": "Success",
                        "Message": "发送成功",
                        "RequestId": result.get("ResponseMetadata", {}).get("RequestId", ""),
                        "VerifyCode": verify_code,
                        "AttemptCount": attempt + 1
                    }
                else:
                    # 获取错误信息
                    error = result.get("ResponseMetadata", {}).get("Error", {})
                    error_code = error.get("Code", "UnknownError")
                    error_message = error.get("Message", "Unknown error occurred")

                    cls._logger.warning(f"火山引擎短信发送失败(尝试 {attempt + 1}/{retry_count}): "
                                        f"{error_code} - {error_message}, 电话号码: {phone_number[:3]}****{phone_number[-4:]}")

                    # 对于特定类型的错误，不继续重试
                    non_retryable_errors = [
                        "InvalidParameter", "AccessDenied", "Unauthorized",
                        "TemplateNotExist", "SignNotExist", "QuotaExceeded"
                    ]

                    if error_code in non_retryable_errors:
                        return {
                            "Code": error_code,
                            "Message": error_message,
                            "RequestId": result.get("ResponseMetadata", {}).get("RequestId", ""),
                            "AttemptCount": attempt + 1
                        }

                    # 如果不是最后一次尝试，继续重试
                    if attempt < retry_count - 1:
                        retry_time = retry_delay * (2 ** attempt)  # 指数退避策略
                        cls._logger.info(
                            f"将在 {retry_time} 秒后重试，电话号码: {phone_number[:3]}****{phone_number[-4:]}")
                        time.sleep(retry_time)
                        continue

                    return {
                        "Code": error_code,
                        "Message": error_message,
                        "RequestId": result.get("ResponseMetadata", {}).get("RequestId", ""),
                        "AttemptCount": attempt + 1
                    }

            except json.JSONDecodeError as e:
                cls._logger.warning(
                    f"火山引擎短信响应解析失败(尝试 {attempt + 1}/{retry_count}): {str(e)}, 电话号码: {phone_number[:3]}****{phone_number[-4:]}")
                if attempt < retry_count - 1:
                    retry_time = retry_delay * (2 ** attempt)
                    cls._logger.info(f"将在 {retry_time} 秒后重试，电话号码: {phone_number[:3]}****{phone_number[-4:]}")
                    time.sleep(retry_time)
                    continue

            except Exception as e:
                error_message = str(e)
                cls._logger.error(
                    f"火山引擎短信未预期异常(尝试 {attempt + 1}/{retry_count}): {error_message}，电话号码: {phone_number[:3]}****{phone_number[-4:]}")
                if attempt < retry_count - 1:
                    retry_time = retry_delay * (2 ** attempt)
                    cls._logger.info(f"将在 {retry_time} 秒后重试，电话号码: {phone_number[:3]}****{phone_number[-4:]}")
                    time.sleep(retry_time)
                    continue

        # 所有尝试都失败
        cls._logger.error(
            f"火山引擎短信发送失败，已达到最大重试次数({retry_count})，电话号码: {phone_number[:3]}****{phone_number[-4:]}")
        return {
            "Code": "RequestFailed",
            "Message": f"火山引擎短信发送失败，已达到最大重试次数({retry_count})",
            "RequestId": "all-attempts-failed",
            "AttemptCount": retry_count
        }
