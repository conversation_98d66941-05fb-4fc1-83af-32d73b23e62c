import asyncio
import json
from typing import Dict, Any

from loguru import logger


async def get_agent_info(task_id: str, agent_id: str, redis_client) -> Dict[str, Any]:
    task_key = f'aip_task:{task_id}'
    agent_key = f'{task_key}_agent_{agent_id}'
    print(f"正在获取 agent 信息，key: {agent_key}")

    agent_info = {}
    try:
        # 获取所有字段
        data = await redis_client.hgetall(agent_key)
        print(f"从 Redis 获取到的原始数据: {data}")

        if not data:
            # 尝试获取单个字段来验证键是否存在
            exists = await redis_client.exists(agent_key)
            print(f"键 {agent_key} 是否存在: {exists}")

            # 尝试列出所有相关的键
            pattern = f'{task_key}_agent_*'
            cursor = 0
            all_keys = []
            while True:
                cursor, keys = await redis_client.scan(cursor, match=pattern, count=100)
                all_keys.extend([k.decode() for k in keys])
                if cursor == 0:
                    break
            print(f"Redis 中存在的相关键: {all_keys}")
            return {}

        # 解码数据
        for key, value in data.items():
            key = key.decode() if isinstance(key, bytes) else str(key)
            value = value.decode() if isinstance(value, bytes) else str(value)
            agent_info[key] = value

        print(f"处理后的 agent 信息: {agent_info}")
        return agent_info

    except Exception as e:
        print(f"获取 agent 信息时出错: {str(e)}")
        return {}


async def stream_agent_content(task_id: str, agent_id: int, redis_client):
    task_key = f'aip_task:{task_id}'
    agent_key = f'{task_key}_agent_{agent_id}'
    content_key = f'{task_key}_agent_{agent_id}_content'
    last_offset = 0
    content_yielded = False
    total_timeout = 300  # 总超时时间为5分钟
    check_interval = 0.001  # 每毫秒检查一次
    empty_content_timeout = 0.1  # 空内容超时时间为10秒

    start_time = asyncio.get_event_loop().time()
    empty_content_start_time = None

    while True:
        agent_status = await redis_client.hget(agent_key, 'status')
        agent_status = agent_status.decode() if agent_status else None

        if agent_id == 7:
            # 对于agent 7，只在状态为"completed"时读取内容
            if agent_status == 'stop':
                if await redis_client.exists(content_key):
                    content = await redis_client.get(content_key)
                    if content:
                        yield content.decode('utf-8', errors='ignore')
                        content_yielded = True
                break  # 为agent 7生成内容后退出循环
        else:
            # 对于其他agents，使用现有逻辑
            if await redis_client.exists(content_key):
                content_length = await redis_client.strlen(content_key)
                if content_length > last_offset:
                    chunk = await redis_client.getrange(content_key, last_offset, -1)
                    content_chunk = chunk.decode('utf-8', errors='ignore')
                    yield content_chunk
                    last_offset = content_length
                    content_yielded = True
                    start_time = asyncio.get_event_loop().time()  # 重置总超时时间
                    empty_content_start_time = None  # 重置空内容计时器
                elif content_length == 0 and empty_content_start_time is None:
                    empty_content_start_time = asyncio.get_event_loop().time()

        agent_status = await redis_client.hget(f'{task_key}_agent_{agent_id}', 'status')

        # 检查空content超时
        if empty_content_start_time and asyncio.get_event_loop().time() - empty_content_start_time > empty_content_timeout:
            print(f"Agent {agent_id} content为空，超过{empty_content_timeout}秒")
            break

        if agent_status in [b'completed', b'stop']:
            if not content_yielded:
                await asyncio.sleep(check_interval)
                continue
            break

        if asyncio.get_event_loop().time() - start_time > total_timeout:
            print(f"Agent {agent_id} 达到总超时时间")
            break

        # 检查空内容超时
        if empty_content_start_time and asyncio.get_event_loop().time() - empty_content_start_time > empty_content_timeout:
            print(f"Agent {agent_id} 内容为空超过 {empty_content_timeout} 秒")
            break

        await asyncio.sleep(check_interval)

    if not content_yielded:
        print(f"警告：Agent {agent_id} 没有产生任何内容")
        # 如果内容为空，我们可以尝试获取其他agent信息
        agent_info = await redis_client.hgetall(agent_key)
        if agent_info:
            yield json.dumps({"agent_info": {k.decode(): v.decode() for k, v in agent_info.items()}})


async def stream_output_content(task_id, redis_client):
    yield json.dumps({"task_id": task_id})
    print(f"\n开始处理任务 {task_id}")

    task_key = f'aip_task:{task_id}'
    agent_last_positions = {}
    last_activity_time = asyncio.get_event_loop().time()
    timeout = 300  # 5分钟超时
    wait_time = 0.1  # 等待新agents的时间（秒）
    no_new_agents_count = 0  # 计数器：连续多少次没有新agent
    max_no_new_agents = 300  # 最大允许连续没有新agent的次数
    last_agent_id = None  # 记录最后一个agent的ID
    sent_contents = set()  # 用于跟踪已发送的内容
    agent_info_cache = {}  # 用于缓存agent信息
    agent_order = []  # 用于保存agent的顺序

    while True:
        # 获取所有agent IDs，并维护顺序
        agent_ids = await redis_client.lrange(f'{task_key}_agents', 0, -1)
        if not agent_ids:
            pattern = f'{task_key}_agent_*'
            cursor = 0
            agent_keys = set()
            while True:
                cursor, keys = await redis_client.scan(cursor, match=pattern, count=100)
                for key in keys:
                    key = key.decode('utf-8')
                    if '_content' not in key:
                        parts = key.split('_agent_')
                        if len(parts) == 2:
                            agent_id = parts[1]
                            agent_keys.add(agent_id)
                if cursor == 0:
                    break
            agent_ids = sorted(agent_keys)
        else:
            agent_ids = [aid.decode() for aid in agent_ids]

        # 更新agent_order，保持原有顺序
        for agent_id in agent_ids:
            if agent_id not in agent_order and "999" not in agent_id:
                agent_order.append(agent_id)

        if not agent_ids:
            no_new_agents_count += 1
            if no_new_agents_count >= max_no_new_agents:
                print("长时间没有新的agent，结束流")
                return
            await asyncio.sleep(wait_time)
            continue

        activity_in_this_loop = False
        all_agents_completed = True
        active_agents_count = 0
        pending_info = {}  # 用于存储待发送的agent信息
        pending_content = {}  # 用于存储待发送的content更新

        # 首先收集所有更新
        for agent_id_str in agent_ids:
            try:
                # 获取agent状态
                agent_key = f'{task_key}_agent_{agent_id_str}'
                agent_status = await redis_client.hget(agent_key, 'status')
                agent_status = agent_status.decode() if agent_status else None

                # 更新最后一个agent的ID
                last_agent_id = agent_id_str.split('_')[-1] if '_' in agent_id_str else agent_id_str

                # 如果agent处于活动状态
                if agent_status and agent_status not in ['completed', 'stop', 'error']:
                    all_agents_completed = False
                    active_agents_count += 1

                # 获取agent基础信息（如果是新agent）
                if agent_id_str not in agent_last_positions and "999" not in agent_id_str:
                    agent_info = await get_agent_info(task_id, agent_id_str, redis_client)
                    if agent_info:
                        info_content = json.dumps({agent_id_str: {"info": agent_info}})
                        if info_content not in sent_contents:
                            pending_info[agent_id_str] = info_content

                # 获取content更新
                if "999" not in agent_id_str:
                    content_key = f'{task_key}_agent_{agent_id_str}_content'
                    current_length = await redis_client.strlen(content_key)

                    if current_length > agent_last_positions.get(agent_id_str, 0):
                        new_content = await redis_client.getrange(
                            content_key,
                            agent_last_positions.get(agent_id_str, 0),
                            -1
                        )
                        new_content = new_content.decode('utf-8', errors='ignore')
                        content_chunk = json.dumps({agent_id_str: {"content_chunk": new_content}})
                        if content_chunk not in sent_contents:
                            pending_content[agent_id_str] = {
                                "chunk": content_chunk,
                                "length": current_length
                            }

            except Exception as e:
                print(f"处理agent {agent_id_str} 时出错: {str(e)}")
                all_agents_completed = False

        # 按照agent_order的顺序发送更新
        for agent_id_str in agent_order:
            # 发送agent信息
            if agent_id_str in pending_info:
                info_content = pending_info[agent_id_str]
                yield info_content
                sent_contents.add(info_content)
                agent_last_positions[agent_id_str] = 0
                activity_in_this_loop = True

            # 发送content更新
            if agent_id_str in pending_content:
                content_data = pending_content[agent_id_str]
                yield content_data["chunk"]
                sent_contents.add(content_data["chunk"])
                agent_last_positions[agent_id_str] = content_data["length"]
                activity_in_this_loop = True
                last_activity_time = asyncio.get_event_loop().time()

        # 检查是否有999标记需要发送
        if last_agent_id == '999' and all_agents_completed and active_agents_count == 0:
            end_content = json.dumps({
                "content": "生成完毕",
                "agent_name_cn": "",
                "agent_name_en": "",
                "agent_id": "999",
                "agent_style": "",
                "task_id": task_id,
                "created_at": "",
                "uuid": "",
                "agent_uuid": "",
                "agent_action": ""
            })
            end_key = f"end_{task_id}"
            if end_key not in sent_contents:
                yield f"data: {end_content}\n\n"
                sent_contents.add(end_key)
                print("所有agents已完成且最后一个agent_id为999，结束流")
                return

        # 检查超时
        if asyncio.get_event_loop().time() - last_activity_time > timeout:
            print(f"任务 {task_id} 在 {timeout} 秒内无活动，超时结束")
            return

        # 如果本轮没有活动，等待一段时间
        if not activity_in_this_loop:
            await asyncio.sleep(wait_time)
        else:
            no_new_agents_count = 0  # 重置计数器

    print("流程正常结束")


async def get_historical_content(task_id: int, redis_client):
    """
    获取指定任务的所有历史数据
    
    Args:
        task_id: 任务ID
        redis_client: Redis客户端
    
    Returns:
        包含所有历史数据的列表
    """
    historical_data = []
    task_key = f'aip_task:{task_id}'

    try:
        # 获取所有agent ID
        agent_ids = await redis_client.lrange(f'{task_key}_agents', 0, -1)
        if not agent_ids:
            return historical_data

        # 创建一个字典来存储每个agent的数据
        agent_data_dict = {}

        for agent_id_str in agent_ids:
            agent_id_str = agent_id_str.decode() if isinstance(agent_id_str, bytes) else agent_id_str
            original_agent_id_str = agent_id_str  # 保存原始的agent_id_str用于排序

            # 跳过999 agent
            if "999" in agent_id_str:
                continue

            # 获取agent基础信息
            agent_key = f'{task_key}_agent_{agent_id_str}'
            agent_info = await redis_client.hgetall(agent_key)

            if not agent_info:
                continue

            # 获取content
            content_key = f'{agent_key}_content'
            content = await redis_client.get(content_key)

            # 即使没有content也构建基础数据
            content = content.decode() if content else "" if isinstance(content, bytes) else ""

            # 构建基础数据
            base_data = {
                "content": content,
                "agent_name_cn": agent_info.get(b"agent_name_cn", b"").decode(),
                "agent_name_en": agent_info.get(b"agent_name_en", b"").decode(),
                "agent_id": agent_info.get(b"agent_id", b"").decode(),
                "agent_style": agent_info.get(b"agent_style", b"").decode(),
                "task_id": task_id,
                "created_at": agent_info.get(b"created_at", b"").decode(),
                "uuid": agent_info.get(b"uuid", b"").decode(),
                "agent_uuid": agent_info.get(b"agent_uuid", b"").decode(),
                "agent_action": agent_info.get(b"agent_action", b"").decode(),
                "language": agent_info.get(b"language", b"").decode()
            }

            # 处理TITLE_CHOOSE类型的内容
            if base_data["agent_style"] == "TITLE_CHOOSE" and content:
                try:
                    content_list = []
                    logger.debug(f"处理TITLE_CHOOSE内容，原始数据: {content}")

                    # 首先尝试直接解析整个content作为JSON数组
                    try:
                        items = json.loads(content)
                        if isinstance(items, list):
                            logger.debug(f"成功解析为JSON数组: {items}")
                            for item in items:
                                if isinstance(item, dict) and "order_cn" in item and "title" in item:
                                    logger.debug(f"添加数组中的选题对象: {item}")
                                    content_list.append(item)
                    except json.JSONDecodeError:
                        # 如果整体解析失败，则按行解析
                        logger.debug("整体JSON解析失败，尝试按行解析")
                        # 移除可能的BOM标记和其他特殊字符
                        content = content.strip().lstrip('\ufeff')
                        raw_content = content.split("\n")
                        for line in raw_content:
                            line = line.strip()
                            if not line:
                                continue
                            try:
                                item = json.loads(line)
                                logger.debug(f"成功解析JSON行: {line}")
                                if isinstance(item, dict) and "order_cn" in item and "title" in item:
                                    logger.debug(f"添加单个选题对象: {item}")
                                    content_list.append(item)
                                elif isinstance(item, list):
                                    logger.debug(f"处理选题对象数组: {item}")
                                    for sub_item in item:
                                        if isinstance(sub_item,
                                                      dict) and "order_cn" in sub_item and "title" in sub_item:
                                            logger.debug(f"添加数组中的选题对象: {sub_item}")
                                            content_list.append(sub_item)
                            except json.JSONDecodeError as e:
                                logger.debug(f"JSON行解析失败: {line}, 错误: {str(e)}")
                                # 尝试清理行数据中的特殊字符后重试
                                try:
                                    cleaned_line = ''.join(char for char in line if ord(char) >= 32)
                                    item = json.loads(cleaned_line)
                                    if isinstance(item, dict) and "order_cn" in item and "title" in item:
                                        logger.debug(f"清理后成功解析选题对象: {item}")
                                        content_list.append(item)
                                except:
                                    continue

                    logger.debug(f"收集到的所有选题对象: {content_list}")

                    if content_list:
                        # 构建最终的选题列表,按数字顺序排序
                        def get_topic_number(order_cn):
                            try:
                                return int(order_cn.replace("选题", ""))
                            except:
                                return float('inf')

                        # 去重并排序
                        unique_content = {}
                        for item in content_list:
                            order_cn = item["order_cn"]
                            logger.debug(f"处理选题 {order_cn}: {item}")
                            if order_cn in unique_content:
                                if item.get("title"):
                                    unique_content[order_cn] = item
                            else:
                                unique_content[order_cn] = item

                        sorted_content = sorted(unique_content.values(),
                                                key=lambda x: get_topic_number(x["order_cn"]))
                        base_data["content"] = json.dumps(sorted_content, ensure_ascii=False)
                except Exception as e:
                    logger.error(f"处理TITLE_CHOOSE内容时出错: {str(e)}")
                    # 在出错时，保留原始内容而不是跳过
                    logger.debug(f"保留原始内容: {content}")

            # 将数据存储到字典中，使用原始的agent_id_str作为键
            agent_data_dict[original_agent_id_str] = base_data

        # 按照agents列表的顺序构建最终的历史数据列表
        for agent_id_str in agent_ids:
            agent_id_str = agent_id_str.decode() if isinstance(agent_id_str, bytes) else agent_id_str
            if "999" not in agent_id_str and agent_id_str in agent_data_dict:
                historical_data.append(agent_data_dict[agent_id_str])

    except Exception as e:
        logger.error(f"获取历史数据时出错: {str(e)}")

    return historical_data


async def generate_stream(task_id: int, redis_client):
    """
    生成流式响应，按数据增量输出
    
    Args:
        task_id: 任务ID
        redis_client: Redis客户端
    """
    sent_uuid_content = set()  # 用于跟踪已发送的内容
    last_activity_time = asyncio.get_event_loop().time()
    timeout = 300  # 5分钟超时
    check_interval = 0.05  # 缩短检查间隔到0.05秒
    task_completed = False  # 用于跟踪任务是否完成
    end_marker_sent = False  # 用于跟踪是否已发送结束标记
    agent_content_cache = {}  # 用于缓存每个agent的最新内容
    agent_info_sent = set()  # 用于跟踪已发送基本信息的agent

    try:
        task_key = f'aip_task:{task_id}'

        while not task_completed:
            try:
                # 检查超时
                current_time = asyncio.get_event_loop().time()
                if current_time - last_activity_time > timeout:
                    logger.warning(f"任务 {task_id} 在 {timeout} 秒内无活动，超时结束")
                    break

                # 获取当前的agents列表
                agents = await redis_client.lrange(f'{task_key}_agents', 0, -1)
                agents = [aid.decode() if isinstance(aid, bytes) else aid for aid in agents]
                activity_in_this_loop = False

                for agent_id in agents:
                    # 如果发现999标记，设置任务完成但不输出内容
                    if "999" in agent_id:
                        task_completed = True
                        break

                    agent_key = f'{task_key}_agent_{agent_id}'
                    content_key = f'{agent_key}_content'

                    # 获取agent基础信息
                    agent_info = await redis_client.hgetall(agent_key)
                    if not agent_info:
                        continue

                    # 如果是新agent，先发送基本信息
                    if agent_id not in agent_info_sent:
                        base_data = {
                            "content": "",
                            "agent_name_cn": agent_info.get(b"agent_name_cn", b"").decode(),
                            "agent_name_en": agent_info.get(b"agent_name_en", b"").decode(),
                            "agent_id": agent_info.get(b"agent_id", b"").decode(),
                            "agent_style": agent_info.get(b"agent_style", b"").decode(),
                            "task_id": task_id,
                            "created_at": agent_info.get(b"created_at", b"").decode(),
                            "uuid": agent_info.get(b"uuid", b"").decode(),
                            "agent_uuid": agent_info.get(b"agent_uuid", b"").decode(),
                            "agent_action": agent_info.get(b"agent_action", b"").decode(),
                            "language": agent_info.get(b"language", b"").decode()
                        }
                        yield f"data: {json.dumps(base_data, ensure_ascii=False)}\n\n"
                        agent_info_sent.add(agent_id)
                        activity_in_this_loop = True
                        last_activity_time = current_time

                    # 获取当前content
                    current_content = await redis_client.get(content_key)
                    current_content = current_content.decode() if current_content else ""

                    # 如果有新内容
                    if current_content and (
                            agent_id not in agent_content_cache or
                            len(current_content) > len(agent_content_cache.get(agent_id, ""))
                    ):
                        # 获取增量内容
                        last_content = agent_content_cache.get(agent_id, "")
                        if not last_content:
                            increment = current_content
                        else:
                            increment = current_content[len(last_content):]

                        if increment:  # 只有在有增量内容时才发送
                            # 处理TITLE_CHOOSE类型的内容
                            agent_style = agent_info.get(b"agent_style", b"").decode()
                            if agent_style == "TITLE_CHOOSE":
                                try:
                                    # 清洗和去重选题数据
                                    cleaned_content = clean_title_choose_content(increment)
                                    if cleaned_content:
                                        increment = cleaned_content
                                except Exception as e:
                                    logger.error(f"清洗选题内容时出错: {str(e)}")

                            base_data = {
                                "content": increment,
                                "agent_name_cn": agent_info.get(b"agent_name_cn", b"").decode(),
                                "agent_name_en": agent_info.get(b"agent_name_en", b"").decode(),
                                "agent_id": agent_info.get(b"agent_id", b"").decode(),
                                "agent_style": agent_info.get(b"agent_style", b"").decode(),
                                "task_id": task_id,
                                "created_at": agent_info.get(b"created_at", b"").decode(),
                                "uuid": agent_info.get(b"uuid", b"").decode(),
                                "agent_uuid": agent_info.get(b"agent_uuid", b"").decode(),
                                "agent_action": agent_info.get(b"agent_action", b"").decode(),
                                "language": agent_info.get(b"language", b"").decode()
                            }

                            # 生成唯一标识
                            uuid_content_key = f"{base_data['uuid']}_{increment}"
                            if uuid_content_key not in sent_uuid_content:
                                sent_uuid_content.add(uuid_content_key)
                                yield f"data: {json.dumps(base_data, ensure_ascii=False)}\n\n"
                                activity_in_this_loop = True
                                last_activity_time = current_time

                                # 更新缓存
                                agent_content_cache[agent_id] = current_content

                # 如果本轮没有活动，等待一段时间
                if not activity_in_this_loop and not task_completed:
                    await asyncio.sleep(check_interval)

            except Exception as e:
                logger.error(f"处理数据流时出错: {str(e)}")
                await asyncio.sleep(check_interval)
                continue

    except Exception as e:
        logger.error(f"生成流式响应时出错: {str(e)}")
        return


def clean_title_choose_content(content: str) -> str:
    """
    清洗TITLE_CHOOSE类型的内容
    
    Args:
        content: 原始内容字符串
    
    Returns:
        清洗后的内容字符串
    """
    try:
        # 移除可能的BOM标记和其他特殊字符
        content = content.strip().lstrip('\ufeff')

        # 解析所有可能的JSON对象
        all_items = []
        for line in content.split('\n'):
            line = line.strip()
            if not line:
                continue

            try:
                item = json.loads(line)
                if isinstance(item, list):
                    all_items.extend(item)
                else:
                    all_items.append(item)
            except json.JSONDecodeError:
                continue

        # 清洗和去重
        cleaned_items = {}
        for item in all_items:
            if not isinstance(item, dict):
                continue

            if "order_cn" not in item or "title" not in item:
                continue

            order_cn = item["order_cn"]
            # 选择最完整的描述
            if order_cn in cleaned_items:
                current_because = cleaned_items[order_cn].get("because", "")
                new_because = item.get("because", "")
                if len(new_because) > len(current_because):
                    cleaned_items[order_cn] = item
            else:
                cleaned_items[order_cn] = item

        # 按选题序号排序
        def get_topic_number(order_cn):
            try:
                return int(order_cn.replace("选题", ""))
            except:
                return float('inf')

        sorted_items = sorted(cleaned_items.values(), key=lambda x: get_topic_number(x["order_cn"]))

        # 转换回JSON字符串
        return json.dumps(sorted_items, ensure_ascii=False)

    except Exception as e:
        logger.error(f"清洗选题内容时出错: {str(e)}")
        return content


async def get_all_content(task_id: str, redis_client):
    all_content = {}
    task_key = f'aip_task:{task_id}'
    all_agents = await redis_client.lrange(f'{task_key}_agents', 0, -1)
    for agent_id in all_agents:
        agent_id = int(agent_id.decode())
        agent_info = await get_agent_info(task_id, str(agent_id), redis_client)
        agent_content = ""
        async for content_chunk in stream_agent_content(task_id, agent_id, redis_client):
            agent_content += content_chunk
        all_content[str(agent_id)] = {"info": agent_info, "content": agent_content}
    return all_content


async def is_task_completed(task_id: str, redis_client):
    task_key = f'aip_task:{task_id}'
    task_status = await redis_client.hget(task_key, 'status')
    return task_status == b'completed'


async def continue_stream(task_id: int, redis_client):
    """
    从999 agent的位置开始生成流数据，采用数据增量方式输出
    
    Args:
        task_id: 任务ID
        redis_client: Redis客户端
    """
    sent_uuid_content = set()  # 用于跟踪已发送的内容
    last_activity_time = asyncio.get_event_loop().time()
    timeout = 300  # 5分钟超时
    check_interval = 0.05  # 检查间隔
    agent_content_cache = {}  # 用于缓存每个agent的最新内容
    agent_info_sent = set()  # 用于跟踪已发送基本信息的agent
    task_completed = False  # 用于跟踪任务是否完成

    # 获取agents列表中最后一个元素的索引
    task_key = f'aip_task:{task_id}'
    last_agent = await redis_client.lindex(f'{task_key}_agents', -1)
    if not last_agent:
        print("未找到最后一个agent的索引")
        return

    # 从最后一个agent的值中获取序号 (例如: "10_999" -> 10)
    try:
        start_from_seq = int(last_agent.decode().split('_')[0]) + 1
        print(f"将从序号 {start_from_seq} 开始生成流数据")
    except (AttributeError, IndexError, ValueError) as e:
        print(f"解析agent序号失败: {str(e)}")
        return

    while not task_completed:
        try:
            # 检查超时
            current_time = asyncio.get_event_loop().time()
            if current_time - last_activity_time > timeout:
                print(f"任务 {task_id} 在 {timeout} 秒内无活动，超时结束")
                break

            # 获取当前的agents列表
            agents = await redis_client.lrange(f'{task_key}_agents', 0, -1)
            agents = [aid.decode() if isinstance(aid, bytes) else aid for aid in agents]
            activity_in_this_loop = False

            for agent_id in agents:
                try:
                    # 检查agent_id中的序号
                    current_seq = int(agent_id.split('_')[0])
                    if current_seq < start_from_seq:
                        continue

                    # 如果发现999标记，设置任务完成但不输出内容
                    if "999" in agent_id:
                        task_completed = True
                        continue

                    agent_key = f'{task_key}_agent_{agent_id}'
                    content_key = f'{agent_key}_content'

                    # 获取agent基础信息
                    agent_info = await redis_client.hgetall(agent_key)
                    if not agent_info:
                        continue

                    # 如果是新agent，先发送基本信息
                    if agent_id not in agent_info_sent:
                        base_data = {
                            "content": "",
                            "agent_name_cn": agent_info.get(b"agent_name_cn", b"").decode(),
                            "agent_name_en": agent_info.get(b"agent_name_en", b"").decode(),
                            "agent_id": agent_info.get(b"agent_id", b"").decode(),
                            "agent_style": agent_info.get(b"agent_style", b"").decode(),
                            "task_id": task_id,
                            "created_at": agent_info.get(b"created_at", b"").decode(),
                            "uuid": agent_info.get(b"uuid", b"").decode(),
                            "agent_uuid": agent_info.get(b"agent_uuid", b"").decode(),
                            "agent_action": agent_info.get(b"agent_action", b"").decode(),
                            "language": agent_info.get(b"language", b"").decode()
                        }
                        yield f"data: {json.dumps(base_data, ensure_ascii=False)}\n\n"
                        agent_info_sent.add(agent_id)
                        activity_in_this_loop = True
                        last_activity_time = current_time

                    # 获取当前content
                    current_content = await redis_client.get(content_key)
                    current_content = current_content.decode() if current_content else ""

                    # 如果有新内容
                    if current_content and (
                            agent_id not in agent_content_cache or
                            len(current_content) > len(agent_content_cache.get(agent_id, ""))
                    ):
                        # 获取增量内容
                        last_content = agent_content_cache.get(agent_id, "")
                        if not last_content:
                            increment = current_content
                        else:
                            increment = current_content[len(last_content):]

                        if increment:  # 只有在有增量内容时才发送
                            # 处理TITLE_CHOOSE类型的内容
                            agent_style = agent_info.get(b"agent_style", b"").decode()
                            if agent_style == "TITLE_CHOOSE":
                                try:
                                    # 清洗和去重选题数据
                                    cleaned_content = clean_title_choose_content(increment)
                                    if cleaned_content:
                                        increment = cleaned_content
                                except Exception as e:
                                    logger.error(f"清洗选题内容时出错: {str(e)}")

                            base_data = {
                                "content": increment,
                                "agent_name_cn": agent_info.get(b"agent_name_cn", b"").decode(),
                                "agent_name_en": agent_info.get(b"agent_name_en", b"").decode(),
                                "agent_id": agent_info.get(b"agent_id", b"").decode(),
                                "agent_style": agent_info.get(b"agent_style", b"").decode(),
                                "task_id": task_id,
                                "created_at": agent_info.get(b"created_at", b"").decode(),
                                "uuid": agent_info.get(b"uuid", b"").decode(),
                                "agent_uuid": agent_info.get(b"agent_uuid", b"").decode(),
                                "agent_action": agent_info.get(b"agent_action", b"").decode(),
                                "language": agent_info.get(b"language", b"").decode()
                            }

                            # 生成唯一标识
                            uuid_content_key = f"{base_data['uuid']}_{increment}"
                            if uuid_content_key not in sent_uuid_content:
                                sent_uuid_content.add(uuid_content_key)
                                yield f"data: {json.dumps(base_data, ensure_ascii=False)}\n\n"
                                activity_in_this_loop = True
                                last_activity_time = current_time

                                # 更新缓存
                                agent_content_cache[agent_id] = current_content

                except Exception as e:
                    print(f"处理agent {agent_id} 时出错: {str(e)}")
                    continue

            if not activity_in_this_loop and not task_completed:
                await asyncio.sleep(check_interval)

        except Exception as e:
            print(f"处理数据流时出错: {str(e)}")
            await asyncio.sleep(check_interval)
            continue
