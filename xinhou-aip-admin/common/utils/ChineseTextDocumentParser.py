# !/usr/bin/python3
# -*- coding: utf-8 -*-

import json
import os
import re

import jieba.analyse
from langchain.docstore.document import Document
from loguru import logger


class ChineseTextDocumentParser:
    def __init__(self, source_file, max_length_per_doc, keyword_count_per_doc):
        self.source_file = source_file
        self.max_length_per_doc = max_length_per_doc
        self.keyword_count_per_doc = keyword_count_per_doc

    def _extract_keywords(self, content):
        # 使用 jieba 分词和 TF-IDF 提取关键词
        keywords = jieba.analyse.extract_tags(content, topK=self.keyword_count_per_doc)
        return keywords

    def _perform_semantic_chunkation(self, content):
        # 中文分句
        # 使用正则表达式分割句子，并保留分隔符
        sentences = re.split(r'(?<=[。！？])', content)

        # 过滤掉空字符串，如果句子只有分隔符，不将其视为空字符串
        sentences = [sentence for sentence in sentences if sentence.strip()]

        chunk_docs = []
        current_doc = ""

        for sentence in sentences:
            if len(current_doc) + len(sentence) <= self.max_length_per_doc:
                current_doc += sentence + " "
            else:
                keywords = self._extract_keywords(current_doc.strip())
                chunk_docs.append({"doc_content": current_doc.strip(), "content_length": len(current_doc.strip()),
                                   "keywords": keywords})
                current_doc = sentence + " "

        # 处理最后一个文档
        if current_doc:
            keywords = self._extract_keywords(current_doc.strip())
            chunk_docs.append(
                {"doc_content": current_doc.strip(), "content_length": len(current_doc.strip()), "keywords": keywords})
        # return chunk_docs
        documents = []
        metadata = {"source": self.source_file}
        for doc in chunk_docs:
            if doc:
                new_doc = Document(page_content=doc.get("doc_content"), metadata=metadata)
                documents.append(new_doc)
        return documents

    def parse_document(self):
        file_info = self._get_file_info()
        print(f"File Name: {file_info['name']}")
        print(f"File Size: {file_info['size']} bytes")
        print(f"File Encoding: {file_info['encoding']}")
        print("")

        with open(self.file_path, 'r', encoding='utf-8') as file:
            text_to_chunk = self._clean_text(file.read())

        # 进行语义分段
        chunk_docs = self._perform_semantic_chunkation(text_to_chunk)

        print(f"Number of Segments: {len(chunk_docs)}")
        for i, chunk_doc in enumerate(chunk_docs):
            print(f"Segment {i + 1}: {json.dumps(chunk_doc, ensure_ascii=False)}")
            print()

        return chunk_docs

    def parse_text(self, text):
        text_to_chunk = self._clean_text(text)
        return self._perform_semantic_chunkation(text_to_chunk)

    def _get_file_info(self):
        # 获取文件信息
        file_info = {
            'name': os.path.basename(self.file_path),
            'size': os.path.getsize(self.file_path),
            'encoding': 'utf-8',  # 默认编码为utf-8，可以根据需要修改
        }
        return file_info

    @staticmethod
    def _clean_text(text):
        # 去除常见转义字符和换行符
        cleaned_text = re.sub(r'\\[\'"\\/bfnrt]', '', text)
        cleaned_text = re.sub(r'\n', ' ', cleaned_text)
        return cleaned_text


if __name__ == '__main__':
    # 解析文档并输出结果
    # chunk_docs = ChineseTextDocumentParser("鸠摩罗什法师大义.txt", 500, 20).parse_document()
    parser = ChineseTextDocumentParser("鸠摩罗什法师大义.txt", 500, 20)
    text = "嫌愦五人舍我而去。坐道场时。以十六心。得阿那含。以十八心。断无色界结。以三十四心。破一切烦恼。得一切智。成佛已具受人法。饥渴寒热老病死等。虽心得解脱。身犹有碍。但以一切智慧大悲心为胜耳。如是等诸论义师。皆因佛语。说菩萨相。于是各生异端。得中者少。意谓菩萨得无生法忍。舍生死身。即堕无量无边法中。如阿罗汉。既入无余涅槃。堕在无量无边法中。不得说言若天若人若在若灭。何以故。因缘故名为人。因缘散自然而息。无有一定实灭者。但名有变异身。得如是法门。便欲灭度时。十方佛告言。善男子。汝未得如是无量无边见顿佛身。又未得无量禅定智慧等诸佛功德。汝但得一法门。勿以一法门故。自以为足。当念本愿。怜愍众生。令不知如是寂灭相故。堕三恶道。受诸苦恼。汝所得者。虽是究竟真实之法。但未是证时。尔时菩萨。受佛教已。自念本愿。还以大悲。入于生死。是菩萨。名之不在涅槃不在世间。无有定相。以种种方便。度脱众生。设有问言。菩萨答尔。无复实生受勤苦。无诸恼患。功勋甚少。应答是事不然。著于凡夫时。以颠倒著心。要期果报。虽修苦行。皆非实行。今得诸法实相。具涅槃乐。而入生死。化度众生。是为希有设复问言。若此人戏想都灭。又无我心。"  # 这里替换为您的文本
    chunk_docs = parser.parse_text(text)
    logger.info(f"[文档训练]解析训练文件后，共生成[{chunk_docs.__len__()}]分段数！")
    for i, chunk_doc in enumerate(chunk_docs):
        logger.info(chunk_doc)
