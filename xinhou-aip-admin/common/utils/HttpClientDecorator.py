import logging
from functools import wraps

import requests
from retry import retry


class HttpClient:
    def __init__(self, base_url, retry_times=3, retry_delay=1, retry_backoff=2, timeout=5):
        self.base_url = base_url
        self.retry_times = retry_times
        self.retry_delay = retry_delay
        self.retry_backoff = retry_backoff
        self.timeout = timeout

    @retry(exceptions=requests.exceptions.RequestException, tries=3, delay=1, backoff=2)
    def request(self, method, path, headers=None, json_data=None):
        url = self.base_url + path
        try:
            response = requests.request(method, url, headers=headers, json=json_data, timeout=self.timeout)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            logging.error(f"Request failed: {e}")
            return None


def http_client(base_url, path, method="GET", headers={"Content-Type": "application/json"}, json_data={}, retry_times=3,
                retry_delay=1, retry_backoff=2, timeout=5):
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            client = HttpClient(base_url, retry_times, retry_delay, retry_backoff, timeout)

            # 提取调用方式二中传入的 headers 和 json_data 参数，如果没有传入则使用默认值
            headers_kwargs = kwargs.pop('headers', headers)
            json_data_kwargs = kwargs.pop('json_data', json_data)

            # 使用调用方式二中传入的参数调用请求方法
            response_data = client.request(method=method, path=path,
                                           headers=headers_kwargs, json_data=json_data_kwargs)
            # 将响应数据作为参数传递给被装饰的函数
            kwargs["response_data"] = response_data
            return func(*args, **kwargs)

        return wrapper

    return decorator


class OpenaiRemoteService:

    @staticmethod
    @http_client("http://127.0.0.1:8000", path="/test/summary/callback", json_data={
        "summary_process_key": "1",
        "summary_process_result": "测试"
    }, headers={
        "tenant_id": '888888',
        "classify_type": 'role',
        "classify_id": '888888',
        "platform_code": 'pt'
    }, method="POST")
    def health(response_data, **kwargs):
        if response_data:
            # 在这里处理业务逻辑，可以通过 res_data 获取响应数据
            print("Received response data:", response_data)
        else:
            print("No response data received.")


def main():
    # 调用方式默认使用@http_client注解时通过header 和json_data等参数设置
    OpenaiRemoteService.health()

    # 调用方式二
    OpenaiRemoteService.health(json_data={
        "summary_process_key": "2",
        "summary_process_result": "测试"
    }, headers={
        "tenant_id": '888888',
        "classify_type": 'role',
        "classify_id": '888888',
        "platform_code": 'pt'
    }, method="POST")


if __name__ == "__main__":
    main()
