import os

import aiohttp
from loguru import logger


async def sendImages(textContent, context):
    """
    发送微信群组机器人消息
    :param textContent: 消息内容
    """
    # url为群组机器人WebHook，配置项
    # url = "https://open.feishu.cn/open-apis/bot/v2/hook/dda63684-a3df-4ecb-a5cf-30277fb5764e"
    url = context.notice.images
    domain = context.channel.domain
    textContent += "\n渠道：" + domain
    headers = {
        "content-type": "application/json"
    }
    msg = {"msg_type": "text",
           "content": {
               "text": "<at user_id=\"all\"></at> " + '\n' + textContent,
           }}
    try:
        env = str(os.environ.get('DEPLOY_ENV'))  # 确保使用正确的环境变量键名
        if env != 'None' and env != 'test' and env != 'uat':
            async with aiohttp.ClientSession() as session:
                async with session.post(url, headers=headers, json=msg) as response:
                    if response.status != 200:
                        logger.error(f"告警机器人消息发送失败，状态码：{response.status}")
                        return False
                    return True
        return True
    except Exception as e:
        logger.error(f"告警机器人消息发送失败：{str(e)}")
        return False


async def sendPrecontact(textContent, context):
    """
    发送微信群组机器人消息
    :param textContent: 消息内容
    """
    # url为群组机器人WebHook，配置项
    # url = "https://open.feishu.cn/open-apis/bot/v2/hook/dda63684-a3df-4ecb-a5cf-30277fb5764e"
    url = context.notice.precontract
    domain = context.channel.domain
    textContent += "\n渠道：" + domain
    headers = {
        "content-type": "application/json"
    }
    msg = {"msg_type": "text",
           "content": {
               "text": "<at user_id=\"all\"></at> " + '\n' + textContent,
           }}
    try:
        env = str(os.environ.get('DEPLOY_ENV'))  # 确保使用正确的环境变量键名
        if env != 'None' and env != 'test' and env != 'uat':
            async with aiohttp.ClientSession() as session:
                async with session.post(url, headers=headers, json=msg) as response:
                    if response.status != 200:
                        logger.error(f"告警机器人消息发送失败，状态码：{response.status}")
                        return False
                    return True
        return True
    except Exception as e:
        logger.error(f"告警机器人消息发送失败：{str(e)}")
        return False
