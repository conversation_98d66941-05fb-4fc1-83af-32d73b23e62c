# !/usr/bin/python3
# -*- coding: utf-8 -*-

"""
数字人多媒体表服务类
----------------------------------------------------
@Project :   xinhou-openai-admin
@File    :   MediaModelService.py
@Contact :   <EMAIL>

@Modify Time      <AUTHOR>    @Desciption
------------      -------    --------    -----------
2023/08/15 10:00  peng.shen   v1.0.0     None
"""

import json

from fastapi import UploadFile
from loguru import logger
from sqlalchemy.orm import Session
from xinhou_openai_framework.core.orm.service.BaseServiceImpl import BaseServiceImpl

from common.entity.MediaModel import MediaModel
from common.remote.VideoRemoteService import VideoRemoteService
from common.utils.FilesUtils import save_files_v1_oss


class MediaModelService(BaseServiceImpl[MediaModel]):
    """
    数字人多媒体服务类
    """

    def __init__(self, db: Session):
        super(MediaModelService, self).__init__(db, MediaModel)

    async def upload_media(self, title: str, pid: int, file: UploadFile) -> dict:
        """
        上传媒体文件并创建记录
        """
        try:
            # 上传文件到OSS
            file_infos = await save_files_v1_oss([file])
            if not file_infos:
                raise ValueError("文件上传失败")

            file_info = file_infos[0]

            # 创建媒体模型记录
            media_model = MediaModel(
                title=title,
                pid=pid,
                media_url=file_info['file_url'],
                pic_url=file_info['file_review_pic'],
                status=1,
                del_flag=1,
                transcode=0  # 初始转码状态为0
            )

            # 保存到数据库
            result = self.save(media_model)
            return result

        except Exception as e:
            logger.error(f"上传媒体失败: {str(e)}")
            raise

    async def transcode_media(self, id: int) -> dict:
        """
        转码媒体文件
        """
        try:
            # 查找媒体记录
            media_model = self.find_by_id(MediaModel(id=id))
            if not media_model:
                raise ValueError("未找到ID对应视频模型")

            if not media_model.media_url:
                raise ValueError("媒体URL不能为空")

            transcode_result = VideoRemoteService.transcode_video(
                json_data={"input_url": media_model.media_url}
            )

            # 更新转码状态和URL
            if transcode_result and transcode_result.get('output_url'):
                media_model.media_url = transcode_result['output_url']
                media_model.transcode = 2  # 转码成功
                # 保存分辨率信息
                if 'resolution' in transcode_result:
                    media_model.resolution = json.dumps(transcode_result['resolution'])
            else:
                media_model.transcode = 1  # 转码失败
                media_model.remark = "转码失败：未获取到转码后的URL"

            result = self.update(media_model)
            return result

        except ValueError as e:
            # 参数验证错误
            logger.error(f"转码失败: {str(e)}")
            raise
        except Exception as e:
            # 其他错误（如网络错误、服务器错误等）
            logger.error(f"转码失败: {str(e)}")
            # 更新转码状态为失败
            if media_model:
                media_model.transcode = 1
                media_model.remark = f"转码失败：{str(e)}"
                self.update(media_model)
            raise

    async def upload_media_by_url(self, title: str, pid: int, oss_url: str):
        """
        通过OSS URL上传视频形象模型文件
        
        Args:
            title: 媒体标题
            pid: IP ID
            oss_url: OSS文件URL
        
        Returns:
            上传结果信息
        """
        # 验证参数
        if not title or not pid or not oss_url:
            raise ValueError("标题、IP ID和OSS URL不能为空")
        
        # 从URL获取文件名和扩展名
        filename = oss_url.split('/')[-1]
        file_ext = filename.split('.')[-1].lower() if '.' in filename else ''
        
        # 验证文件类型（如果需要）
        allowed_extensions = ['mp4', 'mov', 'avi', 'mkv']  # 示例允许的扩展名
        if file_ext not in allowed_extensions:
            raise ValueError(f"不支持的文件类型: {file_ext}，仅支持: {', '.join(allowed_extensions)}")
        
        # 创建媒体模型记录
        media_model = MediaModel(
            title=title,
            pid=pid,
            media_url=oss_url,
            status=1,
            del_flag=1,
            transcode=0
        )
        
        # 保存记录
        self.db.add(media_model)
        self.db.commit()
        self.db.refresh(media_model)
        
        # 如果需要，可以在这里触发转码任务
        
        return media_model
