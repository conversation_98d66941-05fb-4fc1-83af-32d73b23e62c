# !/usr/bin/python3
# -*- coding: utf-8 -*-

"""
视频预览图生成控制器
----------------------------------------------------
@Project :   xinhou-aip-admin
@File    :   VideoPreviewGeneratorController.py
@Contact :   <EMAIL>

@Modify Time      <AUTHOR>    @Desciption
------------      -------    --------    -----------
2024/03/21 14:04   logic      1.0         None
"""

import os
import tempfile
from urllib.parse import urlparse, unquote

import cv2
import requests
from fastapi import APIRouter
from loguru import logger
from sqlalchemy import text
from sqlalchemy.orm import Session
from xinhou_openai_framework.core.db.DatabaseManager import DatabaseManager
from xinhou_openai_framework.utils.OssUtil import OssUtil

api = APIRouter()


class VideoPreviewGenerator:
    def __init__(self, db: Session):
        self.db = db
        self.oss_util = OssUtil()

    def clean_url(self, url: str) -> str:
        if not url:
            return ""
        parsed_url = urlparse(url)
        clean_url = f"{parsed_url.scheme}://{parsed_url.netloc}{parsed_url.path}"
        return unquote(clean_url)

    async def generate_video_preview(self, video_path: str, frame_path: str) -> bool:
        """生成视频预览图"""
        try:
            video = cv2.VideoCapture(video_path)
            if video.isOpened():
                success, frame = video.read()
                if success:
                    cv2.imwrite(frame_path, frame)
                    video.release()
                    return True
            video.release()
            return False
        except Exception as e:
            logger.error(f"生成视频预览图失败: {str(e)}")
            return False

    async def process_video_url(self, video_url: str, temp_dir: str) -> str:
        """处理视频URL并生成预览图"""
        try:
            if not video_url:
                return None

            # 下载视频文件
            response = requests.get(video_url, stream=True)
            response.raise_for_status()

            # 生成临时文件路径
            video_path = os.path.join(temp_dir, f"temp_video_{os.urandom(8).hex()}.mp4")
            preview_path = f"{video_path}.jpg"

            # 保存视频到临时文件
            with open(video_path, 'wb') as f:
                f.write(response.content)

            # 生成预览图
            preview_success = await self.generate_video_preview(video_path, preview_path)
            if not preview_success:
                return None

            # 上传预览图到OSS（添加重试逻辑）
            preview_oss_path = f"video/uploads/preview_{os.path.basename(preview_path)}"
            
            import random
            import time
            
            # 添加重试逻辑
            max_retries = 3
            retry_count = 0
            upload_success = False
            preview_result = None
            
            while retry_count < max_retries and not upload_success:
                try:
                    logger.info(f"尝试上传预览图到OSS (尝试 {retry_count + 1}/{max_retries}): {preview_oss_path}")
                    self.oss_util.upload_file(preview_path, preview_oss_path)
                    preview_result = self.oss_util.get_object(preview_oss_path)
                    
                    if not preview_result or not preview_result.success:
                        raise Exception(f"获取OSS对象失败: {preview_result.message if preview_result else '未知错误'}")
                    
                    upload_success = True
                    logger.info(f"预览图上传OSS成功: {preview_oss_path}")
                    
                except Exception as retry_error:
                    retry_count += 1
                    error_msg = f"上传预览图到OSS失败 (尝试 {retry_count}/{max_retries})"
                    logger.error(error_msg)
                    
                    if retry_count < max_retries:
                        retry_interval = 2 + (random.random() * 3)  # 2-5秒间随机等待时间
                        logger.info(f"等待 {retry_interval:.2f} 秒后重试上传")
                        time.sleep(retry_interval)
                    else:
                        logger.error(f"上传预览图到OSS失败，已达最大重试次数: {preview_oss_path}")
                        
            # 清理临时文件
            os.remove(video_path)
            os.remove(preview_path)

            if preview_result and preview_result.success:
                return self.clean_url(preview_result.data.signed_url)
            return None

        except Exception as e:
            logger.error(f"处理视频URL失败: {str(e)}")
            return None

    async def process_media_model(self):
        """处理t_media_model表中的数据"""
        try:
            query = text("""
                SELECT id, media_url 
                FROM t_media_model 
                WHERE pic_url IS NULL 
                AND media_url IS NOT NULL 
                AND del_flag = 1
                LIMIT 100
            """)

            while True:
                results = self.db.execute(query).fetchall()
                if not results:
                    break

                with tempfile.TemporaryDirectory() as temp_dir:
                    for row in results:
                        try:
                            preview_url = await self.process_video_url(row.media_url, temp_dir)
                            if preview_url:
                                update_query = text("""
                                    UPDATE t_media_model 
                                    SET pic_url = :preview_url 
                                    WHERE id = :id
                                """)
                                self.db.execute(update_query, {"preview_url": preview_url, "id": row.id})
                                self.db.commit()
                                logger.info(f"更新media_model成功，ID: {row.id}")
                        except Exception as e:
                            logger.error(f"处理media_model记录失败，ID: {row.id}, 错误: {str(e)}")
                            self.db.rollback()

        except Exception as e:
            logger.error(f"处理media_model表失败: {str(e)}")

    async def process_embedding_file(self):
        """处理t_embedding_file表中的数据"""
        try:
            query = text("""
                SELECT id, file_url 
                FROM t_embedding_file 
                WHERE file_review_pic IS NULL 
                AND file_url != '' 
                AND file_url LIKE '%.mp4'
                AND is_delete = 0
                LIMIT 100
            """)

            while True:
                results = self.db.execute(query).fetchall()
                if not results:
                    break

                with tempfile.TemporaryDirectory() as temp_dir:
                    for row in results:
                        try:
                            preview_url = await self.process_video_url(row.file_url, temp_dir)
                            if preview_url:
                                update_query = text("""
                                    UPDATE t_embedding_file 
                                    SET file_review_pic = :preview_url 
                                    WHERE id = :id
                                """)
                                self.db.execute(update_query, {"preview_url": preview_url, "id": row.id})
                                self.db.commit()
                                logger.info(f"更新embedding_file成功，ID: {row.id}")
                        except Exception as e:
                            logger.error(f"处理embedding_file记录失败，ID: {row.id}, 错误: {str(e)}")
                            self.db.rollback()

        except Exception as e:
            logger.error(f"处理embedding_file表失败: {str(e)}")


if __name__ == "__main__":
    # 设置日志
    logger.add("video_preview_generator.log", rotation="500 MB")

    # 获取数据库会话
    db = DatabaseManager().get_session()

    # 运行生成器
    import asyncio

    generator = VideoPreviewGenerator(db)
    asyncio.run(generate_preview(db))
