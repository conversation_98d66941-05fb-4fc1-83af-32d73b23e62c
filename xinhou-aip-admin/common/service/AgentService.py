from typing import List, Optional

from sqlalchemy import select

from common.entity.Agent import Agent


class AgentService:
    def __init__(self, db_session):
        self.db = db_session

    def create(self, agent: Agent) -> Agent:
        """创建Agent"""
        self.db.add(agent)
        self.db.commit()
        self.db.refresh(agent)
        return agent

    def update(self, agent: Agent) -> Agent:
        """更新Agent"""
        stmt = select(Agent).where(Agent.id == agent.id, Agent.del_flag == 1)
        existing_agent = self.db.execute(stmt).scalar_one_or_none()
        
        if existing_agent:
            for key, value in agent.__dict__.items():
                if key != '_sa_instance_state' and value is not None:
                    setattr(existing_agent, key, value)
            
            self.db.commit()
            self.db.refresh(existing_agent)
            return existing_agent
        return None

    def get_by_id(self, id: int) -> Optional[Agent]:
        """根据ID获取Agent"""
        stmt = select(Agent).where(Agent.id == id, Agent.del_flag == 1)
        return self.db.execute(stmt).scalar_one_or_none()

    def list_all(self, status: Optional[int] = None) -> List[Agent]:
        """获取所有Agent列表"""
        stmt = select(Agent).where(Agent.del_flag == 1)
        if status is not None:
            stmt = stmt.where(Agent.status == status)
        return self.db.execute(stmt).scalars().all()

    def list_by_type(self, agent_type: int, status: Optional[int] = None) -> List[Agent]:
        """根据类型获取Agent列表"""
        stmt = select(Agent).where(
            Agent.agent_type == agent_type,
            Agent.del_flag == 1
        )
        if status is not None:
            stmt = stmt.where(Agent.status == status)
        return self.db.execute(stmt).scalars().all()

    def list_by_role(self, agent_role: int, status: Optional[int] = None) -> List[Agent]:
        """根据角色获取Agent列表"""
        stmt = select(Agent).where(
            Agent.agent_role == agent_role,
            Agent.del_flag == 1
        )
        if status is not None:
            stmt = stmt.where(Agent.status == status)
        return self.db.execute(stmt).scalars().all()

    def delete(self, id: int) -> bool:
        """软删除Agent"""
        stmt = select(Agent).where(Agent.id == id, Agent.del_flag == 1)
        agent = self.db.execute(stmt).scalar_one_or_none()
        
        if agent:
            agent.del_flag = 2
            self.db.commit()
            return True
        return False
