from datetime import datetime
from typing import Optional

from loguru import logger
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.orm import Session
from xinhou_openai_framework.core.orm.service.BaseServiceImpl import BaseServiceImpl

from common.entity.VirtualHuman import VirtualHuman


class VirtualHumanService(BaseServiceImpl[VirtualHuman]):
    """
    超虚拟人模型制作服务类
    """

    def __init__(self, db: Session):
        super(VirtualHumanService, self).__init__(db, VirtualHuman)

    def save(self, model: VirtualHuman) -> VirtualHuman:
        """
        保存超虚拟人模型信息

        Args:
            model: 模型实体对象

        Returns:
            保存后的模型对象
        """
        try:
            return super().save(model)
        except Exception as e:
            logger.error(f"保存超虚拟人模型失败: {str(e)}")
            raise e

    def update_by_id(self, id: int, data: dict) -> Optional[VirtualHuman]:
        """
        通过ID更新模型信息

        Args:
            id: 模型ID
            data: 更新的数据字典

        Returns:
            更新后的模型对象，如果未找到返回None
        """
        try:
            model = self.find_by_id(VirtualHuman(id=id))
            if not model:
                return None

            for key, value in data.items():
                if hasattr(model, key) and value is not None:
                    setattr(model, key, value)

            model.updated_at = datetime.now()
            self.db.commit()
            return model
        except SQLAlchemyError as e:
            self.db.rollback()
            logger.error(f"更新超虚拟人模型失败: {str(e)}")
            raise e

    def find_all(self, search: dict) -> list[VirtualHuman]:
        """
        查询所有符合条件的超虚拟人模型

        Args:
            search: 查询条件

        Returns:
            符合条件的模型列表
        """
        query = self.db.query(VirtualHuman)

        # 添加查询条件
        if search.pid is not None:
            query = query.filter(VirtualHuman.pid == search.pid)
        if search.model_type:
            query = query.filter(VirtualHuman.model_type == search.model_type)
        if search.speech_style:
            query = query.filter(VirtualHuman.speech_style == search.speech_style)
        if search.interview_style:
            query = query.filter(VirtualHuman.interview_style == search.interview_style)
        if search.status is not None:
            query = query.filter(VirtualHuman.status == search.status)

        # 只查询未删除的记录
        query = query.filter(VirtualHuman.del_flag == 1)

        return query.all()
