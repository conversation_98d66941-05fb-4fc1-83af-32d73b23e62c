from typing import List, Optional, Dict, Any

from sqlalchemy import or_
from sqlalchemy.orm import Session
from xinhou_openai_framework.core.orm.service.BaseServiceImpl import BaseServiceImpl
from xinhou_openai_framework.pages.PageHelper import PageHelper

from apps.admin.schema.ProductSchema import ReqProductFindSchema
from common.entity.Product import Product


class ProductService(BaseServiceImpl[Product]):
    """
    产品服务类
    """

    def __init__(self, db: Session):
        super(ProductService, self).__init__(db, Product)

    def find_by(self, search: PageHelper) -> List[Product]:
        """
        分页查询产品
        """
        return super().find_by(search)

    def find_all(self, search: ReqProductFindSchema) -> List[Dict[str, Any]]:
        query = self.db.query(Product)

        # 添加过滤条件
        if search.status:
            query = query.filter(Product.status == search.status)
        if search.unit:
            query = query.filter(Product.unit == search.unit)

        # 添加价格范围过滤
        if search.min_price:
            query = query.filter(Product.money >= search.min_price)
        if search.max_price:
            query = query.filter(Product.money <= search.max_price)

        # 添加模糊搜索
        if search.search_term:
            search_filter = or_(
                Product.name.like(f"%{search.search_term}%"),
                Product.remark.like(f"%{search.search_term}%"),
                Product.features.like(f"%{search.search_term}%"),
                Product.desc.like(f"%{search.search_term}%")
            )
            query = query.filter(search_filter)

        # 执行查询并返回结果
        results = query.all()

        # 将查询结果转换为可序列化的字典列表
        return [self._to_dict(product) for product in results]

    def _to_dict(self, product: Product) -> Dict[str, Any]:
        return {
            "id": product.id,
            "name": product.name,
            "money": float(product.money) if product.money_en else None,
            "status": product.status,
            "remark": product.remark,
            "unit": product.unit,
            "features": product.features,
            "desc": product.desc,
            "create_by": product.create_by,
            "created_at": product.created_at,
            "update_by": product.update_by,
            "updated_at": product.updated_at,
            "name_en": product.name_en,
            "money_en": float(product.money_en) if product.money_en else None,
            "unit_en": product.unit_en,
            "features_en": product.features_en,
            "desc_en": product.desc_en
        }

    def save(self, product: Product) -> Product:
        """
        保存产品
        """
        return super().save(product)

    def update(self, product: Product) -> Optional[Product]:
        """
        更新产品
        """
        return super().update(product)

    def delete(self, product: Product) -> None:
        """
        删除产品
        """
        super().delete(product)

    def find_by_id(self, product: Product) -> Optional[Product]:
        """
        根据ID查询产品
        """
        return super().find_by_id(product)
