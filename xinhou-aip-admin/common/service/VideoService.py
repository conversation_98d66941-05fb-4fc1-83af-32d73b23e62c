import asyncio

from fastapi import HTTPException
from loguru import logger
from sqlalchemy.orm import Session

from apps.admin.queue.message.AIPVideoMessage import AIPVideoMessage
from apps.admin.queue.producer.AIPVideoProducer import AIPVideoProducer
from apps.admin.schema.VoiceSchema import AudioType<PERSON>num, ReqCreateVideoWithAudioSchema, \
    ResQueryVideoTaskSchema
from apps.admin.schema.WorkSchema import ReqWorkSaveSchema
from common.contents.AgentsUserContents import AgentsUserContents
from common.entity.EmbeddingFile import EmbeddingFile
from common.entity.Ip import Ip
from common.entity.Task import Task
from common.entity.User import User
from common.entity.VoiceModel import VoiceModel
from common.remote.AudioRemoteService import AudioRemoteService
from common.remote.VideoRemoteService import VideoRemoteService
from common.service.EmbeddingFileService import EmbeddingFileService
from common.service.IpService import IpService
from common.service.TaskService import TaskService
from common.service.UserService import UserService
from common.service.VoiceModelService import VoiceModelService
from common.service.WorkService import WorkService
from common.utils.AudioUtils import get_audio_duration


class VideoService:
    def __init__(self, db: Session):
        self.db = db
        self.task_service = TaskService(db)
        self.work_service = WorkService(db)
        self.embedding_file_service = EmbeddingFileService(db)
        self.ip_service = IpService(db)
        self.user_service = UserService(db)
        self.voice_model_service = VoiceModelService(db)

    async def query_video_task(self, search):
        try:
            task = self.task_service.find_by_id(Task(id=search.task_id))
            if not task:
                raise HTTPException(status_code=404, detail="任务不存在")

            # 如果任务已完成
            if task.progress == 8:
                work = await self.work_service.query_work_by_type(search.task_id, 3)
                if work:
                    embedding_info = self.embedding_file_service.find_by_id(EmbeddingFile(id=work.file_id))
                    return ResQueryVideoTaskSchema(
                        status=3,
                        video_url=embedding_info.file_url,
                        message="视频已完成",
                        code=0
                    ).dict()

            # 任务进行中
            return ResQueryVideoTaskSchema(
                status=2,
                video_url="",
                message="视频生成中",
                code=0
            ).dict()

        except Exception as e:
            logger.error(f"查询视频任务时发生错误: {str(e)}")
            raise HTTPException(status_code=500, detail=f"查询视频任务失败: {str(e)}")

    async def _generate_audio(self, task_data):
        if task_data.audio_type == AudioTypeEnum.generate:
            task = self.task_service.find_by_id(Task(id=task_data.task_id))
            if not task:
                raise HTTPException(status_code=404, detail="任务不存在")
            task.progress = 4
            task.remark = ""
            task = self.task_service.save(task)

            work_info = await self.work_service.query_work_by_type(task_id=task_data.task_id, work_type=1)
            if not work_info or not work_info.content:
                task.remark = "文案信息不存在, 无法合成音频"
                raise HTTPException(status_code=404, detail="文案信息不存在")

            voice_info = self.voice_model_service.find_by_id(VoiceModel(id=task_data.voice_id))
            content = work_info.content.decode('utf-8') if isinstance(work_info.content, bytes) else work_info.content
            if len(content) > 2000:
                task.remark = "音频生成失败: 文案长度超过两千字(包含标点符号)。"
                raise Exception(f"音频生成失败: 文案长度超过两千字(包含标点符号)。")

            try:
                result = AudioRemoteService.audio_generate(json_data={"voice_name": voice_info.clone_name,
                                                                      "draft": content, "server_name": "ALi"})
            except:
                task.progress = 6
                task = self.task_service.save(task)
                raise Exception(f"远程调用合成音频失败：voice_prefix={voice_info.clone_name}, draft={content}")

            embedding_file_info = self.embedding_file_service.save(
                EmbeddingFile(
                    pid=task_data.pid,
                    file_url=result['audio_url'],
                    emb_type=5,
                    source="音频合成"
                )
            )
            audio_url = result['audio_url']

            await self.work_service.save(
                ReqWorkSaveSchema(
                    task_id=task_data.task_id,
                    work_type=2,
                    file_id=embedding_file_info["id"],
                    status=1,
                    pid=task_data.pid,
                    title=content[:100]
                )
            )
            task.progress = 5
            task = self.task_service.save(task)
            return audio_url
        else:
            return task_data.audio_url

    async def _handle_completed_video(self, task, video_url, pid):
        """处理已完成的视频任务"""
        works = await self.work_service.query_work_by_type(task.id, 3)
        if not works:
            embedding_file_info = self.embedding_file_service.save(
                EmbeddingFile(
                    pid=pid,
                    file_url=video_url,
                    emb_type=5,
                    source="视频合成"
                )
            )

            await self.work_service.save(
                ReqWorkSaveSchema(
                    task_id=task.id,
                    work_type=3,
                    file_id=embedding_file_info["id"],
                    status=1,
                    pid=pid,
                    title=task.title,
                    video_model_id=task.video_model_id
                )
            )
            logger.info(f"为任务 {task.id} 创建了新的作品记录")

        task.progress = 8
        self.task_service.update(task)

    async def create_video_with_audio(self, task_data: ReqCreateVideoWithAudioSchema, context):
        """使用已有音频创建视频"""
        task = None
        try:
            # 检查IP是否过期
            if not self.ip_service.check_expire_time(task_data.pid):
                raise HTTPException(status_code=403, detail="IP 已过期")

            if not self.ip_service.check_total_use(task_data.pid):
                raise HTTPException(status_code=403, detail="IP 使用时长已达上限（60分钟）")

            # 获取IP信息
            ip_info = self.ip_service.find_by_id(Ip(id=task_data.pid))
            if not ip_info:
                raise HTTPException(status_code=404, detail="IP信息不存在")

            # 通过 pid 获取 user_name
            current_user = self.user_service.find_by_id(User(id=ip_info.uid))
            # 计算用户名的长度，如果超过4个字符则只取后4个字符

            truncated_user_name = current_user.user_name[-4:] if len(
                current_user.user_name) > 4 else current_user.user_name

            # 计算可用于ip_name的最大长度（19减去用户名长度和下划线）
            max_ip_name_length = 19 - len(truncated_user_name) - 1

            # 如果ip_name超出限制，进行截断
            truncated_ip_name = ip_info.ip_name[:max_ip_name_length] if len(
                ip_info.ip_name) > max_ip_name_length else ip_info.ip_name

            domain = context.channel.domain
            # 组合title
            title = f"{truncated_user_name}_{truncated_ip_name}_{domain}"

            # 查找并更新任务状态
            task = self.task_service.find_by_id(Task(id=task_data.task_id))
            if not task:
                raise HTTPException(status_code=404, detail="任务不存在")

            task.progress = 7  # 视频生成中
            task = self.task_service.save(task)

            # 调用远程视频服务创建任务
            request_data = {
                "video_url": task_data.video_url,
                "audio_url": task_data.audio_url,
                "server_name": "FeiYing",
                "title": title  # 使用截断后的title
            }
            logger.info(f'使用音频与视频进行合成: {request_data}')
            try:
                result = VideoRemoteService.create_video_task(json_data=request_data)
                if isinstance(result, list) and len(result) > 0:
                    result = result[0]

                if not isinstance(result, dict) or 'video_task_id' not in result:
                    task.progress = 9
                    error_message = result.get('message', '未知错误') if isinstance(result, dict) else str(result)
                    task.remark = f"创建视频失败: {error_message}"
                    self.task_service.update(task)
                    raise ValueError(error_message)
                task.video_job_id = result['video_task_id']
                logger.info(f"更新任务视频job_id为: {task.video_job_id}, video_model_id为: {task.video_model_id}")

                # 确保 video_job_id 转换为字符串
                message = AIPVideoMessage(
                    task_id=task.id,
                    video_job_id=str(task.video_job_id),
                    pid=task.pid,
                    title=task.title,
                    url=context.aip.remote.video,
                    uuid=task_data.uuid,
                    redis_key=task_data.redis_key
                )
                logger.info(f"发送视频任务消息: {message}")
                await AIPVideoProducer.video_push(
                    AgentsUserContents.AIP_VIDEO_QUEUE,
                    message
                )
                task.progress = 7
                self.task_service.update(task)

                # 返回符合 ResQueryVideoTaskSchema 的结果
                return ResQueryVideoTaskSchema(
                    status=2,  # 处理中状态
                    video_url="",  # 还没有视频URL
                    message="视频任务创建成功，正在处理中",
                    code=0,
                    task_id=task.id,
                    video_task_id=task.video_job_id
                ).dict()

            except Exception as e:
                task.progress = 9
                error_message = str(e)
                if isinstance(e, HTTPException):
                    error_message = e.detail
                elif hasattr(e, 'response'):
                    try:
                        error_message = e.response.json().get('message', str(e))
                    except:
                        error_message = str(e)

                task.remark = f"创建视频失败: {error_message}"
                self.task_service.update(task)
                raise HTTPException(status_code=500, detail=error_message)
        except Exception as e:
            logger.error(f"创建视频任务时发生错误: {str(e)}")
            if isinstance(e, HTTPException):
                raise e
            raise HTTPException(status_code=500, detail=str(e))

    async def _wait_for_video_completion(self, task, task_data, audio_url):
        """等待视频任务完成"""
        MAX_RETRIES = 60
        BASE_INTERVAL = 10
        MAX_INTERVAL = 60

        retry_count = 0
        current_interval = BASE_INTERVAL

        while retry_count < MAX_RETRIES:
            try:
                # 重新获取最新的任务状态
                task = self.task_service.find_by_id(Task(id=task_data.task_id))
                if not task:
                    raise HTTPException(status_code=404, detail="任务不存在")

                # 构建查询参数
                query_params = {
                    "video_task_id": int(task.video_job_id),
                    "server_name": "FeiYing"
                }
                logger.info(f"查询视频任务状态，参数: {query_params}")

                # 查询视频任务状态
                try:
                    result = VideoRemoteService.query_video_task(json_data=query_params)
                    logger.info(f"查询结果: {result}")
                except Exception as e:
                    logger.error(f"查询视频任务状态失败: {str(e)}")
                    await asyncio.sleep(current_interval)
                    retry_count += 1
                    continue

                # 处理返回结果
                if isinstance(result, list) and len(result) > 0:
                    result = result[0]

                if not isinstance(result, dict):
                    logger.error(f"意外的响应格式: {result}")
                    await asyncio.sleep(current_interval)
                    retry_count += 1
                    continue

                if result.get('status') == 4:  # 失败
                    task.progress = 9
                    self.task_service.update(task)
                    raise HTTPException(status_code=500, detail=result.get('message', '视频生成失败'))

                if result.get('status') == 3:  # 成功
                    try:
                        embedding_file_info = self.embedding_file_service.save(
                            EmbeddingFile(
                                pid=task_data.pid,
                                file_url=result.get('video_Url'),
                                emb_type=5,
                                source="视频合成"
                            )
                        )

                        await self.work_service.save(
                            ReqWorkSaveSchema(
                                task_id=task.id,
                                work_type=3,
                                file_id=embedding_file_info['id'],
                                status=1,
                                pid=task.pid,
                                title=task.title,
                                video_model_id=task.video_model_id if task.video_model_id != "None" else None
                            )
                        )
                        logger.info(f"为任务 {task.id} 创建了新的作品记录")

                        task.progress = 8
                        self.task_service.update(task)

                        try:
                            use_time = get_audio_duration(audio_url)
                            if use_time > 0:
                                self.ip_service.update_use_time(task_data.pid, use_time)
                                logger.info(f"更新了用户 {task_data.pid} 的使用时长: {use_time}")
                            else:
                                logger.warning(f"获取到的音频时长为 {use_time}，未更新用户使用时长")
                        except Exception as e:
                            logger.error(f"更新用户使用时长时发生错误: {str(e)}")

                        return ResQueryVideoTaskSchema(
                            status=result.get('status'),
                            video_url=result.get('video_Url'),
                            audio_url=audio_url,
                            message=result.get('message'),
                            code=result.get('code', 0)
                        ).dict()
                    except Exception as e:
                        logger.error(f"处理成功状态时发生错误: {str(e)}")
                        raise HTTPException(status_code=500, detail=str(e))

                # 调整轮询间隔
                if result.get('status') == 1:  # 等待中
                    current_interval = min(current_interval * 1.5, MAX_INTERVAL)
                elif result.get('status') == 2:  # 处理中
                    current_interval = BASE_INTERVAL

                logger.info(
                    f"视频任务 {task.video_job_id} 状态: {'等待中' if result.get('status') == 1 else '处理中'}, "
                    f"下次检查间隔: {current_interval}秒")

                await asyncio.sleep(current_interval)
                retry_count += 1

            except HTTPException:
                raise
            except Exception as e:
                logger.error(f"等待视频完成时发生错误: {str(e)}")
                raise HTTPException(status_code=500, detail=str(e))

        raise HTTPException(status_code=500, detail="视频生成超时，请稍后重试")
