# !/usr/bin/python3
# -*- coding: utf-8 -*-

from sqlalchemy.orm import Session
from xinhou_openai_framework.core.orm.service.BaseServiceImpl import BaseServiceImpl

from apps.admin.schema.ReqRpaTaskSchema import ReqRpaTaskResultSchema
from common.contents.RpaTaskEnums import RobotStatusEnum
from common.entity.RpaTask import RpaTask


class RpaTaskService(BaseServiceImpl[RpaTask]):
    """
    任务服务类
    """

    def __init__(self, db: Session):
        super(RpaTaskService, self).__init__(db, RpaTask)

    async def add_task(self, robot_id: int, robot_params: str):
        """
        新增一个待执行的任务
        """
        rpa_task = RpaTask()
        rpa_task.robot_id = robot_id
        rpa_task.robot_params = robot_params
        rpa_task.robot_status = RobotStatusEnum.WAITING.name
        rpa_task.create_by = "admin"
        rpa_task.update_by = "admin"
        result = self.save(rpa_task)  # noqa
        return result

    async def get_todo_list(self, robot_id):
        """
        获取待做列表
        """
        rpa_task = RpaTask()
        rpa_task.robot_id = robot_id
        rpa_task.robot_status = RobotStatusEnum.WAITING.name
        return self.find_all(rpa_task)  # noqa

    async def update_task_result(self, req: ReqRpaTaskResultSchema):
        rpa_task = RpaTask()
        rpa_task.id = req.id
        rpa_task.robot_status = req.robot_status
        rpa_task.result_info = req.result_info
        rpa_task.result_file = req.result_file
        return self.update(rpa_task)    # noqa
