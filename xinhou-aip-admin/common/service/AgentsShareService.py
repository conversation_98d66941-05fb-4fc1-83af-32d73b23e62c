import logging
import uuid
from typing import Optional

from fastapi import HTTPException
from sqlalchemy.orm import Session

from common.entity.AgentsHistory import AgentsHistory
from common.entity.AgentsShare import AgentsShare
from common.service.AgentsHistoryService import AgentsHistoryService

logger = logging.getLogger(__name__)


class AgentsShareService:
    """Agent任务分享服务类"""

    def __init__(self, db: Session):
        self.db = db
        self.agents_history_service = AgentsHistoryService(db)

    def create(self, agents_share: AgentsShare) -> AgentsShare:
        """创建分享记录"""
        # 查询历史记录是否存在
        history = self.agents_history_service.get_by_pid_and_task_id(agents_share.pid, agents_share.task_id)
        if not history:
            raise HTTPException(status_code=404, detail=f"找不到对应的历史记录: {agents_share.task_id}")

        # 生成分享码
        agents_share.share_code = self._generate_share_code()

        # 保存到数据库
        self.db.add(agents_share)
        self.db.commit()
        self.db.refresh(agents_share)
        return agents_share

    def update(self, id: int, is_public: bool, remark: Optional[str] = None,
               update_by: Optional[str] = None) -> AgentsShare:
        """更新分享记录"""
        share = self.get_by_id(id)
        if not share:
            raise HTTPException(status_code=404, detail=f"找不到对应的分享记录: {id}")
        share.is_public = is_public
        if remark is not None:
            share.remark = remark
        if update_by is not None:
            share.update_by = update_by

        self.db.commit()
        self.db.refresh(share)
        return share

    def get_by_id(self, id: int) -> Optional[AgentsShare]:
        """通过ID获取分享记录"""
        return self.db.query(AgentsShare).filter(AgentsShare.id == id).first()

    def get_by_share_code(self, share_code: str) -> Optional[AgentsShare]:
        """通过分享码获取分享记录"""
        return self.db.query(AgentsShare).filter(AgentsShare.share_code == share_code).first()

    def get_by_task_id(self, task_id: int) -> Optional[AgentsShare]:
        """通过任务ID获取分享记录"""
        return self.db.query(AgentsShare).filter(AgentsShare.task_id == task_id).first()

    def get_history_by_share_code(self, share_code: str) -> Optional[AgentsHistory]:
        """通过分享码获取历史记录"""
        share = self.get_by_share_code(share_code)
        if not share:
            return None

        # 如果分享记录不公开，返回None
        if not share.is_public:
            return None

        # 获取历史记录
        return self.agents_history_service.get_by_pid_and_task_id(share.pid, share.task_id)

    def delete(self, id: int) -> bool:
        """删除分享记录"""
        share = self.get_by_id(id)
        if not share:
            return False

        self.db.delete(share)
        self.db.commit()
        return True

    def _generate_share_code(self) -> str:
        """生成分享码"""
        return uuid.uuid4().hex[:16]  # 生成16位的随机字符串作为分享码
