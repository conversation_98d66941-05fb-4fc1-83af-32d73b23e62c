# !/usr/bin/python3
# -*- coding: utf-8 -*-

"""
声音模型信息表服务类
----------------------------------------------------
@Project :   xinhou-openai-admin
@File    :   VoiceModel.py
@Contact :   <EMAIL>

@Modify Time      <AUTHOR>    @Desciption
------------      -------    --------    -----------
2023/08/15 10:00  peng.shen   v1.0.0     None
"""

from sqlalchemy.orm import Session
from xinhou_openai_framework.core.orm.service.BaseServiceImpl import BaseServiceImpl

from common.entity.VoiceModel import VoiceModel


class VoiceModelService(BaseServiceImpl[VoiceModel]):
    """
    声音模型服务类
    """

    def __init__(self, db: Session):
        super(VoiceModelService, self).__init__(db, VoiceModel)