# !/usr/bin/python3
# -*- coding: utf-8 -*-

"""
用户信息表服务类
----------------------------------------------------
@Project :   xinhou-openai-admin
@File    :   User.py
@Contact :   <EMAIL>

@Modify Time      <AUTHOR>    @Desciption
------------      -------    --------    -----------
2023/04/16 22:04  peng.shen   v1.0.0     None
"""

from datetime import datetime
from math import ceil
from typing import Optional

from dateutil.relativedelta import relativedelta
from loguru import logger
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.orm import Session
from xinhou_openai_framework.core.orm.service.BaseServiceImpl import BaseServiceImpl

from common.entity.Ip import Ip
from common.entity.IpKnowledge import IpKnowledge
from common.service.KnowledgeService import KnowledgeService


class IpService(BaseServiceImpl[Ip]):
    """
    IP服务类
    """

    def __init__(self, db: Session):
        super(IpService, self).__init__(db, Ip)
        self.knowledge_service = KnowledgeService(db)

    def create_ip_with_knowledge(self, ip_data: dict, creator: str):
        # 创建 IP
        new_ip = self.save(Ip(**ip_data))

        # 创建对应的知识库记录
        new_knowledge = IpKnowledge(
            pid=new_ip.id,
            knowledge_source="我的文件",
            knowledge_type=1,
            create_by=creator,
            update_by=creator
        )
        self.knowledge_service.save(new_knowledge)

        return new_ip

    def bind_knowledge_base(self, src_name: str, msg_id: str, ip_name: str):
        ip = self.db.query(Ip).filter(Ip.ip_name == ip_name).first()
        if not ip:
            return False, f"知识库 {ip_name} 不存在，请先创建知识库"

        existing_binding = self.db.query(Ip).filter(Ip.group_id == msg_id).first()
        if existing_binding:
            existing_binding.ip_name = ip_name
            existing_binding.group_name = src_name.encode()
        else:
            ip.group_id = msg_id
            ip.group_name = src_name.encode()

        self.db.commit()
        return True, f"成功为群组 {src_name} 绑定知识库 {ip_name}"

    def unbind_knowledge_base(self, msg_id: str):
        ip = self.db.query(Ip).filter(Ip.group_id == msg_id).first()
        if not ip:
            return False, "未找到绑定的知识库"

        ip_name = ip.ip_name
        ip.group_id = None
        ip.group_name = None
        self.db.commit()
        return True, f"成功为群组解绑知识库 {ip_name}"

    def get_bound_knowledge_base(self, msg_id: str):
        ip = self.db.query(Ip).filter(Ip.group_id == msg_id).first()
        if ip:
            return True, ip.ip_name
        return False, None

    def update_use_time(self, pid: int, use_time: int):
        ip = self.db.query(Ip).filter(Ip.id == pid).with_for_update().first()
        if ip:
            # 将 use_time 转换为整数秒，向上取整
            use_time_seconds = ceil(use_time)
            if ip.remain_point is not None:
                ip.remain_point -= use_time_seconds
                self.db.commit()
                logger.info(f"已经更新用户 {pid} 的使用时长. 消耗时长为: {use_time_seconds} 秒")
            else:
                logger.warning(f"用户 {pid} 的剩余时长为 None，无法更新")
        else:
            logger.warning(f"没有根据 {pid} 找到IP")

    def check_expire_time(self, pid: int) -> bool:
        ip = self.db.query(Ip).filter(Ip.id == pid).first()
        if not ip:
            logger.warning(f"没有根据 {pid} 找到IP")
            return False

        if ip.expire_time is None:
            logger.warning(f"用户 {pid} 的到期时间未设置")
            return True  # 如果未设置到期时间，我们假设它永不过期

        if ip.expire_time <= datetime.now():
            logger.info(f"用户 {pid} 的IP已过期. 过期时间: {ip.expire_time}")
            return False

        return True

    def check_total_use(self, pid: int) -> bool:
        ip = self.db.query(Ip).filter(Ip.id == pid).first()
        if ip and ip.remain_point is not None:
            return ip.remain_point >= 0
        return False

    def update_pay(self, pid: str):
        """
        更新用户的支付状态
        :param pid: 逗号分隔的用户ID字符串
        """
        if pid is not None:
            pid_list = pid.split(',')
            try:
                for single_pid in pid_list:
                    ip = self.db.query(Ip).filter(Ip.id == single_pid).with_for_update().first()
                    if ip:
                        ip.remain_point = 3600
                        ip.expire_time = datetime.now() + relativedelta(months=1)
                        self.db.commit()
                        logger.info(f"已更新用户 {single_pid} 的支付状态为已支付")
                    else:
                        logger.warning(f"没有找到ID为 {single_pid} 的用户")

            except SQLAlchemyError as e:
                self.db.rollback()
                logger.error(f"更新用户支付状态时发生错误: {str(e)}")
            finally:
                self.db.close()

    def update_by_name(self, ip_name: str, remain_point: int, expire_time: datetime, update_by: str) -> Optional[Ip]:
        """
        通过IP名称更新积分和到期时间
        
        Args:
            ip_name: IP名称
            remain_point: 剩余积分(秒)
            expire_time: 到期时间
            update_by: 更新者
        
        Returns:
            更新后的IP对象，如果未找到返回None
        """
        # 查找IP记录
        ip = self.db.query(Ip).filter(Ip.ip_name == ip_name).first()
        if not ip:
            return None

        # 更新信息
        ip.remain_point = remain_point
        ip.expire_time = expire_time
        ip.update_by = update_by

        try:
            self.db.commit()
            logger.info(f"成功更新IP {ip_name} 的积分和到期时间")
            return ip
        except SQLAlchemyError as e:
            self.db.rollback()
            logger.error(f"更新IP {ip_name} 时发生错误: {str(e)}")
            raise e

    def get_by_name(self, ip_name: str) -> Optional[Ip]:
        """
        通过IP名称查询IP信息

        Args:
            ip_name: IP名称

        Returns:
            Optional[Ip]: 如果找到则返回IP对象，否则返回None
        """
        try:
            ip = self.db.query(Ip).filter(
                Ip.ip_name == ip_name,
                Ip.del_flag == 1
            ).first()
            return ip
        except Exception as e:
            logger.error(f"[get_by_name][error]: {str(e)}")
            return None

    def add_points(self, ip_id: int, points: float, update_by: str) -> Optional[Ip]:
        """
        为IP增加点数
        
        Args:
            ip_id: IP ID
            points: 要增加的点数
            update_by: 更新者
            
        Returns:
            Optional[Ip]: 更新后的IP对象，如果未找到返回None
        """
        ip = self.find_by_id(Ip(id=ip_id))
        if not ip:
            return None

        try:
            ip.remain_point = (ip.remain_point or 0) + points
            ip.update_by = update_by
            ip.updated_at = datetime.now()
            ip.expire_time = None
            self.db.commit()
            logger.info(f"成功为IP {ip_id} 增加点数: {points}")
            return ip
        except SQLAlchemyError as e:
            self.db.rollback()
            logger.error(f"为IP {ip_id} 增加点数时发生错误: {str(e)}")
            raise e
