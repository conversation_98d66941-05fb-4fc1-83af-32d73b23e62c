import json
import time
from typing import Optional
from urllib.parse import urlparse, unquote
import uuid

import aiohttp
from fastapi import HTTPException
from loguru import logger
from openai import OpenAI
from sqlalchemy import text, and_
from sqlalchemy.orm import Session
from xinhou_openai_framework.core.context.model.AppContext import AppContext
from xinhou_openai_framework.core.context.model.FrameworkConfig import Redis
from xinhou_openai_framework.core.context.model.SystemContext import ctx
from xinhou_openai_framework.utils.OssUtil import OssUtil

from apps.admin.schema.WorkSchema import ReqWorkSaveSchema
from common.entity.AgentsHistory import AgentsHistory
from common.entity.EmbeddingFile import EmbeddingFile
from common.entity.Task import Task
from common.entity.VoiceModel import VoiceModel
from common.entity.Work import Work
from common.remote.AudioRemoteService import FishRemoteService
from common.service.EmbeddingFileService import EmbeddingFileService
from common.service.TaskService import TaskService
from common.service.VoiceModelService import VoiceModelService
from common.service.WorkService import WorkService
from common.utils.RedisHistoryUtil import RedisHistoryUtil


class AudioService:
    def __init__(self, db: Session):
        self.db = db
        self.voice_model_service = VoiceModelService(db)
        self.task_service = TaskService(db)
        self.work_service = WorkService(db)
        self.embedding_file_service = EmbeddingFileService(db)

    async def clone_fish_voice(self, title: str, voice_file: bytes, filename: str, content_type: str, pid: int):
        try:
            try:
                # 尝试使用原有的Fish服务
                form_data = aiohttp.FormData()
                form_data.add_field('title', title)
                form_data.add_field('voice_file', voice_file, filename=filename, content_type=content_type)

                result = await FishRemoteService.audio_clone(form_data=form_data)

                file_name = f"fish_voice_{int(time.time())}.wav"
                oss_path = f'audio/uploads/{file_name}'

                oss_util = OssUtil()
                oss_util.bucket.put_object(
                    oss_path,
                    voice_file,
                    headers={'Content-Type': content_type, 'Content-Disposition': 'inline'}
                )

                oss_file = oss_util.get_object(oss_path)
                cleaned_url = self.clean_url(oss_file.data.signed_url)

                voice_model = VoiceModel(
                    voice_name=title,
                    voice_url=cleaned_url,
                    clone_name=result['data'].get('_id', ''),
                    pid=pid,
                    remark="Fish克隆声音",
                    status=1
                )
                self.voice_model_service.save(voice_model)

                return {
                    "message": "声音克隆成功",
                    "code": 200,
                    "clone_name": result['data'].get('_id', ''),
                    "voice_name": result['data'].get('title', '')
                }

            except Exception as fish_error:
                logger.error(f"原始Fish服务克隆失败，启用备用方案: {str(fish_error)}")
                # 启用备用方案
                return await self._clone_fish_voice_backup(title, voice_file, filename, content_type, pid)

        except Exception as e:
            failed_voice_model = VoiceModel(
                voice_name=title,
                pid=pid,
                remark="Fish克隆声音失败",
                status=2,
                del_flag=2
            )
            self.voice_model_service.save(failed_voice_model)

            raise HTTPException(status_code=500, detail=f"声音克隆失败: {str(e)}")

    async def _clone_fish_voice_backup(self, title: str, voice_file: bytes, filename: str, content_type: str, pid: int):
        """备用声音克隆服务"""
        try:
            logger.info("开始使用备用服务克隆声音")

            # 构建表单数据
            form_data = aiohttp.FormData()
            form_data.add_field('title', title)
            form_data.add_field('voice_file', voice_file,
                                filename=filename,
                                content_type=content_type)
            form_data.add_field('pid', str(pid))

            logger.info(f"备用服务克隆请求参数: title={title}, filename={filename}")

            # 调用备用克隆服务
            result = await FishRemoteService.backup_audio_clone(form_data=form_data)

            # 从响应中正确获取数据
            if not result or 'data' not in result:
                raise ValueError("备用服务返回数据格式错误")

            data = result['data']
            if not data.get('backservice_code') == 200:
                raise ValueError("备用服务克隆失败")

            # 保存到数据库
            voice_model = VoiceModel(
                voice_name=title,
                voice_url=data.get('back_voice_url'),
                back_clone_name=data.get('back_clone_name'),
                back_voice_url=data.get('back_voice_url'),
                pid=pid,
                remark="备用服务克隆声音",
                status=1,
                ref_text=data.get('ref_text')
            )
            self.voice_model_service.save(voice_model)

            return {
                "message": "声音克隆成功(备用服务)",
                "code": 200,
                "clone_name": data.get('back_clone_name'),
                "voice_name": title,
                "back_voice_url": data.get('back_voice_url'),
                "ref_text": data.get('ref_text')
            }

        except Exception as e:
            logger.error(f"备用服务克隆声音失败: {str(e)}")
            failed_voice_model = VoiceModel(
                voice_name=title,
                pid=pid,
                remark=f"备用服务克隆声音失败: {str(e)}",
                status=2,
                del_flag=2
            )
            self.voice_model_service.save(failed_voice_model)
            raise HTTPException(status_code=500, detail=f"备用服务克隆声音失败: {str(e)}")

    async def generate_fish_audio(self, task_id: int, pid: int, voice_name: str,
                                  voice_id: Optional[int] = None, content: Optional[str] = None):
        """生成音频"""
        # 保存原始任务ID，以便在异常处理中使用
        original_task_id = task_id
        
        try:
            # 1. 处理内容
            generate_text = await self._prepare_generate_text(task_id, content)
            # 确保文本编码正确
            if isinstance(generate_text, bytes):
                generate_text = generate_text.decode('utf-8', errors='ignore')

            # 2. 获取任务
            task = self.task_service.find_by_id(Task(id=task_id))
            if not task:
                raise HTTPException(status_code=404, detail="任务不存在")

            # 保存关键的任务属性
            task_id = task.id
            task_pid = task.pid
            task_audio_model_id = task.audio_model_id
            task_title = task.title
            
            # 3. 获取或创建语音模型
            voice_model = None
            voice_model_clone_name = None
            voice_model_back_clone_name = None
            voice_model_back_voice_url = None
            voice_model_ref_text = None
            
            if voice_id:
                voice_model = self.voice_model_service.find_by_id(VoiceModel(id=voice_id))
                if not voice_model:
                    raise HTTPException(status_code=404, detail="未找到指定的声音模型")
                
                # 保存语音模型的关键属性
                voice_model_id = voice_model.id
                voice_model_name = voice_model.voice_name
                voice_model_url = voice_model.voice_url
                voice_model_clone_name = voice_model.clone_name
                voice_model_back_clone_name = voice_model.back_clone_name
                voice_model_back_voice_url = voice_model.back_voice_url
                voice_model_ref_text = voice_model.ref_text

            # 记录语音模型信息
            logger.info(
                f"开始处理语音模型, voice_id={voice_id}, " + 
                f"clone_name={voice_model_clone_name}, " + 
                f"back_clone_name={voice_model_back_clone_name}")

            # 5. 处理结果文本
            result_new_content = generate_text.split("</think>")[-1]
            
            # 6. 尝试使用主服务
            try:
                if voice_id and not voice_model_clone_name:
                    logger.info("主服务克隆名称不存在,尝试克隆")
                    
                    # 克隆前重新获取最新的voice_model
                    fresh_voice_model = self.voice_model_service.find_by_id(VoiceModel(id=voice_id))
                    if not fresh_voice_model:
                        raise ValueError("无法获取有效的声音模型")
                    
                    # 使用新的数据库会话对象进行克隆
                    updated_model = await self._create_main_clone(fresh_voice_model)
                    
                    # 更新语音模型属性
                    if updated_model:
                        voice_model_clone_name = updated_model.clone_name
                        voice_model_back_clone_name = updated_model.back_clone_name
                        voice_model_back_voice_url = updated_model.back_voice_url
                        voice_model_ref_text = updated_model.ref_text

                # 重新获取任务对象确保会话有效
                fresh_task = self.task_service.find_by_id(Task(id=task_id))
                if not fresh_task:
                    raise ValueError(f"无法获取任务信息: {task_id}")
                
                # 保存新的任务属性
                fresh_task_id = fresh_task.id
                fresh_task_pid = fresh_task.pid

                # 使用主服务生成音频，传递属性而不是对象
                return await self._generate_fish_audio_original(
                    task=fresh_task,
                    pid=pid,
                    voice_name=voice_name,
                    text=result_new_content,
                    voice_id=voice_id,
                    back_clone_name=voice_model_back_clone_name,
                    back_voice_url=voice_model_back_voice_url,
                    ref_text=voice_model_ref_text
                )

            except Exception as e:
                logger.error(f"主服务生成失败,尝试使用备用服务: {str(e)}")

                # 如果主服务失败,确保有备用克隆名称
                if voice_id and not voice_model_back_clone_name:
                    logger.info("备用服务克隆名称不存在,尝试克隆")
                    
                    # 使用最新的数据库会话对象进行克隆
                    fresh_voice_model = self.voice_model_service.find_by_id(VoiceModel(id=voice_id))
                    if fresh_voice_model:
                        updated_model = await self._create_backup_clone(fresh_voice_model)
                        if updated_model:
                            voice_model_back_clone_name = updated_model.back_clone_name
                            voice_model_back_voice_url = updated_model.back_voice_url
                            voice_model_ref_text = updated_model.ref_text

                # 重新获取任务对象以确保会话有效
                fresh_task = self.task_service.find_by_id(Task(id=task_id))
                if not fresh_task:
                    # 如果无法获取新的任务对象，则使用保存的属性创建一个新的任务对象
                    logger.warning(f"无法重新获取任务ID为 {task_id} 的对象，使用属性值")
                    fresh_task = Task(id=task_id, pid=task_pid)

                # 使用备用服务，传递属性而不是对象
                return await self._generate_fish_audio_backup(
                    task=fresh_task,
                    generate_text=result_new_content,
                    back_clone_name=voice_model_back_clone_name,
                    back_voice_url=voice_model_back_voice_url,
                    ref_text=voice_model_ref_text
                )

        except Exception as e:
            error_message = f"音频生成完全失败: {str(e)}"
            logger.error(error_message)

            # 不使用旧的task对象，而是重新查询
            try:
                fresh_task = self.task_service.find_by_id(Task(id=task_id))
                if fresh_task:
                    fresh_task.progress = 6
                    fresh_task.remark = error_message
                    self.task_service.save(fresh_task)
            except Exception as task_error:
                logger.error(f"更新任务状态失败: {str(task_error)}")

            raise HTTPException(status_code=500, detail=error_message)

    async def _prepare_generate_text(self, task_id: int, content: Optional[str] = None) -> str:
        """准备生成文本"""
        try:
            # 如果提供了内容，使用LLM处理
            if content:
                processed_content = await self._process_content_with_llm(content)

                # 获取任务信息以获取pid
                task = self.task_service.find_by_id(Task(id=task_id))
                if not task:
                    logger.warning(f"未找到任务: task_id={task_id}")
                    return processed_content

                # 保存处理后的内容到work表
                await self.work_service.update_work_content(
                    task_id=task_id,
                    content=processed_content,
                    pid=task.pid
                )

                return processed_content
            else:
                # 如果没有提供内容，尝试从work表获取
                logger.info(f"尝试从work表获取内容: task_id={task_id}")
                work = await self.work_service.query_work_by_type(task_id=task_id, work_type=1)
                if not work or not work.content:
                    logger.warning(f"未找到文字稿内容: task_id={task_id}")
                    raise ValueError("未找到文字稿内容，无法生成音频")

                content = work.content
                if isinstance(content, bytes):
                    content = content.decode('utf-8', errors='ignore')

                return content
        except Exception as e:
            logger.error(f"准备生成文本失败: {str(e)}")
            raise HTTPException(status_code=500, detail=f"准备生成文本失败: {str(e)}")

    async def _create_main_clone(self, voice_model: VoiceModel) -> VoiceModel:
        """创建主服务克隆"""
        try:
            # 保存关键ID变量
            voice_model_id = voice_model.id if voice_model else None
            voice_name = voice_model.voice_name if voice_model else None
            voice_url = voice_model.voice_url if voice_model else None
            
            # 确保在当前会话中有效
            if voice_model_id:
                voice_model = self.voice_model_service.find_by_id(VoiceModel(id=voice_model_id))
                if not voice_model:
                    logger.error(f"无法从数据库获取有效的voice_model: id={voice_model_id}")
                    return None
                # 刷新更新对象属性
                voice_name = voice_model.voice_name
                voice_url = voice_model.voice_url

            logger.info(f"开始从URL下载音频文件: {voice_url}")
            if not voice_url:
                logger.error("Voice URL 为空")
                return voice_model
            
            try:
                voice_file = await self.download_file(voice_url)
                if not voice_file or len(voice_file) < 1024 * 10:  # 至少10KB
                    logger.error(f"下载的音频文件过小或为空: {len(voice_file) if voice_file else 0} 字节")
                    return voice_model
                
                logger.info(f"成功下载音频文件，大小: {len(voice_file)/1024:.2f}KB")
            except Exception as download_error:
                logger.error(f"下载音频文件失败: {str(download_error)}")
                return voice_model

            form_data = aiohttp.FormData()
            form_data.add_field('title', voice_name)
            form_data.add_field('voice_file', voice_file,
                                filename=f"{voice_name}.wav",
                                content_type="audio/wav")

            try:
                logger.info("开始调用Fish服务克隆声音")
                clone_result = await FishRemoteService.audio_clone(form_data=form_data)
                if not clone_result or 'data' not in clone_result:
                    logger.error(f"Fish服务返回无效结果: {clone_result}")
                    return voice_model
                
                clone_id = clone_result['data'].get('_id', '')
                if not clone_id:
                    logger.error("Fish服务返回的克隆ID为空")
                    return voice_model
                
                # 异步操作完成后，重新从数据库获取最新的对象
                if voice_model_id:
                    # 重新查询以获取新会话中的对象
                    fresh_voice_model = self.voice_model_service.find_by_id(VoiceModel(id=voice_model_id))
                    if fresh_voice_model:
                        fresh_voice_model.clone_name = clone_id
                        self.voice_model_service.update(fresh_voice_model)
                        logger.info(f"主服务克隆成功,clone_name: {clone_id}")
                        return fresh_voice_model
                
                # 如果无法使用数据库对象，则返回一个包含所需属性的新对象
                result_model = VoiceModel(
                    id=voice_model_id,
                    voice_name=voice_name,
                    voice_url=voice_url,
                    clone_name=clone_id,
                    back_clone_name=voice_model.back_clone_name if voice_model else None,
                    back_voice_url=voice_model.back_voice_url if voice_model else None,
                    ref_text=voice_model.ref_text if voice_model else None
                )
                logger.info(f"主服务克隆成功,clone_name: {clone_id} (返回新对象)")
                return result_model
            except Exception as e:
                logger.error(f"调用Fish服务克隆失败: {str(e)}")
                # 不抛出异常，允许流程继续尝试备用方案
                
            return voice_model

        except Exception as e:
            logger.error(f"主服务克隆失败: {str(e)}")
            # 返回原始对象，以便后续流程可以尝试备用方案
            return voice_model

    async def _create_backup_clone(self, voice_model: VoiceModel) -> VoiceModel:
        """创建备用服务克隆"""
        try:
            # 保存关键ID变量
            voice_model_id = voice_model.id if voice_model else None
            voice_name = voice_model.voice_name if voice_model else None
            voice_url = voice_model.voice_url if voice_model else None
            back_voice_url = voice_model.back_voice_url if voice_model else None
            
            # 确保在当前会话中有效
            if voice_model_id:
                voice_model = self.voice_model_service.find_by_id(VoiceModel(id=voice_model_id))
                if not voice_model:
                    logger.error(f"无法从数据库获取有效的voice_model: id={voice_model_id}")
                    return None
                # 刷新变量
                voice_name = voice_model.voice_name
                voice_url = voice_model.voice_url
                back_voice_url = voice_model.back_voice_url

            logger.info(f"开始从URL下载音频文件用于备用服务克隆")
            download_url = voice_url if voice_url else back_voice_url
            if not download_url:
                logger.error("没有可用的下载URL")
                return voice_model
            
            try:
                voice_file = await self.download_file(download_url)
                if not voice_file or len(voice_file) < 1024 * 10:  # 至少10KB
                    logger.error(f"下载的音频文件过小或为空: {len(voice_file) if voice_file else 0} 字节")
                    return voice_model
                
                logger.info(f"成功下载音频文件，大小: {len(voice_file)/1024:.2f}KB")
            except Exception as download_error:
                logger.error(f"下载音频文件失败: {str(download_error)}")
                return voice_model

            form_data = aiohttp.FormData()
            form_data.add_field('title', voice_name)
            form_data.add_field('voice_file', voice_file,
                                filename=f"{voice_name}.wav",
                                content_type="audio/wav")

            try:
                result = await FishRemoteService.backup_audio_clone(form_data=form_data)
                data = result.get('data', {})

                if not result or not data.get('backservice_code') == 200:
                    logger.error(f"备用服务克隆失败, 响应: {result}")
                    return voice_model

                # 获取克隆信息
                back_clone_name = data.get('back_clone_name', '')
                new_back_voice_url = data.get('back_voice_url', '')
                ref_text = data.get('ref_text', '')
                
                # 异步操作完成后，重新从数据库获取最新的对象
                if voice_model_id:
                    fresh_voice_model = self.voice_model_service.find_by_id(VoiceModel(id=voice_model_id))
                    if fresh_voice_model:
                        fresh_voice_model.back_clone_name = back_clone_name
                        fresh_voice_model.back_voice_url = new_back_voice_url
                        fresh_voice_model.ref_text = ref_text
                        self.voice_model_service.update(fresh_voice_model)
                        logger.info(f"备用服务克隆成功: {back_clone_name}")
                        return fresh_voice_model
                
                # 如果无法使用数据库对象，则返回一个包含所需属性的新对象
                result_model = VoiceModel(
                    id=voice_model_id,
                    voice_name=voice_name,
                    voice_url=voice_url,
                    clone_name=voice_model.clone_name if voice_model else None,
                    back_clone_name=back_clone_name,
                    back_voice_url=new_back_voice_url,
                    ref_text=ref_text
                )
                logger.info(f"备用服务克隆成功: {back_clone_name} (返回新对象)")
                return result_model

            except Exception as e:
                logger.error(f"备用服务克隆失败: {str(e)}")
                return voice_model

        except Exception as e:
            logger.error(f"备用服务克隆失败: {str(e)}")
            return voice_model

    async def _generate_fish_audio_original(self, task: Task, pid: int, voice_name: str, text: str,
                                            voice_id: Optional[int] = None, back_clone_name: str = None,
                                            back_voice_url: str = None, ref_text: str = None):
        """原始Fish服务生成音频"""
        try:
            # 在操作前保存任务ID和其他关键变量，而不是直接使用对象
            task_id = task.id if task else None
            if not task_id:
                raise ValueError("无效的任务ID")

            if not voice_name:
                raise ValueError("无法获取有效的voice_name")

            json_data = {
                "task_id": task_id,
                "pid": pid,
                "voice_name": voice_name,
                "text": text,
                "server_name": "Fish"
            }

            logger.info(f"调用Fish音频生成服务: voice_name={voice_name}, text长度={len(text)}")
            try:
                # 异步调用
                result = await FishRemoteService.audio_generate_async(json_data=json_data)
            except Exception as e:
                logger.error(f"调用Fish音频生成服务失败: {str(e)}")
                raise

            # 处理二进制响应或URL响应，获取audio_url
            audio_url = self._extract_audio_url(result)
            if not audio_url:
                raise ValueError(f"无法获取音频URL: {result}")

            logger.info(f"最终获取的音频URL: {audio_url}")

            # 保存到embedding_file表
            embedding_file_info = self.embedding_file_service.save(
                EmbeddingFile(
                    pid=pid,
                    file_url=audio_url,
                    emb_type=5,
                    source="Fish音频合成"
                )
            )

            # 准备标题
            title = text[:60]
            if isinstance(title, bytes):
                title = title.decode('utf-8', errors='ignore')
            else:
                title = str(title)

            # 保存work记录
            await self.work_service.save(
                ReqWorkSaveSchema(
                    task_id=task_id,
                    work_type=2,
                    file_id=embedding_file_info.get("id"),
                    status=1,
                    pid=pid,
                    title=title,
                    audio_model_id=str(voice_id) if voice_id else None
                )
            )

            # 重新获取任务对象，更新状态
            fresh_task = self.task_service.find_by_id(Task(id=task_id))
            if fresh_task:
                fresh_task.progress = 5  # 音频生成完成
                self.task_service.save(fresh_task)

            return {"audio_url": audio_url}

        except Exception as e:
            logger.error(f"原始Fish服务生成音频失败: {str(e)}")
            # 重新获取任务对象，记录错误
            try:
                fresh_task = self.task_service.find_by_id(Task(id=task_id))
                if fresh_task:
                    fresh_task.progress = 6  # 音频生成失败
                    fresh_task.remark = str(e)
                    self.task_service.save(fresh_task)
            except Exception as session_error:
                logger.error(f"更新任务状态失败: {str(session_error)}")
            raise ValueError(f"原始Fish服务生成音频失败: {str(e)}")

    def _extract_audio_url(self, result):
        """从各种可能的响应格式中提取音频URL"""
        audio_url = None
        
        # 处理二进制响应
        if isinstance(result, dict) and result.get('is_binary'):
            logger.info(f"收到二进制音频数据")
            
            binary_data = result.get('binary_data')
            if not binary_data:
                return None
                
            content_type = result.get('content_type', 'audio/mpeg')
            filename = f"generate_voice_{uuid.uuid4()}.mp3"
            
            try:
                oss_path = f'audio/generated/{filename}'
                oss_util = OssUtil()
                
                # 添加重试逻辑
                max_retries = 3
                retry_count = 0
                upload_success = False
                
                while retry_count < max_retries and not upload_success:
                    try:
                        logger.info(f"尝试上传音频文件到OSS (尝试 {retry_count + 1}/{max_retries}): {oss_path}")
                        oss_util.bucket.put_object(
                            oss_path,
                            binary_data,
                            headers={'Content-Type': content_type, 'Content-Disposition': f'inline; filename="{filename}"'}
                        )
                        
                        oss_file = oss_util.get_object(oss_path)
                        upload_success = True
                        logger.info(f"音频文件上传成功: {oss_path}")
                        return self.clean_url(oss_file.data.signed_url)
                    except Exception as retry_error:
                        retry_count += 1
                        error_msg = f"上传音频文件到OSS失败 (尝试 {retry_count}/{max_retries})"
                        logger.error(error_msg)

                        if retry_count < max_retries:
                            import random
                            import time
                            retry_interval = 2 + (random.random() * 3)  # 2-5秒间随机等待时间
                            logger.info(f"等待 {retry_interval:.2f} 秒后重试上传")
                            time.sleep(retry_interval)
                        else:
                            logger.error(f"上传音频文件到OSS失败，已达最大重试次数: {oss_path}")
                            raise  # 重新抛出异常，触发外层的异常处理
                
                return None  # 这行代码不会执行，因为成功时会直接返回，失败时会抛出异常
            except Exception as e:
                logger.error(f"上传音频文件到OSS失败: {str(e)}")
                return None
                
        # 处理URL响应
        if isinstance(result, str):
            audio_url = result
        elif isinstance(result, dict):
            if 'body' in result:
                try:
                    response_data = json.loads(result['body']) if isinstance(result['body'], str) else result['body']
                    audio_url = response_data.get('data')
                except Exception:
                    pass
            elif 'data' in result:
                audio_url = result['data']
            elif 'audio_url' in result:
                audio_url = result['audio_url']
                
        return audio_url

    async def _generate_fish_audio_backup(self, task: Task, generate_text: str, back_clone_name: str,
                                          back_voice_url: str, ref_text: str):
        """备用服务生成音频"""
        logger.info("开始使用备用服务生成音频")
        try:
            # 保存任务ID和pid，不直接使用对象
            task_id = task.id if task else None
            pid = task.pid if task else None
            audio_model_id = task.audio_model_id if task else None
            
            if not task_id or not pid:
                raise ValueError("无效的任务ID或项目ID")

            # 准备请求数据
            json_data = {
                "generate_text": generate_text,
                "back_clone_name": back_clone_name,
                "back_voice_url": back_voice_url if back_voice_url else "",
                "ref_text": ref_text if ref_text else ""
            }

            # 调用备用服务
            try:
                result = FishRemoteService.backup_audio_generate(json_data=json_data)
            except Exception as e:
                logger.error(f"调用备用服务失败: {str(e)}")
                raise

            # 处理响应，获取audio_url
            audio_url = self._extract_backup_audio_url(result)
            if not audio_url:
                # 如果仍无法获取URL，尝试直接调用
                audio_url = await self._direct_call_backup_service(generate_text, back_clone_name)
                
            if not audio_url:
                raise ValueError("无法获取有效的音频URL")

            logger.info(f"最终获取的音频URL: {audio_url}")

            # 保存到embedding_file表 - 使用新的会话
            embedding_file_info = self.embedding_file_service.save(
                EmbeddingFile(
                    pid=pid,
                    file_url=audio_url,
                    emb_type=5,
                    source="备用服务音频合成"
                )
            )

            # 保存到work表
            title = generate_text[:60]
            if isinstance(title, bytes):
                title = title.decode('utf-8', errors='ignore')
            else:
                title = str(title)

            await self.work_service.save(
                ReqWorkSaveSchema(
                    task_id=task_id,
                    work_type=2,
                    file_id=embedding_file_info.get("id"),
                    status=1,
                    pid=pid,
                    title=title,
                    audio_model_id=str(audio_model_id) if audio_model_id else None
                )
            )

            # 更新任务状态 - 重新获取任务对象而不是使用传入的对象
            fresh_task = self.task_service.find_by_id(Task(id=task_id))
            if fresh_task:
                fresh_task.progress = 5  # 音频生成完成
                self.task_service.save(fresh_task)

            return {"audio_url": audio_url}

        except Exception as e:
            error_message = f"备用服务生成音频失败: {str(e)}"
            logger.error(error_message)
            try:
                # 确保task_id已定义
                if task_id:
                    # 重新获取任务对象，更新失败状态
                    fresh_task = self.task_service.find_by_id(Task(id=task_id))
                    if fresh_task:
                        fresh_task.progress = 6  # 音频生成失败
                        fresh_task.remark = error_message
                        self.task_service.save(fresh_task)
            except Exception as session_error:
                logger.error(f"更新任务状态失败: {str(session_error)}")
            raise HTTPException(status_code=500, detail=error_message)

    def _extract_backup_audio_url(self, result):
        """从备用服务响应中提取音频URL"""
        audio_url = None
        
        # 处理二进制响应
        if isinstance(result, dict) and result.get('is_binary'):
            logger.info("收到二进制音频数据")
            binary_data = result.get('binary_data')
            if binary_data:
                content_type = result.get('content_type', 'audio/mpeg')
                filename = f"backup_voice_{int(time.time())}.mp3"
                
                try:
                    oss_path = f'audio/generated/{filename}'
                    oss_util = OssUtil()
                    oss_util.bucket.put_object(
                        oss_path,
                        binary_data,
                        headers={'Content-Type': content_type, 'Content-Disposition': f'inline; filename="{filename}"'}
                    )
                    
                    oss_file = oss_util.get_object(oss_path)
                    return self.clean_url(oss_file.data.signed_url)
                except Exception as e:
                    logger.error(f"上传音频文件到OSS失败: {str(e)}")
                    return None
        
        # 处理各种可能的URL响应格式
        if isinstance(result, str):
            audio_url = result
        elif isinstance(result, dict):
            # 直接尝试从常见字段中提取URL
            for key in ['url', 'audio_url', 'voice_url', 'data']:
                if key in result and isinstance(result[key], str):
                    return result[key]
            
            # 尝试处理嵌套字典
            if 'data' in result and isinstance(result['data'], dict):
                data = result['data']
                for key in ['url', 'audio_url', 'voice_url']:
                    if key in data and isinstance(data[key], str):
                        return data[key]
        
        return audio_url

    @staticmethod
    def clean_url(url: str) -> str:
        parsed_url = urlparse(url)
        clean_url = f"{parsed_url.scheme}://{parsed_url.netloc}{parsed_url.path}"
        decoded_url = unquote(clean_url)
        return decoded_url

    def get_audio_duration(self, input_url: str):
        try:

            # 只传递远程服务需要的参数
            remote_data = {
                "input_url": input_url
            }
            result = FishRemoteService.get_audio_duration(json_data=remote_data)
            if not result.get("duration"):
                raise ValueError("未获取到音频时长")
            return {
                "duration": result["duration"]
            }
        except Exception as e:
            logger.error(f"控制音频速度失败: {str(e)}")
            raise HTTPException(status_code=500, detail=f"获取音频时长失败: {str(e)}")

    async def control_audio_speed(self, input_url: str, speed: float, pid: int, task_id: int = None,
                                  uuid: str = None, redis_client: Redis = None) -> dict:
        """控制音频速度"""
        try:
            # 只传递远程服务需要的参数
            remote_data = {
                "input_url": input_url,
                "speed": speed
            }

            result = FishRemoteService.control_audio_speed(json_data=remote_data)
            if not result.get("output_url"):
                raise ValueError("未获取到处理后的音频URL")

            # 从原始音频URL找到对应的work记录
            original_work = await self.work_service.query_work_by_file_url(input_url)
            if not original_work:
                raise ValueError("未找到原始音频记录")

            audio_duration = self.get_audio_duration(
                input_url=result.get("output_url")
            )
            # 保存到 embedding_file 表
            embedding_file_info = self.embedding_file_service.save(
                EmbeddingFile(
                    pid=pid,
                    file_url=result["output_url"],
                    emb_type=5,
                    source=f"音频变速处理(x{speed})",
                    duration=audio_duration["duration"]
                )
            )

            # 创建音频作品记录到 work 表，保持原始task_id
            await self.work_service.save(
                ReqWorkSaveSchema(
                    task_id=task_id or original_work.task_id,  # 优先使用传入的task_id
                    work_type=2,  # 2表示音频类型
                    file_id=embedding_file_info.get("id"),
                    status=1,  # 1表示正常状态
                    pid=pid,
                    remark=f"音频变速处理(x{speed})"  # 使用处理说明作为备注
                )
            )

            # 如果提供了 uuid 和 task_id，更新历史记录
            if uuid and task_id:
                from common.service.AgentsHistoryService import AgentsHistoryService
                agents_history = AgentsHistoryService(self.db).get_by_pid_and_task_id(pid, task_id)
                if agents_history:
                    try:
                        # 解析 history 字段
                        history_str = agents_history.history.decode('utf-8')
                        # 处理可能的转义字符
                        if history_str.startswith('"') and history_str.endswith('"'):
                            history_str = history_str[1:-1].encode().decode('unicode_escape')

                        import json
                        from datetime import datetime
                        history_list = json.loads(history_str)

                        # 查找并更新指定 uuid 的内容
                        updated = False
                        for item in history_list:
                            if item.get('uuid') == uuid:
                                content = json.loads(item['content'])
                                item['content'] = json.dumps({
                                    "audio_url": content['audio_url'],
                                    "new_audio_url": result["output_url"],
                                    "speed": speed,
                                    "duration": audio_duration["duration"]
                                })
                                updated = True
                                break

                        if not updated:
                            logger.warning(f"未找到匹配的 uuid: {uuid}")
                        else:
                            # 将数据转换为 UTF-8 格式存储
                            history_json = json.dumps(history_list, ensure_ascii=False)
                            history_bytes = history_json.encode('utf-8')

                            # 更新 history 字段
                            self.db.query(AgentsHistory).filter(
                                AgentsHistory.id == agents_history.id
                            ).update({
                                'history': history_bytes,
                                'updated_at': datetime.now()
                            })

                            self.db.commit()

                            # 同步更新Redis缓存
                            if redis_client:
                                await RedisHistoryUtil.sync_history_to_redis(redis_client, task_id, history_list)

                    except Exception as e:
                        logger.error(f"更新 AgentsHistory 失败: {str(e)}")
                        raise HTTPException(status_code=500, detail=f"更新 AgentsHistory 失败: {str(e)}")

            return {
                "output_url": result["output_url"],
                "speed": speed,
                "file_id": embedding_file_info.get("id"),
                "duration": audio_duration["duration"]
            }

        except Exception as e:
            logger.error(f"控制音频速度失败: {str(e)}")
            raise HTTPException(status_code=500, detail=f"控制音频速度失败: {str(e)}")

    async def download_file(self, url: str) -> bytes:
        async with aiohttp.ClientSession() as session:
            async with session.get(url) as response:
                return await response.read()

    async def _process_content_with_llm(self, content: str) -> str:
        """使用大语言模型处理内容"""
        try:
            system_prompt = """你的主要任务就是根据用户的请求去分析用户需要转换为音频的内容即可，你负责识别并提取，保证需要转换为音频的内容与用户输入完全一样，只需要输出提取结果即可。
            优秀的提取示例： 用户输入：音频专家，请将以下文案生成音频： [ 到底还有谁没来宏源期货开户？品种研报、交易软件、策略机会、专属服务]，你的输出：到底还有谁没来宏源期货开户？品种研报、交易软件、策略机会、专属服务
            """
            context: AppContext = ctx.__getattr__("context")

            # 首先尝试使用 OpenAI
            try:
                client = OpenAI(api_key=context.llm_config.one_api.api_key[0],
                                base_url=context.llm_config.one_api.base_url[0])
                response = client.chat.completions.create(
                    model="gpt-4o",
                    messages=[
                        {"role": "system", "content": system_prompt},
                        {"role": "user", "content": content},
                    ],
                    temperature=0.1,
                    stream=False
                )
                return response.choices[0].message.content

            except Exception as e:
                logger.error(f"OpenAI API调用失败，尝试使用ChatGLM: {str(e)}")
                # 如果 OpenAI 失败，尝试使用 ChatGLM
                from zhipuai import ZhipuAI
                client = ZhipuAI(api_key=context.zhipuai.api_key)
                response = client.chat.completions.create(
                    model="glm-4-flash",
                    messages=[
                        {"role": "system", "content": system_prompt},
                        {"role": "user", "content": content},
                    ],
                    temperature=0.8
                )
                return response.choices[0].message.content

        except Exception as e:
            logger.error(f"处理内容失败: {str(e)}")
            raise HTTPException(status_code=500, detail="处理用户请求暂时不可用")

    async def update_audio_durations(self):
        """更新所有音频作品的持续时间"""
        try:
            # 查询所有音频类型的作品
            audio_works = self.db.query(Work) \
                .join(EmbeddingFile, Work.file_id == EmbeddingFile.id, isouter=True) \
                .filter(
                and_(
                    Work.work_type == 2,
                    EmbeddingFile.emb_type == 5,
                    EmbeddingFile.duration.is_(None)
                )
            ).all()
            logger.info(f"开始更新 {len(audio_works)} 个音频作品的持续时间")

            updated_count = 0
            error_count = 0

            # 遍历所有音频作品
            for work in audio_works:
                try:
                    # 查询对应的文件URL
                    audio_info = self.embedding_file_service.find_by_id(EmbeddingFile(id=work.file_id))

                    if not audio_info or not audio_info.file_url:
                        logger.warning(f"作品ID {work.id} 没有找到有效的文件URL")
                        continue
                        # 获取音频持续时间
                    remote_data = {
                        "input_url": audio_info.file_url
                    }
                    result = FishRemoteService.get_audio_duration(json_data=remote_data)

                    if not result or "duration" not in result:
                        logger.warning(f"无法获取作品ID {work.id} 的音频持续时间")
                        continue

                    # 更新作品的持续时间
                    duration = result["duration"]
                    audio_info.duration = duration
                    self.embedding_file_service.update(audio_info)

                    logger.info(f"已更新作品ID {work.id},文件 ID {audio_info.id} 的持续时间: {duration}秒")
                    updated_count += 1

                except Exception as e:
                    error_count += 1
                    logger.error(f"更新作品ID {work.id} ,文件 ID {audio_info.id} 的持续时间时出错: {str(e)}")
                    self.db.rollback()

            logger.info(f"音频持续时间更新完成: 成功 {updated_count}, 失败 {error_count}")
            return {"updated": updated_count, "errors": error_count}

        except Exception as e:
            logger.error(f"更新音频持续时间过程中发生错误: {str(e)}")
            raise HTTPException(status_code=500, detail=f"更新音频持续时间失败: {str(e)}")

    async def _direct_call_backup_service(self, text: str, voice_id: str):
        """直接调用备用服务获取音频"""
        try:
            # 构建直接请求URL的参数
            direct_params = {
                "text": text,
                "voice_id": voice_id,
                "format": "mp3"
            }

            # 直接调用远程服务获取音频
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f"{FishRemoteService.context.aip.remote.audio}/direct/tts",
                    json=direct_params, 
                    timeout=60
                ) as response:
                    if response.status == 200:
                        content_type = response.headers.get('content-type', 'audio/mpeg')
                        if content_type.startswith('audio/'):
                            # 获取二进制音频数据
                            binary_data = await response.read()
                            filename = f"direct_voice_{int(time.time())}.mp3"

                            # 上传到OSS
                            oss_path = f'audio/generated/{filename}'
                            oss_util = OssUtil()
                            oss_util.bucket.put_object(
                                oss_path,
                                binary_data,
                                headers={'Content-Type': content_type,
                                        'Content-Disposition': f'inline; filename="{filename}"'}
                            )

                            oss_file = oss_util.get_object(oss_path)
                            return self.clean_url(oss_file.data.signed_url)
                        else:
                            # 如果不是音频，尝试解析JSON响应
                            resp_json = await response.json()
                            logger.info(f"直接调用远程服务返回JSON: {resp_json}")
                            if 'url' in resp_json:
                                return resp_json['url']
                            elif 'audio_url' in resp_json:
                                return resp_json['audio_url']
                            elif 'data' in resp_json and isinstance(resp_json['data'], str):
                                return resp_json['data']
            return None
        except Exception as e:
            logger.error(f"直接调用远程服务获取音频失败: {str(e)}")
            return None

    async def clone_fish_voice_by_url(self, title: str, voice_url: str, pid: int):
        """
        通过OSS URL进行Fish复刻声音
        
        Args:
            title: 声音标题
            voice_url: OSS音频文件URL
            pid: IP ID
        
        Returns:
            克隆结果信息
        """
        # 验证参数
        if not title or not voice_url or not pid:
            raise ValueError("标题、音频URL和IP ID不能为空")
        
        # 从URL获取文件名
        filename = voice_url.split('/')[-1]
        
        # 调用Fish服务进行声音克隆
        clone_result = FishRemoteService.clone_voice({
            "voice_url": voice_url,
            "voice_name": title
        })
        
        if not clone_result or "clone_name" not in clone_result:
            raise Exception("声音克隆失败，未获取到克隆名称")
        
        # 创建声音模型记录
        voice_model = VoiceModel(
            title=title,
            pid=pid,
            voice_url=voice_url,
            voice_name=title,
            clone_name=clone_result["clone_name"],
            status=1  # 假设1表示正常状态
        )
        
        # 保存记录
        voice_model_service = VoiceModelService(self.db)
        voice_model = voice_model_service.save(voice_model)
        
        return {
            "voice_id": voice_model.id,
            "title": title,
            "voice_url": voice_url,
            "clone_name": clone_result["clone_name"]
        }
