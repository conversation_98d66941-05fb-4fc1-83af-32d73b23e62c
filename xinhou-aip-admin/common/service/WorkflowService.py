from typing import List, Optional

from fastapi import HTTPException
from loguru import logger
from sqlalchemy import select
from sqlalchemy.orm import Session

from common.entity.Agent import Agent
from common.entity.LLM import LLM
from common.entity.Tool import Tool
from common.entity.Workflow import Workflow
from common.entity.WorkflowAgent import WorkflowAgent
from common.entity.WorkflowPid import WorkflowPid


class WorkflowService:
    def __init__(self, db: Session):
        self.db = db

    def create_workflow(self, workflow: Workflow, agent_ids: List[int]) -> Workflow:
        """创建工作流及其关联的Agent"""
        try:
            # 创建工作流
            self.db.add(workflow)
            self.db.flush()

            # 创建工作流-Agent关联
            for order, agent_id in enumerate(agent_ids, 1):
                workflow_agent = WorkflowAgent(
                    workflow_id=workflow.id,
                    agent_id=agent_id,
                    execution_order=order
                )
                self.db.add(workflow_agent)

            self.db.commit()
            return workflow
        except Exception as e:
            self.db.rollback()
            logger.error(f"创建工作流失败: {str(e)}")
            raise HTTPException(status_code=500, detail=f"创建工作流失败: {str(e)}")

    def update_workflow(self, workflow: Workflow, agent_ids: Optional[List[int]] = None) -> Workflow:
        """更新工作流及其关联的Agent"""
        try:
            existing = self.db.query(Workflow).filter(Workflow.id == workflow.id).first()
            if not existing:
                raise HTTPException(status_code=404, detail="工作流不存在")

            # 更新工作流基本信息
            for key, value in workflow.__dict__.items():
                if value is not None and key != '_sa_instance_state':
                    setattr(existing, key, value)

            # 如果提供了新的agent_ids，更新工作流-Agent关联
            if agent_ids is not None:
                # 删除现有关联
                self.db.query(WorkflowAgent).filter(
                    WorkflowAgent.workflow_id == workflow.id
                ).delete()

                # 创建新的关联
                for order, agent_id in enumerate(agent_ids, 1):
                    workflow_agent = WorkflowAgent(
                        workflow_id=workflow.id,
                        agent_id=agent_id,
                        execution_order=order
                    )
                    self.db.add(workflow_agent)

            self.db.commit()
            return existing
        except Exception as e:
            self.db.rollback()
            logger.error(f"更新工作流失败: {str(e)}")
            raise HTTPException(status_code=500, detail=f"更新工作流失败: {str(e)}")

    def get_workflow_agents(self, workflow_id: int) -> List[dict]:
        """获取工作流关联的所有Agent信息"""
        # 联合查询获取Agent和LLM信息
        stmt = select(
            Agent, LLM
        ).join(
            WorkflowAgent, WorkflowAgent.agent_id == Agent.id
        ).join(
            LLM, Agent.llm_id == LLM.id
        ).where(
            WorkflowAgent.workflow_id == workflow_id,
            WorkflowAgent.del_flag == 1,
            Agent.del_flag == 1,
            LLM.del_flag == 1
        ).order_by(WorkflowAgent.execution_order)

        results = self.db.execute(stmt).all()

        result = []
        for agent, llm in results:
            agent_info = {
                "id": agent.id,
                "agent_name_cn": agent.agent_name_cn,
                "agent_name_en": agent.agent_name_en,
                "agent_code": agent.agent_code,
                "agent_type": agent.agent_type,
                "agent_role": agent.agent_role,
                "influence_scope": agent.influence_scope,
                "prompt_cn": agent.prompt_cn,
                "prompt_en": agent.prompt_en,
                "agent_style": agent.agent_style,
                "description": agent.description,
                "agent_action": agent.agent_action,
                "status": agent.status,
                "llm_name": llm.llm_name,
                "model_name": llm.model_name,
                "api_url": llm.api_url,
                "api_key": llm.api_key,
                "api_config": llm.api_config,
                "tool_ids": agent.tool_ids or ""
            }

            # 如果存在 tool_ids，获取工具信息
            if agent.tool_ids:
                tool_ids = [int(tid) for tid in agent.tool_ids.split(',') if tid]
                tools = (
                    self.db.query(Tool)
                    .filter(Tool.id.in_(tool_ids), Tool.del_flag == 1)
                    .all()
                )
                agent_info["tools"] = [{
                    "id": tool.id,
                    "tool_name": tool.tool_name,
                    "tool_function": tool.tool_function,
                    "llm_id": tool.llm_id
                } for tool in tools]
            else:
                agent_info["tools"] = []

            result.append(agent_info)

        return result

    def list_by_pid(self, pid: int, status: int = None) -> List[Workflow]:
        """通过 PID 获取工作流列表"""
        query = (
            self.db.query(Workflow)
            .join(WorkflowPid, Workflow.id == WorkflowPid.workflow_id)
            .filter(
                WorkflowPid.pid == pid,
                WorkflowPid.del_flag == 1,
                Workflow.del_flag == 1
            )
        )

        if status is not None:
            query = query.filter(Workflow.status == status)

        result = query.order_by(Workflow.id.desc()).all()

        # 如果没有找到工作流，返回默认工作流(id=1)
        if not result:
            default_workflow = (
                self.db.query(Workflow)
                .filter(
                    Workflow.is_default == 1,
                    Workflow.del_flag == 1
                )
            )
            if status is not None:
                default_workflow = default_workflow.filter(Workflow.status == status)
            result = default_workflow.all()

        return result

    def delete_workflow(self, workflow_id: int) -> bool:
        """删除工作流及关联的Agent"""
        try:
            # 先删除工作流-Agent关联
            self.db.query(WorkflowAgent).filter(
                WorkflowAgent.workflow_id == workflow_id
            ).update({"del_flag": 2})

            # 再删除工作流
            workflow = self.db.query(Workflow).filter(Workflow.id == workflow_id).first()
            if not workflow:
                raise HTTPException(status_code=404, detail="工作流不存在")

            workflow.del_flag = 2
            self.db.commit()
            return True

        except Exception as e:
            self.db.rollback()
            logger.error(f"删除工作流失败: {str(e)}")
            raise HTTPException(status_code=500, detail=f"删除工作流失败: {str(e)}")

    def get_workflow_by_pid(self, pid: int) -> Workflow:
        """通过 PID 获取工作流"""
        query = (
            self.db.query(Workflow)
            .join(WorkflowPid, Workflow.id == WorkflowPid.workflow_id)
            .filter(
                WorkflowPid.pid == pid,
                WorkflowPid.del_flag == 1,
                WorkflowPid.status == 1,
                Workflow.del_flag == 1,
                Workflow.status == 1
            )
            .order_by(Workflow.id.desc())
        )

        result = query.first()

        # 如果没有找到工作流，返回默认工作流
        if not result:
            result = (
                self.db.query(Workflow)
                .filter(
                    Workflow.is_default == 1,
                    Workflow.del_flag == 1,
                    Workflow.status == 1
                )
                .first()
            )

        return result
