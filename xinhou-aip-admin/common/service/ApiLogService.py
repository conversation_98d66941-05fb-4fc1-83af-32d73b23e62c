# !/usr/bin/python3
# -*- coding: utf-8 -*-

"""
API日志服务类
----------------------------------------------------
@Project :   xinhou-openai-admin
@File    :   ApiLogService.py
@Contact :   <EMAIL>

@Modify Time      <AUTHOR>    @Desciption
------------      -------    --------    -----------
2023/08/01 10:00  peng.shen   v1.0.0     None
"""

import json
import uuid
from typing import Optional

from fastapi import Request, Response
from loguru import logger
from sqlalchemy import insert
from xinhou_openai_framework.core.orm.service.BaseServiceImpl import BaseServiceImpl

from common.entity.ApiLog import ApiLog
from common.entity.User import User


class ApiLogService(BaseServiceImpl[ApiLog]):
    """
    API日志服务类
    """

    async def create_log(self, request: Request, response: Response,
                         execution_time: int, current_user: Optional[User] = None) -> None:
        """
        创建API日志
        :param request: 请求对象
        :param response: 响应对象
        :param execution_time: 执行时间(毫秒)
        :param current_user: 当前用户
        :return: None
        """
        try:
            # 获取请求信息
            url = str(request.url)
            method = request.method
            headers = dict(request.headers)
            client_ip = request.client.host if request.client else None
            user_agent = headers.get("user-agent", "")

            # 获取请求参数
            params = dict(request.query_params)

            # 获取请求体 - 注意：请求体可能已经被消费
            body = None
            # 我们不再尝试读取请求体，因为它可能已经被消费
            # 如果需要记录请求体，应该在中间件中提前保存

            # 获取响应信息
            response_code = response.status_code
            response_body = getattr(response, "body", None)
            response_data = None

            if response_body:
                try:
                    response_data = response_body.decode('utf-8', errors='replace')
                except:
                    response_data = str(response_body)

            # 获取用户信息
            user_id = None
            user_name = None
            if current_user:
                user_id = current_user.id
                user_name = current_user.login_name

            # 获取API描述和模块
            api_description = None
            module = None

            # 从路径中提取模块
            path_parts = request.url.path.strip('/').split('/')
            if len(path_parts) > 0:
                module = path_parts[0] if path_parts[0] else None

            # 创建日志数据
            log_data = {
                "trace_id": str(uuid.uuid4()),
                "user_id": user_id,
                "user_name": user_name,
                "client_ip": client_ip,
                "user_agent": user_agent,
                "request_url": url,
                "request_method": method,
                "request_params": json.dumps(params, ensure_ascii=False) if params else None,
                "request_body": body,  # 可能为None
                "response_code": response_code,
                "response_data": response_data,
                "error_message": None,  # 错误信息在异常处理中设置
                "execution_time": execution_time,
                "api_description": api_description,
                "module": module,
                "status": 1 if 200 <= response_code < 400 else 0  # 成功状态
            }

            # 保存日志 - 直接执行SQL插入
            try:
                # 使用原生SQL插入
                stmt = insert(ApiLog).values(**log_data)
                self.db.execute(stmt)
                self.db.commit()
            except Exception as e:
                logger.error(f"保存日志失败: {str(e)}")

        except Exception as e:
            logger.error(f"创建API日志失败: {str(e)}")

    async def create_error_log(self, request: Request, error: Exception,
                               execution_time: int, current_user: Optional[User] = None) -> None:
        """
        创建错误日志
        :param request: 请求对象
        :param error: 异常对象
        :param execution_time: 执行时间(毫秒)
        :param current_user: 当前用户
        :return: None
        """
        try:
            # 获取请求信息
            url = str(request.url)
            method = request.method
            headers = dict(request.headers)
            client_ip = request.client.host if request.client else None
            user_agent = headers.get("user-agent", "")

            # 获取请求参数
            params = dict(request.query_params)

            # 获取请求体 - 注意：请求体可能已经被消费
            body = None
            # 我们不再尝试读取请求体，因为它可能已经被消费

            # 获取用户信息
            user_id = None
            user_name = None
            if current_user:
                user_id = current_user.id
                user_name = current_user.login_name

            # 获取API描述和模块
            api_description = None
            module = None

            # 从路径中提取模块
            path_parts = request.url.path.strip('/').split('/')
            if len(path_parts) > 0:
                module = path_parts[0] if path_parts[0] else None

            # 创建日志数据
            log_data = {
                "trace_id": str(uuid.uuid4()),
                "user_id": user_id,
                "user_name": user_name,
                "client_ip": client_ip,
                "user_agent": user_agent,
                "request_url": url,
                "request_method": method,
                "request_params": json.dumps(params, ensure_ascii=False) if params else None,
                "request_body": body,  # 可能为None
                "response_code": 500,  # 错误状态码
                "response_data": None,
                "error_message": str(error),
                "execution_time": execution_time,
                "api_description": api_description,
                "module": module,
                "status": 0  # 失败状态
            }

            # 保存日志 - 直接执行SQL插入
            try:
                # 使用原生SQL插入
                stmt = insert(ApiLog).values(**log_data)
                self.db.execute(stmt)
                self.db.commit()
            except Exception as e:
                logger.error(f"保存日志失败: {str(e)}")

        except Exception as e:
            logger.error(f"创建错误日志失败: {str(e)}")
