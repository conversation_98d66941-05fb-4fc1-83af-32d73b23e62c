# !/usr/bin/python3
# -*- coding: utf-8 -*-

"""
作品信息表服务类
----------------------------------------------------
@Project :   xinhou-openai-admin
@File    :   WorkService.py
@Contact :   <EMAIL>

@Modify Time      <AUTHOR>    @Desciption
------------      -------    --------    -----------
2024/07/17 10:00  assistant  v1.0.0      作品表服务类
"""
from typing import Dict, Any, List, Optional

from fastapi import HTTPException, UploadFile
from loguru import logger
from sqlalchemy import and_
from sqlalchemy import select, or_, desc
from sqlalchemy.orm import Session, aliased
from xinhou_openai_framework.core.orm.service.BaseServiceImpl import BaseServiceImpl

from apps.admin.schema.WorkSchema import ReqWorkFindSchema, ReqWorkSaveSchema, ReqWorkUpdateSchema
from common.entity.AgentsHistory import AgentsHistory
from common.entity.EmbeddingFile import EmbeddingFile
from common.entity.Work import Work
from common.remote.AudioRemoteService import FishRemoteService
from common.service.AgentsHistoryService import AgentsHistoryService
from common.service.EmbeddingFileService import EmbeddingFileService
from common.utils.FilesUtils import save_files_v1_oss
from common.utils.RedisHistoryUtil import RedisHistoryUtil


class WorkService(BaseServiceImpl[Work]):
    """
    作品服务类
    """

    def __init__(self, db: Session):
        super(WorkService, self).__init__(db, Work)

    def find_all(self, search: ReqWorkFindSchema) -> List[Dict[str, Any]]:
        # 创建 EmbeddingFile 的子查询
        file_subquery = (
            select(EmbeddingFile.id, EmbeddingFile.file_url, EmbeddingFile.file_review_pic)
            .correlate(Work)
            .where(EmbeddingFile.id == Work.file_id)
            .alias("file_subquery")
        )

        # 将子查询作为 Work 的一个属性
        file_alias = aliased(EmbeddingFile, file_subquery)

        # 构建主查
        query = (
            self.db.query(Work, file_alias.file_url.label('file_url'),
                          file_alias.file_review_pic.label('file_review_pic'))
            .outerjoin(file_alias, Work.file_id == file_alias.id)
        )

        # 添加过滤条件
        if search.pid:
            query = query.filter(Work.pid == search.pid)
        if search.task_id:
            query = query.filter(Work.task_id == search.task_id)
        if search.work_type:
            query = query.filter(Work.work_type == search.work_type)
        if search.status:
            query = query.filter(Work.status == search.status)

        # 添加日期范围过滤
        if search.date_from:
            query = query.filter(Work.created_at >= search.date_from)
        if search.date_to:
            query = query.filter(Work.created_at <= search.date_to)

        # 添加模糊搜索
        if search.search_term:
            search_filter = or_(
                Work.title.ilike(f"%{search.search_term}%"),
                Work.content.ilike(f"%{search.search_term}%")
            )
            query = query.filter(search_filter)

        # 添加按 id 降序排序
        query = query.order_by(desc(Work.id))

        # 执行查询并返回结果
        results = query.all()

        # 将查询结果转换为可序列化的字典列表
        return [self._to_dict(work, file_url, file_review_pic) for work, file_url, file_review_pic in results]

    def _to_dict(self, work: Work, file_url: str, file_review_pic: str) -> Dict[str, Any]:
        work_dict = {
            'id': work.id,
            'task_id': work.task_id,
            'work_type': work.work_type,
            'title': work.title,
            'content': work.content,
            'file_id': work.file_id,
            'status': work.status,
            'remark': work.remark,
            'pid': work.pid,
            'create_by': work.create_by,
            'created_at': work.created_at.isoformat() if work.created_at else None,
            'update_by': work.update_by,
            'updated_at': work.updated_at.isoformat() if work.updated_at else None,
            'file_url': file_url,
            'file_review_pic': file_review_pic,
            'audio_model_id': work.audio_model_id,
            'video_model_id': work.video_model_id
        }
        return work_dict

    async def save(self, model: ReqWorkSaveSchema):
        db_work = Work(**model.dict())
        return super().save(db_work)

    async def update(self, model: ReqWorkUpdateSchema, redis_client):
        try:
            # 如果存在 uuid，需要更新 AgentsHistory 中的内容
            if model.uuid and model.task_id and model.content:
                # 获取 AgentsHistory
                agents_history = AgentsHistoryService(self.db).get_by_pid_and_task_id(model.pid, model.task_id)
                if agents_history:
                    try:
                        # 解析 history 字段
                        history_str = agents_history.history.decode('utf-8')
                        # 处理可能的转义字符
                        if history_str.startswith('"') and history_str.endswith('"'):
                            history_str = history_str[1:-1].encode().decode('unicode_escape')

                        import json
                        from datetime import datetime
                        history_list = json.loads(history_str)
                        logger.info(history_str)

                        # 查找并更新指定 uuid 的内容，同时获取 work_id
                        updated = False
                        work_id = None
                        for item in history_list:
                            if item.get('uuid') == model.uuid:
                                item['content'] = model.content
                                work_id = item.get('work_id')
                                updated = True
                                break

                        if not updated:
                            logger.warning(f"未找到匹配的 uuid: {model.uuid}")
                            raise HTTPException(status_code=404, detail=f"未找到匹配的 uuid: {model.uuid}")

                        if not work_id:
                            logger.warning(f"未找到对应的 work_id, uuid: {model.uuid}")
                            raise HTTPException(status_code=404, detail=f"未找到对应的 work_id")

                        # 获取 Work 记录
                        existing_work = self.db.query(Work).filter(Work.id == work_id).first()
                        if not existing_work:
                            raise HTTPException(status_code=404, detail="Work not found")

                        # 将数据转换为 UTF-8 格式存储
                        history_json = json.dumps(history_list, ensure_ascii=False)
                        history_bytes = history_json.encode('utf-8')

                        # 更新 history 字段
                        self.db.query(AgentsHistory).filter(
                            AgentsHistory.id == agents_history.id
                        ).update({
                            'history': history_bytes,
                            'updated_at': datetime.now()
                        })

                        # 同步更新Redis缓存
                        await RedisHistoryUtil.sync_history_to_redis(redis_client, model.task_id, history_list)

                        # 更新 Work 表中的内容
                        if existing_work:
                            existing_work.content = model.content
                            existing_work.updated_at = datetime.now()
                            self.db.commit()

                    except Exception as e:
                        logger.error(f"更新 AgentsHistory 失败: {str(e)}")
                        raise HTTPException(status_code=500, detail=f"更新 AgentsHistory 失败: {str(e)}")

            # 提交所有更改
            self.db.commit()

            return existing_work

        except Exception as e:
            self.db.rollback()
            logger.error(f"更新失败: {str(e)}")
            raise HTTPException(status_code=500, detail=f"更新失败: {str(e)}")

    async def userUpdate(self, model: ReqWorkUpdateSchema):
        db_work = self.db.query(Work).filter(Work.task_id == model.task_id).filter(Work.work_type == 1).first()
        if not db_work:
            raise HTTPException(status_code=404, detail="Work not found")
        for key, value in model.dict().items():
            setattr(db_work, key, value)
        self.db.commit()
        self.db.refresh(db_work)
        return db_work

    async def delete(self, work: Work) -> bool:
        """
        删除作品
        Args:
            work: 作品实体
        Returns:
            bool: 删除是否成功
        """
        try:
            # 查找作品
            existing_work = self.db.query(Work).filter(
                Work.id == work.id
            ).first()

            if not existing_work:
                return False

            # 删除作品
            self.db.delete(existing_work)
            self.db.commit()

            return True
        except Exception as e:
            logger.error(f"删除作品失败: {str(e)}")
            self.db.rollback()
            return False

    async def find_work_with_task(self, search: ReqWorkFindSchema):
        query = self.db.query(Work)
        if search.task_id is not None:
            query = query.filter(Work.task_id == search.task_id)
        if search.work_type is not None:
            query = query.filter(Work.work_type == search.work_type)
        if search.status is not None:
            query = query.filter(Work.status == search.status)
        if search.pid is not None:
            query = query.filter(Work.pid == search.pid)
        return query.all()

    async def upload_audio(self, task_id, pid, file: UploadFile, db, audio_model_id: Optional[str] = None):
        file_info = await save_files_v1_oss([file])
        embedding_file_model: EmbeddingFile = EmbeddingFile(**file_info[0])
        embedding_file_model.emb_type = 5
        embedding_file_model.source = "用户上传"
        embedding_file_model.pid = pid
        remote_data = {
            "input_url": embedding_file_model.file_url
        }
        result = FishRemoteService.get_audio_duration(json_data=remote_data)

        if not result or "duration" not in result:
            logger.warning(f"无法获取文件地址: {embedding_file_model.file_url} 的音频持续时间")
        else:
            # 更新作品的持续时间
            duration = result["duration"]
            embedding_file_model.duration = duration
        embedding_file_info: EmbeddingFile = EmbeddingFileService(db).save(embedding_file_model)

        work_info = ReqWorkSaveSchema(
            task_id=task_id,
            work_type=2,  # 2表示音频类型
            file_id=embedding_file_info.get("id"),
            status=1,
            pid=pid,
            audio_model_id=audio_model_id  # 添加音频模型ID
        )
        work = await self.save(work_info)

        return embedding_file_info

    async def query_work_by_type(self, task_id: int, work_type: int):
        """
        查询指定任务的指定类型的作品
        :param task_id: 任务ID
        :param work_type: 作品类型 (1:文案, 2:音频, 3:视频)
        :return: 最新的作品记录
        """
        work = self.db.query(Work).filter(
            and_(
                Work.task_id == task_id,
                Work.work_type == work_type
            )
        ).order_by(desc(Work.created_at)).first()
        return work

    async def query_work_result(self, task_id: int, work_type: int):
        work_audio_info = self.db.query(Work).filter(
            and_(
                Work.task_id == task_id,
                Work.work_type == 2
            )
        ).first()
        work_video_info = self.db.query(Work).filter(
            and_(
                Work.task_id == task_id,
                Work.work_type == 3
            )
        ).first()
        return work_audio_info, work_video_info

    async def query_work_by_file_url(self, file_url: str) -> Work:
        """
        通过文件URL查询work记录
        :param file_url: 文件URL
        :return: Work对象或None
        """
        try:
            # 联表查询，通过file_url找到对应的work记录
            result = self.db.query(Work).join(
                EmbeddingFile,
                and_(
                    Work.file_id == EmbeddingFile.id,
                    EmbeddingFile.file_url == file_url
                )
            ).first()

            return result
        except Exception as e:
            return None

    async def query_works_by_type(self, task_id: int, work_type: int) -> List[Work]:
        """
        查询指定任务和类型的所有作品
        """
        works = self.db.query(Work).filter(
            Work.task_id == task_id,
            Work.work_type == work_type
        ).order_by(Work.created_at.desc()).all()
        return works
        
    async def query_all_works_by_task_id(self, task_id: int) -> List[Work]:
        """
        查询指定任务的所有作品
        
        Args:
            task_id: 任务ID
            
        Returns:
            所有关联的作品列表
        """
        works = self.db.query(Work).filter(
            Work.task_id == task_id
        ).order_by(Work.work_type, Work.created_at.desc()).all()
        return works
    
    async def update_work_content(self, task_id: int, content: str, pid: int = None):
        """
        更新或创建文字稿内容
        :param task_id: 任务ID
        :param content: 文字稿内容
        :param pid: 项目ID（创建新记录时需要）
        :return: 更新或创建的work记录
        """
        logger.info(f"更新文字稿内容: task_id={task_id}, content长度={len(content)}")
        
        # 查找是否已存在文字稿
        work = self.db.query(Work).filter(
            and_(
                Work.task_id == task_id,
                Work.work_type == 1  # 1表示文本类型
            )
        ).first()
        
        if work:
            # 如果存在，更新内容
            logger.info(f"找到现有文字稿记录，ID={work.id}，进行更新")
            work.content = content
            self.db.commit()
            self.db.refresh(work)
        else:
            # 如果不存在且提供了pid，创建新记录
            if pid is not None:
                logger.info(f"未找到文字稿记录，创建新记录: task_id={task_id}, pid={pid}")
                work_info = ReqWorkSaveSchema(
                    task_id=task_id,
                    work_type=1,  # 1表示文本类型
                    content=content,
                    status=1,
                    pid=pid,
                    title=content[:100] if content else "文字稿"
                )
                work = await self.save(work_info)
            else:
                logger.warning(f"未找到文字稿记录且未提供pid，无法创建新记录: task_id={task_id}")
                return None
            
        return work
