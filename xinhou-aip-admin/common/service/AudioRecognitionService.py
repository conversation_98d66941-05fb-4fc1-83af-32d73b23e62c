#!/usr/bin/python3
# -*- coding: utf-8 -*-
"""
音视频文件识别服务
----------------------------------------------------
@Project :   xinhou-openai-admin
@File    :   AudioRecognitionService.py
@Contact :   <EMAIL>

@Modify Time      <AUTHOR>    @Desciption
------------      -------    --------    -----------
2023/6/14 16:52   logic      1.0         音视频文件识别服务
"""
import json
import time
from typing import Dict, Any, Optional

from aliyunsdkcore.acs_exception.exceptions import ClientException, ServerException
from aliyunsdkcore.client import AcsClient
from aliyunsdkcore.request import CommonRequest
from loguru import logger
from xinhou_openai_framework.core.context.model.SystemContext import ctx


class AudioRecognitionService:
    """
    阿里云智能语音交互服务 - 音视频文件识别服务
    基于阿里云ISI录音文件识别API实现
    """

    # 响应参数
    KEY_TASK = "Task"
    KEY_TASK_ID = "TaskId"
    KEY_STATUS_TEXT = "StatusText"
    KEY_RESULT = "Result"

    # 状态值
    STATUS_SUCCESS = "SUCCESS"
    STATUS_RUNNING = "RUNNING"
    STATUS_QUEUEING = "QUEUEING"

    def __init__(self, ak_id: str = None, ak_secret: str = None):
        """
        初始化音视频文件识别服务
        :param ak_id: 阿里云AccessKey ID，如果为None则从上下文获取
        :param ak_secret: 阿里云AccessKey Secret，如果为None则从上下文获取
        """
        context = ctx.__getattr__("context")
        ak_id = context.aliyun_nls.access_key_id
        ak_secret = context.aliyun_nls.access_key_secret
        region_id = context.aliyun_nls.region_id
        self.KEY_APP_KEY = context.aliyun_nls.project_app_key
        self.client = AcsClient(ak_id, ak_secret, region_id)

    def submit_task(self, file_url: str, enable_words: bool = False,
                    auto_split: bool = False) -> Optional[str]:
        """
        提交音视频文件识别任务
        :param file_url: 音视频文件URL
        :param enable_words: 是否开启词级别时间戳识别
        :param auto_split: 是否开启智能分轨
        :return: 任务ID或None（如果失败）
        """
        # 如果URL包含@@@，说明是控制器传来的格式，需要提取真实URL
        if "@@@" in file_url:
            parts = file_url.split("@@@")
            if len(parts) == 2:
                file_url = parts[1]
                logger.info(f"从复合URL中提取实际文件URL: {file_url}")

        # 创建请求
        post_request = CommonRequest()
        post_request.set_domain("filetrans.cn-shanghai.aliyuncs.com")
        post_request.set_version("2018-08-17")
        post_request.set_product("nls-filetrans")
        post_request.set_action_name("SubmitTask")
        post_request.set_method('POST')
        # 设置任务参数
        task = {
            "appkey": self.KEY_APP_KEY,
            "file_link": file_url,
            "version": "4.0",
            "enable_words": bool(enable_words),  # 确保是布尔值
            "enable_sample_rate_adaptive": True
        }

        # 如果启用智能分轨
        if auto_split:
            task["auto_split"] = bool(auto_split)  # 确保是布尔值

        # 转换为JSON
        task_json = json.dumps(task)
        logger.info(f"提交的任务参数: {task_json}")

        # 添加正文参数
        post_request.add_body_params("Task", task_json)

        try:
            # 发送请求
            logger.info(f"发送请求，头部信息: {post_request.get_headers()}")
            response = self.client.do_action_with_exception(post_request)
            if isinstance(response, bytes):
                response = response.decode('utf-8')  # 确保响应是字符串

            response_dict = json.loads(response)

            logger.info(f"录音文件识别任务提交响应: {response_dict}")

            status_text = response_dict.get("StatusText")
            if status_text == "SUCCESS":
                logger.info("录音文件识别请求成功响应！")
                return response_dict.get("TaskId")
            else:
                logger.error(f"录音文件识别请求失败! 状态: {status_text}")
                return None

        except ServerException as e:
            logger.error(f"服务器异常: {e}")
            return None
        except ClientException as e:
            logger.error(f"客户端异常: {e}")
            return None
        except Exception as e:
            logger.error(f"提交任务出现未知异常: {e}, 类型: {type(e)}")
            import traceback
            logger.error(traceback.format_exc())
            return None

    def get_task_result(self, task_id: str) -> Dict[str, Any]:
        """
        获取音视频文件识别任务结果
        :param task_id: 任务ID
        :return: 任务结果字典
        """
        # 创建请求
        get_request = CommonRequest()
        get_request.set_domain("filetrans.cn-shanghai.aliyuncs.com")
        get_request.set_version("2018-08-17")
        get_request.set_product("nls-filetrans")
        get_request.set_action_name("GetTaskResult")
        get_request.set_method('GET')
        get_request.add_query_param("TaskId", task_id)

        try:
            # 发送请求
            response = self.client.do_action_with_exception(get_request)
            if isinstance(response, bytes):
                response = response.decode('utf-8')

            response_dict = json.loads(response)

            logger.debug(f"获取任务结果响应: {response_dict}")

            return response_dict
        except Exception as e:
            logger.error(f"获取任务结果异常: {e}")
            return {"StatusText": "ERROR", "ErrorMessage": str(e)}

    def wait_for_task_result(self, task_id: str, polling_interval: int = 10) -> Dict[str, Any]:
        """
        等待并轮询获取任务结果
        :param task_id: 任务ID
        :param polling_interval: 轮询间隔（秒）
        :return: 任务结果字典
        """
        while True:
            result = self.get_task_result(task_id)
            status_text = result.get("StatusText")

            # 如果任务仍在运行或排队中，继续轮询
            if status_text in ["RUNNING", "QUEUEING"]:
                logger.info(f"任务 {task_id} 状态: {status_text}，等待 {polling_interval} 秒后再次查询")
                time.sleep(polling_interval)
            else:
                # 任务已完成或出错
                logger.info(f"任务 {task_id} 完成，状态: {status_text}")
                return result

    def extract_text_from_result(self, result: Dict[str, Any]) -> Optional[str]:
        """
        从识别结果中提取纯文本内容
        :param result: 识别结果字典
        :return: 提取的文本或None（如果失败）
        """
        try:
            if result.get("StatusText") != "SUCCESS":
                logger.error(f"任务未成功完成，状态: {result.get('StatusText')}")
                return None

            result_content = result.get("Result")
            if not result_content:
                logger.error("结果内容为空")
                return None

            # 解析JSON内容
            result_obj = json.loads(result_content)
            sentences = result_obj.get("Sentences", [])

            # 拼接所有句子的文本
            full_text = " ".join(sentence.get("Text", "") for sentence in sentences)
            return full_text

        except Exception as e:
            logger.error(f"提取文本内容时出现异常: {e}")
            return None
