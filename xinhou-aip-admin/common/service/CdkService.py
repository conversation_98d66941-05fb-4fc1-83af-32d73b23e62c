# common/service/CdkService.py
from typing import List, Optional

from sqlalchemy import select

from common.entity.Cdk import Cdk, CdkUsageLog


class CdkService:
    def __init__(self, db_session):
        self.db = db_session

    def create(self, cdk: Cdk) -> Cdk:
        """创建CDK"""
        self.db.add(cdk)
        self.db.commit()
        self.db.refresh(cdk)
        return cdk

    def update(self, cdk: Cdk) -> Optional[Cdk]:
        """更新CDK"""
        stmt = select(Cdk).where(Cdk.id == cdk.id, Cdk.del_flag == 1)
        existing_cdk = self.db.execute(stmt).scalar_one_or_none()

        if existing_cdk:
            for key, value in cdk.__dict__.items():
                if key != '_sa_instance_state' and value is not None:
                    setattr(existing_cdk, key, value)

            self.db.commit()
            self.db.refresh(existing_cdk)
            return existing_cdk
        return None

    def get_by_id(self, id: int) -> Optional[Cdk]:
        """根据ID获取CDK"""
        stmt = select(Cdk).where(Cdk.id == id, Cdk.del_flag == 1)
        return self.db.execute(stmt).scalar_one_or_none()

    def get_by_key(self, cdk_key: str) -> Optional[Cdk]:
        """根据CDK key获取CDK"""
        stmt = select(Cdk).where(Cdk.cdk_key == cdk_key, Cdk.del_flag == 1)
        return self.db.execute(stmt).scalar_one_or_none()

    def list_all(self, status: Optional[int] = None) -> List[Cdk]:
        """获取所有CDK列表"""
        stmt = select(Cdk).where(Cdk.del_flag == 1)
        if status is not None:
            stmt = stmt.where(Cdk.status == status)
        return self.db.execute(stmt).scalars().all()

    def delete(self, id: int) -> bool:
        """软删除CDK"""
        stmt = select(Cdk).where(Cdk.id == id, Cdk.del_flag == 1)
        cdk = self.db.execute(stmt).scalar_one_or_none()

        if cdk:
            cdk.del_flag = 2
            self.db.commit()
            return True
        return False


class CdkUsageLogService:
    def __init__(self, db_session):
        self.db = db_session

    def create(self, log: CdkUsageLog) -> CdkUsageLog:
        """创建使用记录"""
        self.db.add(log)
        self.db.commit()
        self.db.refresh(log)
        return log

    def get_by_cdk_id(self, cdk_id: int) -> List[CdkUsageLog]:
        """获取CDK的使用记录"""
        stmt = select(CdkUsageLog).where(CdkUsageLog.cdk_id == cdk_id)
        return self.db.execute(stmt).scalars().all()

    def get_by_uid(self, uid: int) -> List[CdkUsageLog]:
        """获取用户的使用记录"""
        stmt = select(CdkUsageLog).where(CdkUsageLog.uid == uid)
        return self.db.execute(stmt).scalars().all()
