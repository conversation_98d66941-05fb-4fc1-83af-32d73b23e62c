# !/usr/bin/python3
# -*- coding: utf-8 -*-

"""
训练嵌入文件信息表服务类
----------------------------------------------------
@Project :   xinhou-openai-admin
@File    :   EmbeddingFileService.py
@Contact :   <EMAIL>

@Modify Time      <AUTHOR>    @Desciption
------------      -------    --------    -----------
2023/04/16 22:04  peng.shen   v1.0.0     None
"""
from datetime import datetime
from typing import List

from sqlalchemy import func
from sqlalchemy.orm import Session
from xinhou_openai_framework.core.orm.service.BaseServiceImpl import BaseServiceImpl

from common.entity.EmbeddingDocument import EmbeddingDocument
from common.entity.EmbeddingFile import EmbeddingFile


class EmbeddingFileService(BaseServiceImpl[EmbeddingFile]):
    def __init__(self, db: Session):
        super(EmbeddingFileService, self).__init__(db, EmbeddingFile)

    def save(self, model: EmbeddingFile) -> dict:
        current_time = datetime.now()
        model.created_at = current_time
        model.updated_at = current_time
        saved_model = super().save(model)
        return {
            "id": saved_model.id,
            "file_name": saved_model.file_name,
            "file_name_uuid": saved_model.file_name_uuid,
            "file_type": saved_model.file_type,
            "file_size": saved_model.file_size,
            "file_path": saved_model.file_path,
            "file_url": saved_model.file_url,
            "file_educate_status": saved_model.file_educate_status,
            "remark": saved_model.remark,
            "file_review_pic": saved_model.file_review_pic
        }

    def find_by_status(self):
        return self.db.query(EmbeddingFile).filter(
            (self.cls.file_educate_status != 1) &
            (self.cls.file_type == "txt") &
            (self.cls.emb_type <= 3) &
            (self.cls.o_url != None)).all()

    def find_by_ids(self, file_ids: List[int]) -> List[EmbeddingFile]:
        """
        根据文件ID列表获取文件
        :param file_ids: 文件ID列表
        :return: 文件列表
        """
        return self.db.query(EmbeddingFile).filter(EmbeddingFile.id.in_(file_ids)).all()

    def find_active_by_ids(self, file_ids: List[int]) -> List[EmbeddingFile]:
        """
        根据文件ID列表获取未被删除的文件
        :param file_ids: 文件ID列表
        :return: 未被删除的文件列表
        """
        return self.db.query(EmbeddingFile).filter(
            EmbeddingFile.id.in_(file_ids),
            EmbeddingFile.is_delete == 0
        ).all()

    def mark_as_deleted(self, file_id: int) -> bool:
        """
        将文件标记为已删除
        :param file_id: 文件ID
        :return: 操作是否成功
        """
        try:
            self.db.query(EmbeddingFile).filter(EmbeddingFile.id == file_id).update({"is_delete": 1})
            self.db.commit()
            return True
        except Exception as e:
            self.db.rollback()
            return False

    def find_by_ids_with_first_page_content(self, file_ids: List[int]) -> List[dict]:
        """
        根据文件ID列表获取文件，并包含每个文件的第一段page_content
        """
        subquery = (
            self.db.query(
                EmbeddingDocument.embedding_file_id,
                func.min(EmbeddingDocument.id).label('min_id')
            )
            .filter(EmbeddingDocument.embedding_file_id.in_(file_ids))
            .group_by(EmbeddingDocument.embedding_file_id)
            .subquery()
        )

        files_with_content = (
            self.db.query(EmbeddingFile, EmbeddingDocument.page_content)
            .outerjoin(subquery, EmbeddingFile.id == subquery.c.embedding_file_id)
            .outerjoin(EmbeddingDocument, (EmbeddingDocument.embedding_file_id == subquery.c.embedding_file_id) & (EmbeddingDocument.id == subquery.c.min_id))
            .filter(EmbeddingFile.id.in_(file_ids))
            .all()
        )

        result = []
        for file, page_content in files_with_content:
            file_dict = file.to_dict()
            file_dict['first_page_content'] = page_content.decode('utf-8') if isinstance(page_content, bytes) else page_content
            result.append(file_dict)

        return result

    def get_by_id(self, file_id: int) -> EmbeddingFile:
        """
        根据ID获取文件记录
        :param file_id: 文件ID
        :return: EmbeddingFile对象
        """
        return self.db.query(EmbeddingFile).filter(EmbeddingFile.id == file_id).first()
        
    def get_download_url(self, file_id: int) -> str:
        """
        获取文件的下载URL
        
        Args:
            file_id: 文件ID
            
        Returns:
            文件下载URL或None
        """
        file = self.get_by_id(file_id)
        if file and file.file_url:
            return file.file_url
        return None


