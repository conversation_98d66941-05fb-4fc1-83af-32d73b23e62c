from typing import List
from typing import Optional

from loguru import logger
from sqlalchemy import or_, func, and_, case, desc
from sqlalchemy.orm import Session, aliased
from xinhou_openai_framework.core.exception.GlobalBusinessException import GlobalBusinessException
from xinhou_openai_framework.core.orm.service.BaseServiceImpl import BaseServiceImpl
from xinhou_openai_framework.pages.Paginate import Paginate

from common.entity.EmbeddingDocument import EmbeddingDocument
from common.entity.EmbeddingFile import EmbeddingFile


class MyFileKnowledgeService(BaseServiceImpl[EmbeddingFile]):
    def __init__(self, db: Session):
        super(MyFileKnowledgeService, self).__init__(db, EmbeddingFile)

    def get_personal_files(self, file_ids: List[int], file_name: Optional[str], page_size: int, page_num: int):
        SourceFile = aliased(EmbeddingFile)
        ProcessedFile = aliased(EmbeddingFile)

        # 子查询：获取每个文件的第一个 page_content
        doc_subquery = (
            self.db.query(
                EmbeddingDocument.embedding_file_id,
                func.min(EmbeddingDocument.id).label('min_id')
            )
            .group_by(EmbeddingDocument.embedding_file_id)
            .subquery()
        )

        query = (
            self.db.query(SourceFile, ProcessedFile.id.label('processed_id'), EmbeddingDocument.page_content)
            .distinct(SourceFile.id)  # 使用distinct确保每个源文件只返回一次
            .outerjoin(
                ProcessedFile,
                and_(
                    ProcessedFile.oid == SourceFile.id
                )
            )
            .outerjoin(
                doc_subquery,
                case(
                    (SourceFile.id == doc_subquery.c.embedding_file_id, SourceFile.id),
                    else_=ProcessedFile.id
                ) == doc_subquery.c.embedding_file_id
            )
            .outerjoin(
                EmbeddingDocument,
                and_(
                    EmbeddingDocument.embedding_file_id == doc_subquery.c.embedding_file_id,
                    EmbeddingDocument.id == doc_subquery.c.min_id
                )
            )
            .filter(SourceFile.id.in_(file_ids), SourceFile.is_delete == 0, SourceFile.emb_type < 5)
            .group_by(SourceFile.id, ProcessedFile.id, EmbeddingDocument.page_content)
            .order_by(desc(SourceFile.created_at))
        )

        # 应用文件名过滤
        if file_name:
            query = query.filter(or_(
                SourceFile.file_name.ilike(f"%{file_name}%"),
                SourceFile.file_name_uuid.ilike(f"%{file_name}%")
            ))

        # 使用Paginate进行分页
        paginate = Paginate(query, page_num, page_size, orders=[])

        # 处理结果
        results = []
        for source_file, processed_id, page_content in paginate.items:
            file_dict = source_file.to_dict()
            file_dict['first_page_content'] = page_content.decode('utf-8') if isinstance(page_content, bytes) else page_content
            results.append(file_dict)

        # 创建新的Paginate对象
        new_paginate = Paginate(query, page_num, page_size, orders=[])
        new_paginate.items = results

        return new_paginate

    def delete_personal_file(self, file_id: int) -> bool:
        try:
            # 更新 t_embedding_file 表，假删除操作，is_delete字段设为1
            self.db.query(EmbeddingFile).filter(EmbeddingFile.id == file_id).update({"is_delete": 1})

            # 从 t_embedding_document 表中真删除对应的记录
            self.db.query(EmbeddingDocument).filter(EmbeddingDocument.embedding_file_id == file_id).delete()

            self.db.commit()
            logger.info(f"成功删除文件ID为 {file_id} 的记录")
            return True
        except Exception as e:
            self.db.rollback()
            logger.error(f"删除文件失败: {str(e)}")
            raise GlobalBusinessException(500, f"删除文件失败: {str(e)}")
