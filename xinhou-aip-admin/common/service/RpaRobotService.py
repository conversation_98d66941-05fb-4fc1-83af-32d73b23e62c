# !/usr/bin/python3
# -*- coding: utf-8 -*-

from typing import Optional

from sqlalchemy import and_
from sqlalchemy.orm import Session
from xinhou_openai_framework.core.orm.service.BaseServiceImpl import BaseServiceImpl

from common.entity.RpaRobot import RpaRobot


class RpaRobotService(BaseServiceImpl[RpaRobot]):
    """
    任务服务类
    """

    def __init__(self, db: Session):
        super(RpaRobotService, self).__init__(db, RpaRobot)

    async def get_robot_id_by_name(self, robot_name: str) -> Optional[int]:
        robot_info = self.db.query(RpaRobot).filter(
            and_(
                RpaRobot.robot_name == robot_name,
            )
        ).first()
        id = robot_info.id if robot_info else None
        return id
