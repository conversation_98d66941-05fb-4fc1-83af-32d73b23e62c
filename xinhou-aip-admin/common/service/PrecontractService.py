from typing import List, Optional, Dict, Any

from sqlalchemy import or_
from sqlalchemy.orm import Session
from xinhou_openai_framework.core.orm.service.BaseServiceImpl import BaseServiceImpl
from xinhou_openai_framework.pages.PageHelper import PageHelper

from apps.admin.schema.ProductSchema import ReqProductFindSchema
from common.entity.Precontract import Precontract


class PrecontractService(BaseServiceImpl[Precontract]):
    """
    预约服务类
    """

    def __init__(self, db: Session):
        super(PrecontractService, self).__init__(db, Precontract)


    def _to_dict(self, precontract: Precontract) -> Dict[str, Any]:
        return {
            "id": precontract.id,
            "mobile": precontract.mobile,
            "name": precontract.name,
            "company": precontract.company,
            "city": precontract.city,
            "remark": precontract.remark,
            "create_by": precontract.create_by,
            "created_at": precontract.created_at,
            "update_by": precontract.update_by,
            "updated_at": precontract.updated_at,
        }

    def save(self, precontract: Precontract) -> Precontract:
        """
        保存预约
        """
        return super().save(precontract)

    def update(self, precontract: Precontract) -> Optional[Precontract]:
        """
        更新预约
        """
        return super().update(precontract)

    def delete(self, precontract: Precontract) -> None:
        """
        删除预约
        """
        super().delete(precontract)

    def find_by_id(self, precontract: Precontract) -> Optional[Precontract]:
        """
        根据ID查询预约
        """
        return super().find_by_id(precontract)