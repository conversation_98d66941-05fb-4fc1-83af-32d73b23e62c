import logging
import json
from xinhou_openai_framework.core.context.model.AppContext import App<PERSON>ontext
from xinhou_openai_framework.core.context.model.SystemContext import ctx
from xinhou_openai_framework.core.decorator.HttpClientDecorator import http_client

context: AppContext = ctx.__getattr__("context")

from langchain_community.graphs import Neo4jGraph

class GraphKnowledgeService:

    def __init__(self):
        self.kg_graph = Neo4jGraph(
                url=context.graph_knowledge.url, 
                database=context.graph_knowledge.db, 
                username=context.graph_knowledge.name, 
                password=str(context.graph_knowledge.password)
                )

    def is_create_graph_knowledge(self, knowledge_index: str) -> bool:
        """Check if knowledge graph exists"""
        try:
            number = self.kg_graph.query(f"MATCH (n:`{knowledge_index}`) RETURN count(n)")[0]["count(n)"]
            if number:
                return True
            else:
                return False
        except Exception as e:
            logging.error(f"Error checking knowledge graph: {e}")
            return False
    
    @staticmethod
    async def get_redis_kg_data(redis_client, task_id):
        agent_keys = await redis_client.lrange(f"aip_task:{task_id}_agents", 0, -1)
        # 遍历检查每个 agent
        for agent_id in agent_keys:
            agent_id = agent_id.decode('utf-8') if isinstance(agent_id, bytes) else agent_id
            tmp_key = f"aip_task:{task_id}_agent_{agent_id}"
            # 获取 uuid
            tmp_action = await redis_client.hget(tmp_key, 'agent_action')
            tmp_action = tmp_action.decode('utf-8') if isinstance(tmp_action, bytes) else tmp_action
            if tmp_action == "KGDATA":
                data_key = f"{tmp_key}_content"
                result = await redis_client.get(data_key)
                result_data = json.loads(result)
                return result_data
        return []
