# !/usr/bin/python3
# -*- coding: utf-8 -*-

"""
支付服务
----------------------------------------------------
@Project :   xinhou-openai-admin
@File    :   PaymentService.py
@Contact :   <EMAIL>

@Modify Time      <AUTHOR>    @Desciption
------------      -------    --------    -----------
2024/09/18 10:30  ChatGPT    v1.0.0      None
2024/09/18 14:00  ChatGPT    v1.1.0      使用BaseServiceImpl和AppContext
2024/09/18 15:30  ChatGPT    v1.2.0      添加支付日志记录
2024/09/18 19:00  ChatGPT    v1.3.0      优化签名验证和错误处理
"""
import hashlib
import json
import time
from datetime import datetime, timedelta
from decimal import Decimal
from typing import Dict, Any, List
from urllib.parse import urlencode

import requests
from loguru import logger
from sqlalchemy.orm import Session
from xinhou_openai_framework.core.context.model.AppContext import AppContext
from xinhou_openai_framework.core.context.model.SystemContext import ctx
from xinhou_openai_framework.core.orm.service.BaseServiceImpl import BaseServiceImpl

from apps.admin.schema.PaymentSchema import (
    ReqPaymentRefundSchema, ReqPaymentFindSchema
)
from common.entity.Ip import Ip
from common.entity.PaymentLog import PaymentLog
from common.entity.PaymentOrder import PaymentOrder
from common.entity.Product import Product
from common.entity.User import User
from common.service import WxNotificationService
from common.service.UserService import UserService

context: AppContext = ctx.__getattr__("context")


class DecimalEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, Decimal):
            return str(obj)
        return super(DecimalEncoder, self).default(obj)


class PaymentService(BaseServiceImpl[PaymentOrder]):
    def __init__(self, db: Session):
        super(PaymentService, self).__init__(db, PaymentOrder)
        self.api_url = context.pay.url
        self.pid = context.pay.pid
        self.key = context.pay.pkey
        self.notify_url = context.pay.notify_url
        self.return_url = context.pay.return_url
        self.user_service = UserService(db)

    def generate_out_trade_no(self) -> str:
        now = datetime.now()
        return f"ORDER{now.strftime('%Y%m%d%H%M%S')}{now.microsecond:06d}"

    def _verify_sign(self, data: dict) -> bool:
        received_sign = data.pop("sign", "")
        data.pop("sign_type", "")

        calculated_sign = self._generate_sign(data)

        # 获取完整的签名字符串
        sign_string = self._get_sign_string(data)  # 新增这行

        is_valid = calculated_sign.lower() == received_sign.lower()

        if not is_valid:
            logger.error(f"Sign verification failed. Calculated: {calculated_sign}, Received: {received_sign}")
            logger.error(f"Data used for signing: {data}")
            logger.error(f"Full sign string: {sign_string}")  # 修改这行
        else:
            logger.info("Sign verification successful")

        return is_valid

    def _get_sign_string(self, data: dict) -> str:
        # 移除空值和签名相关的字段
        sign_params = {k: v for k, v in data.items() if v and k not in ['sign', 'sign_type']}
        # 按字母顺序排序参数
        sorted_params = sorted(sign_params.items())
        # 构建签名字符串
        sign_string = "&".join(f"{k}={v}" for k, v in sorted_params)
        # 添加密钥
        return sign_string + self.key

    def _generate_sign(self, params: dict) -> str:
        # 按照指定顺序构建签名字符串
        ordered_keys = ['money', 'name', 'notify_url', 'out_trade_no', 'pid', 'return_url', 'type']
        sign_parts = []
        for key in ordered_keys:
            if key in params and params[key]:
                sign_parts.append(f"{key}={params[key]}")

        sign_string = '&'.join(sign_parts)

        # 添加密钥
        sign_string += self.key

        logger.debug(f"Sign string before MD5: {sign_string}")

        # 计算 MD5
        md5_sign = hashlib.md5(sign_string.encode('UTF-8')).hexdigest()

        logger.debug(f"Generated MD5 sign: {md5_sign}")

        return md5_sign

    async def handle_payment_notify(self, notify_data: Dict[str, Any]) -> bool:
        try:
            # 获取订单信息
            order = self.db.query(PaymentOrder).filter(
                PaymentOrder.out_trade_no == notify_data['out_trade_no']
            ).first()

            if not order:
                logger.error(f"Order not found: {notify_data['out_trade_no']}")
                return False

            if order.trade_status == 'PAID':
                logger.info(f"Order already paid: {order.out_trade_no}")
                return True

            # 验证支付状态
            if notify_data['trade_status'] == 'TRADE_SUCCESS':
                # 更新订单状态
                order.trade_status = 'PAID'
                order.trade_no = notify_data['trade_no']
                order.pay_time = datetime.now()
                order.notify_time = datetime.now()

                # 更新用户积分
                user = self.db.query(User).filter(User.id == order.user_id).first()
                if user:
                    user.remain_point += order.point_amount
                self.db.commit()
                logger.info(f"Payment successful: {notify_data}")
                return True

            return False

        except Exception as e:
            self.db.rollback()
            logger.error(f"Error handling payment notify: {str(e)}")
            return False

    async def handle_payment_notify_v2(self, notify_data: Dict[str, Any]) -> bool:
        try:
            logger.info(f"[notify_v2] 收到支付通知参数: {notify_data}")

            # 获取订单信息
            order = self.db.query(PaymentOrder).filter(
                PaymentOrder.out_trade_no == notify_data['out_trade_no']
            ).first()
            logger.info(f"[notify_v2] 订单查找结果: {order}")

            if not order:
                logger.error(f"[notify_v2] Order not found: {notify_data['out_trade_no']}")
                return False

            if order.trade_status == 'PAID':
                logger.info(f"[notify_v2] Order already paid: {order.out_trade_no}")
                return True

            # 验证支付状态
            if notify_data['trade_status'] == 'TRADE_SUCCESS':
                logger.info(f"[notify_v2] 支付状态为TRADE_SUCCESS，准备更新订单状态")
                order.trade_status = 'PAID'
                order.trade_no = notify_data['trade_no']
                order.pay_time = datetime.now()
                order.notify_time = datetime.now()

                # 用户信息
                user = self.db.query(User).filter(User.id == order.user_id).first()
                logger.info(f"[notify_v2] 用户查找结果: {user}")

                # 解析extra_param
                if not order.extra_param:
                    logger.error(f"[notify_v2] No extra_param found in order: {order.out_trade_no}")
                    return False
                try:
                    param_data = json.loads(order.extra_param)
                    logger.info(f"[notify_v2] extra_param解析结果: {param_data}")
                except Exception as e:
                    logger.error(f"[notify_v2] extra_param解析失败: {e}")
                    return False

                # 判断模型类型
                is_image_model = 'image_url' in param_data
                is_virtual_human = 'has_collection' in param_data
                logger.info(f"[notify_v2] is_image_model={is_image_model}, is_virtual_human={is_virtual_human}")

                message = ""
                if is_image_model:
                    from common.entity.ImageModel import ImageModel
                    image_model = ImageModel(
                        pid=param_data.get('pid'),
                        image_url=param_data.get('image_url'),
                        model_type=param_data.get('model_type'),
                        speech_style=param_data.get('speech_style'),
                        interview_style=param_data.get('interview_style'),
                        duration=param_data.get('duration'),
                        appearance_note=param_data.get('appearance_note'),
                        status=param_data.get('status', 1),
                        submit_status=param_data.get('submit_status', 1)
                    )
                    self.db.add(image_model)
                    self.db.flush()
                    logger.info(f"[notify_v2] 图片模型保存成功: {image_model}")

                    ip = None
                    if 'pid' in param_data:
                        ip = self.db.query(Ip).filter(Ip.id == param_data.get('pid')).first()
                    ip_name = ip.ip_name if ip else "未知IP"
                    pay_time_str = order.pay_time.strftime(
                        '%Y-%m-%dT%H:%M:%S') if order.pay_time else datetime.now().strftime('%Y-%m-%dT%H:%M:%S')
                    amount_str = f"{order.total_amount:.0f}" if order.total_amount else "0"
                    order_no = order.out_trade_no
                    message = (
                        f"[撒花]{user.phone}\n"
                        f"({ip_name})于{pay_time_str}，扫码支付【图片制作专属模型】{amount_str}元~\n"
                        f"订单号：{order_no}\n"
                        f"图片URL：{param_data.get('image_url', '')}；"
                        f"专属模型类型：{param_data.get('model_type', '')}；"
                        f"口播风格：{param_data.get('speech_style', '')}；"
                        f"访谈风格：{param_data.get('interview_style', 'None')}；"
                        f"模型时长：{param_data.get('duration', '')}；"
                        f"形象要求备注：{param_data.get('appearance_note', '')}；"
                        f"提交状态：{'已提交' if param_data.get('submit_status', 1) == 1 else '已上传'}~\n"
                    )
                elif is_virtual_human:
                    from common.entity.VirtualHuman import VirtualHuman
                    vh_model = VirtualHuman(
                        pid=param_data.get('pid'),
                        model_type=param_data.get('model_type'),
                        speech_style=param_data.get('speech_style'),
                        interview_style=param_data.get('interview_style'),
                        duration=param_data.get('duration'),
                        appearance_note=param_data.get('appearance_note'),
                        status=param_data.get('status', 1),
                        submit_status=param_data.get('submit_status', 1),
                        has_collection=param_data.get('has_collection', 0)
                    )
                    self.db.add(vh_model)
                    self.db.flush()
                    logger.info(f"[notify_v2] 虚拟人模型保存成功: {vh_model}")

                    ip = None
                    if 'pid' in param_data:
                        ip = self.db.query(Ip).filter(Ip.id == param_data.get('pid')).first()
                    ip_name = ip.ip_name if ip else "未知IP"
                    pay_time_str = order.pay_time.strftime(
                        '%Y-%m-%dT%H:%M:%S') if order.pay_time else datetime.now().strftime('%Y-%m-%dT%H:%M:%S')
                    amount_str = f"{order.total_amount:.0f}" if order.total_amount else "0"
                    order_no = order.out_trade_no
                    message = (
                        f"[撒花]{user.phone}\n"
                        f"({ip_name})于{pay_time_str}，扫码支付【超虚拟人模型制作】{amount_str}元~\n"
                        f"订单号：{order_no}\n"
                        f"专属模型类型：{param_data.get('model_type', '')}；"
                        f"口播风格：{param_data.get('speech_style', '')}；"
                        f"访谈风格：{param_data.get('interview_style', 'None')}；"
                        f"模型时长：{param_data.get('duration', '')}；"
                        f"形象要求备注：{param_data.get('appearance_note', '')}；"
                        f"提交状态：{'已提交' if param_data.get('submit_status', 1) == 1 else '已上传'}~\n"
                    )
                else:
                    logger.warning(f"[notify_v2] Unknown model type in extra_param: {param_data}")

                # 发送通知消息
                if message:
                    await WxNotificationService.sendImages(message, context)
                    logger.info(f"[notify_v2] 通知消息发送成功")

                self.db.commit()
                logger.info(f"[notify_v2] 数据库提交成功，支付处理完成: {notify_data}")
                return True

            logger.info(f"[notify_v2] 非TRADE_SUCCESS状态，忽略")
            return False

        except Exception as e:
            self.db.rollback()
            logger.error(f"[notify_v2] Error handling payment notify: {str(e)}")
            return False

    def init_payment(self, user_id: int, product_id: int, pay_type: str, client_ip: str = None, device: str = None) -> \
            Dict[str, Any]:
        try:
            # 获取产品信息
            product = self.db.query(Product).filter(Product.id == product_id).first()
            if not product:
                raise ValueError(f"Product not found: {product_id}")

            # 创建订单
            order = PaymentOrder(
                out_trade_no=self.generate_out_trade_no(),
                user_id=user_id,
                product_id=product_id,
                product_name=product.name,
                total_amount=product.money,
                payment_type=pay_type.upper(),
                trade_status='PENDING',
                notify_url=self.notify_url,
                return_url=self.return_url,
                client_ip=client_ip,
                device=device,
                point_amount=product.point_amount,
                valid_days=product.valid_days
            )

            if product.valid_days > 0:
                order.expire_time = datetime.now() + timedelta(days=product.valid_days)

            self.db.add(order)
            self.db.commit()

            # 构建支付参数
            pay_params = {
                'pid': self.pid,
                'type': pay_type.lower(),
                'out_trade_no': order.out_trade_no,
                'notify_url': order.notify_url,
                'return_url': order.return_url,
                'name': order.product_name,
                'money': str(order.total_amount)
            }

            # 生成签名
            pay_params['sign'] = self._generate_sign(pay_params)
            pay_params['sign_type'] = 'MD5'

            # 返回结果
            return {
                'code': 1,
                'msg': '支付初始化成功',
                'out_trade_no': order.out_trade_no,
                'pay_url': f"{self.api_url}/submit.php?{urlencode(pay_params)}",
                'status': None
            }

        except Exception as e:
            self.db.rollback()
            logger.error(f"Error initializing payment: {str(e)}")
            raise e

    def init_payment_v2(self, user_id: int, product_id: int, pay_type: str, client_ip: str = None, device: str = None,
                        param=None) -> Dict[str, Any]:
        try:
            # 获取产品信息
            product = self.db.query(Product).filter(Product.id == product_id).first()
            if not product:
                raise ValueError(f"Product not found: {product_id}")

            # 将param对象转换为JSON字符串
            extra_param = json.dumps(param.dict(), cls=DecimalEncoder) if param else None

            # 创建订单
            order = PaymentOrder(
                out_trade_no=self.generate_out_trade_no(),
                user_id=user_id,
                product_id=product_id,
                product_name=product.name,
                total_amount=product.money,
                payment_type=pay_type.upper(),
                trade_status='PENDING',
                notify_url=self.notify_url,
                return_url=self.return_url,
                client_ip=client_ip,
                device=device,
                extra_param=extra_param,
                customer_id=getattr(param, "pid", None) if param else None
            )
            self.db.add(order)
            self.db.commit()
            # 构建支付参数
            pay_params = {
                'pid': self.pid,
                'type': pay_type.lower(),
                'out_trade_no': order.out_trade_no,
                'notify_url': order.notify_url,
                'name': order.product_name,
                'money': str(order.total_amount)
            }

            # 生成签名
            pay_params['sign'] = self._generate_sign(pay_params)
            pay_params['sign_type'] = 'MD5'

            # 发起支付请求
            try:
                response = requests.post(f"{self.api_url}mapi.php", data=pay_params, timeout=10)
                response.raise_for_status()  # 检查HTTP错误
                result = response.json()

                # 记录API响应结果
                self._log_payment(order.id, "API_RESPONSE", json.dumps(result, cls=DecimalEncoder))

                # 处理API响应
                if result.get('code') == 1:
                    # 成功情况下，更新订单信息
                    order.trade_no = result.get('trade_no')
                    order.qrcode_url = result.get('img')  # 二维码图片地址
                    self.db.commit()

                    # 返回标准化的成功响应
                    return {
                        'code': 1,
                        'msg': '支付初始化成功',
                        'out_trade_no': order.out_trade_no,
                        'trade_no': result.get('trade_no'),
                        'O_id': result.get('O_id'),
                        'pay_url': result.get('payurl'),  # 支付跳转url
                        'qrcode_url': result.get('qrcode'),  # 二维码链接
                        'img': result.get('img'),  # 二维码图片地址
                        'status': None,
                        'created_at': order.created_at  # 返回订单创建时间
                    }
                else:
                    # 失败情况，记录错误但不抛出异常
                    logger.error(f"Payment API error: {result.get('msg')}")
                    return {
                        'code': 0,
                        'msg': result.get('msg') or '支付初始化失败',
                        'out_trade_no': order.out_trade_no,
                        'status': None,
                        'created_at': order.created_at  # 返回订单创建时间
                    }

            except requests.RequestException as e:
                logger.error(f"Payment request failed: {str(e)}")
                return {
                    'code': 0,
                    'msg': f'支付请求失败: {str(e)}',
                    'out_trade_no': order.out_trade_no,
                    'status': None,
                    'created_at': order.created_at  # 返回订单创建时间
                }

        except Exception as e:
            self.db.rollback()
            logger.error(f"Error initializing payment: {str(e)}")
            raise e

    async def query_payment(self, out_trade_no: str) -> dict:
        payment_order = self.db.query(PaymentOrder).filter(PaymentOrder.out_trade_no == out_trade_no).first()
        if not payment_order:
            return {
                'code': 0,
                'msg': '订单不存在',
                'out_trade_no': out_trade_no
            }

        query_url = f"{self.api_url}api.php?act=order&pid={self.pid}&key={self.key}&out_trade_no={out_trade_no}"
        try:
            response = requests.get(query_url, timeout=10)
            response.raise_for_status()
            result = response.json()

            if str(result.get("code")) == '1':
                # 更新本地订单状态
                new_status = 'PAID' if str(result.get("status")) == '1' else 'PENDING'
                payment_order.trade_status = new_status
                self.update(payment_order)
                self._log_payment(payment_order.id, "QUERY", json.dumps(result, cls=DecimalEncoder))
                return {
                    'code': 1,
                    'msg': result.get('msg', '查询订单号成功！'),
                    'trade_no': result.get('trade_no'),
                    'out_trade_no': result.get('out_trade_no'),
                    'type': result.get('type'),
                    'pid': result.get('pid'),
                    'addtime': result.get('addtime'),
                    'endtime': result.get('endtime'),
                    'name': result.get('name'),
                    'money': result.get('money'),
                    'status': int(result.get('status', 0)),
                    'param': result.get('param', ''),
                    'buyer': result.get('buyer', ''),
                }
            else:
                self._log_payment(payment_order.id, "QUERY_FAIL", json.dumps(result, cls=DecimalEncoder))
                logger.error(f"Payment query failed: {result.get('msg', 'Unknown error')}")
                return {
                    'code': 0,
                    'msg': result.get('msg', '查询失败'),
                    'out_trade_no': out_trade_no
                }
        except requests.RequestException as e:
            logger.error(f"Error querying payment: {str(e)}")
            return {
                'code': 0,
                'msg': f'查询失败: 网络错误',
                'out_trade_no': out_trade_no
            }

    async def refund_payment(self, req: ReqPaymentRefundSchema) -> dict:
        payment_order = self.db.query(PaymentOrder).filter(PaymentOrder.out_trade_no == req.out_trade_no).first()
        if not payment_order:
            return {"code": 0, "msg": "订单不存在"}

        refund_url = f"{self.api_url}api.php?act=refund"
        params = {
            "pid": self.pid,
            "key": self.key,
            "out_trade_no": req.out_trade_no,
            "money": str(req.money)
        }
        try:
            response = requests.post(refund_url, data=params, timeout=10)
            response.raise_for_status()
            result = response.json()

            if result["code"] == 1:
                # 更新本地订单状态
                payment_order.trade_status = 'REFUNDED'
                payment_order.refund_amount = req.money
                self.update(payment_order)
                self._log_payment(payment_order.id, "REFUND", f"退款成功: {json.dumps(result, cls=DecimalEncoder)}")
                return {"code": 1, "msg": "退款成功"}
            else:
                self._log_payment(payment_order.id, "REFUND_FAIL",
                                  f"退款失败: {json.dumps(result, cls=DecimalEncoder)}")
                logger.error(f"Refund failed: {result.get('msg', 'Unknown error')}")
                return {"code": 0, "msg": f"退款失败: {result.get('msg', 'Unknown error')}"}
        except requests.RequestException as e:
            logger.error(f"Error refunding payment: {str(e)}")
            return {"code": 0, "msg": f"退款失败: 网络错误"}

    def _log_payment(self, order_id: int, log_type: str, content: str):
        try:
            log = PaymentLog(
                order_id=order_id,
                log_type=log_type,
                content=content
            )
            self.db.add(log)
            self.db.commit()
        except Exception as e:
            logger.error(f"Error logging payment: {str(e)}")

    def find_all(self, search: ReqPaymentFindSchema) -> List[Dict[str, Any]]:
        query = self.db.query(PaymentOrder)

        if search.out_trade_no:
            query = query.filter(PaymentOrder.out_trade_no == search.out_trade_no)
        if search.trade_status:
            query = query.filter(PaymentOrder.trade_status == search.trade_status)
        if search.payment_type:
            query = query.filter(PaymentOrder.payment_type == search.payment_type)
        if search.date_from:
            query = query.filter(PaymentOrder.created_at >= search.date_from)
        if search.date_to:
            query = query.filter(PaymentOrder.created_at <= search.date_to)

        results = query.all()
        return [self._to_dict(order) for order in results]

    def _to_dict(self, order: PaymentOrder) -> Dict[str, Any]:
        return {
            'id': order.id,
            'out_trade_no': order.out_trade_no,
            'trade_no': order.trade_no,
            'user_id': order.user_id,
            'product_name': order.product_name,
            'total_amount': str(order.total_amount),
            'payment_type': order.payment_type,
            'trade_status': order.trade_status,
            'pay_time': order.pay_time.strftime('%Y-%m-%d %H:%M:%S') if order.pay_time else None,
            'point_amount': order.point_amount,
            'valid_days': order.valid_days,
            'expire_time': order.expire_time.strftime('%Y-%m-%d %H:%M:%S') if order.expire_time else None
        }

    def get_payment_logs(self, order_id: int) -> List[Dict[str, Any]]:
        logs = self.db.query(PaymentLog).filter(PaymentLog.order_id == order_id).order_by(
            PaymentLog.created_at.desc()).all()
        return [
            {
                'id': log.id,
                'order_id': log.order_id,
                'log_type': log.log_type,
                'content': log.content,
                'created_at': log.created_at.isoformat() if log.created_at else None
            }
            for log in logs
        ]

    def create_order(self, user_id: int, product_id: int, point_amount: int, payment_type: str) -> PaymentOrder:
        """
        建支付订单
        
        Args:
            user_id: 用户ID
            product_id: 商品ID
            point_amount: 充值点数
            payment_type: 支付方式
            
        Returns:
            创建的订单对象
        """
        # 生成订单号
        out_trade_no = f"ORDER_{int(time.time())}_{user_id}"

        order = PaymentOrder(
            out_trade_no=out_trade_no,
            user_id=user_id,
            product_id=product_id,
            product_name=f"{point_amount}数充值",
            point_amount=point_amount,
            total_amount=self._calculate_amount(point_amount),
            payment_type=payment_type,
            trade_status='PENDING',
            create_by=str(user_id)
        )

        self.save(order)
        return order
