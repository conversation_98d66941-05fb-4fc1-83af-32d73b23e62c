# !/usr/bin/python3
# -*- coding: utf-8 -*-

"""
知识库服务类
----------------------------------------------------
@Project :   xinhou-aip-admin
@File    :   KnowledgeService.py
@Contact :   <EMAIL>

@Modify Time      <AUTHOR>    @Desciption
------------      -------    --------    -----------
2024/07/18 10:30   fancy     v1.0.0      初始创建
"""

import asyncio
import json
from typing import List

from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.orm import Session
from xinhou_openai_framework.core.exception.GlobalBusinessException import GlobalBusinessException
from xinhou_openai_framework.core.orm.service.BaseServiceImpl import BaseServiceImpl

from apps.admin.schema.KnowledgePlatformSchema.KnowledgePlatformEnums import KnowledgePlatformEnums
from apps.admin.schema.KnowledgeSchema import KnowledgeSearchSchema
from apps.admin.schema.SocialMediaKnowledgeSchema import UpdateSocialMediaAccountRequest
from common.entity.IpKnowledge import IpKnowledge
from common.service.EmbeddingFileService import EmbeddingFileService
from common.service.RpaRobotService import RpaRobotService
from common.service.RpaTaskService import RpaTaskService


class KnowledgeService(BaseServiceImpl[IpKnowledge]):
    """
    知识库服务类
    """

    def __init__(self, db: Session):
        super(KnowledgeService, self).__init__(db, IpKnowledge)
        self.embedding_file_service = EmbeddingFileService(db)
        self.rpa_task_service = RpaTaskService(db)
        self.rpa_robot_service = RpaRobotService(db)

    def get_personal_knowledge_with_files(self, pid: int) -> List[dict]:
        """
        获取个人知识库列表，包含相关文件，并按knowledge_type升序排序
        :param pid:Ip对应的ID
        :return: 排序后的个人知识库列表，每个知识库包含相关文件信息
        """
        knowledge_list = self.find_all(IpKnowledge(pid=pid))
        result = []
        for knowledge in knowledge_list:
            knowledge_dict = {
                'id': knowledge.id,
                'knowledge_source': knowledge.knowledge_source,
                'knowledge_type': knowledge.knowledge_type,
                'account_url': knowledge.account_url,
                'pid': knowledge.pid,
                'create_by': knowledge.create_by,
                'update_by': knowledge.update_by,
                'created_at': knowledge.created_at.isoformat() if knowledge.created_at else None,
                'updated_at': knowledge.updated_at.isoformat() if knowledge.updated_at else None,
                'remark': knowledge.remark,
                'file_list': knowledge.file_list,
                'avatar': knowledge.avatar,
                'is_myself': knowledge.is_myself
            }
            result.append(knowledge_dict)

        # 按knowledge_type升序排序
        return sorted(result, key=lambda x: x['knowledge_type'])

    def count_douyin_accounts(self, pid: int, is_myself: int) -> int:
        """
        统计用户已绑定的抖音账号数量（包括绑定中和绑定成功的）
        :param pid: IP ID
        :return: 绑定的抖音账号数量
        """
        return self.db.query(IpKnowledge).filter(
            IpKnowledge.pid == pid,
            IpKnowledge.knowledge_type == 3,
            IpKnowledge.is_myself == is_myself
        ).count()

    async def add_social_media_account(self, pid: int, account_url: str, is_myself: int):
        """
        添加社交媒体账号
        :param pid: IP ID
        :param account_url: 个人账号链接URL
        :param is_myself: 是否是自己的媒体账号，默认为0
        :return: 个人知识库列表
        """
        platform = KnowledgePlatformEnums.get_platform(account_url)
        if not platform:
            raise GlobalBusinessException(400, "不支持的平台")

        # 如果是抖音平台，检查已绑定账号数量
        if platform.knowledge_type == 3:
            account_count = self.count_douyin_accounts(pid, is_myself)
            if account_count >= 3:
                raise GlobalBusinessException(400, "抱歉，当前已达账号最大绑定额度~")

        knowledge = IpKnowledge(
            pid=pid,
            account_url=account_url,
            knowledge_source=platform.knowledge_source,
            knowledge_type=platform.knowledge_type,
            create_by='admin',
            update_by='admin',
            file_list='[]',
            is_myself=is_myself
        )
        created_knowledge = self.save(knowledge)

        try:
            if platform.knowledge_source == '我的抖音':
                await self._process_douyin_account(created_knowledge, account_url)
            else:
                raise GlobalBusinessException(400, f"平台 {platform.knowledge_source} 暂不支持自动数据获取")

            return created_knowledge
        except Exception as e:
            self.delete(created_knowledge)  # 如果出错，删除已创建的知识库对象
            raise GlobalBusinessException(500, f"处理社交媒体账号时发生错误: {str(e)}")

    def delete_social_media_account(self, account_id: int):
        knowledge = self.db.query(IpKnowledge).filter(
            IpKnowledge.id == account_id
        ).first()

        if not knowledge:
            raise GlobalBusinessException(404, "未找到指定的社交媒体账号")

        self.delete(knowledge)

        return True

    def get_knowledge_files(self, knowledge_id: int) -> List[dict]:
        """
        获取指定知识库的所有文件，包括每个文件的第一段page_content
        """
        knowledge = self.find_by_id(IpKnowledge(id=knowledge_id))
        if not knowledge:
            raise GlobalBusinessException(404, "知识库不存在")

        file_ids = json.loads(knowledge.file_list) if knowledge.file_list else []
        return self.embedding_file_service.find_by_ids_with_first_page_content(file_ids)

    def add_file_to_knowledge(self, knowledge_id: int, file_id: int):
        knowledge = self.find_by_id(IpKnowledge(id=knowledge_id))
        if not knowledge:
            raise GlobalBusinessException(404, "知识库不存在")

        file_list = json.loads(knowledge.file_list) if knowledge.file_list else []
        if file_id not in file_list:
            file_list.append(file_id)
            knowledge.file_list = json.dumps(file_list)
            self.update(knowledge)

    def remove_file_from_knowledge(self, knowledge_id: int, file_id: int):
        """
        从知识库中移除文件
        :param knowledge_id: 知识库ID
        :param file_id: 文件ID
        """
        knowledge = self.find_by_id(IpKnowledge(id=knowledge_id))
        if not knowledge:
            raise GlobalBusinessException(404, "知识库不存在")

        file_list = json.loads(knowledge.file_list) if knowledge.file_list else []
        if file_id in file_list:
            file_list.remove(file_id)
            knowledge.file_list = json.dumps(file_list)
            self.update(knowledge)

    def get_files_for_knowledge(self, knowledge_id: int) -> List[int]:
        """
        获取知识库中的所有文件ID
        :param knowledge_id: 知识库ID
        :return: 文件ID列表
        """
        knowledge = self.find_by_id(IpKnowledge(id=knowledge_id))
        if not knowledge:
            raise GlobalBusinessException(404, "知识库不存在")

        return json.loads(knowledge.file_list) if knowledge.file_list else []

    def remove_file_from_all_knowledge(self, file_id: int):
        """
        从所有知识库中移除指定文件
        :param file_id: 文件ID
        """
        potential_knowledge = self.db.query(IpKnowledge).filter(
            IpKnowledge.file_list.like(f'%{file_id}%')
        ).all()

        for knowledge in potential_knowledge:
            file_list = json.loads(knowledge.file_list) if knowledge.file_list else []
            if file_id in file_list:
                file_list.remove(file_id)
                knowledge.file_list = json.dumps(file_list)
                self.update(knowledge)

    def get_knowledge_by_field(self,
                               knowledge_source: str, knowledge_type: int) -> List[IpKnowledge]:
        search = KnowledgeSearchSchema(knowledge_source=knowledge_source, knowledge_type=knowledge_type)
        return self.find_all(search)

    async def update_social_media_account(self, req: UpdateSocialMediaAccountRequest):
        knowledge = self.find_by_id(IpKnowledge(id=req.id))
        if not knowledge:
            raise GlobalBusinessException(404, "未找到指定的社交媒体账号")

        update_data = req.dict(exclude_unset=True, exclude={'id'})

        for key, value in update_data.items():
            if hasattr(knowledge, key):
                setattr(knowledge, key, value)

        try:
            self.db.commit()
        except SQLAlchemyError as e:
            self.db.rollback()
            raise GlobalBusinessException(500, f"数据库更新失败: {str(e)}")

        # 重新获取更新后的记录
        updated_knowledge = self.find_by_id(IpKnowledge(id=req.id))
        return updated_knowledge

    async def _process_douyin_account(self, knowledge: IpKnowledge, account_url: str):
        info_robot_name = "douyin_user_info_bot"
        parse_robot_name = "douyin_video_parse_bot"

        info_robot_id = await self.rpa_robot_service.get_robot_id_by_name(info_robot_name)
        parse_robot_id = await self.rpa_robot_service.get_robot_id_by_name(parse_robot_name)

        if info_robot_id is None or parse_robot_id is None:
            raise GlobalBusinessException(404, f"Robot '{info_robot_name}' or '{parse_robot_name}' not found")

        info_robot_params = json.dumps({
            "knowledge_id": knowledge.id,
            "url": account_url
        })

        parse_robot_params = json.dumps({
            "knowledge_id": knowledge.id,
            "account_url": account_url,
            "pid": knowledge.pid,
        })

        try:
            # 创建两个任务并等待它们都完成
            await asyncio.gather(
                self.rpa_task_service.add_task(info_robot_id, info_robot_params),
                self.rpa_task_service.add_task(parse_robot_id, parse_robot_params)
            )
        except Exception as e:
            raise GlobalBusinessException(500, "创建RPA任务失败")

    async def upload_knowledge_file_by_url(self, knowledge_id: int, oss_url: str):
        """
        通过OSS URL上传文件到知识库
        
        Args:
            knowledge_id: 知识库ID
            oss_url: OSS文件URL
        
        Returns:
            上传结果信息
        """
        # 验证知识库是否存在
        knowledge = self.find_by_id(IpKnowledge(id=knowledge_id))
        if not knowledge:
            raise GlobalBusinessException("知识库不存在")

        # 从URL获取文件名
        filename = oss_url.split('/')[-1]

        # 创建文件记录
        from common.entity.EmbeddingFile import EmbeddingFile

        file_record = EmbeddingFile(
            file_name=filename,
            file_url=oss_url,
            file_type=filename.split('.')[-1] if '.' in filename else '',
            file_educate_status=0,
            is_delete=0,
            emb_type=1,  # 设置为用户知识类型
            create_by='system',
            update_by='system'
        )

        # 保存文件记录
        self.db.add(file_record)
        self.db.commit()

        # 将文件添加到知识库
        file_list = json.loads(knowledge.file_list) if knowledge.file_list else []
        file_list.append(file_record.id)
        knowledge.file_list = json.dumps(file_list)
        self.update(knowledge)

        # 触发文件处理（如果需要）
        # 这里可以添加调用处理文件的异步任务

        return {
            "knowledge_id": knowledge_id,
            "file_id": file_record.id,
            "file_name": filename,
            "file_url": oss_url
        }
