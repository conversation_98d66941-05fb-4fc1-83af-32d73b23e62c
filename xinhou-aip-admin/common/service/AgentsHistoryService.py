import json
from datetime import datetime
from typing import List, Dict, Any, Optional

from fastapi import HTTPException
from sqlalchemy.orm import Session
from xinhou_openai_framework.core.orm.service.BaseServiceImpl import BaseServiceImpl

from apps.admin.schema.AgentsHistorySchema import ReqAgentsHistoryUpdateSchema
from common.entity.AgentsHistory import AgentsHistory


class AgentsHistoryService(BaseServiceImpl[AgentsHistory]):
    def __init__(self, db: Session):
        super(AgentsHistoryService, self).__init__(db, AgentsHistory)

    def create(self, model: AgentsHistory) -> AgentsHistory:
        return super().save(model)

    def get_by_pid_and_task_id(self, pid: int, task_id: int) -> AgentsHistory:
        return self.db.query(AgentsHistory).filter(
            AgentsHistory.pid == pid,
            AgentsHistory.task_id == task_id
        ).first()

    async def update(self, model: ReqAgentsHistoryUpdateSchema):
        # 先获取现有记录
        existing_history = self.db.query(AgentsHistory).filter(AgentsHistory.id == model.id).first()
        if not existing_history:
            raise HTTPException(status_code=404, detail="AgentsHistory not found")
        
        # 只更新请求中包含的非空字段
        update_data = model.dict(exclude_unset=True)
        for key, value in update_data.items():
            if value is not None:  # 只更新非空值
                setattr(existing_history, key, value)
        
        # 更新时间和更新人
        existing_history.updated_at = datetime.now()
        if model.update_by:
            existing_history.update_by = model.update_by
        
        return super().update(existing_history)

    def get_by_pid(self, pid: int) -> List[AgentsHistory]:
        """通过PID获取历史记录列表"""
        return self.db.query(AgentsHistory).filter(
            AgentsHistory.pid == pid
        ).all()
        
    @staticmethod
    def parse_history_data(history: AgentsHistory) -> Dict[str, Any]:
        """解析历史记录数据
        
        Args:
            history: AgentsHistory实例
            
        Returns:
            解析后的历史记录数据
        """
        result = {
            "id": history.id,
            "task_id": history.task_id,
            "pid": history.pid,
            "status": history.status,
            "remark": history.remark,
            "created_at": history.created_at.strftime("%Y-%m-%d %H:%M:%S") if history.created_at else None,
            "updated_at": history.updated_at.strftime("%Y-%m-%d %H:%M:%S") if history.updated_at else None,
            "create_by": history.create_by,
            "update_by": history.update_by,
            "history": []
        }
        
        # 解析历史记录
        if history.history:
            try:
                # 将bytes转为字符串
                history_str = history.history.decode('utf-8')
                
                # 处理可能的转义字符
                if history_str.startswith('\"') and history_str.endswith('\"'):
                    history_str = history_str[1:-1].encode().decode('unicode_escape')
                
                # 解析JSON字符串为Python对象
                history_data = json.loads(history_str)
                result["history"] = history_data
            except Exception as e:
                # 如果解析失败，设置为空列表
                result["history"] = []
                result["parse_error"] = str(e)
        
        return result