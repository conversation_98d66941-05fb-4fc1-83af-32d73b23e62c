from typing import List, Optional
from sqlalchemy import select
from common.entity.Tool import Tool


class ToolService:
    def __init__(self, db_session):
        self.db = db_session

    def create(self, tool: Tool) -> Tool:
        """创建Tool"""
        self.db.add(tool)
        self.db.commit()
        self.db.refresh(tool)
        return tool

    def update(self, tool: Tool) -> Tool:
        """更新Tool"""
        stmt = select(Tool).where(Tool.id == tool.id, Tool.del_flag == 1)
        existing_tool = self.db.execute(stmt).scalar_one_or_none()
        
        if existing_tool:
            for key, value in tool.__dict__.items():
                if key != '_sa_instance_state' and value is not None:
                    setattr(existing_tool, key, value)
            
            self.db.commit()
            self.db.refresh(existing_tool)
            return existing_tool
        return None

    def get_by_id(self, id: int) -> Optional[Tool]:
        """根据ID获取Tool"""
        stmt = select(Tool).where(Tool.id == id, Tool.del_flag == 1)
        return self.db.execute(stmt).scalar_one_or_none()

    def list_all(self, status: Optional[int] = None) -> List[Tool]:
        """获取所有Tool列表"""
        stmt = select(Tool).where(Tool.del_flag == 1)
        if status is not None:
            stmt = stmt.where(Tool.status == status)
        return self.db.execute(stmt).scalars().all()

    def list_by_type(self, tool_type: int, status: Optional[int] = None) -> List[Tool]:
        """根据类型获取Tool列表"""
        stmt = select(Tool).where(
            Tool.tool_type == tool_type,
            Tool.del_flag == 1
        )
        if status is not None:
            stmt = stmt.where(Tool.status == status)
        return self.db.execute(stmt).scalars().all()

    def delete(self, id: int) -> bool:
        """软删除Tool"""
        stmt = select(Tool).where(Tool.id == id, Tool.del_flag == 1)
        tool = self.db.execute(stmt).scalar_one_or_none()
        
        if tool:
            tool.del_flag = 2
            self.db.commit()
            return True
        return False 