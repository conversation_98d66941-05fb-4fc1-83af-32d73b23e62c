from sqlalchemy.orm import Session
from xinhou_openai_framework.core.orm.service.BaseServiceImpl import BaseServiceImpl

from common.entity.IpPrompt import IpPrompt


class IpPromptService(BaseServiceImpl[IpPrompt]):
    """IP Prompt服务类"""
    
    def __init__(self, db: Session):
        super(IpPromptService, self).__init__(db, IpPrompt)

    def get_by_ip_name(self, ip_name: str):
        """根据IP名称获取Prompt"""
        return self.db.query(IpPrompt).filter(
            IpPrompt.ip_name == ip_name
        ).first()

    def get_by_pid(self, pid: int):
        """根据PID获取Prompt"""
        return self.db.query(IpPrompt).filter(
            IpPrompt.pid == pid
        ).first() 