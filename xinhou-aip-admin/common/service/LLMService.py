from typing import List, Optional

from fastapi import HTTPException
from loguru import logger
from sqlalchemy.orm import Session

from common.entity.LLM import LLM


class LLMService:
    def __init__(self, db: Session):
        self.db = db

    def create(self, llm: LLM) -> LLM:
        """创建LLM模型"""
        try:
            self.db.add(llm)
            self.db.flush()
            self.db.commit()
            return llm
        except Exception as e:
            self.db.rollback()
            logger.error(f"创建LLM失败: {str(e)}")
            raise HTTPException(status_code=500, detail=f"创建LLM失败: {str(e)}")

    def update(self, llm: LLM) -> LLM:
        """更新LLM模型"""
        try:
            existing = self.db.query(LLM).filter(LLM.id == llm.id).first()
            if not existing:
                raise HTTPException(status_code=404, detail="LLM不存在")
            
            for key, value in llm.__dict__.items():
                if value is not None and key != '_sa_instance_state':
                    setattr(existing, key, value)
            
            self.db.commit()
            return existing
        except Exception as e:
            self.db.rollback()
            logger.error(f"更新LLM失败: {str(e)}")
            raise HTTPException(status_code=500, detail=f"更新LLM失败: {str(e)}")

    def delete(self, llm_id: int) -> bool:
        """删除LLM模型(软删除)"""
        try:
            llm = self.db.query(LLM).filter(LLM.id == llm_id).first()
            if not llm:
                raise HTTPException(status_code=404, detail="LLM不存在")
            
            llm.del_flag = 2
            self.db.commit()
            return True
        except Exception as e:
            self.db.rollback()
            logger.error(f"删除LLM失败: {str(e)}")
            raise HTTPException(status_code=500, detail=f"删除LLM失败: {str(e)}")

    def get_by_id(self, llm_id: int) -> Optional[LLM]:
        """根据ID获取LLM模型"""
        return self.db.query(LLM).filter(
            LLM.id == llm_id,
            LLM.del_flag == 1
        ).first()

    def get_by_code(self, llm_code: str) -> Optional[LLM]:
        """根据编码获取LLM模型"""
        return self.db.query(LLM).filter(
            LLM.llm_code == llm_code,
            LLM.del_flag == 1
        ).first()

    def list_all(self, status: Optional[int] = None) -> List[LLM]:
        """获取所有LLM模型列表"""
        query = self.db.query(LLM).filter(LLM.del_flag == 1)
        if status is not None:
            query = query.filter(LLM.status == status)
        return query.all() 