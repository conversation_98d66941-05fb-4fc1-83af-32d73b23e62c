from typing import List, Dict

from loguru import logger
from sqlalchemy.orm import Session

from apps.admin.queue.message.AIPAnalysisMessage import AIPAnalysisMessage
from apps.admin.queue.message.AIPEmbeddingMessage import AIPEmbeddingMessage
from apps.admin.queue.producer.AIPAnalysisProducer import AIPAnalysisProducer
from apps.admin.queue.producer.AIPEmbeddingProducer import AIPEmbeddingProducer
from apps.admin.schema.KnowledgeSchema import KnowledgeSearchSchema
from common.contents.AgentsUserContents import AgentsUserContents
from common.entity.EmbeddingFile import EmbeddingFile
from common.entity.IpKnowledge import IpKnowledge
from common.remote.RagRemoteService import RagRemoteService
from common.service.EmbeddingDocumentService import EmbeddingDocumentService
from common.service.EmbeddingFileService import EmbeddingFileService
from common.service.KnowledgeService import KnowledgeService


class RagService:
    def __init__(self, db: Session):
        self.db = db
        self.embedding_file_service = EmbeddingFileService(db)
        self.embedding_doc_service = EmbeddingDocumentService(db)
        self.knowledge_service = KnowledgeService(db)

    async def delete_embedding(self, pid: int, file_id: int):
        """
        删除嵌入数据的业务逻辑
        """
        # 1. 查找并更新EmbeddingFile
        embedding_file = self.embedding_file_service.get_by_id(file_id)
        if not embedding_file or embedding_file.pid != pid:
            raise ValueError("未找到对应的文件记录")

        # 更新is_delete状态
        embedding_file.is_delete = 1
        self.embedding_file_service.update(embedding_file)

        # 2. 查找对应的EmbeddingDocument记录
        embedding_docs = self.embedding_doc_service.find_by_file_id(file_id)

        if embedding_docs:
            # 收集所有需要删除的collection_name和pk，将pk转换为字符串
            delete_data = {
                "collection_name": embedding_docs[0].collection_name,
                "pks": [str(doc.pk) for doc in embedding_docs]  # 将pk转换为字符串
            }

            logger.info(f"准备删除向量数据: {delete_data}")

            # 3. 调用远程服务删除向量数据
            result = RagRemoteService.delete_embedding(json_data=delete_data)

            # 4. 删除成功后,删除本地document记录
            for doc in embedding_docs:
                self.embedding_doc_service.delete(doc)

            return result
        return "No embedding documents found to delete"

    async def upload_file(self, file_infos: List[Dict], pid: int, emb_type: int, task_id: int = None, knowledge_id: int = None) -> List[Dict]:
        """
        上传文件的通用处理逻辑
        
        参数:
            file_infos: 文件信息列表
            pid: 项目ID
            emb_type: 嵌入类型 (1=知识库文件, 3=对标知识库, 5=其他)
            task_id: 任务ID (可选)
            knowledge_id: 知识库ID (可选)
        
        返回:
            已保存文件的信息列表
        """
        logger.info(f"开始处理文件上传: pid={pid}, emb_type={emb_type}, task_id={task_id}, knowledge_id={knowledge_id}, 文件数量={len(file_infos)}")
        
        # 知识库处理
        knowledge = None
        try:
            # 根据knowledge_id查找知识库
            if knowledge_id:
                logger.info(f"尝试查找指定ID的知识库: {knowledge_id}")
                knowledge = self.knowledge_service.find_by_id(IpKnowledge(id=knowledge_id))
                if not knowledge:
                    logger.warning(f"未找到指定ID的知识库: {knowledge_id}")
            
            # 如果未找到知识库，则根据pid和emb_type查找
            if not knowledge:
                logger.info(f"根据pid={pid}和emb_type={emb_type}查找知识库")
                knowledge_search = KnowledgeSearchSchema(pid=pid, knowledge_type=emb_type)
                knowledge_list = self.knowledge_service.find_all(knowledge_search)
                if knowledge_list:
                    knowledge = knowledge_list[0]
                    logger.info(f"找到匹配的知识库: id={knowledge.id}, source={knowledge.knowledge_source}")
            
            # 如果仍未找到知识库，则创建一个新的知识库
            if not knowledge:
                if emb_type == 1:
                    logger.info(f"未找到PID为{pid}的知识库，创建默认知识库")
                    knowledge = IpKnowledge(
                        pid=pid,
                        knowledge_source=f"我的文件",
                        knowledge_type=1,
                        create_by='admin',
                        update_by='admin',
                        file_list='[]'
                    )
                    knowledge = self.knowledge_service.save(knowledge)
                    logger.info(f"创建了新的知识库: id={knowledge.id}")
                elif emb_type == 3:
                    logger.info(f"未找到PID为{pid}的对标知识库，创建默认对标知识库")
                    knowledge = IpKnowledge(
                        pid=pid,
                        knowledge_source=f"我的抖音",
                        knowledge_type=3,
                        create_by='admin',
                        update_by='admin',
                        file_list='[]'
                    )
                    knowledge = self.knowledge_service.save(knowledge)
                    logger.info(f"创建了新的对标知识库: id={knowledge.id}")
                else:
                    logger.warning(f"未找到知识库且emb_type={emb_type}不支持自动创建")
        except Exception as e:
            logger.error(f"处理知识库时出错: {str(e)}", exc_info=True)
            raise ValueError(f"处理知识库失败: {str(e)}")
        
        # 处理文件信息
        saved_files = []
        for index, vo in enumerate(file_infos):
            try:
                logger.info(f"处理文件 {index+1}/{len(file_infos)}: {vo['file_name']}")
                
                # 创建文件记录
                file_data = EmbeddingFile(
                    pid=pid,
                    emb_type=emb_type,
                    file_name=vo['file_name'],
                    file_name_uuid=vo['file_name_uuid'],
                    file_type=vo['file_type'],
                    file_size=vo['file_size'],
                    file_path=vo['file_path'],
                    file_url=vo['file_url'],
                    file_review_pic=vo.get('file_review_pic'),  # 使用get方法避免KeyError
                    create_by="admin",
                    update_by="admin",
                    is_delete=0
                )
                
                # 保存文件记录
                saved_file = self.embedding_file_service.save(file_data)
                logger.info(f"文件记录已保存: id={saved_file['id']}")
                saved_files.append(saved_file)
                
                # 关联知识库（如果有）
                if knowledge:
                    logger.info(f"将文件 id={saved_file['id']} 关联到知识库 id={knowledge.id}")
                    self.knowledge_service.add_file_to_knowledge(knowledge.id, saved_file['id'])
                
                # 不处理emb_type=5的文件（其他类型）
                if emb_type != 5:
                    # 准备消息数据
                    message_data = {
                        "id": saved_file['id'],
                        "pid": pid,
                        "file_url": saved_file['file_url']
                    }
                    
                    # 如果有task_id，添加到消息数据中
                    if task_id:
                        message_data["task_id"] = task_id
                    
                    # 根据文件类型发送到不同的队列
                    file_type = vo['file_type'].lower()
                    if file_type == 'txt':
                        logger.info(f"发送文本文件 id={saved_file['id']} 到嵌入队列")
                        message = AIPEmbeddingMessage(**message_data)
                        await AIPEmbeddingProducer.embedding_push(AgentsUserContents.AIP_EMBEDDING_QUEUE, message)
                    else:
                        logger.info(f"发送非文本文件 id={saved_file['id']} 到分析队列")
                        message = AIPAnalysisMessage(**message_data)
                        await AIPAnalysisProducer.analysis_push(AgentsUserContents.AIP_ANALYSIS_QUEUE, message)
            except Exception as e:
                logger.error(f"处理文件 {vo.get('file_name', 'unknown')} 失败: {str(e)}", exc_info=True)
                # 继续处理其他文件，而不是直接失败整个批次
        
        logger.info(f"文件上传处理完成，成功处理 {len(saved_files)}/{len(file_infos)} 个文件")
        return saved_files

    async def hybrid_search(self, search_params: dict):
        """
        混合向量搜索的业务逻辑
        """
        try:
            # 准备知识库搜索参数 - 移除task_id
            knowledge_params = search_params.copy()
            knowledge_params["collection_name"] = "aip_knowledge_data"
            if "task_id" in knowledge_params:
                del knowledge_params["task_id"]

            # 执行知识库搜索
            knowledge_results = RagRemoteService.hybrid_search(json_data=knowledge_params)

            # 如果有task_id，继续搜索工作流数据
            if search_params.get("task_id"):
                work_params = search_params.copy()
                work_params["collection_name"] = "aip_work_data"
                work_results = RagRemoteService.hybrid_search(json_data=work_params)

                # 确保结果是列表类型
                if isinstance(work_results, dict) and 'results' in work_results:
                    work_results = work_results['results']
                if isinstance(knowledge_results, dict) and 'results' in knowledge_results:
                    knowledge_results = knowledge_results['results']

                # 转换为列表类型
                knowledge_results = knowledge_results if isinstance(knowledge_results, list) else []
                work_results = work_results if isinstance(work_results, list) else []

                # 合并结果并按score排序
                all_results = knowledge_results + work_results
                sorted_results = sorted(all_results, key=lambda x: x.get('score', 0), reverse=True)

                # 只返回limit指定数量的结果
                limit = search_params.get("limit", 5)
                limited_results = sorted_results[:limit]
                return limited_results

            # 如果没有task_id，只返回知识库结果
            if isinstance(knowledge_results, dict) and 'results' in knowledge_results:
                knowledge_results = knowledge_results['results']
            knowledge_results = knowledge_results if isinstance(knowledge_results, list) else []

            sorted_results = sorted(knowledge_results, key=lambda x: x.get('score', 0), reverse=True)
            limit = search_params.get("limit", 5)
            limited_results = sorted_results[:limit]
            return limited_results

        except Exception as e:
            logger.error(f"混合搜索执行失败: {str(e)}")
            raise e
