# !/usr/bin/python3
# -*- coding: utf-8 -*-

"""
用户信息表服务类
----------------------------------------------------
@Project :   xinhou-openai-admin
@File    :   User.py
@Contact :   <EMAIL>

@Modify Time      <AUTHOR>    @Desciption
------------      -------    --------    -----------
2023/04/16 22:04  peng.shen   v1.0.0     None
"""
from datetime import datetime, timed<PERSON>ta
from typing import Optional

from fastapi import HTTPException, status
from fastapi.security import OAuth2PasswordBearer
from jose import jwt, JWTError
from loguru import logger
from passlib.context import CryptContext
from pydantic import BaseModel
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.orm import Session
from xinhou_openai_framework.core.cache.RedisConnectionPool import RedisConnectionPool
from xinhou_openai_framework.core.context.model.AppContext import AppContext
from xinhou_openai_framework.core.context.model.SystemContext import ctx
from xinhou_openai_framework.core.exception.CodeEnum import CodeEnum
from xinhou_openai_framework.core.orm.service.BaseServiceImpl import BaseServiceImpl
from xinhou_openai_framework.core.reponse.R import R
from xinhou_openai_framework.utils.DateUtil import DateUtil
from xinhou_openai_framework.utils.Md5Util import Md5Util

from apps.admin.schema.UserSchema import ReqUserMobileRegisterSchema
from common.entity.User import User
from common.utils.AliSmsUtils import AliSmsUtils
from common.utils.HuoshanSmsUtils import HuoshanSmsUtils

context: AppContext = ctx.__getattr__("context")


class TokenData(BaseModel):
    username: str = None


pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

oauth2_scheme = OAuth2PasswordBearer(tokenUrl="token")


def create_access_token(data: dict, expires_delta: timedelta):
    to_encode = data.copy()
    expire = datetime.utcnow() + expires_delta
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, context.framework.SECRET_KEY, algorithm=context.framework.ALGORITHM)
    return encoded_jwt


class UserService(BaseServiceImpl[User]):
    """
    用户服务类
    """

    def __init__(self, db: Session):
        super(UserService, self).__init__(db, User)

    async def login(self, model: User):
        model.del_flag = 1
        model.login_pwd = Md5Util.md5_string(model.login_pwd)
        users = self.find_all(model)
        if users and len(users) > 0:
            user = users[0]
            # Generate JWT token
            access_token_expires = timedelta(minutes=context.framework.ACCESS_TOKEN_EXPIRE_MINUTES)
            access_token = create_access_token(
                data={"sub": user.login_name}, expires_delta=access_token_expires
            )

            # 将 token 保存到 Redis
            try:
                redis_pool = await RedisConnectionPool().get_pool()
                redis_key = f"user_token:{user.login_name}"
                stored_token = await redis_pool.get(redis_key)
                if stored_token:
                    access_token = stored_token
                    logger.info(f"用户 {user.login_name} 的 token 已存在 Redis")
                else:
                    await redis_pool.setex(
                        redis_key,
                        int(access_token_expires.total_seconds()),
                        access_token
                    )
                    logger.info(f"用户 {user.login_name} 的 token 已保存到 Redis")
            except Exception as e:
                logger.error(f"保存 token 到 Redis 失败: {str(e)}")

            return {"access_token": access_token, "token_type": "bearer"}
        else:
            raise R.jsonify(CodeEnum.LOGIN_ERR_PWD)

    def register(self, model: User):
        model.login_pwd = Md5Util.md5_string(model.login_pwd)
        model.user_type = 2
        model.update_by = "admin"
        model.updated_at = DateUtil.get_current_datetime()
        model.create_by = "admin"
        model.created_at = DateUtil.get_current_datetime()
        return self.save(model)

    async def get_user_by_token(self, token: str):
        try:
            # 先验证 JWT token 的有效性
            payload = jwt.decode(token, context.framework.SECRET_KEY, algorithms=[context.framework.ALGORITHM])
            username = payload.get("sub")
            if username is None:
                raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="无效的身份验证凭据")

            # 验证 Redis 中是否存在该 token
            try:
                redis_pool = await RedisConnectionPool().get_pool()
                redis_key = f"user_token:{username}"
                stored_token = await redis_pool.get(redis_key)

                if not stored_token or stored_token.decode('utf-8') != token:
                    raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="token已失效，请重新登录")
            except HTTPException as he:
                # 如果是我们主动抛出的 HTTPException，直接继续抛出
                raise he
            except Exception as e:
                logger.error(f"从 Redis 验证 token 失败: {str(e)}")
                # Redis 服务异常时，也要求重新登录
                raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="token验证失败，请重新登录")

            users = self.find_all(User(login_name=username))
            if not users:
                raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="用户未找到")
            return users[0]
        except JWTError:
            raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="无效的身份验证凭据")

    async def change_password(self, user_id: int, old_password: str, new_password: str) -> bool:
        user = self.find_by_id(User(id=user_id))
        if not user:
            return False

        if Md5Util.md5_string(old_password) != user.login_pwd:
            return False

        user.login_pwd = Md5Util.md5_string(new_password)
        user.update_by = str(user_id)
        user.updated_at = DateUtil.get_current_datetime()
        self.update(user)

        # 删除用户的 token
        try:
            redis_pool = await RedisConnectionPool().get_pool()
            await redis_pool.delete(f"user_token:{user.login_name}")
        except Exception as e:
            logger.error(f"删除用户token失败: {str(e)}")

        return True

    async def change_password_by_mobile(self, mobile: str, verify_code: str, new_password: str, redis) -> tuple:
        """
        通过手机号和验证码修改密码
        :param mobile: 手机号
        :param verify_code: 验证码
        :param new_password: 新密码
        :param redis: redis连接池
        :return: (是否成功, 消息)
        """
        # 验证验证码
        verify_result, verify_msg = await self.verify_code(mobile, verify_code, redis)
        if not verify_result:
            return False, verify_msg

        # 查找用户
        users = self.find_all(User(mobile=mobile))
        if not users:
            return False, "未找到该手机号对应的用户"

        user = users[0]

        # 更新密码
        user.login_pwd = Md5Util.md5_string(new_password)
        user.update_by = str(user.id)
        user.updated_at = DateUtil.get_current_datetime()
        self.update(user)

        # 删除用户的 token
        try:
            await redis.delete(f"user_token:{user.login_name}")
        except Exception as e:
            logger.error(f"删除用户token失败: {str(e)}")

        return True, "密码修改成功"

    async def send_verify_code(self, mobile: str, redis_pool) -> tuple:
        """
        发送验证码到指定手机号
        :param mobile: 手机号
        :param redis_pool: redis连接池
        :return: 是否发送成功
        """
        verify_code = AliSmsUtils.generate_code()
        # 将验证码存储到Redis,设置5分钟过期
        redis_pool.setnx(f"verify_code:{mobile}", verify_code)
        redis_pool.expire(f"verify_code:{mobile}", 300)
        # 调用短信发送服务发送验证码
        sms_info = AliSmsUtils.send_sms(verify_code, mobile)
        if sms_info.get("Code") != "OK":
            return False, sms_info.get("Message")
        return True, "发送成功"

    async def send_verify_code_v2(self, mobile: str, redis_pool) -> tuple:
        """
        发送验证码到指定手机号
        :param mobile: 手机号
        :param redis_pool: redis连接池
        :return: 是否发送成功
        """
        verify_code = HuoshanSmsUtils.generate_code()
        # 将验证码存储到Redis,设置5分钟过期
        await redis_pool.setnx(f"verify_code:{mobile}", verify_code)
        await redis_pool.expire(f"verify_code:{mobile}", 300)
        # 调用短信发送服务发送验证码
        sms_info = HuoshanSmsUtils.send_sms_verify_code(verify_code, mobile)
        if sms_info.get("Code") != "OK":
            return False, sms_info.get("Message")
        return True, "发送成功"

    async def verify_code(self, mobile: str, code: str, redis) -> tuple:
        """
        验证手机验证码
        :param mobile: 手机号
        :param code: 验证码
        :param redis: redis连接池
        :return: 验证是否成功
        """
        stored_code = await redis.get(f"verify_code:{mobile}")
        if not stored_code:
            return False, "验证码已超时, 请重新获取！"
        if str(stored_code, 'utf-8') != code:
            return False, "验证码错误，请重试！"
        await redis.delete(f"verify_code:{mobile}")  # 验证成功后删除验证码
        return True, "验证通过"

    async def mobile_login(self, mobile: str, verify_code: str, redis):
        """
        手机验证码登录
        :param mobile: 手机号
        :param verify_code: 验证码
        :param redis: redis连接池
        :return: 登录成功返回token,失败返回None
        """
        users = self.find_all(User(mobile=mobile))
        user_exist = users and len(users) > 0
        if not user_exist:
            return False, "用户不存在, 请确认后重试"
        is_verify, message = await self.verify_code(mobile, verify_code, redis)
        if not is_verify:
            return False, message
        user = users[0]
        # 将 token 保存到 Redis
        access_token_expires = timedelta(minutes=context.framework.ACCESS_TOKEN_EXPIRE_MINUTES)
        access_token = create_access_token(
            data={"sub": user.login_name}, expires_delta=access_token_expires
        )
        try:
            redis_pool = await RedisConnectionPool().get_pool()
            redis_key = f"user_token:{user.login_name}"
            stored_token = await redis_pool.get(redis_key)
            if stored_token:
                access_token = stored_token
                logger.info(f"用户 {user.login_name} 的 token 已存在 Redis")
            else:
                await redis_pool.setex(
                    redis_key,
                    int(access_token_expires.total_seconds()),
                    access_token
                )
                logger.info(f"用户 {user.login_name} 的 token 已保存到 Redis")
        except Exception as e:
            logger.error(f"保存 token 到 Redis 失败: {str(e)}")

        return True, {"access_token": access_token, "token_type": "bearer", "mobile": mobile}

    async def mobile_register(self, model: ReqUserMobileRegisterSchema, redis) -> tuple:
        """
        手机号注册并登录
        :param model: 注册信息
        :param redis: redis连接池
        :return: 注册并登录成功返回token,失败返回None
        """
        # 检查手机号是否已注册
        existing_user = self.find_all(User(mobile=model.mobile))
        if existing_user and len(existing_user) > 0:  # 注册过的手机号 就直接走登录逻辑
            login_info = await self.mobile_login(model.mobile, model.verify_code, redis)
            return login_info
        is_verify, message = await self.verify_code(model.mobile, model.verify_code, redis)
        if not is_verify:
            return False, message

        # 创建新用户
        new_user = User(
            login_name=model.mobile,  # 使用手机号作为登录名
            login_pwd='8ddcff3a80f4189ca1c9d4d902c3c909',  # 默认密码 88888888
            mobile=model.mobile,
            user_name=model.mobile,
            user_type=2,  # 假设2为普通用户
            status=1,  # 假设1为正常状态
            del_flag=1,  # 假设1为未删除
            create_by="system",
            created_at=DateUtil.get_current_datetime(),
            update_by="system",
            updated_at=DateUtil.get_current_datetime()
        )
        self.save(new_user)
        # 生成JWT token
        access_token_expires = timedelta(minutes=context.framework.ACCESS_TOKEN_EXPIRE_MINUTES)
        access_token = create_access_token(
            data={"sub": new_user.login_name}, expires_delta=access_token_expires
        )
        return True, {"access_token": access_token, "token_type": "bearer", "mobile": model.mobile}

    def update_remain_point(self, user_id: int, point_amount: float) -> Optional[User]:
        """
        更新用户剩余点数
        
        Args:
            user_id: 用户ID
            point_amount: 要增加的点数
            
        Returns:
            更新后的用户对象，如果未找到返回None
        """
        user = self.find_by_id(User(id=user_id))
        if not user:
            return None

        user.remain_point = (user.remain_point or 0) + point_amount
        user.update_by = str(user_id)
        user.updated_at = DateUtil.get_current_datetime()

        try:
            self.db.commit()
            logger.info(f"成功更新用户 {user_id} 的剩余点数")
            return user
        except SQLAlchemyError as e:
            self.db.rollback()
            logger.error(f"更新用户 {user_id} 点数时发生错误: {str(e)}")
            raise e

    def check_and_deduct_points(self, user_id: int, points: float) -> bool:
        """
        检查并扣除用户点数
        
        Args:
            user_id: 用户ID
            points: 需要扣除的点数
            
        Returns:
            bool: 是否扣除成功
        """
        user = self.find_by_id(User(id=user_id))
        if not user or (user.remain_point or 0) < points:
            return False

        try:
            user.remain_point -= points
            user.update_by = str(user_id)
            user.updated_at = DateUtil.get_current_datetime()
            self.db.commit()
            logger.info(f"成功扣除用户 {user_id} 的点数: {points}")
            return True
        except SQLAlchemyError as e:
            self.db.rollback()
            logger.error(f"扣除用户 {user_id} 点数时发生错误: {str(e)}")
            return False
