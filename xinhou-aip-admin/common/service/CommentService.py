# !/usr/bin/python3
# -*- coding: utf-8 -*-

"""
任务服务类
----------------------------------------------------
@Project :   xinhou-openai-admin
@File    :   commentService.py
@Contact :   <EMAIL>

@Modify Time      <AUTHOR>    @Desciption
------------      -------    --------    -----------
2023/04/16 22:04  peng.shen   v1.0.0     None
2023/07/18 17:32  ChatGPT     v1.1.0     添加查询任务列表方法
2023/07/18 17:45  ChatGPT     v1.1.1     修复timedelta导入问题
2024/07/18 18:30  ChatGPT     v1.2.0     添加CRUD操作
"""

from typing import List, Optional

from sqlalchemy.orm import Session
from xinhou_openai_framework.core.orm.service.BaseServiceImpl import BaseServiceImpl
from xinhou_openai_framework.pages.PageHelper import PageHelper

from common.entity.Comment import Comment


class CommentService(BaseServiceImpl[Comment]):
    """
    神评论服务类
    """

    def __init__(self, db: Session):
        super(CommentService, self).__init__(db, Comment)

    def find_by(self, search: PageHelper) -> List[Comment]:
        """
        分页查询任务
        """
        return super().find_by(search)

    def find_all(self, search: dict) -> List[Comment]:
        """
        查询所有符合条件的任务
        """
        return super().find_all(search)

    def save(self, comment: Comment) -> Comment:
        """
        保存任务
        """
        return super().save(comment)

    def update(self, comment: Comment) -> Optional[Comment]:
        """
        更新任务
        """
        return super().update(comment)

    def delete(self, comment: Comment) -> None:
        """
        删除任务.
        """
        super().delete(comment)

    def find_by_id(self, comment: Comment) -> Optional[Comment]:
        """
        根据ID查询任务
        """
        return super().find_by_id(comment)
