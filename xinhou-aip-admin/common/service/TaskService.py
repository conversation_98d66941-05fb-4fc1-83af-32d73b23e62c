# !/usr/bin/python3
# -*- coding: utf-8 -*-

"""
任务服务类
----------------------------------------------------
@Project :   xinhou-openai-admin
@File    :   TaskService.py
@Contact :   <EMAIL>

@Modify Time      <AUTHOR>    @Desciption
------------      -------    --------    -----------
2023/04/16 22:04  peng.shen   v1.0.0     None
2023/07/18 17:32  ChatGPT     v1.1.0     添加查询任务列表方法
2023/07/18 17:45  ChatGPT     v1.1.1     修复timedelta导入问题
2024/07/18 18:30  ChatGPT     v1.2.0     添加CRUD操作
"""

from datetime import datetime, timedelta
from typing import List, Optional, Dict, Any

from fastapi import HTTPException
from loguru import logger
from sqlalchemy import and_, or_, desc
from sqlalchemy.orm import Session
from xinhou_openai_framework.core.orm.service.BaseServiceImpl import BaseServiceImpl
from xinhou_openai_framework.pages.PageHelper import PageHelper

from apps.admin.schema.TaskSchema import ReqTaskFindSchema
from common.entity.EmbeddingFile import EmbeddingFile
from common.entity.MediaModel import MediaModel
from common.entity.Task import Task
from common.entity.Ip import Ip
from common.entity.VoiceModel import VoiceModel
from common.service.EmbeddingFileService import EmbeddingFileService
from common.service.MediaModelService import MediaModelService
from common.service.VoiceModelService import VoiceModelService
from common.service.WorkService import WorkService


class TaskService(BaseServiceImpl[Task]):
    """
    任务服务类
    """

    def __init__(self, db: Session):
        super(TaskService, self).__init__(db, Task)

    async def get_latest_tasks(self, pid: int):
        """
        获取24小时内最新的5条记录
        """
        end_time = datetime.now()
        start_time = end_time - timedelta(hours=24)

        latest_tasks = self.db.query(Task).filter(
            Task.pid == pid,
            Task.created_at.between(start_time, end_time)
        ).order_by(Task.created_at.desc()).limit(5).all()
        ip_info = self.find_by_id(Ip(id=pid))
        info = {"video_point": ip_info.remain_point, "expire_time": ip_info.expire_time, "works": latest_tasks}
        return info

    async def query_tasks(self, query_date: datetime, pid: int):
        tasks = self.db.query(Task).filter(
            and_(
                Task.pid == pid,
                Task.created_at >= query_date,
                Task.created_at < query_date + timedelta(days=1),
                Task.del_flag == 1
            )
        ).all()

        result = {
            "drafts": [],
            "scripts": [],
            "videos": []
        }

        for task in tasks:
            task_dict = {
                "id": task.id,
                "pid": task.pid,
                "status": task.status,
                "progress": task.progress,
                "created_at": task.created_at,
                "remark": task.remark,
                "audio_model_id": task.audio_model_id,
                "video_model_id": task.video_model_id
            }

            if task.progress == 0:
                result["drafts"].append(task_dict)
            elif task.progress == 1:
                result["scripts"].append(task_dict)
            elif task.progress == 3:
                result["videos"].append(task_dict)

        return result

    def find_by(self, search: PageHelper) -> List[Task]:
        """
        分页查询任务
        """
        return super().find_by(search)

    def find_all(self, search: ReqTaskFindSchema) -> List[Dict[str, Any]]:
        query = self.db.query(Task)

        # 添加过滤条件
        if search.pid:
            query = query.filter(Task.pid == search.pid)
        if search.status:
            query = query.filter(Task.status == search.status)
        if search.progress:
            query = query.filter(Task.progress == search.progress)

        # 添加日期范围过滤
        if search.date_from:
            query = query.filter(Task.created_at >= search.date_from)
        if search.date_to:
            query = query.filter(Task.created_at <= search.date_to)

        # 添加模糊搜索
        if search.search_term:
            search_filter = or_(
                Task.title.like(f"%{search.search_term}%"),
            )
            query = query.filter(search_filter)
        # 添加按 id 降序排序
        query = query.order_by(desc(Task.id))
        # 执行查询并返回结果
        results = query.all()

        # 将查询结果转换为可序列化的字典列表
        return [self._to_dict(task) for task in results]

    def _to_dict(self, task: Task) -> Dict[str, Any]:
        return {
            "id": task.id,
            "pid": task.pid,
            "status": task.status,
            "progress": task.progress,
            "title": task.title,
            "created_at": task.created_at,
            "updated_at": task.updated_at,
            "audio_model_id": task.audio_model_id,
            "video_model_id": task.video_model_id
        }

    def save(self, task: Task) -> Task:
        """
        保存任务
        """
        return super().save(task)

    def update(self, task: Task) -> Optional[Task]:
        """
        更新任务
        """
        return super().update(task)

    def delete(self, task: Task) -> None:
        """
        删除任务.
        """
        super().delete(task)

    def find_by_id(self, task: Task) -> Optional[Task]:
        """
        根据ID查询任务
        """
        return super().find_by_id(task)

    async def get_task_status(self, task_id: int) -> Dict[str, Any]:
        # 查询任务信息
        task = self.find_by_id(Task(id=task_id))
        if not task:
            raise HTTPException(status_code=404, detail="Task not found")

        # 初始化响应数据
        response_data = {
            "status_code": 200,
            "message": "",
            "task_info": {
                "id": task.id,
                "progress": task.progress,
                "title": task.title,
                "remark": task.remark,
                "created_at": task.created_at,
                "updated_at": task.updated_at
            },
            "audio_info": None,
            "video_info": None,
            "audio_model_info": None,
            "video_model_info": None
        }

        try:
            # 查询音频和视频作品信息
            work_service = WorkService(self.db)
            audio_work = await work_service.query_work_by_type(task_id, 2)  # 音频类型
            video_work = await work_service.query_work_by_type(task_id, 3)  # 视频类型

            # 获取文件信息服务
            embedding_file_service = EmbeddingFileService(self.db)

            # 处理音频信息
            # 处理音频信息
            if audio_work and audio_work.file_id:
                # 查找该任务下所有音频类型的作品
                audio_works = await work_service.query_works_by_type(task_id, 2)
                # 获取原始音频（没有变速处理标记的）
                original_audio_work = next(
                    (work for work in audio_works if not work.remark or 'audio_model_id' in work.remark),
                    audio_works[0] if audio_works else None
                )

                if original_audio_work:
                    audio_file = embedding_file_service.find_by_id(EmbeddingFile(id=original_audio_work.file_id))
                    if audio_file:
                        response_data["audio_info"] = {
                            "file_url": audio_file.file_url,
                            "created_at": original_audio_work.created_at.isoformat() if original_audio_work.created_at else None,
                            "updated_at": original_audio_work.updated_at.isoformat() if original_audio_work.updated_at else None
                        }

            # 处理视频信息
            if video_work and video_work.file_id:
                video_file = embedding_file_service.find_by_id(EmbeddingFile(id=video_work.file_id))
                if video_file:
                    response_data["video_info"] = {
                        "file_url": video_file.file_url,
                        "created_at": video_work.created_at.isoformat() if video_work.created_at else None,
                        "updated_at": video_work.updated_at.isoformat() if video_work.updated_at else None
                    }

            # 处理音频模型信息
            if task.audio_model_id:
                voice_model_service = VoiceModelService(self.db)
                audio_model = voice_model_service.find_by_id(VoiceModel(id=int(task.audio_model_id)))
                if audio_model:
                    response_data["audio_model_info"] = {
                        "id": audio_model.id,
                        "voice_name": audio_model.voice_name,
                        "voice_url": audio_model.voice_url
                    }

            # 处理视频模型信息
            if task.video_model_id:
                media_model_service = MediaModelService(self.db)  # Instantiate the service
                video_model = media_model_service.find_by_id(MediaModel(id=int(task.video_model_id)))
                if video_model:
                    response_data["video_model_info"] = {
                        "id": video_model.id,
                        "title": video_model.title,
                        "media_url": video_model.media_url,
                        "pic_url": video_model.pic_url
                    }

            # 设置状态消息
            progress_messages = {
                0: "口播稿初始化中",
                1: "正在生成口播稿",
                2: "口播稿已完成，等待音频制作",
                3: "口播稿制作失败",
                4: "音频合成中",
                5: "音频制作完成，等待视频制作",
                6: "音频制作失败",
                7: "视频生成中",
                8: "制作完成",
                9: "视频制作失败"
            }
            response_data["message"] = progress_messages.get(task.progress, "未知状态")

            return response_data

        except Exception as e:
            logger.error(f"获取任务状态详情失败: {str(e)}")
            raise HTTPException(status_code=500, detail=f"获取任务状态详情失败: {str(e)}")
