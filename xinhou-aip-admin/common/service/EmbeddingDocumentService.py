from typing import List

from sqlalchemy.orm import Session
from xinhou_openai_framework.core.orm.service.BaseServiceImpl import BaseServiceImpl

from common.entity.EmbeddingDocument import EmbeddingDocument


class EmbeddingDocumentService(BaseServiceImpl[EmbeddingDocument]):
    """
    训练内容信息表服务类
    """

    def __init__(self, db: Session):
        super(EmbeddingDocumentService, self).__init__(db, EmbeddingDocument)

    def batch_save(self, embedding_file_id: str,collection_name: str, docs: List[dict]):
        models = []
        for doc in docs:
            models.append(EmbeddingDocument(**{
                "collection_name": collection_name,
                "metadata_source": doc.get('file_name'),
                "embedding_file_id": embedding_file_id,
                "page_content": doc.get('content'),
                "pk": doc.get('pk'),
                "create_by": "admin",
                "update_by": "admin",
            }))
        self.save_all(models)

    def find_by_pks(self, pks: List[str]):
        lists: List[EmbeddingDocument] = self.db.query(EmbeddingDocument).filter(self.cls.pk.in_(pks)).all()
        return lists

    def find_by_pk(self, pk: str):
        lists: List[EmbeddingDocument] = self.db.query(EmbeddingDocument).filter(self.cls.pk == (pk)).all()
        return lists

    def batch_delete_by_embedding_file_id(self, tenant_id, embedding_file_id) -> bool:
        lists: List[EmbeddingDocument] = self.find_all(EmbeddingDocument(**{
            "embedding_file_id": embedding_file_id
        }))
        return self.delete_all(lists)

    def find_by_file_id(self, file_id: int) -> List[EmbeddingDocument]:
        """
        根据文件ID查找嵌入文档记录
        :param file_id: 文件ID
        :return: 嵌入文档列表
        """
        return self.db.query(EmbeddingDocument).filter(
            EmbeddingDocument.embedding_file_id == file_id
        ).all()
