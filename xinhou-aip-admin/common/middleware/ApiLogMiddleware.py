# !/usr/bin/python3
# -*- coding: utf-8 -*-

"""
API日志中间件
----------------------------------------------------
@Project :   xinhou-openai-admin
@File    :   ApiLogMiddleware.py
@Contact :   <EMAIL>

@Modify Time      <AUTHOR>    @Desciption
------------      -------    --------    -----------
2023/08/01 10:00  peng.shen   v1.0.0     None
"""

import json
import time
import uuid
from typing import Callable

from fastapi import Request, Response
from jose import jwt, JWTError
from loguru import logger
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.types import ASGIApp
from xinhou_openai_framework.core.db.DatabaseManager import DatabaseManager

from common.entity.ApiLog import ApiLog
from common.entity.User import User


class ApiLogMiddleware(BaseHTTPMiddleware):
    """
    API日志中间件
    """

    def __init__(self, app: ASGIApp):
        super().__init__(app)
        # 延迟加载context，避免循环依赖
        self.context = None
        # 获取数据库管理器实例
        self.db_manager = DatabaseManager.get_instance()

    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        # 开始时间
        start_time = time.time()

        # 创建一个新的数据库会话
        db = None
        # 保存请求头信息，以便在后续处理中使用
        headers = dict(request.headers)
        auth_header = headers.get("authorization", "")

        try:
            # 获取数据库会话
            db = self.db_manager.SessionLocal()

            # 获取当前用户
            current_user = None
            try:
                # 尝试从请求中获取当前用户
                if auth_header and auth_header.startswith("Bearer ") and auth_header != "Bearer undefined":
                    token = auth_header.replace("Bearer ", "")
                    if token and token.strip():  # 确保token不为空
                        try:
                            # 延迟加载context
                            if self.context is None:
                                from xinhou_openai_framework.core.context.model.SystemContext import ctx
                                self.context = ctx.__getattr__("context")

                            # 解析JWT token
                            payload = jwt.decode(
                                token,
                                self.context.framework.SECRET_KEY,
                                algorithms=[self.context.framework.ALGORITHM]
                            )
                            username = payload.get("sub")
                            if username:
                                # 直接使用原生db查询用户信息
                                try:
                                    user = db.query(User).filter(User.login_name == username).first()
                                    if user:
                                        current_user = user
                                        logger.info(f"dispatch中获取到用户: ID={user.id}, 用户名={user.login_name}")
                                except Exception as e:
                                    logger.error(f"查询用户信息失败: {str(e)}")
                        except JWTError:
                            logger.error("JWT token解析失败")
                        except Exception as e:
                            logger.error(f"解析token失败: {str(e)}")
            except Exception as e:
                logger.error(f"获取当前用户失败: {str(e)}")

            # 获取请求体
            request_body = None
            if request.method in ["POST", "PUT", "PATCH"]:
                try:
                    # 使用更安全的方式获取请求体
                    # 不再使用 request.body() 和 request._receive()，这可能导致请求处理阻塞
                    content_type = headers.get("content-type", "")
                    if "application/json" in content_type.lower():
                        # 从请求头中获取内容长度
                        content_length = headers.get("content-length", "0")
                        if content_length and int(content_length) > 0:
                            # 从日志中获取json_body
                            try:
                                # 尝试从请求对象中获取已解析的JSON
                                if hasattr(request, "json"):
                                    json_data = await request.json()
                                    request_body = json.dumps(json_data, ensure_ascii=False)
                                elif hasattr(request, "_json"):
                                    request_body = json.dumps(request._json, ensure_ascii=False)
                            except Exception as e:
                                logger.error(f"获取JSON请求体失败: {str(e)}")
                except Exception as e:
                    logger.error(f"获取请求体失败: {str(e)}")

            try:
                # 调用下一个中间件或路由处理函数
                response = await call_next(request)

                # 尝试获取响应体
                response_body = None
                try:
                    # 检查响应类型，处理各种响应类型
                    response_class_name = response.__class__.__name__
                    response_module = response.__class__.__module__
                    
                    # 检查内容类型和响应头，判断是否为真正的流式响应
                    content_type = response.headers.get('content-type', '')
                    is_real_streaming = (
                        'text/event-stream' in content_type.lower() or  # SSE流
                        'multipart/x-mixed-replace' in content_type.lower() or  # 多部分替换流
                        'application/octet-stream' in content_type.lower() or  # 二进制流
                        ('content-disposition' in response.headers and 'attachment' in response.headers.get('content-disposition', '').lower()) or  # 文件下载
                        any(stream_type in content_type.lower() for stream_type in ['audio/', 'video/'])  # 音视频流
                    )
                    
                    # 检查是否是明确的流式响应类型（不包括中间件包装的_StreamingResponse）
                    is_explicit_streaming_response = (
                        ('StreamingResponse' in response_class_name and response_class_name != '_StreamingResponse') or 
                        'FileResponse' in response_class_name
                    )
                    
                    # 真正的流式响应：明确的流式响应类型或内容类型/头部表明是流
                    is_streaming = is_real_streaming or is_explicit_streaming_response
                    
                    logger.debug(f"响应类型: {response_class_name}, 模块: {response_module}, 内容类型: {content_type}, 是否流式: {is_streaming}")
                    
                    if not is_streaming:
                        # 获取响应体内容
                        response_body_bytes = b""
                        
                        # 对于_StreamingResponse，我们需要读取body_iterator
                        if response_class_name == '_StreamingResponse' and hasattr(response, 'body_iterator'):
                            # 保存原始迭代器
                            original_iterator = response.body_iterator
                            chunks = []
                            
                            # 读取所有数据
                            async for chunk in original_iterator:
                                chunks.append(chunk)
                                response_body_bytes += chunk
                            
                            # 创建新的响应对象
                            response = Response(
                                content=response_body_bytes,
                                status_code=response.status_code,
                                headers=dict(response.headers),
                                media_type=response.media_type
                            )
                        elif hasattr(response, "body") and response.body:
                            response_body_bytes = response.body
                        
                        # 创建新的响应对象，保留原始响应的所有属性
                        if response_body_bytes:
                            # 如果我们还没有创建新的响应对象
                            if response_class_name != 'Response':
                                response = Response(
                                    content=response_body_bytes,
                                    status_code=response.status_code,
                                    headers=dict(response.headers),
                                    media_type=response.media_type
                                )
                            
                            # 尝试解码响应体用于日志记录
                            try:
                                response_body = response_body_bytes.decode('utf-8', errors='replace')
                            except:
                                response_body = str(response_body_bytes)
                        else:
                            logger.warning("响应体为空")
                    else:
                        logger.info(f"检测到真正的流式响应: {response_class_name}，不记录响应体内容")
                        response_body = f"[{response_class_name}，内容未记录]"
                except Exception as e:
                    logger.error(f"获取响应体失败: {str(e)}")
                    logger.exception(e)

                # 计算执行时间
                execution_time = int((time.time() - start_time) * 1000)

                # 记录日志 - 只记录非GET请求
                if request.method != "GET" and request.method != "OPTIONS":
                    # 对于流式响应，我们在返回响应前记录日志
                    # 这样可以避免等待整个流完成
                    if 'is_streaming' in locals() and is_streaming:
                        await self._create_log(request, response, execution_time, current_user, db, request_body, auth_header, is_streaming=True)
                    else:
                        await self._create_log(request, response, execution_time, current_user, db, request_body, auth_header)

                return response
            except Exception as e:
                # 计算执行时间
                execution_time = int((time.time() - start_time) * 1000)

                # 记录错误日志 - 即使是GET请求，错误也记录
                await self._create_error_log(request, e, execution_time, current_user, db, request_body, auth_header)

                # 重新抛出异常，让异常处理中间件处理
                raise e
        finally:
            # 关闭数据库会话
            if db is not None:
                db.close()

    async def _create_log(self, request: Request, response: Response,
                          execution_time: int, current_user, db, request_body=None, auth_header=None, is_streaming=False) -> None:
        """
        创建API日志
        :param request: 请求对象
        :param response: 响应对象
        :param execution_time: 执行时间(毫秒)
        :param current_user: 当前用户
        :param db: 数据库会话
        :param request_body: 请求体内容
        :param auth_header: 认证头信息
        :param is_streaming: 是否为流式响应
        :return: None
        """
        try:
            # 获取请求信息
            url = str(request.url)
            method = request.method
            headers = dict(request.headers)
            client_ip = request.client.host if request.client else None
            user_agent = headers.get("user-agent", "")

            # 获取请求参数
            params = dict(request.query_params)

            # 获取响应信息
            response_code = response.status_code
            response_data = None

            # 获取响应体
            try:
                # 如果已知是流式响应，直接标记
                if is_streaming:
                    logger.info(f"流式响应: {response.__class__.__name__}，不记录响应体内容")
                    response_data = f"[流式响应，内容未记录]"
                else:
                    # 检查响应类型
                    response_class_name = response.__class__.__name__
                    
                    # 检查内容类型和响应头，判断是否为真正的流式响应
                    content_type = response.headers.get('content-type', '')
                    is_real_streaming = (
                        'text/event-stream' in content_type.lower() or
                        'multipart/x-mixed-replace' in content_type.lower() or
                        'application/octet-stream' in content_type.lower() or
                        ('content-disposition' in response.headers and 'attachment' in response.headers.get('content-disposition', '').lower()) or
                        any(stream_type in content_type.lower() for stream_type in ['audio/', 'video/'])
                    )
                    
                    # 检查是否是明确的流式响应类型
                    is_explicit_streaming_response = (
                        ('StreamingResponse' in response_class_name and response_class_name != '_StreamingResponse') or 
                        'FileResponse' in response_class_name
                    )
                    
                    # 真正的流式响应
                    if is_real_streaming or is_explicit_streaming_response:
                        logger.info(f"流式响应: {response_class_name}，不记录响应体内容")
                        response_data = f"[流式响应，内容未记录]"
                    else:
                        # 尝试从之前保存的响应体获取内容
                        response_body = getattr(response, "body", None)
                        if response_body:
                            try:
                                # 尝试解码为UTF-8
                                if isinstance(response_body, bytes):
                                    response_data = response_body.decode('utf-8', errors='replace')
                                else:
                                    response_data = str(response_body)
                                
                                # 限制日志中响应体的长度，避免日志过大
                                if len(response_data) > 1000:
                                    logger.info(f"获取到响应体(已截断): {response_data[:1000]}...")
                                    # 可选：截断过长的响应数据
                                    response_data = response_data[:1000] + "... [截断]"
                                else:
                                    logger.info(f"获取到响应体: {response_data}")
                            except Exception as e:
                                logger.error(f"解码响应体失败: {str(e)}")
                                response_data = str(response_body)
                        else:
                            logger.warning("响应体为空")
            except Exception as e:
                logger.error(f"获取响应体失败: {str(e)}")

            # 获取用户信息
            user_id = None
            user_name = None

            # 调试日志
            if current_user:
                logger.info(f"_create_log中的current_user: {current_user}")

            # 从current_user获取用户信息
            if current_user:
                try:
                    user_id = current_user.id
                    user_name = current_user.login_name
                    logger.info(f"从current_user获取用户信息: ID={user_id}, 用户名={user_name}")
                except Exception as e:
                    logger.error(f"从current_user获取用户信息失败: {str(e)}")

            # 如果current_user为空或获取失败，尝试从token获取
            if not user_id or not user_name:
                logger.info("尝试从token获取用户信息")
                # 尝试从请求头中获取用户信息
                if not auth_header:
                    auth_header = request.headers.get("Authorization", "")
                    if not auth_header:
                        auth_header = request.headers.get("authorization", "")

                logger.info(f"Authorization头: {auth_header}")

                # 修复检查逻辑，使用小写比较
                if auth_header and auth_header.lower().startswith("bearer "):
                    token = auth_header.replace("Bearer ", "").replace("bearer ", "")
                    if token and token.strip():  # 确保token不为空
                        try:
                            # 延迟加载context
                            if self.context is None:
                                from xinhou_openai_framework.core.context.model.SystemContext import ctx
                                self.context = ctx.__getattr__("context")

                            # 解析JWT token
                            payload = jwt.decode(
                                token,
                                self.context.framework.SECRET_KEY,
                                algorithms=[self.context.framework.ALGORITHM]
                            )
                            username = payload.get("sub")
                            if username:
                                logger.info(f"从token获取到用户名: {username}")
                                # 直接使用原生db查询用户信息
                                try:
                                    user = db.query(User).filter(User.login_name == username).first()
                                    if user:
                                        user_id = user.id
                                        user_name = user.login_name
                                        logger.info(f"从数据库获取用户信息: ID={user_id}, 用户名={user_name}")
                                    else:
                                        logger.warning(f"数据库中未找到用户: {username}")
                                except Exception as e:
                                    logger.error(f"查询用户信息失败: {str(e)}")
                            else:
                                logger.warning("token中未包含用户名")
                        except Exception as e:
                            logger.error(f"解析token失败: {str(e)}")
                    else:
                        logger.warning("token为空")
                else:
                    logger.warning(f"未找到有效的Authorization头: {auth_header}")

            # 获取API描述和模块
            api_description = None
            module = None

            # 从路径中提取模块
            path_parts = request.url.path.strip('/').split('/')
            if len(path_parts) > 0:
                module = path_parts[0] if path_parts[0] else None

            # 创建日志对象
            api_log = ApiLog(
                trace_id=str(uuid.uuid4()),
                user_id=user_id,
                user_name=user_name,
                client_ip=client_ip,
                user_agent=user_agent,
                request_url=url,
                request_method=method,
                request_params=json.dumps(params, ensure_ascii=False) if params else None,
                request_body=request_body,
                response_code=response_code,
                response_data=response_data,
                error_message=None,
                execution_time=execution_time,
                api_description=api_description,
                module=module,
                status=1 if 200 <= response_code < 400 else 0
            )

            # 记录日志对象信息
            logger.info(f"准备保存API日志: trace_id={api_log.trace_id}, user_id={api_log.user_id}, user_name={api_log.user_name}, response_data长度={len(api_log.response_data) if api_log.response_data else 0}")

            # 保存日志
            try:
                db.add(api_log)
                db.commit()
                logger.info(f"API日志保存成功: {api_log.trace_id}")
            except Exception as e:
                db.rollback()
                logger.error(f"保存日志失败: {str(e)}")

        except Exception as e:
            logger.error(f"创建API日志失败: {str(e)}")

    async def _create_error_log(self, request: Request, error: Exception,
                                execution_time: int, current_user, db, request_body=None, auth_header=None) -> None:
        """
        创建错误日志
        :param request: 请求对象
        :param error: 异常对象
        :param execution_time: 执行时间(毫秒)
        :param current_user: 当前用户
        :param db: 数据库会话
        :param request_body: 请求体内容
        :param auth_header: 认证头信息
        :return: None
        """
        try:
            # 获取请求信息
            url = str(request.url)
            method = request.method
            headers = dict(request.headers)
            client_ip = request.client.host if request.client else None
            user_agent = headers.get("user-agent", "")

            # 获取请求参数
            params = dict(request.query_params)

            # 获取用户信息
            user_id = None
            user_name = None
            if current_user:
                user_id = current_user.id
                user_name = current_user.login_name
            else:
                # 尝试从请求头中获取用户信息
                if not auth_header:
                    auth_header = request.headers.get("Authorization", "")
                    if not auth_header:
                        auth_header = request.headers.get("authorization", "")

                # 修复检查逻辑，使用小写比较
                if auth_header and auth_header.lower().startswith("bearer "):
                    token = auth_header.replace("Bearer ", "").replace("bearer ", "")
                    if token and token.strip():  # 确保token不为空
                        try:
                            # 延迟加载context
                            if self.context is None:
                                from xinhou_openai_framework.core.context.model.SystemContext import ctx
                                self.context = ctx.__getattr__("context")

                            # 解析JWT token
                            payload = jwt.decode(
                                token,
                                self.context.framework.SECRET_KEY,
                                algorithms=[self.context.framework.ALGORITHM]
                            )
                            username = payload.get("sub")
                            if username:
                                # 直接使用原生db查询用户信息
                                try:
                                    user = db.query(User).filter(User.login_name == username).first()
                                    if user:
                                        user_id = user.id
                                        user_name = user.login_name
                                except Exception as e:
                                    logger.error(f"查询用户信息失败: {str(e)}")
                        except Exception as e:
                            logger.error(f"解析token失败: {str(e)}")

            # 获取API描述和模块
            api_description = None
            module = None

            # 从路径中提取模块
            path_parts = request.url.path.strip('/').split('/')
            if len(path_parts) > 0:
                module = path_parts[0] if path_parts[0] else None

            # 创建日志对象
            api_log = ApiLog(
                trace_id=str(uuid.uuid4()),
                user_id=user_id,
                user_name=user_name,
                client_ip=client_ip,
                user_agent=user_agent,
                request_url=url,
                request_method=method,
                request_params=json.dumps(params, ensure_ascii=False) if params else None,
                request_body=request_body,
                response_code=500,
                response_data=None,
                error_message=str(error),
                execution_time=execution_time,
                api_description=api_description,
                module=module,
                status=0
            )

            # 保存日志
            try:
                db.add(api_log)
                db.commit()
            except Exception as e:
                db.rollback()
                logger.error(f"保存日志失败: {str(e)}")

        except Exception as e:
            logger.error(f"创建错误日志失败: {str(e)}")
