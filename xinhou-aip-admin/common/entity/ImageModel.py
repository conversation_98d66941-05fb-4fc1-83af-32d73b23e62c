from sqlalchemy import Column, Integer, String, Text, TIMESTAMP
from xinhou_openai_framework.core.orm.entity.BaseEntity import BaseEntity


class ImageModel(BaseEntity):
    __tablename__ = 't_image_model'

    pid = Column(Integer, nullable=True, comment='父ID')
    image_url = Column(String(500), comment='图片URL')
    model_type = Column(String(50), nullable=False, comment='专属模型类型(口播/访谈)')
    speech_style = Column(String(50), nullable=True, comment='口播风格(激情/笑容/严肃/悲伤)')
    interview_style = Column(String(50), nullable=True, comment='访谈风格(对谈/教育/知识分享)')
    duration = Column(String(20), nullable=False, comment='模型时长(10-20s/20-30s/30-40s/40-50s)')
    appearance_note = Column(Text, comment='形象要求备注')
    status = Column(Integer, default=1, comment='状态:1=正常,2=禁用')
    del_flag = Column(Integer, default=1, comment='删除标志:1=存在,2=删除')
    submit_status = Column(Integer, default=1, comment='状态:1=已提交,2=已上传')
    upload_time = Column(TIMESTAMP, nullable=False, comment='上传时间')
