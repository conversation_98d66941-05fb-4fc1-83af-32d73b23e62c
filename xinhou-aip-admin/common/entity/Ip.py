# !/usr/bin/python3
# -*- coding: utf-8 -*-

"""
IP信息表模型类
----------------------------------------------------
@Project :   xinhou-openai-admin
@File    :   User.py
@Contact :   <EMAIL>

@Modify Time      <AUTHOR>    @Desciption
------------      -------    --------    -----------
2023/04/16 22:04  peng.shen   v1.0.0     None
"""

from sqlalchemy import Column, Integer, String, TIMESTAMP, Float
from xinhou_openai_framework.core.orm.entity.BaseEntity import BaseEntity


class Ip(BaseEntity):
    __tablename__ = 't_ip'

    uid = Column(Integer, comment='用户ID')
    ip_name = Column(String(30), comment='IP名称')
    avatar = Column(String(500), comment='头像路径')
    status = Column(Integer, default=1, comment='帐号状态:1=正常,2=禁用')
    del_flag = Column(Integer, default=1, comment='删除标志:1=代表存在,2=代表删除')
    remark = Column(String(500), comment='备注')
    group_id = Column(String(256), comment='群组ID')
    group_name = Column(String(256), comment='群组名称')
    remain_point = Column(Float, comment='用户使用剩余时间')
    expire_time = Column(TIMESTAMP, comment='对应IP到期时间')
