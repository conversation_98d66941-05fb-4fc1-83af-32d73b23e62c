from sqlalchemy import Column, Integer
from xinhou_openai_framework.core.orm.entity.BaseEntity import BaseEntity

class WorkflowAgent(BaseEntity):
    """工作流-Agent关联表"""
    __tablename__ = 't_workflow_agent'

    workflow_id = Column(Integer, nullable=False, comment='工作流ID')
    agent_id = Column(Integer, nullable=False, comment='Agent ID')
    execution_order = Column(Integer, nullable=False, comment='主体执行顺序')
    status = Column(Integer, default=1, comment='任务状态:1=正常,2=禁用')
    del_flag = Column(Integer, default=1, comment='删除标志:1=代表存在,2=代表删除') 