# !/usr/bin/python3
# -*- coding: utf-8 -*-

"""
训练嵌入文件信息表模型类
----------------------------------------------------
@Project :   xinhou-openai-admin
@File    :   EmbeddingFile.py
@Contact :   <EMAIL>

@Modify Time      <AUTHOR>    @Desciption
------------      -------    --------    -----------
2023/04/16 22:04  peng.shen   v1.0.0     None
"""
from datetime import datetime

from pydantic import ConfigDict
from sqlalchemy import Column, Integer, String, TEXT, DateTime
from sqlalchemy.orm import relationship
from xinhou_openai_framework.core.orm.entity.BaseEntity import BaseEntity


class EmbeddingFile(BaseEntity):
    __tablename__ = 't_embedding_file'
    model_config = ConfigDict(from_attributes=True)

    pid = Column(String(100), comment="IPID")
    file_name = Column(String(100), comment="文件名称")
    is_delete = Column(Integer, default=0, comment='文件假删除标志：0=未删除，1=已删除')
    file_name_uuid = Column(String(100), comment="文件UUID名称")
    file_type = Column(String(100), comment="文件类型")
    file_size = Column(Integer, default='0', comment="文件大小")
    file_path = Column(String(100), comment="文件访问路径")
    file_url = Column(String(100), comment="文件下载路径")
    file_educate_status = Column(Integer, default='0', comment="文件训练状态:0=未训练,1=已训练,3=训练异常")
    emb_type = Column(Integer, comment="训练类型：1=用户知识;2=对标内容;3=热点数据")
    file_word_num = Column(Integer, default=0, comment="文件字数")
    remark = Column(String(500), comment="备注")
    oid = Column(Integer, comment="源文件id")
    o_url = Column(String(500), comment="源url")
    file_review_pic = Column((TEXT), comment="文件预览图")
    source = Column(String(100), comment="来源")
    create_by = Column(String(11), comment="创建人")
    created_at = Column(DateTime, default=datetime.now, nullable=False)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now, nullable=False)
    extra = Column(String(255), comment= '额外信息')
    duration = Column(Integer, comment= '媒体时长')

    embedding_documents = relationship("EmbeddingDocument", back_populates="embedding_file")

    def to_dict(self):
        return {
            "id": self.id,
            "pid": self.pid,
            "file_name": self.file_name,
            "is_delete": self.is_delete,
            "file_name_uuid": self.file_name_uuid,
            "file_type": self.file_type,
            "file_size": self.file_size,
            "file_path": self.file_path,
            "file_word_num": self.file_word_num,
            "file_url": self.file_url,
            "file_educate_status": self.file_educate_status,
            "emb_type": self.emb_type,
            "remark": self.remark,
            "o_url": self.o_url,
            "file_review_pic": self.file_review_pic,
            "source": self.source,
            "oid": self.oid,
            "create_by": self.create_by,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
            "extra": self.extra
        }