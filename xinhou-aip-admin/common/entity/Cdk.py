# common/entity/Cdk.py
from sqlalchemy import Column, Integer, String, Float, TIMESTAMP, text
from xinhou_openai_framework.core.orm.entity.BaseEntity import BaseEntity


class Cdk(BaseEntity):
    """CDK兑换码表"""
    __tablename__ = 't_cdk'

    cdk_key = Column(String(32), nullable=False, comment='CDK兑换码')
    total_uses = Column(Integer, nullable=False, default=1, comment='可使用总次数')
    used_count = Column(Integer, nullable=False, default=0, comment='已使用次数')
    point = Column(Float, nullable=False, comment='固定点数值')
    used_time = Column(Integer, nullable=False, comment='有效天数')
    status = Column(Integer, default=1, comment='状态:1=未使用,2=已使用,3=已过期,4=已禁用')
    del_flag = Column(Integer, default=1, comment='删除标志:1=存在,2=删除')
    remark = Column(String(500), comment='备注')


# common/entity/CdkUsageLog.py
class CdkUsageLog(BaseEntity):
    """CDK使用记录表"""
    __tablename__ = 't_cdk_usage_log'

    cdk_id = Column(Integer, nullable=False, comment='CDK ID')
    cdk_key = Column(String(32), nullable=False, comment='CDK兑换码')
    uid = Column(Integer, nullable=False, comment='使用者用户ID')
    pid = Column(Integer, nullable=False, comment='使用者PID')
    used_point = Column(Float, nullable=False, default=0, comment='使用点数')
    used_time = Column(TIMESTAMP, nullable=True, server_default=text('CURRENT_TIMESTAMP'), comment='使用时间')
    status = Column(Integer, default=1, comment='状态:1=成功,2=失败')
    remark = Column(String(500), comment='备注')