# !/usr/bin/python3
# -*- coding: utf-8 -*-


from sqlalchemy import Column, VARCHAR, Text, Integer
from xinhou_openai_framework.core.orm.entity.BaseEntity import BaseEntity


class HotTitle(BaseEntity):
    # 数据表名&字段
    __tablename__ = 'hot_title'

    id = Column(Integer, primary_key=True, autoincrement=True)
    folder_name = Column(VARCHAR(255), comment="热点标题")
    hot_time = Column(VARCHAR(255), comment="热点产生时间")
    hot_text = Column(Text, comment="热点内容")
    hot_url = Column(VARCHAR(255), comment="热点链接")
    industry_tags = Column(VARCHAR(255), comment="行业标签")
    content_classify = Column(VARCHAR(255), comment="内容分类")
