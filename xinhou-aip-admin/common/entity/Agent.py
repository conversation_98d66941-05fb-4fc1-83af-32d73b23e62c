from sqlalchemy import Column, Integer, String, Text
from xinhou_openai_framework.core.orm.entity.BaseEntity import BaseEntity


class Agent(BaseEntity):
    """Agent表"""
    __tablename__ = 't_agent'

    agent_name_cn = Column(String(100), nullable=False, comment='Agent名称')
    agent_code = Column(String(100), nullable=False, comment='Agent编码')
    agent_type = Column(Integer, nullable=False, comment='Agent类型:1=系统Agent,2=用户Agent,9=QueryAgent')
    agent_role = Column(Integer, nullable=False, comment='Agent角色:1=意图识别,2=路由,3=开始agent,4=执行,5=结束')
    llm_id = Column(Integer, nullable=False, comment='关联的LLM ID')
    influence_scope = Column(String(255), comment='影响范围Agent IDs')
    prompt_cn = Column(Text, comment='中文prompt')
    prompt_en = Column(Text, comment='英文prompt')
    agent_name_en = Column(String(100), nullable=False, comment='Agent英文名称')
    description = Column(Text, comment='描述')
    agent_style = Column(String(50), default='USUALLY', comment='Agent样式:USUALLY=通用输出,COMMAND=需求分析输出,TITLE_CHOOSE=选题专家输出,INTERVIEW=采访编辑,DATA_LOADING=数据专家加载中,DATA=数据专家输出,STRUCT=结构专家输出,WRITER=文案写手和润色专家输出,VOICE_1=音频专家1,VOICE_2=音频专家2,VOICE_3=音频专家3,VIDEO_1=视频专家1,VIDEO_2=视频专家2,VIDEO_3=视频专家3')
    tool_ids = Column(String(255), comment='关联工具ID')
    status = Column(Integer, default=1, comment='状态:1=正常,2=禁用')
    del_flag = Column(Integer, default=1, comment='删除标志:1=存在,2=删除')
    agent_action = Column(String(255), comment='Agent行为')
