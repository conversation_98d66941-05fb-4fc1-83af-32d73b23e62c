from sqlalchemy import Column, Integer
from xinhou_openai_framework.core.orm.entity.BaseEntity import BaseEntity


class WorkflowPid(BaseEntity):
    __tablename__ = 't_workflow_pid'

    workflow_id = Column(Integer, nullable=False, comment='工作流ID')
    pid = Column(Integer, nullable=False, comment='IP的ID')
    status = Column(Integer, default=1, comment='状态:1=正常,2=禁用')
    del_flag = Column(Integer, default=1, comment='删除标志:1=代表存在,2=代表删除')

