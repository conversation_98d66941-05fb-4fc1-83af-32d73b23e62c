"""
神评论表模型类
----------------------------------------------------
@Project :   xinhou-openai-admin
@File    :   Comment.py
@Contact :   <EMAIL>

@Modify Time      <AUTHOR>    @Desciption
------------      -------    --------    -----------
2024/08/06 10:00  assistant  v1.0.0      神评论表实体类
"""

from sqlalchemy import Column, Integer, String, Text
from xinhou_openai_framework.core.orm.entity.BaseEntity import BaseEntity


class Comment(BaseEntity):
    __tablename__ = 't_comment'
    task_id = Column(Integer, nullable=False, comment='关联的任务id')
    comment_type = Column(Integer, nullable=False,
                          comment='评论类型:1=作者自评,2=观众-搞笑,3=观众-讽刺,3=观众-鼓励,4=朋友圈文案,5=群聊转发')
    content = Column(Text, comment='神评论内容')
    status = Column(Integer, default=1, comment='评论状态:1=正常,2=待审核,3=已删除')

    remark = Column(String(500), comment='备注')
