#!/usr/bin/python3
# -*- coding: utf-8 -*-

"""
用户基础信息表模型类（手机号/姓名/公司/城市）
----------------------------------------------------
@Project :   xinhou-openai-admin
@File    :   UserInfo.py
@Contact :   <EMAIL>

@Modify Time      <AUTHOR>    @Desciption
------------      -------    --------    -----------
2023/04/16 22:15  peng.shen   v1.0.0     新增用户基础信息表
"""

from sqlalchemy import Column, String
from sqlalchemy.sql.schema import UniqueConstraint
from xinhou_openai_framework.core.orm.entity.BaseEntity import BaseEntity


class Precontract(BaseEntity):
    # 数据表名 & 字段定义
    __tablename__ = "t_precontract"
    __table_args__ = (
        UniqueConstraint("mobile", name="idx_mobile"),  # 手机号唯一约束
        {"comment": "预约信息表（手机号/姓名/公司/城市）"},
    )

    mobile = Column(String(20), nullable=False, comment="手机号（唯一标识）")
    name = Column(String(50), comment="姓名")
    business_name = Column(String(100), comment="公司名称")
    city = Column(String(50), comment="城市")
    remark = Column(String(500), comment="备注")
