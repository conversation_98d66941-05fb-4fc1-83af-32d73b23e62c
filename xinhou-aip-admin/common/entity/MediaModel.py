# !/usr/bin/python3
# -*- coding: utf-8 -*-

"""
数字人多媒体表模型类
----------------------------------------------------
@Project :   xinhou-openai-admin
@File    :   MediaModel.py
@Contact :   <EMAIL>

@Modify Time      <AUTHOR>    @Desciption
------------      -------    --------    -----------
2023/08/15 10:00  peng.shen   v1.0.0     None
"""

from sqlalchemy import Column, Integer, String, ForeignKey
from xinhou_openai_framework.core.orm.entity.BaseEntity import BaseEntity


class MediaModel(BaseEntity):
    __tablename__ = 't_media_model'

    title = Column(String(50), comment='标题')
    pic_url = Column(String(255), comment='封面图')
    status = Column(Integer, default=1, comment='视频状态:1=正常,2=禁用')
    media_url = Column(String(4096), comment='媒体URL')
    del_flag = Column(Integer, default=1, comment='删除标志:1=代表存在,2=代表删除')
    remark = Column(String(1024), comment='备注')
    pid = Column(Integer, ForeignKey('t_ip.id'), comment='IPID')
    transcode = Column(Integer, default=1, comment='转码状态:0=转码中；1=转码失败；2=转码成功')
    resolution = Column(String(1024), comment='视频模型详细信息')
