from sqlalchemy import Column, Integer, String
from xinhou_openai_framework.core.orm.entity.BaseEntity import BaseEntity


class Workflow(BaseEntity):
    """工作流表"""
    __tablename__ = 't_workflow'

    workflow_name = Column(String(100), nullable=False, comment='工作流名称')
    workflow_code = Column(String(100), nullable=False, comment='工作流编码')
    description = Column(String(500), comment='工作流描述')
    status = Column(Integer, default=1, comment='状态:1=正常,2=禁用')
    del_flag = Column(Integer, default=1, comment='删除标志:1=代表存在,2=代表删除')
    remark = Column(String(500), comment='备注')
    is_default = Column(Integer, default=0, comment='是否默认:0=否,1=是')