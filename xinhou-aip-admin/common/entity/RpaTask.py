# !/usr/bin/python3
# -*- coding: utf-8 -*-

"""
任务信息表模型类
"""
from sqlalchemy import Column, String, JSON
from xinhou_openai_framework.core.orm.entity.BaseEntity import BaseEntity


class RpaTask(BaseEntity):
    # 数据表名&字段
    __tablename__ = 't_rpa_task'

    robot_id = Column(String(64), comment="机器人id")
    robot_params = Column(JSON, comment="机器人参数")
    robot_status = Column(String(32), default='WAITING', comment="机器人状态")
    ip_name = Column(String(64), comment="运行机器人的ip")
    result_info = Column(JSON, comment="机器人运行结果信息")
    result_file = Column(String(500), comment="机器人运行产生的文件oss链接信息")
