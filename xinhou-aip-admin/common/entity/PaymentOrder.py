from sqlalchemy import Column, Integer, String, DECIMAL, DateTime, Text
from xinhou_openai_framework.core.orm.entity.BaseEntity import BaseEntity


class PaymentOrder(BaseEntity):
    __tablename__ = 't_payment_order'

    trade_no = Column(String(64), comment='平台订单号')
    out_trade_no = Column(String(64), nullable=False, comment='商户订单号')
    user_id = Column(Integer, comment='用户ID')
    product_id = Column(Integer, comment='商品ID')
    product_name = Column(String(255), nullable=False, comment='商品名称')
    total_amount = Column(DECIMAL(10, 2), nullable=False, comment='订单金额')
    payment_type = Column(String(20), nullable=False, comment='支付方式')
    trade_status = Column(String(20), nullable=False, comment='交易状态')
    pay_time = Column(DateTime, comment='支付时间')
    notify_time = Column(DateTime, comment='通知时间')
    notify_url = Column(String(255), comment='异步通知地址')
    return_url = Column(String(255), comment='同步返回地址')
    client_ip = Column(String(64), comment='客户端IP')
    device = Column(String(20), comment='设备类型')
    extra_param = Column(Text, comment='额外参数')
    refund_amount = Column(DECIMAL(10, 2), default=0.00, comment='退款金额')
    refund_time = Column(DateTime, comment='退款时间')
    point_amount = Column(Integer, comment='充值点数')
    valid_days = Column(Integer, comment='有效期(天)')
    expire_time = Column(DateTime, comment='到期时间')
    customer_id = Column(Integer, comment='pid')
