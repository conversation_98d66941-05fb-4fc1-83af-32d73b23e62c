# !/usr/bin/python3
# -*- coding: utf-8 -*-

"""
用户信息表模型类
----------------------------------------------------
@Project :   xinhou-openai-admin
@File    :   User.py
@Contact :   <EMAIL>

@Modify Time      <AUTHOR>    @Desciption
------------      -------    --------    -----------
2023/04/16 22:04  peng.shen   v1.0.0     None
"""

from sqlalchemy import Column, Integer, String, Float
from xinhou_openai_framework.core.orm.entity.BaseEntity import BaseEntity


class User(BaseEntity):
    # 数据表名&字段
    __tablename__ = 't_user'

    login_name = Column(String(30), comment="登录账号")
    login_pwd = Column(String(50), comment="密码")
    user_name = Column(String(30), comment="用户昵称")
    user_type = Column(Integer, comment="用户类型:1=系统用户,2=普通用户")
    email = Column(String(50), comment="用户邮箱")
    phone = Column(String(20), comment="固定电话")
    mobile = Column(String(11), comment="手机号码")
    sex = Column(Integer, comment="用户性别:1=男,2=女,3=未知")
    avatar = Column(String(100), comment="头像路径")
    status = Column(Integer, default='1', comment="帐号状态:1=正常,2=禁用")
    del_flag = Column(Integer, default='1', comment="删除标志:1=代表存在,2=代表删除")
    remark = Column(String(500), comment="备注")
    remain_point = Column(Float, default=0, comment="用户剩余使用时间")
