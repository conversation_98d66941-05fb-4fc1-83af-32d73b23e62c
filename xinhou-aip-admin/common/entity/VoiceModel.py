from sqlalchemy import Column, Integer, String, ForeignKey
from xinhou_openai_framework.core.orm.entity.BaseEntity import BaseEntity


class VoiceModel(BaseEntity):
    __tablename__ = 't_voice_model'

    voice_name = Column(String(100), comment='音色名称')
    voice_url = Column(String(200), comment='试听链接')
    status = Column(Integer, default=1, comment='音色状态:1=正常,2=禁用')
    del_flag = Column(Integer, default=1, comment='删除标志:1=代表存在,2=代表删除')
    remark = Column(String(1024), comment='备注')
    pid = Column(Integer, ForeignKey('t_ip.id'), comment='IPID')
    clone_name = Column(String(255), default=None, comment='声音克隆生成的名称，目前是阿里云返回')
    ref_text = Column(String(1024), default=None, comment='音频文件文本内容')
    back_clone_name = Column(String(1024), default=None, comment='备用克隆生成的名称')
    back_voice_url = Column(String(512), default=None, comment='备用克隆生成的音频链接')
    