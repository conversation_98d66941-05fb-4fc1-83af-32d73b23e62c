# !/usr/bin/python3
# -*- coding: utf-8 -*-

"""
训练内容信息表模型类
----------------------------------------------------
@Project :   marmot-training-admin
@File    :   EmbeddingContent.py
@Contact :   <EMAIL>

@Modify Time      <AUTHOR>    @Desciption
------------      -------    --------    -----------
2023/04/16 22:04  peng.shen   v1.0.0     None
"""

from sqlalchemy import Column, Integer, String, Text, ForeignKey
from sqlalchemy.orm import relationship
from xinhou_openai_framework.core.orm.entity.BaseEntity import BaseEntity


class EmbeddingDocument(BaseEntity):
    # 数据表名&字段
    __tablename__ = 't_embedding_document'

    id = Column(Integer, primary_key=True, autoincrement=True, comment="训练分段ID")
    embedding_file_id = Column(Integer, comment="训练文件ID")
    page_content = Column(Text, comment="训练分段内容")
    metadata_source = Column(String(128), comment="元数据来源")
    collection_name = Column(String(128), comment="集合名称")
    pk = Column(String(128), comment="训练分段ID")
    remark = Column(String(500), comment="备注")

    embedding_file_id = Column(Integer, ForeignKey('t_embedding_file.id'))
    embedding_file = relationship("EmbeddingFile", back_populates="embedding_documents")
