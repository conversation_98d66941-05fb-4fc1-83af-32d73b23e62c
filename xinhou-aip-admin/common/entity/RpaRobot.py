# !/usr/bin/python3
# -*- coding: utf-8 -*-

"""
任务信息表模型类
"""
from sqlalchemy import Column, String, Text, Integer
from xinhou_openai_framework.core.orm.entity.BaseEntity import BaseEntity


class RpaRobot(BaseEntity):
    # 数据表名&字段
    __tablename__ = 't_rpa_robot'

    robot_name = Column(String(64), comment="机器人名称", unique=True)
    robot_type = Column(String(64), comment="机器人类型")
    robot_platform = Column(String(64), comment="机器人平台")
    robot_description = Column(Text, comment="机器人描述")
    is_delete = Column(Integer, default=0, comment="是否删除")
