from sqlalchemy import Column, Integer, String, LargeBinary
from xinhou_openai_framework.core.orm.entity.BaseEntity import BaseEntity

class AgentsHistory(BaseEntity):
    """Agent执行历史记录表"""
    __tablename__ = 't_agents_history'

    task_id = Column(Integer, comment='口播稿的任务id')
    pid = Column(Integer, comment='博主名称')
    status = Column(Integer, default=1, comment='口播稿生成状态1:制作中,2:完成')
    history = Column(LargeBinary, comment='每个agent的完成记录')
    remark = Column(String(500), comment='备注') 