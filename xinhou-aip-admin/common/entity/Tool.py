from sqlalchemy import Column, Integer, String, Text
from xinhou_openai_framework.core.orm.entity.BaseEntity import BaseEntity


class Tool(BaseEntity):
    """Tool表"""
    __tablename__ = 't_tools'

    tool_name = Column(String(100), nullable=False, comment='工具名称')
    tool_function = Column(String(100), nullable=False, comment='工具函数')
    description = Column(Text, comment='工具描述')
    llm_id = Column(Integer, comment='关联的LLM ID')
    status = Column(Integer, default=1, comment='状态:1=正常,2=禁用')
    del_flag = Column(Integer, default=1, comment='删除标志:1=存在,2=删除')
    remark = Column(String(255), comment='备注')
