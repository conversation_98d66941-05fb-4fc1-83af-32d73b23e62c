# !/usr/bin/python3
# -*- coding: utf-8 -*-

"""
API日志实体类
----------------------------------------------------
@Project :   xinhou-openai-admin
@File    :   ApiLog.py
@Contact :   <EMAIL>

@Modify Time      <AUTHOR>    @Desciption
------------      -------    --------    -----------
2023/08/01 10:00  peng.shen   v1.0.0     None
"""

from sqlalchemy import Column, Integer, String, Text, BigInteger, SmallInteger
from xinhou_openai_framework.core.orm.entity.BaseEntity import BaseEntity


class ApiLog(BaseEntity):
    """
    API日志实体类
    """
    __tablename__ = "t_logs"

    trace_id = Column(String(64), nullable=True, comment="请求追踪ID")
    user_id = Column(BigInteger, nullable=True, comment="调用用户ID")
    user_name = Column(String(50), nullable=True, comment="调用用户名称")
    client_ip = Column(String(50), nullable=True, comment="客户端IP")
    user_agent = Column(String(500), nullable=True, comment="用户代理信息")
    request_url = Column(String(255), nullable=False, comment="请求URL")
    request_method = Column(String(10), nullable=False, comment="请求方法(GET/POST等)")
    request_params = Column(Text, nullable=True, comment="请求参数")
    request_body = Column(Text, nullable=True, comment="请求体")
    response_code = Column(Integer, nullable=True, comment="响应状态码")
    response_data = Column(Text, nullable=True, comment="响应数据")
    error_message = Column(Text, nullable=True, comment="错误信息")
    execution_time = Column(BigInteger, nullable=True, comment="执行时间(毫秒)")
    api_description = Column(String(255), nullable=True, comment="API描述")
    module = Column(String(50), nullable=True, comment="所属模块")
    status = Column(SmallInteger, nullable=True, default=1, comment="状态(1=成功,0=失败)")
