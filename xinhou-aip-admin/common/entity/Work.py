# !/usr/bin/python3
# -*- coding: utf-8 -*-

"""
作品信息表模型类
----------------------------------------------------
@Project :   xinhou-openai-admin
@File    :   Work.py
@Contact :   <EMAIL>

@Modify Time      <AUTHOR>    @Desciption
------------      -------    --------    -----------
2024/07/17 10:00  assistant  v1.0.0      作品表实体类
"""

from sqlalchemy import Column, Integer, String, Text
from xinhou_openai_framework.core.orm.entity.BaseEntity import BaseEntity


class Work(BaseEntity):
    __tablename__ = 't_work'
    task_id = Column(Integer, nullable=False, comment='关联的任务id')
    work_type = Column(Integer, nullable=False, comment='作品类型:1=文本,2=音频,3=视频,4=选题内容')
    title = Column(Text, comment='作品标题')
    content = Column(Text, comment='作品内容(用于文本类型)')
    file_id = Column(Integer, comment='关联的文件id(用于音频和视频类型)')
    status = Column(Integer, default=1, comment='作品状态:1=正常,2=待审核,3=已删除')
    remark = Column(String(500), comment='备注')
    pid = Column(Integer, nullable=False, comment='ipid')
    audio_model_id = Column(String(500), comment='音频模型 id')
    video_model_id = Column(String(500), comment='视频模型 id')
