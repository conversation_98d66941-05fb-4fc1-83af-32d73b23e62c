# !/usr/bin/python3
# -*- coding: utf-8 -*-

"""
支付日志实体
----------------------------------------------------
@Project :   xinhou-openai-admin
@File    :   PaymentLog.py
@Contact :   <EMAIL>

@Modify Time      <AUTHOR>    @Desciption
------------      -------    --------    -----------
2024/09/18 15:10  ChatGPT    v1.0.0      None
"""
from sqlalchemy import Column, String, Text, BigInteger, ForeignKey
from xinhou_openai_framework.core.orm.entity.BaseEntity import BaseEntity


class PaymentLog(BaseEntity):
    __tablename__ = 't_payment_log'

    order_id = Column(BigInteger, ForeignKey('t_payment_order.id'), nullable=False, comment='关联的订单ID')
    log_type = Column(String(20), nullable=False, comment='日志类型')
    content = Column(Text, nullable=False, comment='日志内容')
