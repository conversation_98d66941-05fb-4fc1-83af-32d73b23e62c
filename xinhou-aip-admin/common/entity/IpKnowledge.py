# !/usr/bin/python3
# -*- coding: utf-8 -*-

"""
IP知识库实体类
----------------------------------------------------
@Project :   xinhou-aip-admin
@File    :   IpKnowledge.py
@Contact :   <EMAIL>

@Modify Time      <AUTHOR>    @Desciption
------------      -------    --------    -----------
2024/07/18 11:00   fancy     v1.0.0      初始创建
"""

from sqlalchemy import Column, Integer, String, Text, TIMESTAMP
from sqlalchemy.sql import func
from xinhou_openai_framework.core.orm.entity.BaseEntity import BaseEntity


class IpKnowledge(BaseEntity):
    __tablename__ = 't_ip_knowledge'

    id = Column(Integer, primary_key=True, autoincrement=True, comment='与t_embedding_file表的id对应')
    knowledge_source = Column(String(255), nullable=False, comment='知识来源,如：快手、抖音,会修改为用户名')
    knowledge_type = Column(Integer, nullable=False, comment='知识来源：1=文件;2=快手;3=抖音')
    account_url = Column(String(255), comment='账号链接URL')
    pid = Column(Integer, nullable=False, comment='IP对应ID')
    create_by = Column(String(50), comment='创建者')
    update_by = Column(String(50), comment='更新者')
    created_at = Column(TIMESTAMP, server_default=func.now(), comment='创建时间')
    updated_at = Column(TIMESTAMP, server_default=func.now(), onupdate=func.now(), comment='更新时间')
    remark = Column(Text, comment='备注')
    file_list = Column(Text, nullable=False, comment='关联的t_embedding_file id列表')
    avatar = Column(String(255), nullable=True)
    is_myself = Column(Integer, nullable=False, default=0, comment='是否是自己的媒体账号')

    __table_args__ = (
        {'comment': 'IP知识库表'}
    )