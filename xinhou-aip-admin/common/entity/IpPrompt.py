from sqlalchemy import Column, Integer, String, Text
from xinhou_openai_framework.core.orm.entity.BaseEntity import BaseEntity


class IpPrompt(BaseEntity):
    """IP Prompt表"""
    __tablename__ = 'ip_prompt'

    ip_name = Column(String(255), nullable=True, comment='IP名称')
    json = Column(Text, nullable=True, comment='JSON内容')
    summary = Column(Text, nullable=True, comment='摘要')
    pid = Column(Integer, nullable=True, comment='IP ID') 