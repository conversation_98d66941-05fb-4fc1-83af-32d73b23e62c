from sqlalchemy import Column, Integer, String, <PERSON><PERSON><PERSON>, ForeignKey
from xinhou_openai_framework.core.orm.entity.BaseEntity import BaseEntity

class AgentsShare(BaseEntity):
    """Agent任务分享表"""
    __tablename__ = 't_agents_share'

    task_id = Column(Integer, comment='关联的任务ID')
    pid = Column(Integer, comment='博主ID')
    share_code = Column(String(32), unique=True, comment='分享唯一标识码')
    is_public = Column(Boolean, default=False, comment='是否公开分享，True：公开，False：不公开')
    remark = Column(String(500), comment='备注')