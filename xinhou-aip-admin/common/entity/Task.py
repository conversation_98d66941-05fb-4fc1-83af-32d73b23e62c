# !/usr/bin/python3
# -*- coding: utf-8 -*-

"""
任务信息表模型类
"""
from sqlalchemy import Column, Integer, String
from xinhou_openai_framework.core.orm.entity.BaseEntity import BaseEntity


class Task(BaseEntity):
    __tablename__ = 't_task'
    pid = Column(Integer, comment='IPID')
    status = Column(Integer, default=1, comment='帐号状态:1=正常,2=禁用')
    del_flag = Column(Integer, default=1, comment='删除标志:1=代表存在,2=代表删除')
    progress = Column(Integer, default=0, comment='任务进度（0=口播稿初始化,1=生成口播稿中,2=完成口播稿,3=口播稿制作失败4=音频生成中,'
                                                  '5=完成音频,6=音频制作失败,7=视频生成中,8=视频制作完成,9=视频制作失败）')
    title = Column(String(500), comment='标题')
    remark = Column(String(500), comment='备注')
    audio_job_id = Column(String(500), comment='音频任务 id')
    video_job_id = Column(String(500), comment='视频任务 id')
    audio_model_id = Column(String(64), comment='音频模型 id')
    video_model_id = Column(String(64), comment='视频模型 id')
    task_knowledge_ids = Column(String(500), comment='任务知识库ID')
    doc_length = Column(Integer, comment='口播稿文本长度')
    language = Column(String(500), comment='语言')
    is_person = Column(Integer, comment='启动人设')
    is_search = Column(Integer, comment='启动搜索')
    is_rag = Column(Integer, comment='启动RAG')
    style = Column(String(500), comment='设置风格')
    read_score = Column(Integer, comment='启动易读')
