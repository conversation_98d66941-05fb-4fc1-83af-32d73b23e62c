"""
产品表模型类
----------------------------------------------------
@Project :   xinhou-openai-admin
@File    :   Product.py
@Contact :   <EMAIL>

@Modify Time      <AUTHOR>    @Desciption
------------      -------    --------    -----------
2024/09/24 15:45  assistant  v1.0.0      产品表实体类
"""

from sqlalchemy import Column, Integer, String, DECIMAL
from xinhou_openai_framework.core.orm.entity.BaseEntity import BaseEntity


class Product(BaseEntity):
    __tablename__ = 't_product'

    name = Column(String(255), comment='产品名称')
    money = Column(DECIMAL(10, 2), default=0.00, comment='产品价格')
    status = Column(Integer, default=1, comment='产品状态:1=正常,2=禁用')
    unit = Column(String(64), comment='单位(秒/次)')
    features = Column(String(500), comment='特点')
    desc = Column(String(500), comment='描述')
    name_en = Column(String(255), comment='产品名称(英文)')
    money_en = Column(DECIMAL(10, 2), comment='产品价格(英文)')
    unit_en = Column(String(64), comment='单位(英文)')
    features_en = Column(String(500), comment='特点(英文)')
    desc_en = Column(String(500), comment='描述(英文)')
    point_amount = Column(Integer, default=0, nullable=False, comment='充值点数')
    product_type = Column(Integer, default=1, nullable=False, comment='产品类型:1=时长充值,2=其他')
    valid_days = Column(Integer, default=0, comment='有效期(天)')
    sort = Column(Integer, default=0, comment='排序号')
    remark = Column(String(500), comment="备注")
