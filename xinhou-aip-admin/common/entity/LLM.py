from sqlalchemy import Column, Integer, String, JSON
from xinhou_openai_framework.core.orm.entity.BaseEntity import BaseEntity

class LLM(BaseEntity):
    """LLM模型表"""
    __tablename__ = 't_llm'

    llm_name = Column(String(100), nullable=False, comment='LLM名称')
    llm_code = Column(String(100), nullable=False, comment='LLM编码')
    model_name = Column(String(100), nullable=False, comment='模型名称')
    api_url = Column(String(255), nullable=False, comment='API接口地址')
    api_key = Column(String(255), comment='API密钥')
    api_config = Column(JSON, comment='API配置信息')
    status = Column(Integer, default=1, comment='任务状态:1=正常,2=禁用')
    del_flag = Column(Integer, default=1, comment='删除标志:1=代表存在,2=代表删除')
    remark = Column(String(500), comment='备注') 