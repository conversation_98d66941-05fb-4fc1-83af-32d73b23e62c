from loguru import logger
from xinhou_openai_framework.core.context.model.AppContext import AppContext
from xinhou_openai_framework.core.context.model.SystemContext import ctx
from xinhou_openai_framework.core.decorator.HttpClientDecorator import http_client


class F5TTSRemoteService:
    """F5-TTS 远程服务类"""
    context: AppContext = ctx.__getattr__("context")

    @staticmethod
    @http_client(context.aip.remote.f5_tts, path="/transcribe/", method="POST",
                 timeout=60)
    def transcribe_audio(response_data, **kwargs):
        """调用F5-TTS服务进行音频转写"""
        json_data = kwargs.get('json_data', {})
        request_data = {
            "audio_url": json_data.get("audio_url"),
            "language": json_data.get("language", "chinese")
        }
        logger.info(f"F5-TTS transcribe request data: {request_data}")

        if not response_data:
            raise Exception("No response data received")

        logger.info(f"F5-TTS transcribe response: {response_data}")
        return response_data  # 直接返回完整响应

    @staticmethod
    @http_client(context.aip.remote.f5_tts, path="/api/tts/generate", method="POST",
                 timeout=60)
    def generate_audio(response_data, **kwargs):
        """调用F5-TTS服务生成音频"""
        json_data = kwargs.get('json_data', {})
        request_data = {
            "ref_audio_url": json_data.get("ref_audio_url"),
            "gen_text": json_data.get("gen_text"),
            "ref_text": json_data.get("ref_text"),
            "model_name": json_data.get("model_name", "F5-TTS"),
            "remove_silence": json_data.get("remove_silence", False),
            "speed": json_data.get("speed", 0.8),
            "cross_fade_duration": json_data.get("cross_fade_duration", 0.15)
        }
        logger.info(f"F5-TTS service request data: {request_data}")

        if not response_data:
            raise Exception("No response data received")

        logger.info(f"F5-TTS service response: {response_data}")

        # 直接返回响应数据，让上层处理字段映射
        return response_data
