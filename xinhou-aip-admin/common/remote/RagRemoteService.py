from xinhou_openai_framework.core.context.model.AppContext import AppContext
from xinhou_openai_framework.core.context.model.SystemContext import ctx
from xinhou_openai_framework.core.decorator.HttpClientDecorator import http_client


class RagRemoteService:
    """
    Rag 远程服务类，用于调用远程服务。
    """
    context: AppContext = ctx.__getattr__("context")  # 全局变量

    @staticmethod
    @http_client(context.aip.remote.embedding, path="/hybrid_search/embedding", method="POST",
                 timeout=60)
    def create_embedding(response_data, **kwargs):
        """创建单条文本嵌入"""
        if response_data:
            if response_data['code'] == 200:
                return response_data['data']
            else:
                raise ValueError(response_data['msg'])
        else:
            raise Exception("No response data received")

    @staticmethod
    @http_client(context.aip.remote.embedding, path="/hybrid_search/embedding/batch", method="POST",
                 timeout=120)
    def batch_create_embedding(response_data, **kwargs):
        """批量创建文本嵌入"""
        if response_data:
            if response_data['code'] == 200:
                return response_data['data']
            else:
                raise ValueError(response_data['msg'])
        else:
            raise Exception("No response data received")

    @staticmethod
    @http_client(context.aip.remote.embedding, path="/hybrid_search/search", method="POST",
                 timeout=60)
    def hybrid_search(response_data, **kwargs):
        """执行混合向量搜索"""
        if response_data:
            if response_data['code'] == 200:
                return response_data['data']
            else:
                raise ValueError(response_data['msg'])
        else:
            raise Exception("No response data received")

    @staticmethod
    @http_client(context.aip.remote.embedding, path="/hybrid_search/delete_text", method="POST",
                 timeout=60)
    def delete_embedding(response_data, **kwargs):
        """删除嵌入数据"""
        if response_data:
            if response_data['code'] == 200:
                return response_data['data']
            else:
                raise ValueError(response_data['msg'])
        else:
            raise Exception("No response data received")

