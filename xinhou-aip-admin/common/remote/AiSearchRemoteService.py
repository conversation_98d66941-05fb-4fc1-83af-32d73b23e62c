# !/usr/bin/python3
# -*- coding: utf-8 -*-

"""
远程调用管理器类
----------------------------------------------------
@Project :   xinhou-openai-admin
@File    :   OpenAiEmbeddingManager.py
@Contact :   <EMAIL>

@Modify Time      <AUTHOR>    @Desciption
------------      -------    --------    -----------
2023/04/16 22:04  peng.shen   v1.0.0     None
"""
from xinhou_openai_framework.core.context.model.AppContext import AppContext
from xinhou_openai_framework.core.context.model.SystemContext import ctx
from xinhou_openai_framework.core.decorator.HttpClientDecorator import http_client


class AiSearchRemoteService:
    """
    AgentsPlatform 远程服务类，用于调用远程服务。
    """
    context: AppContext = ctx.__getattr__("context")  # 全局变量        # 根据file_urls下载文件保存在本地

    @staticmethod
    @http_client(context.aip.remote.search, path="/hot/search/new_hot_list", method="GET",
                 timeout=60)
    def hot_list(response_data, **kwargs):
        if response_data:
            if response_data['code'] == 200:  # 假设 200 表示成功
                return response_data['data']
            else:
                # 处理错误情况
                raise ValueError(response_data['msg'])
        else:
            raise Exception

    @staticmethod
    @http_client(context.aip.remote.search, path="/ai/search/query_detail", method="POST",
                 timeout=60)
    def query_detail(response_data, **kwargs):
        if response_data:
            if response_data['code'] == 200:  # 假设 200 表示成功
                return response_data['data']
            else:
                # 处理错误情况
                raise ValueError(response_data['msg'])
        else:
            raise Exception
