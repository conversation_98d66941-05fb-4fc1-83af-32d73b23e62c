from xinhou_openai_framework.core.context.model.AppContext import AppContext
from xinhou_openai_framework.core.context.model.SystemContext import ctx
from xinhou_openai_framework.core.decorator.HttpClientDecorator import http_client
import logging


class VideoRemoteService:
    """
    AgentsPlatform 远程服务类，用于调用远程服务。
    """
    context: AppContext = ctx.__getattr__("context")  # 全局变量
    
    # 添加调试日志
    logging.info(f"VideoRemoteService初始化时的配置: aip.remote属性={dir(context.aip.remote) if hasattr(context.aip, 'remote') else '无remote属性'}")

    @staticmethod
    @http_client(context.aip.remote.video, path="/query/videoTask", method="POST",
                 timeout=60)
    def query_video_task(response_data, **kwargs):
        if response_data:
            if response_data['code'] == 200:  # 假设 200 表示成功
                return response_data['data']
            else:
                # 处理错误情况
                raise ValueError(response_data['msg'])
        else:
            raise Exception

    @staticmethod
    @http_client(context.aip.remote.video, path="/create/videoTask", method="POST",
                 timeout=60)
    def create_video_task(response_data, **kwargs):
        if response_data:
            if response_data['code'] == 200:  # 假设 200 表示成功
                return response_data['data']
            elif response_data['code'] == 400:
                raise ValueError("参数错误:" + response_data.get('data', {}).get("e_message") or response_data['msg'])
            else:
                # 处理错误情况
                raise ValueError(response_data['msg'])
        else:
            raise Exception
        
    @staticmethod
    @http_client(context.aip.remote.ffmpeg, path="/transcode", method="POST",
                 timeout=1000)  
    def transcode_video(response_data, **kwargs):
        """转码视频的远程调用方法"""
        if response_data:
            if response_data['code'] == 200:
                return response_data['data']
            else:
                raise ValueError(response_data['msg'])
        else:
            raise Exception("No response data received")