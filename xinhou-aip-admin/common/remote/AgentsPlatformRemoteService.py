# !/usr/bin/python3
# -*- coding: utf-8 -*-

"""
远程调用管理器类
----------------------------------------------------
@Project :   xinhou-openai-admin
@File    :   AgentsPlatformRemoteService.py
@Contact :   <EMAIL>

@Modify Time      <AUTHOR>    @Desciption
------------      -------    --------    -----------
2023/04/16 22:04  peng.shen   v1.0.0     None
"""

import httpx
from fastapi.responses import StreamingResponse
from loguru import logger
from xinhou_openai_framework.core.context.model.AppContext import AppContext
from xinhou_openai_framework.core.context.model.SystemContext import ctx
from xinhou_openai_framework.core.exception.CodeEnum import CodeEnum
from xinhou_openai_framework.core.reponse.R import R


class AgentsPlatformRemoteService:
    """
    AgentsPlatform 远程服务类，用于调用远程服务。
    """
    context: AppContext = ctx.__getattr__("context")  # 全局变量

    @staticmethod
    async def continue_creation_v2_1(json_data=None, **kwargs):
        """
        调用远程服务开始创建任务
        """
        try:
            if not json_data:
                logger.error("No JSON data provided")
                return R.jsonify(CodeEnum.BAD_REQUEST, "请求数据不能为空")

            # 从环境变量获取 baseurl
            base_url = AgentsPlatformRemoteService.context.aip.remote.agents
            if not base_url:
                logger.error("Base URL not configured")
                return R.jsonify(CodeEnum.INTERNAL_SERVER_ERROR, "远程服务地址未配置")

            logger.info(f"Using base URL: {base_url}")
            logger.info(f"Sending JSON data: {json_data}")

            async def stream_response():
                async with httpx.AsyncClient() as client:
                    async with client.stream(
                            "POST",
                            f"{base_url}/agent/chat",
                            json=json_data,
                            headers={
                                "Content-Type": "application/json",
                                "Accept": "text/event-stream",
                            },
                            timeout=300.0
                    ) as response:
                        async for chunk in response.aiter_bytes():
                            if chunk:
                                yield chunk

            return StreamingResponse(
                stream_response(),
                media_type="text/event-stream",
                headers={
                    "Cache-Control": "no-cache",
                    "Connection": "keep-alive",
                    "X-Accel-Buffering": "no",
                    "Content-Type": "text/event-stream",
                }
            )

        except Exception as e:
            error_msg = f"Error in start_creation: {str(e)}"
            logger.error(error_msg)
            return R.jsonify(CodeEnum.INTERNAL_SERVER_ERROR, error_msg)

    @staticmethod
    async def start_creation_v2_1(json_data=None, **kwargs):
        """
        调用远程服务开始创建任务
        """
        try:
            if not json_data:
                logger.error("No JSON data provided")
                return R.jsonify(CodeEnum.BAD_REQUEST, "请求数据不能为空")

            # 从环境变量获取 baseurl
            base_url = AgentsPlatformRemoteService.context.aip.remote.agents
            if not base_url:
                logger.error("Base URL not configured")
                return R.jsonify(CodeEnum.INTERNAL_SERVER_ERROR, "远程服务地址未配置")

            logger.info(f"Using base URL: {base_url}")
            logger.info(f"Sending JSON data: {json_data}")

            async def stream_response():
                async with httpx.AsyncClient() as client:
                    async with client.stream(
                            "POST",
                            f"{base_url}/agent/chat",
                            json=json_data,
                            headers={
                                "Content-Type": "application/json",
                                "Accept": "text/event-stream",
                            },
                            timeout=300.0
                    ) as response:
                        async for chunk in response.aiter_bytes():
                            if chunk:
                                yield chunk

            return StreamingResponse(
                stream_response(),
                media_type="text/event-stream",
                headers={
                    "Cache-Control": "no-cache",
                    "Connection": "keep-alive",
                    "X-Accel-Buffering": "no",
                    "Content-Type": "text/event-stream",
                }
            )

        except Exception as e:
            error_msg = f"Error in start_creation: {str(e)}"
            logger.error(error_msg)
            return R.jsonify(CodeEnum.INTERNAL_SERVER_ERROR, error_msg)
