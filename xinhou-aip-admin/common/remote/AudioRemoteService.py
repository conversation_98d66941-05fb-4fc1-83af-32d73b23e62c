import base64
import logging
import time

import aiohttp
from loguru import logger
from xinhou_openai_framework.core.context.model.AppContext import AppContext
from xinhou_openai_framework.core.context.model.SystemContext import ctx
from common.utils.http_client import http_client


class AudioRemoteService:
    """
    AgentsPlatform 远程服务类，用于调用远程服务。
    """
    context: AppContext = ctx.__getattr__("context")  # 全局变量

    @staticmethod
    @http_client(context.aip.remote.audio, path="/audio/generate", method="POST",
                 timeout=150)
    def audio_generate(response_data, **kwargs):
        if response_data:
            if response_data['code'] == 200:  # 假设 200 表示成功
                return response_data['data']
            else:
                # 处理错误情况
                raise ValueError(response_data['msg'])
        else:
            raise Exception

    @staticmethod
    @http_client(context.aip.remote.audio, path="/audio/clone", method="POST",
                 timeout=300)
    def audio_clone(response_data, **kwargs):
        if response_data:
            if response_data['code'] == 200:  # 假设 200 表示成功
                return response_data['data']
            else:
                # 处理错误情况
                raise ValueError(response_data['msg'])
        else:
            raise Exception



class FishRemoteService:
    context: AppContext = ctx.__getattr__("context")  # 全局变量

    @staticmethod
    async def audio_clone(form_data: aiohttp.FormData):
        base_url = FishRemoteService.context.aip.remote.audio
        url = f"{base_url}/fish/clone"
        async with aiohttp.ClientSession() as session:
            try:
                async with session.post(url, data=form_data) as response:
                    if response.status == 200:
                        result = await response.json()
                        logger.info(f"Fish service response: {result}")
                        return result
                    else:
                        error_text = await response.text()
                        logger.error(f"Fish service error: {response.status}, {error_text}")
                        raise ValueError(f"Fish service error: {response.status}, {error_text}")
            except Exception as e:
                logger.exception("Error calling Fish service")
                raise

    @staticmethod
    @http_client(context.aip.remote.audio, path="/backup/voice/clone", method="POST",
                timeout=150)
    def backup_audio_clone(response_data, **kwargs):
        if response_data:
            if response_data['code'] == 200:
                return response_data['data']
            else:
                raise ValueError(response_data['msg'])
        else:   
            raise Exception
        

    @staticmethod
    @http_client(context.aip.remote.audio, path="/fish/generate", method="POST",
                timeout=300)
    def audio_generate(response_data, response_headers=None, **kwargs):
        """处理音频生成响应，支持直接返回文件流"""
        logger.info(f"音频生成响应头: {response_headers}")
        
        # 检查是否有响应数据
        if not response_data:
            logger.error("未收到响应数据")
            raise Exception("未收到响应数据")
        
        # 记录响应数据类型和大小
        logger.info(f"收到响应数据类型: {type(response_data)}, 大小: {len(response_data) if isinstance(response_data, bytes) else '未知'}")
        
        # 判断响应类型
        if response_headers and 'content-type' in response_headers:
            content_type = response_headers.get('content-type', '')
            logger.info(f"响应内容类型: {content_type}")
            
            # 如果是音频文件类型，直接返回二进制数据和相关信息
            if content_type.startswith('audio/'):
                filename = None
                if 'content-disposition' in response_headers:
                    disposition = response_headers.get('content-disposition', '')
                    logger.info(f"响应内容处置: {disposition}")
                    if 'filename=' in disposition:
                        filename = disposition.split('filename=')[1].strip('"')
                        logger.info(f"提取的文件名: {filename}")
                
                return {
                    'is_binary': True,
                    'binary_data': response_data,
                    'content_type': content_type,
                    'filename': filename
                }
        
        # 如果响应数据是二进制但没有正确的头信息，也作为二进制处理
        if isinstance(response_data, bytes):
            return {
                'is_binary': True,
                'binary_data': response_data,
                'content_type': 'audio/mpeg',  # 默认类型
                'filename': f"fish_voice_{int(time.time())}.mp3"  # 默认文件名
            }
        
        # 如果是JSON响应，按原来的逻辑处理
        try:
            if isinstance(response_data, dict):
                if response_data.get('code') == 200:
                    data = response_data.get('data')
                    if isinstance(data, bytes):
                        data = base64.b64encode(data).decode('utf-8')
                    elif isinstance(data, dict):
                        for key, value in data.items():
                            if isinstance(value, bytes):
                                data[key] = base64.b64encode(value).decode('utf-8')
                    return data
                else:
                    raise ValueError(response_data.get('msg', '未知错误'))
            else:
                # 可能是其他格式的响应
                logger.info(f"收到非JSON响应: {type(response_data)}")
                return response_data
        except Exception as e:
            logger.error(f"处理音频生成响应失败: {str(e)}")
            raise Exception(f"处理音频生成响应失败: {str(e)}")
        
    @staticmethod
    @http_client(context.aip.remote.audio, path="/backup/voice/generate", method="POST",
                timeout=300)
    def backup_audio_generate(response_data, response_headers=None, **kwargs):
        """处理备用服务音频生成响应，支持直接返回文件流"""
        logger.info(f"备用服务音频生成响应头: {response_headers}")
        
        # 检查是否有响应数据
        if not response_data:
            logger.error("未收到响应数据")
            raise Exception("未收到响应数据")

        # 判断响应类型
        if response_headers and 'content-type' in response_headers:
            content_type = response_headers.get('content-type', '')
            logger.info(f"响应内容类型: {content_type}")
            
            # 如果是音频文件类型，直接返回二进制数据和相关信息
            if content_type.startswith('audio/'):
                filename = None
                if 'content-disposition' in response_headers:
                    disposition = response_headers.get('content-disposition', '')
                    logger.info(f"响应内容处置: {disposition}")
                    if 'filename=' in disposition:
                        filename = disposition.split('filename=')[1].strip('"')
                        logger.info(f"提取的文件名: {filename}")
                
                return {
                    'is_binary': True,
                    'binary_data': response_data,
                    'content_type': content_type,
                    'filename': filename
                }
        
        # 如果响应数据是二进制但没有正确的头信息，也作为二进制处理
        if isinstance(response_data, bytes):
            return {
                'is_binary': True,
                'binary_data': response_data,
                'content_type': 'audio/mpeg',  # 默认类型
                'filename': f"backup_voice_{int(time.time())}.mp3"  # 默认文件名
            }
        
        # 如果是JSON响应，按原来的逻辑处理，但增加更多容错处理
        try:
            if isinstance(response_data, dict):
                # 记录完整的响应数据以便调试
                logger.info(f"JSON响应详情: {response_data}")
                
                # 检查各种可能的响应格式
                if 'code' in response_data and response_data['code'] == 200:
                    return response_data.get('data', response_data)
                elif 'status' in response_data and response_data['status'] == 200:
                    return response_data.get('data', response_data)
                elif 'success' in response_data and response_data['success'] is True:
                    return response_data.get('data', response_data)
                elif 'error' in response_data:
                    raise ValueError(f"服务返回错误: {response_data['error']}")
                elif 'msg' in response_data:
                    if 'data' in response_data:
                        return response_data['data']
                    else:
                        raise ValueError(response_data['msg'])
                else:
                    # 如果没有明确的状态码，但有data字段，直接返回
                    if 'data' in response_data:
                        return response_data['data']
                    elif 'audio_url' in response_data:
                        return response_data['audio_url']
                    elif 'url' in response_data:
                        return response_data['url']
                    else:
                        # 如果没有找到预期的字段，直接返回整个响应
                        logger.info("未找到预期的响应字段，返回完整响应")
                        return response_data
            else:
                # 可能是其他格式的响应
                logger.info(f"收到非JSON响应: {type(response_data)}")
                return response_data
        except Exception as e:
            logger.error(f"处理备用服务音频生成响应失败: {str(e)}")
            # 不要抛出异常，而是返回原始响应
            logger.info("返回原始响应数据")
            return response_data

    @staticmethod
    @http_client(context.aip.remote.ffmpeg, path="/speed", method="POST",
                 timeout=150)
    def control_audio_speed(response_data, **kwargs):
        """调用远程服务控制音频速度"""
        if response_data:
            if response_data['code'] == 200:  
                return response_data['data']
            else:
                # 处理错误情况
                raise ValueError(response_data['msg'])
        else:
            raise Exception("No response data received")
        
    @staticmethod
    async def backup_audio_clone(form_data: aiohttp.FormData):
        """备用声音克隆服务"""
        try:
            base_url = FishRemoteService.context.aip.remote.audio
            url = f"{base_url}/backup/voice/clone"
            
            logger.info(f"备用服务请求URL: {url}")
            
            async with aiohttp.ClientSession() as session:
                try:
                    async with session.post(url, data=form_data) as response:
                        if response.status == 200:
                            result = await response.json()
                            logger.info(f"备用服务响应: {result}")
                            if result.get('code') == 200:
                                return result
                            else:
                                error_msg = result.get('msg', '未知错误')
                                logger.error(f"备用服务返回错误: {error_msg}")
                                raise ValueError(f"备用服务返回错误: {error_msg}")
                        else:
                            error_text = await response.text()
                            logger.error(f"备用服务HTTP错误: {response.status}, {error_text}")
                            raise ValueError(f"备用服务HTTP错误: {response.status}, {error_text}")
                except aiohttp.ClientError as e:
                    logger.error(f"备用服务网络错误: {str(e)}")
                    raise ValueError(f"备用服务网络错误: {str(e)}")
        except Exception as e:
            logger.error(f"备用服务调用异常: {str(e)}")
            raise ValueError(f"备用服务调用异常: {str(e)}")

    @staticmethod
    async def audio_generate_async(json_data: dict):
        """异步调用Fish音频生成服务"""
        base_url = FishRemoteService.context.aip.remote.audio
        url = f"{base_url}/fish/generate"
        
        logger.info(f"异步调用Fish音频生成服务: {url}")
        
        async with aiohttp.ClientSession() as session:
            try:
                async with session.post(url, json=json_data) as response:
                    if response.status == 200:
                        # 检查响应类型
                        content_type = response.headers.get('content-type', '')
                        if content_type.startswith('audio/'):
                            # 如果是音频文件，直接返回二进制数据
                            binary_data = await response.read()
                            filename = None
                            if 'content-disposition' in response.headers:
                                disposition = response.headers.get('content-disposition', '')
                                if 'filename=' in disposition:
                                    filename = disposition.split('filename=')[1].strip('"')
                            
                            return {
                                'is_binary': True,
                                'binary_data': binary_data,
                                'content_type': content_type,
                                'filename': filename or f"fish_voice_{int(time.time())}.mp3"
                            }
                        else:
                            # 如果是JSON响应
                            result = await response.json()
                            if result.get('code') == 200:
                                return result.get('data')
                            else:
                                raise ValueError(result.get('msg', '未知错误'))
                    else:
                        error_text = await response.text()
                        logger.error(f"Fish服务错误: {response.status}, {error_text}")
                        raise ValueError(f"Fish服务错误: {response.status}, {error_text}")
            except Exception as e:
                logger.exception("调用Fish服务异常")
                raise

    @staticmethod
    @http_client(context.aip.remote.ffmpeg, path="/audio_duration", method="POST",
                 timeout=300)
    def get_audio_duration(response_data, **kwargs):
        """调用远程服务获取音频时长"""
        if response_data:
            if response_data['code'] == 200:
                return response_data['data']
            else:
                # 处理错误情况
                raise ValueError(response_data['msg'])
        else:
            raise Exception("No response data received")
