# AIP-APT平台后台管理系统 (xinhou-aip-admin)

## 项目概述

AIP-APT平台后台管理系统是一个基于FastAPI框架开发的人工智能内容生成与管理平台，整合了多种AI能力，包括语音生成、视频生成、知识库检索、虚拟人生成等功能。该系统主要用于管理和提供AI内容创作服务，支持多种媒体形式的内容生产。

## 核心功能

### 1. AI内容生成
- **语音/口播稿生成**：自动生成并转换为音频内容
- **视频内容生成**：将文本和音频转换为视频形式
- **多语言支持**：支持多种语言的内容生成

### 2. 知识库与RAG技术
- **文件向量化**：支持将多种格式的文件转换为向量进行存储
- **混合搜索**：实现基于RAG技术的高效检索和内容生成
- **知识图谱**：构建知识图谱提升内容生成的专业度

### 3. 虚拟人技术
- **虚拟人模型制作**：创建定制化虚拟人形象
- **口播风格定制**：支持多种语音风格
- **访谈风格定制**：适配不同场景的虚拟人表现

### 4. 工作流管理
- **任务流程管理**：定义和管理内容生成的工作流
- **Agent协作**：多个AI代理协同完成复杂任务
- **任务状态追踪**：实时监控任务执行状态

### 5. 用户与IP管理
- **用户认证与授权**：管理系统用户的权限
- **IP(知识产权)管理**：对内容生成的IP资源进行管理
- **使用额度与过期控制**：管理用户的使用权限和期限

### 6. 媒体资源管理
- **音频模型管理**：管理不同的音频生成模型
- **视频模型管理**：管理视频生成相关的模型资源
- **文件存储与检索**：支持多种格式文件的存储和检索

## 技术架构

- **后端框架**：FastAPI (Python 3.10)
- **数据存储**：MySQL、Redis
- **队列系统**：基于Redis的消息队列
- **远程调用**：基于Nacos的微服务架构
- **文件存储**：OSS对象存储
- **容器化**：Docker支持

## 系统组件

### 核心模块
- **apps/admin**：后台管理功能
- **apps/api**：API接口
- **apps/mock**：模拟数据接口
- **common**：通用业务逻辑
- **protos**：协议定义文件

### 控制器模块
- **Agents系列控制器**：管理AI代理与内容生成
- **Virtual Human控制器**：虚拟人模型管理
- **RAG控制器**：检索增强生成系统
- **Knowledge相关控制器**：知识库管理
- **媒体相关控制器**：音频/视频/图像模型管理

## 部署要求

- Python 3.10+
- MySQL 8.0+
- Redis 7.0+
- Nacos 2.0.2+（用于服务注册与配置）

## 快速开始

### 环境设置
```bash
# 更新pip库
pip install --upgrade pip

# 安装依赖
pip install -r requirements.txt
```

### 启动应用
```bash
python main.py
```

默认情况下，应用将在 http://0.0.0.0:8000 启动。可以通过修改application.yml或特定环境的配置文件调整相关设置。

## 环境配置

系统支持多环境配置，包括：
- application-dev.yml：开发环境
- application-test.yml：测试环境
- application-uat.yml：UAT环境
- application-prod.yml：生产环境

可以通过修改application.yml中的`framework.profiles.active`参数选择当前激活的环境。

## 技术特点

1. 基于FastAPI的高性能API服务
2. 微服务架构支持，与其他系统无缝集成
3. 基于消息队列的异步任务处理
4. 分层架构设计，便于扩展和维护
5. 完善的异常处理和日志记录机制
6. 健康检查与监控支持
7. Docker容器化支持 