framework:
  datasource:
    url: mysql+pymysql://mysql-7f7aab269535-public.rds.volces.com:33306/xinhou_aip_db?charset=utf8mb4
    username: mysql_prod
    password: bag!eck9etd-cyp.VAY
    pool:
      pool_pre_ping: True
      pool_size: 20
      pool_timeout: 120
      echo: True
  redis:
    host: redis-shzlodrakbb35vyja.redis.volces.com
    port: 6379
    password: xinh0u@Passwd
    timeout: 10000ms
    database: 8
  cache:
    type: redis
  logging:
    path:
    level: debug
  SECRET_KEY: D26RckVyJhin8U0EeTyQ7dOd6SO9jFmkcox0RFuQZ9M
  ALGORITHM: HS256
  ACCESS_TOKEN_EXPIRE_MINUTES: 43200

model:
  bge:
    name: sbert
    path: /opt/disk1/models/baai/bge-m3
    device: cuda
  bge_reranker:
    path: /opt/disk1/models/baai/bge-reranker-v2-m3
  analysis:
    model_path: /opt/disk1/models/.EasyOCR/model
    user_path: /opt/disk1/models/.EasyOCR/user_network
kb_config:
  script_chunk_size: 100
  script_overlap_size: 0
  person_chunk_size: 250
  person_overlap_size: 50
  hot_chunk_size: 100
  hot_overlap_size: 0
  rerank_flag: True
  additional_num_for_rerank: 20
  vector_search_score_threshold: 1.1
  milvus_insert_batch_size: 10
  embedding_batch_size: 200
  embedding_line_max_len: 1000
file:
  bge:
    path: "/opt/disk1/upload/multi_agents"
  ocr:
    path: "/opt/disk1/upload/static/cache_images"
  oss:
    path: "/opt/disk1/upload/static/processed"
aliyun_oss:
  endpoint: https://oss-cn-shanghai.aliyuncs.com
  access_key_id: LTAI5tBy74Jif3NxAi32vZyb
  access_key_secret: ******************************
  bucket_name: wechat-luobo
aliyun_acs:
  endpoint: green-cip.cn-shanghai.aliyuncs.com
  region_id: cn-shanghai
  access_key_id: LTAI5tBy74Jif3NxAi32vZyb
  access_key_secret: ******************************
aliyun_tw:
  endpoint: https://oss-cn-shanghai.aliyuncs.com
  region_id: cn-shanghai
  access_key_id: LTAI5t5d9RvgYRxpJw4Rf8kP
  access_key_secret: ******************************
  bucket_name: wechat-luobo
docs:
  swagger-ui:
    path: /swagger-ui.html
    enabled: true
  enabled: true
  packages-to-scan: apps/**
  paths-to-match: /**
milvus:
  host: openai-milvus.xinhouai.com
  port: 19530
  db_name: "xinhou_sp"
  collection_name: "xinhou_sp"
  batch_size: 10
  dim: 1024
llm_config:
  one_api:
    base_url:
      - https://api.gpt2share.com/v1
    api_key:
      - sk-X8QR18KsVO4f3RRZE4FcBb3c8fDe4f08Aa3dD8C4202f7395
aip:
  remote:
    admin: https://xinhou-aip-admin.chatonai.com
    knowledge: https://xinhou-aip-knowledge.chatonai.com
    search: https://xinhou-aip-search.chatonai.com
    agents: https://xinhou-aip-agents.chatonai.com
    embedding: http://*************:8000
    analysis: http://*************:8001






