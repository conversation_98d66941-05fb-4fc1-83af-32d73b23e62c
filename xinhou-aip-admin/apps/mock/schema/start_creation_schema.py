from typing import Optional

from pydantic import BaseModel, Field

from apps.admin.schema.AgentsAdminSchema import LanguageEnum


class Request_start_creation(BaseModel):
    audio_model_id: Optional[str] = Field(default=None, description="音频模型ID")
    doc_length: Optional[int] = Field(default=None, description="文档长度")
    language: Optional[LanguageEnum] = Field(default=None, description="语言")
    pid: int = Field(description="IPID")
    progress: Optional[int] = Field(default=None, description="进度", ge=0, le=100)
    query: Optional[str] = Field(default=None, description="主题")
    remark: Optional[str] = Field(default=None, description="备注")
    status: Optional[int] = Field(default=None, description="状态")
    task_id: Optional[int] = Field(default=None, description="任务ID")
    task_knowledge_ids: Optional[str] = Field(default=None, description="任务知识库ID")
    video_model_id: Optional[str] = Field(default=None, description="视频模型ID")

    class Config:
        json_schema_extra = {
            "example": {
                "audio_model_id": "audio_001",
                "doc_length": 1000,
                "language": "zh",
                "pid": 12345,
                "progress": 50,
                "query": "示例主题",
                "remark": "示例备注",
                "status": 1,
                "task_id": 67890,
                "task_knowledge_ids": "1,2,3",
                "video_model_id": "video_001",
            }
        }


class Request_continue_creation(BaseModel):
    audio_model_id: Optional[str] = Field(default=None, description="音频模型ID")
    doc_length: Optional[int] = Field(default=None, description="文档长度")
    language: Optional[LanguageEnum] = Field(default=None, description="语言")
    pid: int = Field(description="IPID")
    progress: Optional[int] = Field(default=None, description="进度", ge=0, le=100)
    query: Optional[str] = Field(default=None, description="主题")
    remark: Optional[str] = Field(default=None, description="备注")
    status: Optional[int] = Field(default=None, description="状态")
    task_id: Optional[int] = Field(default=None, description="任务ID")
    task_knowledge_ids: Optional[str] = Field(default=None, description="任务知识库ID")
    video_model_id: Optional[str] = Field(default=None, description="视频模型ID")

    class Config:
        json_schema_extra = {
            "example": {
                "audio_model_id": "audio_001",
                "doc_length": 1000,
                "language": "zh",
                "pid": 12345,
                "progress": 50,
                "query": "示例主题",
                "remark": "示例备注",
                "status": 1,
                "task_id": 67890,
                "task_knowledge_ids": "1,2,3",
                "video_model_id": "video_001"
            }
        }
