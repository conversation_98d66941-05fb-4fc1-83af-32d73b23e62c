# !/usr/bin/python3
# -*- coding: utf-8 -*-

import asyncio
import json
from pathlib import Path
from typing import AsyncGenerator

from fastapi import APIRouter
from fastapi.responses import StreamingResponse

from apps.mock.schema.start_creation_schema import (
    Request_start_creation,
    Request_continue_creation,
)

api = APIRouter()


async def stream_generator() -> AsyncGenerator[str, None]:
    """异步生成器函数，用于流式返回数据"""
    json_path = Path(__file__).parent.parent / "data" / "stream_mock_data.json"
    try:
        with open(json_path, "r", encoding="utf-8") as f:
            data = json.load(f)
            for message in data["messages"]:
                if message.get("agent_style") == "TITLE_CHOOSE":
                    yield f"data: {json.dumps(message, ensure_ascii=False)}\n\n"
                    break

                # 模拟流式输出的延迟
                await asyncio.sleep(0.5)
                yield f"data: {json.dumps(message, ensure_ascii=False)}\n\n"
    except FileNotFoundError:
        yield 'data: {"error": "Mock data file not found"}\n\n'
    except json.JSONDecodeError:
        yield 'data: {"error": "Invalid JSON format in mock data"}\n\n'


@api.post(
    "/mock/agents/v2.1/start_creation",
    tags=["mock"],
    summary="文本流式输出",
    description="此接口用于模拟AI文本流式响应输出",
)
async def stream_text(request: Request_start_creation):
    return StreamingResponse(
        stream_generator(),
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
        },
    )


@api.post(
    "/mock/agents/v2.1/continue_creation",
    tags=["mock"],
    summary="继续输出接口",
    description="此接口用于模拟继续输出接口",
)
async def stream_audio(request: Request_continue_creation):
    # 这里可以根据需要实现音频流式输出的逻辑
    return StreamingResponse(
        handle_start_continue(request),
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
        },
    )


async def handle_start_continue(request):
    """从指定位置开始的流式输出生成器函数"""
    json_path = Path(__file__).parent.parent / "data" / "stream_mock_data.json"
    try:
        with open(json_path, "r", encoding="utf-8") as f:
            data = json.load(f)
            start_output = False
            wait_time = 0.5
            # 确定开始和结束的agent_style
            if request.query:
                start_style = "USER"
                end_style = "VOICE_1"
            elif request.audio_model_id:
                start_style = "VOICE_2"
                end_style = "VIDEO_1"
            elif request.video_model_id:
                start_style = "VIDEO_2"
                end_style = None

            for message in data["messages"]:
                # 遇到这俩的时候多等待一段时间
                if (
                    message.get("agent_style") == "DATA_LOADING_1"
                    or message.get("agent_style") == "DATA_LOADING_2"
                ):
                    wait_time = 5
                else:
                    wait_time = 0.5

                # 当找到指定的agent_style时开始输出
                if message.get("agent_style") == start_style:
                    start_output = True

                if start_output:
                    # 如果是音频模式且遇到agent_style为21，或视频模式遇到结束，则停止生成
                    if (end_style and message.get("agent_style") == end_style) or (
                        not end_style and message == data["messages"][-1]
                    ):
                        yield f"data: {json.dumps(message, ensure_ascii=False)}\n\n"
                        break

                    # 模拟流式输出的延迟
                    yield f"data: {json.dumps(message, ensure_ascii=False)}\n\n"

                    await asyncio.sleep(wait_time)

    except FileNotFoundError:
        yield 'data: {"error": "Mock data file not found"}\n\n'
    except json.JSONDecodeError:
        yield 'data: {"error": "Invalid JSON format in mock data"}\n\n'
