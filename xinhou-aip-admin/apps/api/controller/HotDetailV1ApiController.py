# !/usr/bin/python3
# -*- coding: utf-8 -*-

from fastapi import APIRouter
from fastapi import Depends
from sqlalchemy.orm import Session
from xinhou_openai_framework.core.db.DatabaseManager import DatabaseManager
from xinhou_openai_framework.core.reponse.R import R
from xinhou_openai_framework.core.reponse.ResModel import ResModel
from xinhou_openai_framework.pages.PageHelper import PageResultHelper, PageHelper

from apps.api.schema.ReqV1HotDetailQuerySchema import ReqV1AHotDetailQuerySchema, ResV1AHotDetailQuerySchema
from common.service.HotDetailService import HotDetailService

api = APIRouter()


@api.post('/hotDetail/query', tags=["查询"],
          response_model=ResModel[PageResultHelper[ResV1AHotDetailQuerySchema]],
          summary="知乎热点查询接口",
          description="此接口支持分页查询知乎热点")
async def hot_detail_query(search: PageHelper[ReqV1AHotDetailQuerySchema],
                           db: Session = Depends(DatabaseManager().get_session)
                           ):
    return R.SUCCESS(HotDetailService(db).find_by(search))
