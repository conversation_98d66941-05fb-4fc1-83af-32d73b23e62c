# !/usr/bin/python3
# -*- coding: utf-8 -*-

import typing as t

from pydantic import BaseModel, Field


class ReqV1AHotDetailQuerySchema(BaseModel):
    folder_name: t.Optional[str] = Field(default=None, title="热点名称", )
    hot_time: t.Optional[str] = Field(default=None, title="热点时间", )
    hot_text: t.Optional[str] = Field(default=None, title="热点内容", )
    hot_url: t.Optional[int] = Field(default=None, title="热点链接", )
    industry_tags: t.Optional[str] = Field(default=None, title="行业标签", )
    content_classify: t.Optional[str] = Field(default=None, title="内容分类", )


class ResV1AHotDetailQuerySchema(BaseModel):
    """
    分页查询返回结果
    """

    id: t.Optional[int] = Field(default=None, title="参数主键", )
    folder_name: t.Optional[str] = Field(default=None, title="热点名称", )
    hot_time: t.Optional[str] = Field(default=None, title="热点时间", )
    hot_text: t.Optional[str] = Field(default=None, title="热点内容", )
    hot_url: t.Optional[int] = Field(default=None, title="热点链接", )
    industry_tags: t.Optional[str] = Field(default=None, title="行业标签", )
    content_classify: t.Optional[str] = Field(default=None, title="内容分类", )
