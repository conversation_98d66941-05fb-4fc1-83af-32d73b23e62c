# !/usr/bin/python3
# -*- coding: utf-8 -*-

"""
预约信息表控制器类
----------------------------------------------------
@Project :   xinhou-openai-admin
@File    :   Precontact.py
@Contact :   <EMAIL>

@Modify Time      <AUTHOR>    @Desciption
------------      -------    --------    -----------
2025/01/21 10:04  Cyue       v1.0.0      None
"""

from typing import List

from fastapi import APIRouter, Depends, Path
from loguru import logger
from sqlalchemy.orm import Session
from xinhou_openai_framework.core.cache.RedisConnectionPool import RedisConnectionPool
from xinhou_openai_framework.core.context.model.AppContext import AppContext
from xinhou_openai_framework.core.context.model.SystemContext import ctx
from xinhou_openai_framework.core.db.DatabaseManager import DatabaseManager
from xinhou_openai_framework.core.exception.GlobalExceptionType import ParameterException
from xinhou_openai_framework.core.reponse.R import R
from xinhou_openai_framework.core.reponse.ResModel import ResModel
from xinhou_openai_framework.pages.PageHelper import PageResultHelper, PageHelper

from apps.admin.controller.UserController import check_current_user
from apps.admin.schema.PrecontractSchema import (
    ReqPrecontractSaveSchema, ReqPrecontractUpdateSchema, ResPrecontractDetailSchema
)
from common.entity.Precontract import Precontract
from common.entity.User import User
from common.service import WxNotificationService
from common.service.PrecontractService import PrecontractService

api = APIRouter()


@api.post(
    "/admin/precontract/create",
    tags=["预约", "v1"],
    summary="[v1]创建预约",
    description="创建预约",
)
async def create_precontract(
        model: ReqPrecontractSaveSchema,
        db: Session = Depends(DatabaseManager().get_session),
        redis_pool: Session = Depends(RedisConnectionPool().get_pool),
):
    logger.info("[create_precontract][request]:{}".format(model.model_dump()))
    context: AppContext = ctx.__getattr__("context")

    # 验证手机验证码
    # is_verify, message = await UserService(db).verify_code(
    #     model.mobile, model.verify_code, redis_pool
    # )
    # if not is_verify:
    #     return R.PARAMETER_ERR(data={"message": message})
    try:
        # 创建预约条目
        precontract = Precontract(
            mobile=model.mobile,
            name=model.name,
            business_name=model.business_name,
            city=model.city,
            remark=model.remark,
        )
        precontractInfo = PrecontractService(db).save(precontract)
        message = f"手机号：{precontractInfo.mobile}\n姓名：{precontractInfo.name or '-'}\n公司名称：{precontractInfo.business_name or '-'}\n城市：{precontractInfo.city or '-'}\n于{precontractInfo.created_at.isoformat()}，提交【联系购买】表单，请尽快联系该线索跟进~"
        await WxNotificationService.sendPrecontact(message, context)
        return R.SUCCESS(data=precontractInfo)
    except Exception as e:
        return R.PARAMETER_ERR(data={"message": str(e)})


@api.post('/admin/precontract/find',
          tags=["预约", "查询", "v1"],
          response_model=ResModel[PageResultHelper[ResPrecontractDetailSchema]],
          summary="[v1][precontract][find]查询预约信息表信息带分页接口",
          description="通过参数模型传递条件查询")
async def find(search: PageHelper,
               current_user: User = Depends(check_current_user),
               db: Session = Depends(DatabaseManager().get_session)):
    return R.SUCCESS(PrecontractService(db).find_by(search))


@api.get("/admin/precontract/detail/{id}",
         tags=["预约", "查询", "v1"],
         response_model=ResModel[ResPrecontractDetailSchema],
         summary="[v1][precontract][detail]获取预约信息表详情接口",
         description="根据ID获取数据")
async def detail(id: int = Path(title="ID不能为空"),
                 current_user: User = Depends(check_current_user),
                 db: Session = Depends(DatabaseManager().get_session)):
    if id is None:
        raise ParameterException()
    return R.SUCCESS(PrecontractService(db).find_by_id(Precontract(id=id)))


@api.post("/admin/precontract/update",
          tags=["预约", "v1"],
          response_model=ResModel[ResPrecontractDetailSchema],
          summary="[v1][precontract][update]更新预约信息表信息接口",
          description="根据ID更新数据")
async def update(model: ReqPrecontractUpdateSchema,
                 current_user: User = Depends(check_current_user),
                 db: Session = Depends(DatabaseManager().get_session)):
    if model.id is None:
        raise ParameterException()

    # 将company字段映射到business_name
    update_data = model.model_dump(exclude_unset=True)
    if 'company' in update_data:
        update_data['business_name'] = update_data.pop('company')

    return R.SUCCESS(PrecontractService(db).update(Precontract(**update_data)))


@api.get("/admin/precontract/delete/{id}",
         tags=["预约", "v1"],
         response_model=ResModel,
         summary="[v1][precontract][delete]删除预约信息表信息接口",
         description="根据ID删除数据")
async def delete(id: int = Path(title="ID不能为空"),
                 current_user: User = Depends(check_current_user),
                 db: Session = Depends(DatabaseManager().get_session)):
    if id is None:
        raise ParameterException()
    return R.SUCCESS(PrecontractService(db).delete(Precontract(id=id)))


@api.post("/admin/precontract/batch_delete",
          tags=["预约", "v1"],
          response_model=ResModel,
          summary="[v1][precontract][batch_delete]批量删除预约信息表信息接口",
          description="批量删除数据")
async def batch_delete(ids: List[int],
                       current_user: User = Depends(check_current_user),
                       db: Session = Depends(DatabaseManager().get_session)):
    if not ids:
        raise ParameterException()

    result = True
    for id in ids:
        try:
            PrecontractService(db).delete(Precontract(id=id))
        except Exception as e:
            logger.error(f"删除预约ID={id}失败: {str(e)}")
            result = False

    return R.SUCCESS(data={"success": result})
