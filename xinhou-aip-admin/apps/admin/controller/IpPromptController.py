from typing import List

from fastapi import APIRouter, Depends, Path
from sqlalchemy.orm import Session
from xinhou_openai_framework.core.db.DatabaseManager import DatabaseManager
from xinhou_openai_framework.core.exception.GlobalExceptionType import ParameterException
from xinhou_openai_framework.core.reponse.R import R
from xinhou_openai_framework.core.reponse.ResModel import ResModel
from xinhou_openai_framework.pages.PageHelper import PageResultHelper, PageHelper

from apps.admin.controller.UserController import check_current_user
from apps.admin.schema.IpPromptSchema import (
    ReqIpPromptFindSchema, ResIpPromptDetailSchema,
    ReqIpPromptSaveSchema, ReqIpPromptUpdateSchema
)
from common.entity.IpPrompt import IpPrompt
from common.entity.User import User
from common.service.IpPromptService import IpPromptService

api = APIRouter()


@api.post('/admin/ip_prompt/find', tags=["IP Prompt", "v1"],
          response_model=ResModel[PageResultHelper[ResIpPromptDetailSchema]],
          summary="[v1]查询IP Prompt信息带分页",
          description="通过参数模型传递条件查询")
async def find(search: PageHelper[ReqIpPromptFindSchema],
               db: Session = Depends(DatabaseManager().get_session)):
    return R.SUCCESS(IpPromptService(db).find_by(search))


@api.post('/admin/ip_prompt/findAll', tags=["IP Prompt", "v1"],
          response_model=ResModel[List[ResIpPromptDetailSchema]],
          summary="[v1]查询所有IP Prompt信息",
          description="通过参数模型传递条件查询")
async def find_all(search: ReqIpPromptFindSchema,
                   db: Session = Depends(DatabaseManager().get_session)):
    return R.SUCCESS(IpPromptService(db).find_all(search))


@api.post("/admin/ip_prompt/save", tags=["IP Prompt", "v1"],
          response_model=ResModel[ResIpPromptDetailSchema],
          summary="[v1]保存IP Prompt信息",
          description="通过参数模型保存数据")
async def save(model: ReqIpPromptSaveSchema,
               current_user: User = Depends(check_current_user),
               db: Session = Depends(DatabaseManager().get_session)):
    prompt = IpPromptService(db).save(IpPrompt(**model.model_dump(exclude_unset=True)))
    return R.SUCCESS(prompt)


@api.post("/admin/ip_prompt/update", tags=["IP Prompt", "v1"],
          response_model=ResModel[ResIpPromptDetailSchema],
          summary="[v1]更新IP Prompt信息",
          description="根据ID更新数据")
async def update(model: ReqIpPromptUpdateSchema,
                 current_user: User = Depends(check_current_user),
                 db: Session = Depends(DatabaseManager().get_session)):
    if model.id is None:
        raise ParameterException()
    return R.SUCCESS(IpPromptService(db).update(IpPrompt(**model.model_dump(exclude_unset=True))))


@api.get("/admin/ip_prompt/delete/{id}", tags=["IP Prompt", "v1"],
         response_model=ResModel,
         summary="[v1]删除IP Prompt信息",
         description="根据ID删除数据")
async def delete(id: int = Path(..., title="ID不能为空"),
                 current_user: User = Depends(check_current_user),
                 db: Session = Depends(DatabaseManager().get_session)):
    if id is None:
        raise ParameterException()
    IpPromptService(db).delete(IpPrompt(id=id))
    return R.SUCCESS()


@api.get("/admin/ip_prompt/detail/{pid}", tags=["IP Prompt", "v1"],
         summary="[v1]获取IP Prompt详情",
         description="根据ID获取数据")
async def detail(pid: int = Path(..., title="PID不能为空"),
                 db: Session = Depends(DatabaseManager().get_session)):
    if pid is None:
        raise ParameterException()
    return R.SUCCESS(IpPromptService(db).get_by_pid(pid))
