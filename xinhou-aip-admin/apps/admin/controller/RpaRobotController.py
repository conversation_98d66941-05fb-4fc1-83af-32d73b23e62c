# !/usr/bin/python3
# -*- coding: utf-8 -*-

"""
任务控制器
----------------------------------------------------
@Project :   xinhou-openai-admin
@File    :   TaskController.py
@Contact :   <EMAIL>

@Modify Time      <AUTHOR>    @Desciption
------------      -------    --------    -----------
2023/07/18 17:32  ChatGPT    v1.0.0      None
2024/07/18 18:30  ChatGPT    v1.1.0      添加CRUD操作
"""

from fastapi import APIRouter, Depends
from sqlalchemy.orm import Session
from xinhou_openai_framework.core.db.DatabaseManager import DatabaseManager
from xinhou_openai_framework.core.reponse.R import R
from xinhou_openai_framework.core.reponse.ResModel import ResModel

from apps.admin.schema.ReqRpaRobotSchema import (
    ReqCreateRpaRobotSchema
)
from common.contents.RpaRobotEnums import RobotPlatformEnum, RobotTypeEnum
from common.entity.RpaRobot import RpaRobot
from common.service.RpaRobotService import RpaRobotService

api = APIRouter()


@api.post("/admin/rpa/create_robot", tags=["任务", "v1"],
          response_model=ResModel,
          summary="[v1][rpa_task][create]创建rpa机器人接口。",
          description="创建rpa任务接口")
async def create_rpa_task(req: ReqCreateRpaRobotSchema,
                          db: Session = Depends(DatabaseManager().get_session)):
    if req.robot_platform not in RobotPlatformEnum.__members__.keys():
        return R.PARAMETER_ERR()
    if req.robot_type not in RobotTypeEnum.__members__.keys():
        return R.PARAMETER_ERR()
    result = RpaRobotService(db).save(RpaRobot(**req.dict()))   # noqa
    return R.SUCCESS(result)
