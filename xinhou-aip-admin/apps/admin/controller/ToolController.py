from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from typing import List, Optional

from xinhou_openai_framework.core.db.DatabaseManager import DatabaseManager
from xinhou_openai_framework.core.reponse.R import R

from apps.admin.schema.ToolSchema import *
from common.entity.Tool import Tool
from common.service.ToolService import ToolService

api = APIRouter()


@api.post("/admin/tool/create", tags=["Tool管理"],
          response_model=ResToolSchema,
          summary="创建Tool",
          description="创建新的Tool配置")
async def create_tool(
        req: ReqToolCreateSchema,
        db: Session = Depends(DatabaseManager().get_session)
):

    tool = Tool(**req.dict())
    result = ToolService(db).create(tool)
    return R.SUCCESS(result)


@api.post("/admin/tool/update", tags=["Tool管理"],
          response_model=ResToolSchema,
          summary="更新Tool",
          description="更新已存在的Tool配置")
async def update_tool(
        req: ReqToolUpdateSchema,
        db: Session = Depends(DatabaseManager().get_session)
):
    if not req.id or req.id <= 0:
        raise HTTPException(status_code=400, detail="无效的Tool ID")

    update_data = {k: v for k, v in req.dict().items() if v is not None}
    if len(update_data) <= 1:  # 只有id字段
        raise HTTPException(status_code=400, detail="没有需要更新的字段")
        
    tool = Tool(**update_data)
    result = ToolService(db).update(tool)
    return R.SUCCESS(result)


@api.post("/admin/tool/delete", tags=["Tool管理"],
          summary="删除Tool",
          description="软删除指定的Tool")
async def delete_tool(
        req: ReqToolDeleteSchema,
        db: Session = Depends(DatabaseManager().get_session)
):
    if not req.id or req.id <= 0:
        raise HTTPException(status_code=400, detail="无效的Tool ID")
        
    result = ToolService(db).delete(req.id)
    return R.SUCCESS(result)


@api.get("/admin/tool/get/{id}", tags=["Tool管理"],
         response_model=ResToolSchema,
         summary="获取Tool详情",
         description="根据ID获取Tool详细信息")
async def get_tool(
        id: int,
        db: Session = Depends(DatabaseManager().get_session)
):
    if not id or id <= 0:
        raise HTTPException(status_code=400, detail="无效的Tool ID")
        
    result = ToolService(db).get_by_id(id)
    if not result:
        raise HTTPException(status_code=404, detail="Tool不存在")
    return R.SUCCESS(result)


@api.get("/admin/tool/list", tags=["Tool管理"],
         response_model=List[ResToolSchema],
         summary="获取Tool列表",
         description="获取所有Tool列表")
async def list_tools(
        status: Optional[int] = None,
        db: Session = Depends(DatabaseManager().get_session)
):
    if status is not None and status not in [1, 2]:
        raise HTTPException(status_code=400, detail="无效的状态值")
        
    result = ToolService(db).list_all(status)
    return R.SUCCESS(result)

