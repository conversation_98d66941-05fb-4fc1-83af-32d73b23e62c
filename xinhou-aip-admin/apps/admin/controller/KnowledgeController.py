# !/usr/bin/python3
# -*- coding: utf-8 -*-

"""
知识库控制器类
----------------------------------------------------
@Project :   xinhou-aip-admin
@File    :   KnowledgeController.py
@Contact :   <EMAIL>

@Modify Time      <AUTHOR>    @Desciption
------------      -------    --------    -----------
2024/07/18 10:00   fancy     v1.0.0      初始创建
"""
import json
from typing import List, Dict

import redis.asyncio as redis
from fastapi import APIRouter, Depends, Path, Form
from loguru import logger
from sqlalchemy.orm import Session
from xinhou_openai_framework.core.cache.RedisConnectionPool import RedisConnectionPool
from xinhou_openai_framework.core.db.DatabaseManager import DatabaseManager
from xinhou_openai_framework.core.exception.CodeEnum import CodeEnum
from xinhou_openai_framework.core.exception.GlobalBusinessException import GlobalBusinessException
from xinhou_openai_framework.core.reponse.R import R
from xinhou_openai_framework.core.reponse.ResModel import ResModel

from apps.admin.schema.CommonV1ReqKnowLedgeSchema import CommonV1ReqKnowLedgeSchema
from apps.admin.schema.KnowledgeSchema import ResPersonalKnowledgeSchema, KnowledgeSearchSchema
from apps.admin.schema.SocialMediaKnowledgeSchema import AddSocialMediaAccountRequest
from apps.admin.schema.SocialMediaKnowledgeSchema import DeleteSocialMediaAccountResponse
from apps.admin.schema.SocialMediaKnowledgeSchema import UpdateSocialMediaAccountRequest
from common.contents.AgentsUserContents import AgentsUserContents
from common.service.KnowledgeService import KnowledgeService

api = APIRouter()


@api.get('/api/personal-knowledge/search/{pid}', tags=["知识库"],
         response_model=ResModel[List[Dict]],  # 改为List[Dict]
         summary="[v1][知识库]获取个人知识库列表",
         description="根据IP ID获取个人知识库列表，包含相关文件信息")
async def get_personal_knowledge(
        pid: int = Path(..., title="Ip对应的ID"),
        db: Session = Depends(DatabaseManager().get_session)
):
    logger.info(f"[查询个人知识库][params]: pid={pid}")
    knowledge_service = KnowledgeService(db)
    result = knowledge_service.get_personal_knowledge_with_files(pid)
    return R.SUCCESS(result)


@api.get('/api/knowledge/files/{knowledge_id}', tags=["知识库"],
         summary="[v1][知识库]获取个人对应识库所有文件",
         description="获取指定知识库的所有文件，包括每个文件的第一段page_content")
async def get_knowledge_files(
        knowledge_id: int = Path(..., title="知识库ID"),
        db: Session = Depends(DatabaseManager().get_session),
):
    knowledge_service = KnowledgeService(db)
    files = knowledge_service.get_knowledge_files(knowledge_id)
    return R.SUCCESS(files)


@api.post('/api/social-media/accounts', tags=["知识库"],
          summary="[v1][知识库]添加社交媒体账号",
          description="添加社交媒体账号")
async def add_social_media_account(
        request: AddSocialMediaAccountRequest,
        db: Session = Depends(DatabaseManager().get_session)
):
    try:
        knowledge_service = KnowledgeService(db)
        created_knowledge = await knowledge_service.add_social_media_account(request.pid, request.account_url,
                                                                             request.is_myself)
        return R.SUCCESS(
            {"message": "Social media account added and RPA task created", "knowledge_id": created_knowledge.id})
    except GlobalBusinessException as e:
        return R.jsonify(CodeEnum.PARAMETER_ERROR, {"error": str(e)})
    except Exception as e:
        return R.SERVER_ERROR()


@api.delete('/api/social-media/accounts/{account_id}',
            tags=["知识库"],
            response_model=DeleteSocialMediaAccountResponse,
            summary="[v1][知识库]删除社交媒体账号",
            description="删除指定的社交媒体账号")
async def delete_social_media_account(
        account_id: int = Path(..., title="账号 ID"),
        db: Session = Depends(DatabaseManager().get_session)
):
    logger.info(f"[删除社交媒体账号][params]:  account_id={account_id}")
    try:
        result = KnowledgeService(db).delete_social_media_account(account_id)
        return R.SUCCESS(result)
    except GlobalBusinessException as e:
        logger.warning(f"删除社交媒体账号失败: {str(e)}")
        return DeleteSocialMediaAccountResponse(success=False, message=e.msg)
    except Exception as e:
        logger.error(f"删除社交媒体账号失败: {str(e)}")
        return DeleteSocialMediaAccountResponse(success=False, message="服务器错误")


@api.post('/api/personal-knowledge/search/detail', tags=["知识库"],
          response_model=ResModel[List[ResPersonalKnowledgeSchema]],
          summary="[v1][知识库]根据type和source查到整个表内容",
          description="根据source和type获取个人账号列表")
async def get_personal_knowledge(
        search: KnowledgeSearchSchema,
        db: Session = Depends(DatabaseManager().get_session)
):
    return R.SUCCESS(KnowledgeService(db).find_all(search))


@api.post('/api/social-media/accounts/update', tags=["知识库"],
          summary="[v1][知识库]更新社交媒体账号",
          description="更新指定的社交媒体账号信息")
async def update_social_media_account(
        request: UpdateSocialMediaAccountRequest,
        db: Session = Depends(DatabaseManager().get_session)
):
    try:
        knowledge_service = KnowledgeService(db)
        updated_knowledge = await knowledge_service.update_social_media_account(request)
        return R.SUCCESS(updated_knowledge)
    except GlobalBusinessException as e:
        return R.jsonify(CodeEnum.PARAMETER_ERROR, {"error": str(e)})
    except Exception as e:
        return R.SERVER_ERROR()


@api.post('/api/knowledge/spo_search', tags=["知识图谱", "查询"],
          summary="[v1][知识图谱]获取知识图谱")
async def spo_search(
        request: CommonV1ReqKnowLedgeSchema,
        redis_client: redis.Redis = Depends(RedisConnectionPool().get_pool)
):
    try:
        data_json = await redis_client.get(AgentsUserContents.AIP_KNOWLEDGE_DATA + request.redis_key)
        if data_json:
            full_data = json.loads(data_json.decode())
            if 'data' in full_data and isinstance(full_data['data'], dict):
                result = full_data['data']
                # 确保 nodes 和 links 存在
                if 'nodes' not in result or 'links' not in result:
                    return R.ID_NOT_FOUND()
                # 添加 status，如果不存在则默认为 True
                result['status'] = full_data.get('status', True)
                return R.SUCCESS(result)
            else:
                return R.ID_NOT_FOUND()
        else:
            return R.ID_NOT_FOUND()
    except ValueError:
        return R.ID_NOT_FOUND()
    except Exception:
        return R.SERVER_ERROR()


@api.post('/api/knowledgefile/upload_by_url', tags=["知识库"],
          summary="[v1][知识库]通过OSS URL上传知识库文件",
          description="通过已有的OSS URL上传文件到知识库，支持逗号分隔的多个URL")
async def upload_knowledge_file_by_url(
        knowledge_id: int = Form(..., description="知识库ID"),
        oss_url: str = Form(..., description="OSS文件URL，多个URL用逗号分隔"),
        db: Session = Depends(DatabaseManager().get_session)
):
    try:
        knowledge_service = KnowledgeService(db)
        # 分割URL字符串并去除空白
        url_list = [url.strip() for url in oss_url.split(',') if url.strip()]

        if not url_list:
            return R.jsonify(CodeEnum.PARAMETER_ERROR, {"error": "未提供有效的OSS URL"})

        results = []
        for url in url_list:
            result = await knowledge_service.upload_knowledge_file_by_url(knowledge_id, url)
            results.append(result)

        return R.SUCCESS(results)
    except GlobalBusinessException as e:
        return R.jsonify(CodeEnum.PARAMETER_ERROR, {"error": str(e)})
    except Exception as e:
        logger.error(f"通过URL上传知识库文件失败: {str(e)}")
        return R.SERVER_ERROR()
