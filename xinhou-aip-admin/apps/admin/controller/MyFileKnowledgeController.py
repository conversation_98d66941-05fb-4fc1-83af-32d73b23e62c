from fastapi import APIRouter, Depends, Path
from sqlalchemy.orm import Session
from xinhou_openai_framework.core.db.DatabaseManager import DatabaseManager
from xinhou_openai_framework.core.reponse.R import R
from xinhou_openai_framework.core.reponse.ResModel import ResModel
from xinhou_openai_framework.pages.PageHelper import PageResultHelper

from apps.admin.schema.PersonalKnowledgeSchema import ReqPersonalFileListSchema, EmbeddingFileSchema
from common.entity.IpKnowledge import IpKnowledge
from common.service.KnowledgeService import KnowledgeService
from common.service.MyFileKnowledgeService import MyFileKnowledgeService

api = APIRouter()


@api.post('/api/personal-knowledge/files', tags=["知识库"],
          response_model=ResModel[PageResultHelper[EmbeddingFileSchema]],
          summary="获取个人知识库详情内容，用于翻页与关键词查询",
          description="用于前端进行翻页与关键词查询，file_name为可选值")
async def get_personal_knowledge_files(
        req: ReqPersonalFileListSchema,
        db: Session = Depends(DatabaseManager().get_session)
):
    service = MyFileKnowledgeService(db)
    knowledge_service = KnowledgeService(db)

    knowledge = knowledge_service.find_by_id(IpKnowledge(id=req.t_ip_knowledge_id))
    if not knowledge:
        return R.NO_PARAMETER()

    file_ids = knowledge_service.get_files_for_knowledge(req.t_ip_knowledge_id)

    result = service.get_personal_files(
        file_ids,
        req.file_name,
        req.page_size,
        req.page_num
    )

    return R.SUCCESS(result)


@api.delete('/api/personal-knowledge/files/{file_id}', tags=["知识库"],
            response_model=ResModel,
            summary="删除个人知识库文件")
async def delete_personal_knowledge_file(
        file_id: int = Path(..., title="文件ID"),
        db: Session = Depends(DatabaseManager().get_session)
):
    service = MyFileKnowledgeService(db)
    knowledge_service = KnowledgeService(db)

    # 首先从所有知识库中移除这个文件ID
    knowledge_service.remove_file_from_all_knowledge(file_id)

    # 然后删除文件
    result = service.delete_personal_file(file_id)
    return R.SUCCESS(result)
