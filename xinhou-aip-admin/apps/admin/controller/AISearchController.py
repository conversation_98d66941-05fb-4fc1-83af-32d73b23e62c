# !/usr/bin/python3
# -*- coding: utf-8 -*-

"""
训练内容信息表服务类
----------------------------------------------------
@Project :   xinhou-aip-admin
@File    :   AlSearchController.py
@Contact :   <EMAIL>

@Modify Time      <AUTHOR>    @Desciption
------------      -------    --------    -----------
2024/06/05 22:04  chenlong   v1.0.0     None
"""
import json

from fastapi import APIRouter, Depends, Path
from loguru import logger
from openai import OpenAI
from sqlalchemy.orm import Session
from xinhou_openai_framework.core.cache.RedisConnectionPool import RedisConnectionPool
from xinhou_openai_framework.core.context.model.AppContext import AppContext
from xinhou_openai_framework.core.context.model.SystemContext import ctx
from xinhou_openai_framework.core.db.DatabaseManager import DatabaseManager
from xinhou_openai_framework.core.exception.CodeEnum import CodeEnum
from xinhou_openai_framework.core.reponse.R import R
from pydantic import BaseModel

from apps.admin.schema.CommonV1ReqAiSearchSchema import CommonV1ReqAiSearchSchema
from common.remote.AiSearchRemoteService import AiSearchRemoteService
from common.service.IpPromptService import IpPromptService

api = APIRouter()


class RecommendationItem(BaseModel):
    title: str
    score: int

class RecommendationResponse(BaseModel):
    recommendations: list[RecommendationItem]

def _get_ai_recommendations(search_result: dict, person_message: str) -> list:
    """
    获取AI推荐结果
    """
    context: AppContext = ctx.__getattr__("context")
    client = OpenAI(api_key=context.llm_config.one_api.api_key[0],
                    base_url=context.llm_config.one_api.base_url[0])

    try:
        hot_titles = []
        title_to_full_data = {}
        
        for item in search_result:
            hot_titles.append(item['hot_title'])
            title_to_full_data[item['hot_title']] = item

        completion = client.beta.chat.completions.parse(
            model="gpt-4o-mini",
            messages=[
                {
                    "role": "system",
                    "content": "你是一个专业的推荐系统。请根据用户信息为候选标题打分(0-100分)，分数越高表示越推荐，尽量保证多元化。"
                },
                {
                    "role": "user",
                    "content": f"以下是候选标题列表：{hot_titles}, 以下是用户相关信息：{person_message}"
                },
            ],
            response_format=RecommendationResponse,
            temperature=0.8
        )

        recommendations = completion.choices[0].message.parsed
        logger.info(f"GPT返回的推荐结果: {recommendations}")

        # 重建完整的推荐列表
        result = []
        for rec in recommendations.recommendations:
            title = rec.title
            if title in title_to_full_data:
                full_data = title_to_full_data[title].copy()
                full_data['score'] = rec.score
                result.append(full_data)

        # 按分数降序排序
        result.sort(key=lambda x: x.get('score', 0), reverse=True)
        
        logger.info(f"最终处理后的推荐结果: {result}")
        return result

    except Exception as e:
        logger.error(f"获取AI推荐失败: {str(e)}")
        raise


@api.get("/ai_search/v1/hot_list/{pid}", tags=["AI搜索", "查询", "v1"],
         summary="[v1][AI搜索]获取热门搜索项",
         description="""
获取当前热门或流行的搜索项列表。
此端点需要PID参数。
""")
async def hot_list(
        pid: int = Path(..., title="PID不能为空"),
        redis_pool: Session = Depends(RedisConnectionPool().get_pool),
        db: Session = Depends(DatabaseManager().get_session)
):
    try:
        # 检查是否存在针对该pid的缓存推荐
        cache_key = f"aip_material_data:hot_list:pid:{pid}"
        cached_recommendations = await redis_pool.get(cache_key)

        if cached_recommendations:
            # 如果存在缓存，直接返回
            return R.jsonify(CodeEnum.SUCCESS, data=json.loads(cached_recommendations))

        # 获取热搜数据
        hot_list_json = await redis_pool.get("aip_material_data:hot_list")
        search_result = json.loads(hot_list_json) if hot_list_json else AiSearchRemoteService.hot_list(json_data={})

        # 获取用户画像信息
        prompt = IpPromptService(db).get_by_pid(pid)
        if not prompt or not prompt.json:
            # 如果没有用户画像信息或画像内容为空，直接返回热搜数据
            # 缓存结果
            await redis_pool.set(cache_key, json.dumps(search_result))
            await redis_pool.expire(cache_key, 18000)  # 5小时 = 18000秒
            return R.jsonify(CodeEnum.SUCCESS, data=search_result)

        # 获取AI推荐结果
        ai_results = _get_ai_recommendations(search_result, prompt.json)

        # 缓存AI推荐结果，设置5小时过期
        await redis_pool.set(cache_key, json.dumps(ai_results))
        await redis_pool.expire(cache_key, 18000)  # 5小时 = 18000秒

        return R.jsonify(CodeEnum.SUCCESS, data=ai_results)

    except ValueError:
        return R.ID_NOT_FOUND()
    except Exception as e:
        logger.error(f"获取热门搜索项失败: {str(e)}")
        return R.SERVER_ERROR()


@api.get("/ai_search/v1/hot_list_common", tags=["AI搜索", "查询", "v1"],
         summary="[v1][AI搜索]获取通用热门搜索项",
         description="""
获取当前热门或流行的搜索项列表,不进行个性化推荐。
""")
async def hot_list_common(
        redis_pool: Session = Depends(RedisConnectionPool().get_pool)
):
    try:
        # 获取热搜数据
        hot_list_json = await redis_pool.get("aip_material_data:hot_list")
        search_result = json.loads(hot_list_json) if hot_list_json else AiSearchRemoteService.hot_list(json_data={})
        
        return R.jsonify(CodeEnum.SUCCESS, data=search_result)

    except Exception as e:
        logger.error(f"获取热门搜索项失败: {str(e)}")
        return R.SERVER_ERROR()


@api.post("/ai_search/v1/query_detail", tags=["AI搜索", "v1"], summary="[v1][AI搜索]获取搜索查询详情",
          description="""
获取特定搜索查询的详细信息,由请求体中的`task_id`参数标识。

请求体:
- `task_id`(str): 搜索查询的唯一标识符。
""")
async def query_detail(
        request: CommonV1ReqAiSearchSchema
):
    try:
        result = AiSearchRemoteService.query_detail(json_data={"task_id": request.task_id})
        return R.SUCCESS(result)
    except ValueError:
        return R.ID_NOT_FOUND()
    except Exception:
        return R.SERVER_ERROR()
