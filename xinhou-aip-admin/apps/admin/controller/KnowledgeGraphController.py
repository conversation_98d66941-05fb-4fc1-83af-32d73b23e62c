from fastapi import APIRouter, Depends, Path
from sqlalchemy.orm import Session
from xinhou_openai_framework.core.db.DatabaseManager import DatabaseManager
from xinhou_openai_framework.core.reponse.R import R

from common.entity.Ip import Ip
from common.service.IpService import IpService
from common.service.KnowledgeGraphService import GraphKnowledgeService
from aioredis import Redis
from fastapi import APIRouter,Depends, HTTPException
from loguru import logger
from sqlalchemy.orm import Session
from xinhou_openai_framework.core.cache.RedisConnectionPool import RedisConnectionPool
from xinhou_openai_framework.core.db.DatabaseManager import DatabaseManager
from xinhou_openai_framework.core.reponse.R import R

api = APIRouter()
graph_service = GraphKnowledgeService()


@api.get(
    "/api/personal-knowledge-graph/{pid}",
    tags=["用户知识图谱"],
    summary="[v1][知识库]根据pid查看用户的图谱是否建立",
    description="根据pid判断图谱下标是否生成",
)
async def get_personal_graph_knowledge(
        pid: int = Path(title="PID不能为空"),
        db: Session = Depends(DatabaseManager().get_session)
):
    # ip_name = ""
    # ip = IpService(db).find_by_id(Ip(id=pid))
    # if ip:
    #     ip_name = ip.ip_name
    knowledge_index = f"__Entity__{pid}"
    is_knowledge_graph = graph_service.is_create_graph_knowledge(knowledge_index)
    return R.SUCCESS({"knowledge_graph": is_knowledge_graph})


@api.get('/api/personal-graph-data/{task_id}', tags=["用户知识图谱"],
          summary="获取图谱数据",
          description="根据task_id和uuid去获取数据")
async def get_kg_klonwledge_info(
        task_id,
        redis_client: Redis = Depends(RedisConnectionPool().get_pool)
):
    """
    控制音频速度
    :param task_id 和uuid
    :param redis_client: Redis客户端
    :return: 图谱数据
    """
    logger.info(f"[获取到请求参数]: {task_id}")
    try:
        result_data = await graph_service.get_redis_kg_data(redis_client=redis_client,task_id=task_id)
        return R.SUCCESS(data=result_data)
    except Exception as e:
        logger.error(f"获取redis图谱数据错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取redis图谱数据错误: {str(e)}")
    
