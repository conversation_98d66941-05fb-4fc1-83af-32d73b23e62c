from fastapi import APIRouter, Depends
from fastapi.params import Path
from loguru import logger
from sqlalchemy.orm import Session
from xinhou_openai_framework.core.context.model.AppContext import AppContext
from xinhou_openai_framework.core.context.model.SystemContext import ctx
from xinhou_openai_framework.core.db.DatabaseManager import DatabaseManager
from xinhou_openai_framework.core.exception.GlobalExceptionType import ParameterException
from xinhou_openai_framework.core.reponse.R import R
from xinhou_openai_framework.core.reponse.ResModel import ResModel
from xinhou_openai_framework.pages.PageHelper import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, PageHelper

from apps.admin.controller.UserController import check_current_user
from apps.admin.schema.VirtualHumanSchema import (
    VirtualHumanDetailSchema, VirtualHumanFindSchema,
    VirtualHumanSaveSchema, VirtualHumanUpdateSchema
)
from common.entity.Ip import Ip
from common.entity.User import User
from common.entity.VirtualHuman import VirtualHuman
from common.service import WxNotificationService
from common.service.IpService import IpService
from common.service.VirtualHumanService import VirtualHumanService

api = APIRouter()


@api.post('/admin/virtual-human/findAll', tags=["超虚拟人模型制作", "v1"],
          response_model=ResModel[list[VirtualHumanDetailSchema]],
          summary="查询所有超虚拟人模型制作信息接口")
async def find_all(search: VirtualHumanFindSchema,
                   current_user: User = Depends(check_current_user),
                   db: Session = Depends(DatabaseManager().get_session)):
    logger.info("[findAll][request]:{}".format(search.model_dump()))
    return R.SUCCESS(VirtualHumanService(db).find_all(search))


@api.post('/admin/virtual-human/find', tags=["超虚拟人模型制作", "v1"],
          response_model=ResModel[PageResultHelper[VirtualHumanDetailSchema]],
          summary="查询超虚拟人模型制作信息带分页接口")
async def find(search: PageHelper[VirtualHumanFindSchema],
               current_user: User = Depends(check_current_user),
               db: Session = Depends(DatabaseManager().get_session)):
    logger.info("[find][request]:{}".format(search.model_dump()))
    return R.SUCCESS(VirtualHumanService(db).find_by(search))


@api.post('/admin/virtual-human/save', tags=["超虚拟人模型制作", "v1"],
          response_model=ResModel[VirtualHumanDetailSchema],
          summary="保存超虚拟人模型制作信息接口")
async def save(model: VirtualHumanSaveSchema,
               current_user: User = Depends(check_current_user),
               db: Session = Depends(DatabaseManager().get_session)):
    context: AppContext = ctx.__getattr__("context")
    logger.info("[save][request]:{}".format(model.model_dump()))
    result = VirtualHumanService(db).save(VirtualHuman(**model.model_dump()))
    ip = IpService(db).find_by_id(Ip(id=model.pid))
    message = f"[撒花]{current_user.phone}\n（{ip.ip_name}）于{result.created_at.isoformat()}，提交【超虚拟人模型制作】~\n专属模型类型：{result.model_type}；口播风格：{result.speech_style}；访谈风格：{result.interview_style}；模型时长：{result.duration}；形象要求备注：{result.appearance_note}；提交状态：{'已提交' if result.submit_status == 1 else '已上传'}~";
    await WxNotificationService.sendImages(message, context)
    return R.SUCCESS(result)


@api.post('/admin/virtual-human/update', tags=["超虚拟人模型制作", "v1"],
          response_model=ResModel[VirtualHumanDetailSchema],
          summary="更新超虚拟人模型制作信息接口")
async def update(model: VirtualHumanUpdateSchema,
                 current_user: User = Depends(check_current_user),
                 db: Session = Depends(DatabaseManager().get_session)):
    logger.info("[update][request]:{}".format(model.model_dump()))
    if not model.id:
        raise ParameterException()
    return R.SUCCESS(VirtualHumanService(db).update_by_id(model.id, model.model_dump(exclude={'id'})))


@api.get('/admin/virtual-human/delete/{id}', tags=["超虚拟人模型制作", "v1"],
         response_model=ResModel,
         summary="删除超虚拟人模型制作信息接口")
async def delete(id: int = Path(..., title="ID"),
                 current_user: User = Depends(check_current_user),
                 db: Session = Depends(DatabaseManager().get_session)):
    logger.info("[delete][request]:{}".format({"id": id}))
    if not id:
        raise ParameterException()
    VirtualHumanService(db).delete(VirtualHuman(id=id))
    return R.SUCCESS()


@api.get('/admin/virtual-human/detail/{id}', tags=["超虚拟人模型制作", "v1"],
         response_model=ResModel[VirtualHumanDetailSchema],
         summary="获取超虚拟人模型制作详情接口")
async def detail(id: int = Path(..., title="ID"),
                 current_user: User = Depends(check_current_user),
                 db: Session = Depends(DatabaseManager().get_session)):
    logger.info("[detail][request]:{}".format({"id": id}))
    if not id:
        raise ParameterException()
    return R.SUCCESS(VirtualHumanService(db).find_by_id(VirtualHuman(id=id)))
