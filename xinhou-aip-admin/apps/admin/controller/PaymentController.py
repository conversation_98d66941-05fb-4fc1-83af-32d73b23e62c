# !/usr/bin/python3
# -*- coding: utf-8 -*-

"""
支付控制器
----------------------------------------------------
@Project :   xinhou-openai-admin
@File    :   PaymentController.py
@Contact :   <EMAIL>

@Modify Time      <AUTHOR>    @Desciption
------------      -------    --------    -----------
2024/09/18 10:00  ChatGPT    v1.0.0      None
"""

import json
import re
import urllib.parse
import urllib.parse

from fastapi import APIRouter, Depends, Path
from fastapi import Query
from loguru import logger
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.orm import Session
from xinhou_openai_framework.core.db.DatabaseManager import DatabaseManager
from xinhou_openai_framework.core.exception.CodeEnum import CodeEnum
from xinhou_openai_framework.core.reponse.R import R
from xinhou_openai_framework.core.reponse.ResModel import ResModel
from xinhou_openai_framework.pages.PageHelper import PageResultHelper, PageHelper

from apps.admin.controller.UserController import check_current_user
from apps.admin.schema.PaymentSchema import (
    ReqPaymentInitSchema, ResPaymentResultSchema, ReqPaymentRefundSchema, ReqPaymentInitSchemaV2, ReqPaymentFindSchema,
    ResPaymentDetailSchema
)
from common.entity.User import User
from common.service.PaymentService import PaymentService

api = APIRouter()


@api.post("/admin/payment/init", tags=["支付", "v1"],
          response_model=ResModel[ResPaymentResultSchema],
          summary="[v1][payment][init]初始化支付接口",
          description="创建支付订单并返回支付URL")
async def init_payment(req: ReqPaymentInitSchema,
                       current_user: User = Depends(check_current_user),
                       db: Session = Depends(DatabaseManager().get_session)):
    try:
        payment_service = PaymentService(db)
        # 从请求参数中解析product_id
        extra_param = json.loads(req.param)
        product_id = extra_param.get('product_id')

        # 调用service方法时传入pay_type
        result = payment_service.init_payment(
            user_id=current_user.id,
            product_id=product_id,
            pay_type=req.pay_type,  # 传入支付方式
            client_ip=req.client_ip,
            device=req.device
        )
        return R.SUCCESS(result)
    except SQLAlchemyError as e:
        logger.error(f"Database error: {str(e)}")
        return R.SAVE_ERROR()
    except Exception as e:
        logger.error(f"Unexpected error: {str(e)}")
        return R.jsonify(CodeEnum.ID_NOT_FOUND, {"error": str(e)})


@api.get("/admin/payment/query/{out_trade_no}", tags=["支付", "v1"],
         response_model=ResModel[ResPaymentResultSchema],
         summary="[v1][payment][query]查询支付订单接口",
         description="根据商户订单号查询支付订单状态")
async def query_payment(out_trade_no: str = Path(title="商户订单号不能为空"),
                        current_user: User = Depends(check_current_user),
                        db: Session = Depends(DatabaseManager().get_session)):
    try:
        payment_service = PaymentService(db)
        result = await payment_service.query_payment(out_trade_no)
        return R.SUCCESS(result)
    except SQLAlchemyError as e:
        logger.error(f"Database error: {str(e)}")
        return R.QUERY_ERROR()
    except Exception as e:
        logger.error(f"Unexpected error: {str(e)}")
        return R.SERVER_ERROR()


@api.post("/admin/payment/refund", tags=["支付", "v1"],
          response_model=ResModel,
          summary="[v1][payment][refund]退款接口",
          description="对指定订单发起退款请求")
async def refund_payment(req: ReqPaymentRefundSchema,
                         current_user: User = Depends(check_current_user),
                         db: Session = Depends(DatabaseManager().get_session)):
    try:
        payment_service = PaymentService(db)
        result = await payment_service.refund_payment(req)
        return R.SUCCESS(result)
    except SQLAlchemyError as e:
        logger.error(f"Database error: {str(e)}")
        return R.UPDATE_ERROR()
    except Exception as e:
        logger.error(f"Unexpected error: {str(e)}")
        return R.SERVER_ERROR()


@api.get("/admin/payment/notify", tags=["支付", "v1"],
         summary="[v1][payment][notify]支付结果通知接口",
         description="接收支付平台的异步通知")
async def payment_notify(
        pid: str = Query(..., description="支付ID"),
        trade_no: str = Query(..., description="交易号"),
        out_trade_no: str = Query(..., description="商户订单号"),
        type: str = Query(..., description="支付类型"),
        name: str = Query(..., description="商品名称"),
        money: str = Query(..., description="支付金额"),
        trade_status: str = Query(..., description="交易状态"),
        param: str = Query(None, description="附加参数"),
        sign: str = Query(..., description="签名"),
        sign_type: str = Query(..., description="签名类型"),
        db: Session = Depends(DatabaseManager().get_session)
        , param_dict=None):
    try:
        # 记录传入的请求参数
        logger.info("[payment_notify][请求参数]: %s", {
            "pid": pid,
            "trade_no": trade_no,
            "out_trade_no": out_trade_no,
            "type": type,
            "name": name,
            "money": money,
            "trade_status": trade_status,
            "param": param,
            "sign": sign,
            "sign_type": sign_type
        })
        if param is not None:
            # URL解码并解析param JSON字符串
            try:
                # URL解码
                decoded_param = urllib.parse.unquote(param)
                # 移除转义的反斜杠
                cleaned_param = re.sub(r'\\(?=")', '', decoded_param)
                # 解析JSON
                param_dict = json.loads(cleaned_param)
            except json.JSONDecodeError as e:
                logger.error(f"解析param JSON失败: {param}. 错误: {str(e)}")
                return {"error": "无效的param格式"}

        # 构造请求字典
        req = {
            "pid": pid,
            "trade_no": trade_no,
            "out_trade_no": out_trade_no,
            "type": type,
            "name": name,
            "money": money,
            "trade_status": trade_status,
            "param": param_dict,
            "sign": sign,
            "sign_type": sign_type
        }

        # 处理支付通知
        payment_service = PaymentService(db)
        result = await payment_service.handle_payment_notify(req)
        return "success" if result else {"error": result}
    except Exception as e:
        logger.error(f"支付通知处理过程中发生意外错误: {str(e)}")
        return {"error": "服务器内部错误"}


@api.post("/admin/v2/payment/init", tags=["支付", "v2"],
          response_model=ResModel[ResPaymentResultSchema],
          summary="[v2][payment][init]初始化支付接口",
          description="创建支付订单并返回支付URL")
async def init_payment_v2(req: ReqPaymentInitSchemaV2,
                          current_user: User = Depends(check_current_user),
                          db: Session = Depends(DatabaseManager().get_session)):
    try:
        payment_service = PaymentService(db)
        # 调用service方法时传入pay_type
        result = payment_service.init_payment_v2(
            user_id=current_user.id,
            product_id=req.product_id,
            pay_type=req.pay_type,  # 传入支付方式
            client_ip=req.client_ip,
            device=req.device,
            param=req.param
        )
        return R.SUCCESS(result)
    except SQLAlchemyError as e:
        logger.error(f"Database error: {str(e)}")
        return R.SAVE_ERROR()
    except Exception as e:
        logger.error(f"Unexpected error: {str(e)}")
        return R.jsonify(CodeEnum.ID_NOT_FOUND, {"error": str(e)})


@api.get("/admin/v2/payment/notify", tags=["支付", "v2"],
         summary="[v2][payment][notify]支付结果通知接口",
         description="接收支付平台的异步通知")
async def payment_notify(
        pid: str = Query(..., description="支付ID"),
        trade_no: str = Query(..., description="交易号"),
        out_trade_no: str = Query(..., description="商户订单号"),
        type: str = Query(..., description="支付类型"),
        name: str = Query(..., description="商品名称"),
        money: str = Query(..., description="支付金额"),
        trade_status: str = Query(..., description="交易状态"),
        param: str = Query(None, description="附加参数"),
        sign: str = Query(..., description="签名"),
        sign_type: str = Query(..., description="签名类型"),
        db: Session = Depends(DatabaseManager().get_session)
        , param_dict=None):
    try:
        # 记录传入的请求参数
        logger.info("[payment_notify][请求参数]: %s", {
            "pid": pid,
            "trade_no": trade_no,
            "out_trade_no": out_trade_no,
            "type": type,
            "name": name,
            "money": money,
            "trade_status": trade_status,
            "param": param,
            "sign": sign,
            "sign_type": sign_type
        })
        if param is not None:
            # URL解码并解析param JSON字符串
            try:
                # URL解码
                decoded_param = urllib.parse.unquote(param)
                # 移除转义的反斜杠
                cleaned_param = re.sub(r'\\(?=")', '', decoded_param)
                # 解析JSON
                param_dict = json.loads(cleaned_param)
            except json.JSONDecodeError as e:
                logger.error(f"解析param JSON失败: {param}. 错误: {str(e)}")
                return {"error": "无效的param格式"}

        # 构造请求字典
        req = {
            "pid": pid,
            "trade_no": trade_no,
            "out_trade_no": out_trade_no,
            "type": type,
            "name": name,
            "money": money,
            "trade_status": trade_status,
            "param": param_dict,
            "sign": sign,
            "sign_type": sign_type
        }

        # 处理支付通知
        payment_service = PaymentService(db)
        result = await payment_service.handle_payment_notify_v2(req)
        return "success" if result else {"error": result}
    except Exception as e:
        logger.error(f"支付通知处理过程中发生意外错误: {str(e)}")
        return {"error": "服务器内部错误"}


@api.post('/admin/payment/order/find', tags=["支付", "查询", "v1"],
          response_model=ResModel[PageResultHelper[ResPaymentDetailSchema]],
          summary="[v1][payment][order][find]分页查询支付订单表",
          description="通过参数模型传递条件分页查询支付订单")
async def find_payment_order(search: PageHelper[ReqPaymentFindSchema],
                             db: Session = Depends(DatabaseManager().get_session)):
    logger.info("[find][request]:{}".format(search.model_dump()))
    return R.SUCCESS(PaymentService(db).find_by(search))
