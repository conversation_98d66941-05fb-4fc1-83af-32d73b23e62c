# !/usr/bin/python3
# -*- coding: utf-8 -*-

"""
数字人多媒体表控制器类
----------------------------------------------------
@Project :   xinhou-openai-admin
@File    :   MediaModelController.py
@Contact :   <EMAIL>

@Modify Time      <AUTHOR>    @Desciption
------------      -------    --------    -----------
2023/08/15 10:00  peng.shen   v1.0.0     None
"""

from fastapi import APIRouter, Depends, File, Form, UploadFile
from fastapi.params import Path
from loguru import logger
from sqlalchemy.orm import Session
from xinhou_openai_framework.core.db.DatabaseManager import DatabaseManager
from xinhou_openai_framework.core.exception.GlobalExceptionType import ParameterException
from xinhou_openai_framework.core.reponse.R import R
from xinhou_openai_framework.core.reponse.ResModel import ResModel
from xinhou_openai_framework.pages.PageHelper import <PERSON>Result<PERSON>elper, PageHelper

from apps.admin.controller.UserController import check_current_user
from apps.admin.schema.MediaModelSchema import ResMediaModelDetailSchema, ReqMediaModelFindSchema, \
    ReqMediaModelSaveSchema, ReqMediaModelUpdateSchema
from common.entity.MediaModel import MediaModel
from common.entity.User import User
from common.service.MediaModelService import MediaModelService

api = APIRouter()


@api.post('/admin/media/model/find', tags=["视频模型", "v1"],
          response_model=ResModel[PageResultHelper[ResMediaModelDetailSchema]],
          summary="[v1]查询数字人多媒体信息表信息带分页接口",
          description="通过参数模型传递条件查询")
async def find(search: PageHelper[ReqMediaModelFindSchema],
               current_user: User = Depends(check_current_user),
               db: Session = Depends(DatabaseManager().get_session)):
    logger.info("[find][request]:{}".format(search.model_dump()))
    return R.SUCCESS(MediaModelService(db).find_by(search))


@api.post('/admin/media/model/findAll', tags=["视频模型", "v1"],
          response_model=ResModel[PageResultHelper[ResMediaModelDetailSchema]],
          summary="[v1]查询数字人多媒体信息表信息接口",
          description="通过参数模型传递条件查询")
async def find_all(search: ReqMediaModelFindSchema,
                   db: Session = Depends(DatabaseManager().get_session)):
    logger.info("[find][request]:{}".format(search.model_dump()))
    return R.SUCCESS(MediaModelService(db).find_all(search))


@api.post("/admin/media/model/save", tags=["视频模型", "v1"],
          response_model=ResModel[ResMediaModelDetailSchema],
          summary="[v1]保存数字人多媒体信息表信息接口",
          description="通过参数模型保存数据")
async def save(model: ReqMediaModelSaveSchema,
               current_user: User = Depends(check_current_user),
               db: Session = Depends(DatabaseManager().get_session)):
    logger.info("[save][request]:{}".format(model.model_dump_json()))
    return R.SUCCESS(MediaModelService(db).save(MediaModel(**model.model_dump(exclude_unset=True))))


@api.get("/admin/media/model/delete/{id}", tags=["视频模型", "v1"],
         response_model=ResModel,
         summary="[v1]删除数字人多媒体信息表信息接口",
         description="根据ID删除数据")
async def delete(id: int = Path(title="ID不能为空"),
                 current_user: User = Depends(check_current_user),
                 db: Session = Depends(DatabaseManager().get_session)):
    logger.info("[detail][request]:{}".format({"id": id}))
    if id is None:
        raise ParameterException()
    MediaModelService(db).delete(MediaModel(id=id))
    return R.SUCCESS()


@api.post("/admin/media/model/update", tags=["视频模型", "v1"],
          response_model=ResModel[ResMediaModelDetailSchema],
          summary="[v1]更新数字人多媒体信息表信息接口",
          description="根据ID更新数据")
async def update(model: ReqMediaModelUpdateSchema,
                 current_user: User = Depends(check_current_user),
                 db: Session = Depends(DatabaseManager().get_session)):
    logger.info("[save][request]:{}".format(model.model_dump_json()))
    if model.id is None:
        raise ParameterException()
    return R.SUCCESS(MediaModelService(db).update(MediaModel(**model.model_dump(exclude_unset=True))))


@api.get("/admin/media/model/detail/{id}", tags=["视频模型", "v1"],
         response_model=ResModel[ResMediaModelDetailSchema],
         summary="[v1]获取数字人多媒体信息表详情接口",
         description="根据ID获取数据")
async def detail(id: int = Path(title="ID不能为空"),
                 current_user: User = Depends(check_current_user),
                 db: Session = Depends(DatabaseManager().get_session)):
    logger.info("[detail][request]:{}".format({"id": id}))
    if id is None:
        raise ParameterException()
    return R.SUCCESS(MediaModelService(db).find_by_id(MediaModel(id=id)))

@api.post("/admin/media/model/upload", tags=["视频模型", "v2"],
          response_model=ResModel[ResMediaModelDetailSchema],
          summary="[v2]上传视频形象模型文件",
          description="上传视频形象模型文件")
async def upload_media(
    title: str = Form(...),
    pid: int = Form(...),
    file: UploadFile = File(...),
    current_user: User = Depends(check_current_user),
    db: Session = Depends(DatabaseManager().get_session)
):
    try:
        result = await MediaModelService(db).upload_media(title, pid, file)
        return R.SUCCESS(result)
    except ValueError as e:
        logger.error(f"上传失败: {str(e)}")
        return R.PARAMETER_ERR(str(e))
    except Exception as e:
        logger.error(f"上传失败: {str(e)}")
        return R.SERVER_ERROR()

@api.post("/admin/media/model/transcode/{id}", tags=["视频模型", "v2"],
          response_model=ResModel[ResMediaModelDetailSchema],
          summary="[v2]转码视频形象模型文件",
          description="对指定ID的媒体文件进行转码")
async def transcode_media(
    id: int = Path(..., description="媒体模型ID"),
    current_user: User = Depends(check_current_user),
    db: Session = Depends(DatabaseManager().get_session)
):
    try:
        result = await MediaModelService(db).transcode_media(id)
        return R.SUCCESS(result)
    except ValueError as e:
        # 参数错误，比如找不到媒体记录或URL为空
        logger.error(f"转码失败: {str(e)}")
        return R.PARAMETER_ERR(str(e))
    except Exception as e:
        # 其他系统错误
        logger.error(f"转码失败: {str(e)}")
        return R.SERVER_ERROR()

@api.post("/admin/media/model/upload_by_url", tags=["视频模型", "v2"],
          response_model=ResModel[ResMediaModelDetailSchema],
          summary="[v2]通过OSS URL上传视频形象模型文件",
          description="通过已有的OSS URL上传视频形象模型文件，支持逗号分隔的多个URL")
async def upload_media_by_url(
    title: str = Form(...),
    pid: int = Form(...),
    oss_url: str = Form(..., description="OSS文件URL，多个URL用逗号分隔"),
    current_user: User = Depends(check_current_user),
    db: Session = Depends(DatabaseManager().get_session)
):
    try:
        # 分割URL字符串并去除空白
        url_list = [url.strip() for url in oss_url.split(',') if url.strip()]
        
        if not url_list:
            return R.PARAMETER_ERR("未提供有效的OSS URL")
        
        results = []
        media_model_service = MediaModelService(db)
        
        for i, url in enumerate(url_list):
            # 如果有多个URL，为每个URL添加序号后缀
            current_title = title if len(url_list) == 1 else f"{title}_{i+1}"
            result = await media_model_service.upload_media_by_url(current_title, pid, url)
            results.append(result)
        
        # 返回结果列表或第一个结果
        return R.SUCCESS(results[0] if len(results) == 1 else results)
    except ValueError as e:
        logger.error(f"通过URL上传失败: {str(e)}")
        return R.PARAMETER_ERR(str(e))
    except Exception as e:
        logger.error(f"通过URL上传失败: {str(e)}")
        return R.SERVER_ERROR()