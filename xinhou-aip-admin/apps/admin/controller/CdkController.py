# apps/admin/controller/CdkController.py
import random
import string
from datetime import timed<PERSON><PERSON>

from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from xinhou_openai_framework.core.cache.RedisConnectionPool import RedisConnectionPool
from xinhou_openai_framework.core.db.DatabaseManager import DatabaseManager
from xinhou_openai_framework.core.reponse.R import R
from xinhou_openai_framework.utils.Md5Util import Md5Util

from apps.admin.schema.CdkSchema import *
from common.entity.Cdk import Cdk, CdkUsageLog
from common.entity.Ip import Ip
from common.entity.IpPrompt import IpPrompt
from common.entity.User import User
from common.service.CdkService import CdkService
from common.service.UserService import UserService

api = APIRouter()


@api.post("/admin/cdk/create", tags=["CDK管理"],
          response_model=ResCdkSchema,
          summary="创建CDK",
          description="创建新的CDK")
async def create_cdk(
        req: ReqCdkCreateSchema,
        db: Session = Depends(DatabaseManager().get_session)
):
    cdk = Cdk(**req.dict())
    result = CdkService(db).create(cdk)
    return R.SUCCESS(result)


@api.post("/admin/cdk/update", tags=["CDK管理"],
          response_model=ResCdkSchema,
          summary="更新CDK",
          description="更新已存在的CDK")
async def update_cdk(
        req: ReqCdkUpdateSchema,
        db: Session = Depends(DatabaseManager().get_session)
):
    if not req.id or req.id <= 0:
        raise HTTPException(status_code=400, detail="无效的CDK ID")

    update_data = {k: v for k, v in req.dict().items() if v is not None}
    if len(update_data) <= 1:
        raise HTTPException(status_code=400, detail="没有需要更新的字段")

    cdk = Cdk(**update_data)
    result = CdkService(db).update(cdk)
    return R.SUCCESS(result)


@api.get("/admin/cdk/get/{id}", tags=["CDK管理"],
         response_model=ResCdkSchema,
         summary="获取CDK详情",
         description="根据ID获取CDK详细信息")
async def get_cdk(
        id: int,
        db: Session = Depends(DatabaseManager().get_session)
):
    if not id or id <= 0:
        raise HTTPException(status_code=400, detail="无效的CDK ID")

    result = CdkService(db).get_by_id(id)
    if not result:
        raise HTTPException(status_code=404, detail="CDK不存在")
    return R.SUCCESS(result)


@api.get("/admin/cdk/list", tags=["CDK管理"],
         response_model=List[ResCdkSchema],
         summary="获取CDK列表",
         description="获取所有CDK列表")
async def list_cdks(
        status: Optional[int] = None,
        db: Session = Depends(DatabaseManager().get_session)
):
    result = CdkService(db).list_all(status)
    return R.SUCCESS(result)


@api.post("/admin/cdk/delete", tags=["CDK管理"],
          summary="删除CDK",
          description="软删除指定的CDK")
async def delete_cdk(
        req: ReqCdkDeleteSchema,
        db: Session = Depends(DatabaseManager().get_session)
):
    if not req.id or req.id <= 0:
        raise HTTPException(status_code=400, detail="无效的CDK ID")

    result = CdkService(db).delete(req.id)
    return R.SUCCESS(result)


@api.post("/admin/cdk/activate", tags=["CDK管理"],
          response_model=ResCdkActivateSchema,
          summary="激活CDK",
          description="使用CDK激活账号并创建IP")
async def activate_cdk(
        req: ReqCdkActivateSchema,
        db: Session = Depends(DatabaseManager().get_session),
        redis_pool: Session = Depends(RedisConnectionPool().get_pool)
):
    try:
        # 1. 验证手机验证码
        user_service = UserService(db)
        verify_key = f"verify_code:{req.phone}"

        stored_code = await redis_pool.get(verify_key)

        if not stored_code:
            raise HTTPException(status_code=400, detail="验证码已过期")

        if stored_code.decode() != req.verify_code:
            raise HTTPException(status_code=400, detail="验证码错误")

        # 验证码使用后立即删除
        await redis_pool.delete(verify_key)

        # 2. 验证CDK
        cdk_service = CdkService(db)
        cdk = cdk_service.get_by_key(req.cdk_key)

        if not cdk:
            raise HTTPException(status_code=404, detail="CDK不存在")

        if cdk.status != 1:
            raise HTTPException(status_code=400, detail="CDK状态无效")

        if cdk.used_count >= cdk.total_uses:
            cdk.status = 2
            db.commit()
            raise HTTPException(status_code=400, detail="CDK已达到使用次数上限")

        # 3. 获取或创建用户
        user = db.query(User).filter(
            User.phone == req.phone,
            User.del_flag == 1
        ).first()

        if not user:
            # 如果用户不存在，创建新用户
            user = User(
                phone=req.phone,
                login_name=req.phone,
                user_name=req.phone,
                login_pwd=Md5Util.md5_string(req.password),
                status=1,
                del_flag=1,
                create_by='激活码'
            )
            db.add(user)
            db.flush()  # 获取用户ID

        # 4. 创建IP
        current_time = datetime.now()
        new_expire_time = current_time + timedelta(days=cdk.used_time)

        ip = Ip(
            uid=user.id,
            ip_name=req.ip_name,
            avatar=req.avatar,
            status=1,
            del_flag=1,
            remain_point=cdk.point,
            expire_time=new_expire_time,
            create_by='激活码'
        )
        db.add(ip)
        db.flush()  # 获取IP ID

        # 5. 创建IP提示词记录
        ip_prompt = IpPrompt(
            ip_name=req.ip_name,
            pid=ip.id,
            create_by='激活码'
        )
        db.add(ip_prompt)

        # 6. 创建CDK使用记录
        usage_log = CdkUsageLog(
            cdk_id=cdk.id,
            cdk_key=cdk.cdk_key,
            uid=user.id,
            pid=ip.id,
            used_point=cdk.point,
            status=1,
            create_by='激活码'
        )
        db.add(usage_log)

        # 7. 更新CDK状态
        cdk.used_count += 1
        if cdk.used_count >= cdk.total_uses:
            cdk.status = 2

        # 提交事务
        db.commit()

        return R.SUCCESS({
            "phone": user.phone,
            "password": req.password if not user else "复用已有账号，密码未更改",
            "uid": user.id,
            "pid": ip.id
        })

    except HTTPException as e:
        db.rollback()
        raise e
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"激活失败: {str(e)}")


@api.post("/admin/cdk/batch-create", tags=["CDK管理"],
          response_model=ResCdkBatchCreateSchema,
          summary="批量创建CDK",
          description="批量生成指定数量的CDK")
async def batch_create_cdk(
        req: ReqCdkBatchCreateSchema,
        db: Session = Depends(DatabaseManager().get_session)
):
    try:
        cdk_list = []
        created_keys = set()

        # 生成CDK的函数
        def generate_cdk_key(length: int = 16) -> str:
            # 生成包含大写字母和数字的CDK
            chars = string.ascii_uppercase + string.digits
            while True:
                cdk_key = ''.join(random.choices(chars, k=length))
                if cdk_key not in created_keys:
                    created_keys.add(cdk_key)
                    return cdk_key

        # 检查数据库中已存在的CDK
        existing_cdks = db.query(Cdk.cdk_key).filter(Cdk.del_flag == 1).all()
        existing_keys = {cdk[0] for cdk in existing_cdks}
        created_keys.update(existing_keys)

        current_time = datetime.now()

        # 批量创建CDK
        for _ in range(req.count):
            cdk_key = generate_cdk_key()
            cdk = Cdk(
                cdk_key=cdk_key,
                point=req.point,
                used_time=req.used_time,
                total_uses=req.total_uses,
                used_count=0,
                status=1,
                del_flag=1,
                create_by='批量生成',
                remark=req.remark
            )
            db.add(cdk)
            cdk_list.append(cdk_key)

        # 提交事务
        db.commit()

        return R.SUCCESS({
            "total": len(cdk_list),
            "cdk_list": cdk_list
        })

    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"批量生成CDK失败: {str(e)}")
