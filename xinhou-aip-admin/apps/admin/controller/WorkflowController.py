from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from xinhou_openai_framework.core.db.DatabaseManager import DatabaseManager
from xinhou_openai_framework.core.reponse.R import R

from apps.admin.schema.WorkflowSchema import *
from common.entity.Workflow import Workflow
from common.service.WorkflowService import WorkflowService

api = APIRouter()


@api.post("/admin/workflow/create", tags=["工作流管理"],
          response_model=ResWorkflowSchema,
          summary="创建工作流",
          description="创建新的工作流及其关联的Agent")
async def create_workflow(
        req: ReqWorkflowCreateSchema,
        db: Session = Depends(DatabaseManager().get_session)
):
    if not req.workflow_name or not req.workflow_code:
        raise HTTPException(status_code=400, detail="必填字段不能为空")
    
    if not req.agent_ids:
        raise HTTPException(status_code=400, detail="必须指定关联的Agent")
        
    workflow = Workflow(**{k: v for k, v in req.dict().items() if k != 'agent_ids'})
    result = WorkflowService(db).create_workflow(workflow, req.agent_ids)
    return R.SUCCESS(result)


@api.post("/admin/workflow/update", tags=["工作流管理"],
          response_model=ResWorkflowSchema,
          summary="更新工作流",
          description="更新工作流及其关联的Agent")
async def update_workflow(
        req: ReqWorkflowUpdateSchema,
        db: Session = Depends(DatabaseManager().get_session)
):
    if not req.id or req.id <= 0:
        raise HTTPException(status_code=400, detail="无效的工作流ID")

    update_data = {
        k: v for k, v in req.dict().items()
        if v is not None or k in ['agent_ids']  # agent_ids允许为空列表
    }
    
    if len(update_data) <= 1:  # 只有id字段
        raise HTTPException(status_code=400, detail="没有需要更新的字段")
        
    workflow = Workflow(**{k: v for k, v in update_data.items() if k != 'agent_ids'})
    result = WorkflowService(db).update_workflow(workflow, update_data.get('agent_ids'))
    return R.SUCCESS(result)


@api.post("/admin/workflow/delete", tags=["工作流管理"],
          summary="删除工作流",
          description="软删除指定的工作流及其关联的Agent")
async def delete_workflow(
        req: ReqWorkflowDeleteSchema,
        db: Session = Depends(DatabaseManager().get_session)
):
    if not req.id or req.id <= 0:
        raise HTTPException(status_code=400, detail="无效的工作流ID")
        
    result = WorkflowService(db).delete_workflow(req.id)
    return R.SUCCESS(result)


@api.get("/admin/workflow/list", 
         tags=["工作流管理", "查询"],
         response_model=List[ResWorkflowSchema],
         summary="获取工作流列表",
         description="获取工作流列表，如果指定pid则获取该用户的工作流，否则获取所有工作流")
async def list_workflows(
        pid: Optional[int] = None,
        status: Optional[int] = None,
        db: Session = Depends(DatabaseManager().get_session)
):
    # 如果提供了pid，验证其有效性
    if pid is not None and pid <= 0:
        raise HTTPException(status_code=400, detail="无效的用户ID")
        
    if status is not None and status not in [1, 2]:
        raise HTTPException(status_code=400, detail="无效的状态值")
        
    result = WorkflowService(db).list_by_pid(pid, status)
    return R.SUCCESS(result)


@api.get("/admin/workflow/agents/{workflow_id}", 
         tags=["工作流管理", "查询"],
         summary="获取工作流关联的Agent列表",
         description="获取指定工作流关联的所有Agent及其执行顺序")
async def get_workflow_agents(
        workflow_id: int,
        db: Session = Depends(DatabaseManager().get_session)
):
    if not workflow_id or workflow_id <= 0:
        raise HTTPException(status_code=400, detail="无效的工作流ID")
        
    result = WorkflowService(db).get_workflow_agents(workflow_id)
    return R.SUCCESS(result)
