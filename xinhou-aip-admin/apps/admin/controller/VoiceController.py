# !/usr/bin/python3
# -*- coding: utf-8 -*-

"""
声音信息表控制器类
----------------------------------------------------
@Project :   xinhou-openai-admin
@File    :   VoiceModel.py
@Contact :   <EMAIL>

@Modify Time      <AUTHOR>    @Desciption
------------      -------    --------    -----------
2023/08/15 10:00  peng.shen   v1.0.0     初始版本
"""

from fastapi import APIRouter, Depends
from loguru import logger
from sqlalchemy.orm import Session
from xinhou_openai_framework.core.context.model.AppContext import AppContext
from xinhou_openai_framework.core.context.model.SystemContext import ctx
from xinhou_openai_framework.core.db.DatabaseManager import DatabaseManager
from xinhou_openai_framework.core.reponse.R import R
from xinhou_openai_framework.core.reponse.ResModel import ResModel

from apps.admin.schema.VoiceSchema import ReqCreateVideoWithAudioSchema, ReqQueryVideoTaskSchema, \
    ResQueryVideoTaskSchema
from common.service.AudioService import AudioService
from common.service.VideoPreviewGenerator import VideoPreviewGenerator
from common.service.VideoService import VideoService

api = APIRouter()


@api.post('/admin/voice/task/query', tags=["生成视频", "v1"],
          response_model=ResModel[ResQueryVideoTaskSchema],
          summary="[v1]查询视频任务",
          description="根据提供的参数查询视频任务状态")
async def query_video_task(search: ReqQueryVideoTaskSchema,
                           db: Session = Depends(DatabaseManager().get_session)):
    logger.info(f"[查询视频任务][请求参数]: {search.model_dump()}")
    video_service = VideoService(db)
    result = await video_service.query_video_task(search)
    return R.SUCCESS(data=result)


@api.post('/admin/voice/task/createWithAudio', tags=["生成视频", "v3"],
          response_model=ResModel[ResQueryVideoTaskSchema],
          summary="[v3]使用已有音频创建视频任务",
          description="使用已存在的音频文件创建新的视频任务")
async def create_video_with_audio(
        task_data: ReqCreateVideoWithAudioSchema,
        db: Session = Depends(DatabaseManager().get_session)
):
    context: AppContext = ctx.__getattr__("context")

    logger.info(
        f"[创建视频任务][请求参数]: task_id={task_data.task_id}, pid={task_data.pid}, redis_key={task_data.redis_key}, "
        f"video_model_id={task_data.video_model_id}")
    video_service = VideoService(db)
    result = await video_service.create_video_with_audio(task_data, context)
    return R.SUCCESS(result)


@api.post("/video/preview/generate", tags=["视频处理"], summary="生成视频预览图",
          description="为没有预览图的视频生成预览图")
async def generate_preview(db: Session = Depends(DatabaseManager().get_session)):
    try:
        generator = VideoPreviewGenerator(db)
        await generator.process_media_model()
        await generator.process_embedding_file()
        return {"message": "视频预览图生成任务完成"}
    except Exception as e:
        logger.error(f"生成视频预览图失败: {str(e)}")
        return {"error": str(e)}


@api.post("/audio/set/duration", tags=["音频处理"], summary="生成音频时长",
          description="为音频文件批量添加时长")
async def set_duration(db: Session = Depends(DatabaseManager().get_session)):
    try:
        audio_service = AudioService(db)
        result = await audio_service.update_audio_durations()
        return result
    except Exception as e:
        logger.error(f"生成音频时长任务失败: {str(e)}")
        return {"success": False, "error": str(e)}
