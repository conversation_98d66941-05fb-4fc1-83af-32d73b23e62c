# !/usr/bin/python3
# -*- coding: utf-8 -*-

"""
训练内容信息表服务类
----------------------------------------------------
@Project :   xinhou-aip-admin
@File    :   BgeV1AdminController.py
@Contact :   <EMAIL>

@Modify Time      <AUTHOR>    @Desciption
------------      -------    --------    -----------
2024/06/05 22:04  chenlong   v1.0.0     None
"""
import asyncio
import json
from datetime import datetime, timedelta

from fastapi import APIRouter, Depends, Path
from loguru import logger
from sqlalchemy.orm import Session
from starlette.responses import StreamingResponse
from xinhou_openai_framework.core.cache.RedisConnectionPool import RedisConnectionPool
from xinhou_openai_framework.core.db.DatabaseManager import DatabaseManager
from xinhou_openai_framework.core.exception.CodeEnum import CodeEnum
from xinhou_openai_framework.core.reponse.R import R

from apps.admin.controller.UserController import check_current_user
from apps.admin.queue.message.AIPEmbeddingMessage import AIPEmbeddingMessage
from apps.admin.queue.message.AIPSpeechMessage import AIPSpeechMessage
from apps.admin.queue.producer.AIPAnalysisProducer import AIPAnalysisProducer
from apps.admin.queue.producer.AIPEmbeddingProducer import AIPEmbeddingProducer
from apps.admin.queue.producer.AIPSpeechProducer import AIPSpeechProducer
from apps.admin.schema.AgentsAdminSchema import ReqAgentsChatSchema
from apps.admin.schema.CommentBase import CommonV1ResMsgSchema, ReqFailSchema
from apps.admin.schema.CommonV1ReqAgentsSchema import (
    CommonV2ReqAgentsSchema,
    WorkflowInfo, CommonV1ReqAgentsSchema
)
from common.contents.AgentsContents import AgentsContents
from common.contents.AgentsUserContents import AgentsUserContents
from common.entity.EmbeddingFile import EmbeddingFile
from common.entity.Ip import Ip
from common.entity.Task import Task
from common.entity.User import User
from common.remote.AgentsPlatformRemoteService import AgentsPlatformRemoteService
from common.service.AgentsHistoryService import AgentsHistoryService
from common.service.EmbeddingFileService import EmbeddingFileService
from common.service.IpService import IpService
from common.service.TaskService import TaskService
from common.service.WorkService import WorkService
from common.service.WorkflowService import WorkflowService
from common.utils.TaskStreamUtils import generate_stream, continue_stream

api = APIRouter()



@api.post("/agents/v3/start_creation", tags=["口播稿", "v3"], summary="开始生成语音稿件接口v3",
          description="""
注: 
1. 此接口模拟真实的口播稿生成过程,通过循环10次,每次间隔2秒向Redis中写入数据。
2. 后续需要改为真实的口播稿生成接口,流式返回调用结果。
""")
async def start_creation_v3(
        search: ReqAgentsChatSchema,
        db: Session = Depends(DatabaseManager().get_session),
        redis_pool: Session = Depends(RedisConnectionPool().get_pool),
        current_user: User = Depends(check_current_user),
):
    logger.info(f"[save][model]:{search.model_dump_json()}")
    try:
        # IP 验证逻辑
        ip = IpService(db).find_by_id(Ip(id=search.pid))
        if ip is None:
            return R.jsonify(CodeEnum.ID_NOT_FOUND, "IP未找到")

        # 如果过期时间为空，设置默认过期时间为一个月
        if ip.expire_time is None and ip.remain_point is not None and ip.remain_point > 0:
            ip.expire_time = datetime.now() + timedelta(days=30)
            IpService(db).update(ip)

        # 检查 IP 是否过期
        if ip.expire_time is None or ip.expire_time <= datetime.now():
            logger.info(f"IP已过期，过期时间：{ip.expire_time}")
            return R.jsonify(CodeEnum.FORBIDDEN, f"当前IP已过期，过期时间：{ip.expire_time}")
        task = TaskService(db).save(Task(pid=search.pid,
                                         title=search.query,
                                         task_knowledge_ids=search.task_knowledge_ids,
                                         doc_length=search.doc_length,
                                         language=search.language, is_person=search.is_person,
                                         is_search=search.is_search, is_rag=search.is_rag, style=search.style, read_score=search.read_score))
        if not task.id:
            logger.error("创建任务失败，无法获取任务 ID")
            return R.jsonify(CodeEnum.INTERNAL_SERVER_ERROR, "消息推送失败")

        if search.task_knowledge_ids:
            # 将逗号分隔的id转换为列表
            knowledge_ids = [int(x.strip()) for x in search.task_knowledge_ids.split(',')]

            # 获取所有文件信息
            files = EmbeddingFileService(db).find_by_ids(knowledge_ids)
            if not files:
                return R.jsonify(CodeEnum.NOT_FOUND, "未找到训练文件")

            # 为每个文件创建训练消息并推送到队列
            for file in files:
                training_message = AIPEmbeddingMessage(
                    id=file.id,
                    pid=search.pid,
                    task_id=task.id,
                    file_url=file.file_url
                )

                if file.file_type != 'txt':
                    await AIPAnalysisProducer.analysis_push(AgentsUserContents.AIP_ANALYSIS_QUEUE, training_message)
                else:
                    await AIPEmbeddingProducer.embedding_push(AgentsUserContents.AIP_EMBEDDING_QUEUE, training_message)

            # 等待所有文件训练完成
            max_retries = 60  # 最大重试次数(5分钟)
            retry_interval = 2  # 重试间隔(秒)

            for _ in range(max_retries):
                # 刷新会话缓存
                db.expire_all()

                # 查询所有文件的训练状态
                current_files = db.query(EmbeddingFile).filter(
                    EmbeddingFile.id.in_(knowledge_ids)
                ).all()

                if not current_files:
                    return R.jsonify(CodeEnum.NOT_FOUND, "未找到训练文件")

                # 检查是否所有文件都训练完成
                all_trained = True
                for file in current_files:
                    if file.file_educate_status == 3:  # 任一文件训练失败
                        logger.error(f"知识库训练失败: {file.remark}")
                        return R.jsonify(CodeEnum.INTERNAL_SERVER_ERROR, f"知识库训练失败: {file.remark}")
                    elif file.file_educate_status != 1:  # 还有文件未训练完成
                        all_trained = False
                        break

                if all_trained:  # 所有文件都训练成功
                    break

                await asyncio.sleep(retry_interval)
            else:
                return R.jsonify(CodeEnum.INTERNAL_SERVER_ERROR, "知识库训练超时")

            # 获取工作流
        workflow = WorkflowService(db).get_workflow_by_pid(search.pid)
        if not workflow:
            return R.jsonify(CodeEnum.NOT_FOUND, "未找到可用的工作流")

        # 获取工作流关联的agents
        agents = WorkflowService(db).get_workflow_agents(workflow.id)

        if search.audio_model_id:
            search.query = f"帮我将文案生成音频"

        elif search.video_model_id:
            search.query = f"帮我将文案生成视频"

        # 构建请求对象
        request_data = CommonV2ReqAgentsSchema(
            pid=search.pid,
            task_id=task.id,
            query=search.query,
            language=search.language,
            doc_length=search.doc_length,
            agent_uuid=search.agent_uuid,
            audio_url=search.audio_url,
            video_url=search.video_url,
            audio_model_id=search.audio_model_id,
            video_model_id=search.video_model_id,
            is_person=search.is_person,
            is_search=search.is_search,
            is_rag=search.is_rag,
            style=search.style,
            is_knowledge=search.is_knowledge,
            read_score=search.read_score,
            workflow=WorkflowInfo(
                id=workflow.id,
                workflow_name=workflow.workflow_name,
                workflow_code=workflow.workflow_code,
                description=workflow.description,
                agents=agents
            )
        )

        try:
            # 获取远程服务基础URL
            base_url = AgentsPlatformRemoteService.context.aip.remote.agents

            try:
                # 创建消息实例
                request_data_dict = request_data.model_dump()
                aip_speech_message = AIPSpeechMessage.from_request_data(
                    task_id=task.id,
                    pid=search.pid,
                    request_data=request_data_dict,
                    base_url=base_url
                )

                # 推送到消息队列
                push_result = await AIPSpeechProducer.speech_push(AgentsUserContents.AIP_SPEECH_QUEUE,
                                                                  aip_speech_message)
                if not push_result:
                    logger.error("Failed to push message to queue")
                    return R.jsonify(CodeEnum.INTERNAL_SERVER_ERROR, "消息推送失败")
            except Exception as inner_e:
                logger.error(f"Error in message processing: {str(inner_e)}")
                import traceback
                logger.error(f"Traceback: {traceback.format_exc()}")
                return R.jsonify(CodeEnum.INTERNAL_SERVER_ERROR, f"消息处理失败: {str(inner_e)}")

            # 返回流式响应
            return StreamingResponse(
                generate_stream(task.id, redis_pool),
                headers={
                    "Content-Type": "text/event-stream",
                    "Cache-Control": "no-cache",
                    "Connection": "keep-alive",
                    "X-Accel-Buffering": "no",  # 禁用 Nginx 的缓冲
                },
                media_type="text/event-stream"
            )
        except Exception as e:
            logger.error(f"Error calling remote service: {str(e)}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")
            return R.jsonify(CodeEnum.INTERNAL_SERVER_ERROR, "远程服务调用失败")

    except Exception as e:
        logger.error(f"Error in start_creation: {str(e)}")
    return R.SERVER_ERROR()

@api.post("/agents/v3/continue_creation", tags=["口播稿", "v3"], summary="继续生成语音稿件接口v3")
async def continue_creation(
        search: ReqAgentsChatSchema,
        current_user: User = Depends(check_current_user),
        db: Session = Depends(DatabaseManager().get_session),
        redis_pool: Session = Depends(RedisConnectionPool().get_pool),
):
    logger.info(f"[save][model]:{search.model_dump_json()}")
    workflow_service = WorkflowService(db)
    try:
        # 验证IP
        ip = IpService(db).find_by_id(Ip(id=search.pid))
        if ip is None:
            return R.jsonify(CodeEnum.ID_NOT_FOUND, f"IP未找到")
        # 如果过期时间为空，设置默认过期时间为一个月
        if ip.expire_time is None and ip.remain_point is not None and ip.remain_point > 0:
            ip.expire_time = datetime.now() + timedelta(days=30)
            IpService(db).update(ip)

        # 检查 IP 是否过期
        if ip.expire_time is None or ip.expire_time <= datetime.now():
            logger.info(f"IP已过期，过期时间：{ip.expire_time}")
            return R.jsonify(CodeEnum.FORBIDDEN, f"当前IP已过期，过期时间：{ip.expire_time}")

        # 修复类型错误：确保在比较前检查值是否为None
        audio_result = await WorkService(db).query_work_by_type(search.task_id, 2)
        if audio_result and audio_result.file_id:
            audio_info = EmbeddingFileService(db).find_by_id(EmbeddingFile(id=audio_result.file_id))
            if audio_info and audio_info.duration is not None and ip.remain_point is not None:
                if audio_info.duration > ip.remain_point:
                    logger.info(f"生成视频所需要的点数不够，请先充值。")
                    return R.jsonify(CodeEnum.FORBIDDEN,
                                     f"生成视频所需要的点数不够，请先充值。剩余点数: {int(ip.remain_point)}，所需点数: {audio_info.duration}")

        # 删除Redis中的999 agent数据
        task_key = f'aip_task:{search.task_id}'

        # 优化后的代码 - 直接处理列表末尾的999元素
        agents_key = f'{task_key}_agents'

        # 1. 获取列表最后一个元素
        last_agent = await redis_pool.lindex(agents_key, -1)
        if last_agent:
            last_agent_id = last_agent.decode() if isinstance(last_agent, bytes) else last_agent
            if '999' in last_agent_id:
                print(f"从agents列表中删除最后的999元素: {last_agent_id}")
                await redis_pool.lrem(agents_key, 1, last_agent)

                # 2. 直接删除与最后一个999相关的键
                agent_999_key = f'{task_key}_agent_{last_agent_id}'
                content_999_key = f'{agent_999_key}_content'
                keys_to_delete = [agent_999_key, content_999_key]

                # 删除这些键
                if keys_to_delete:
                    print(f"删除999 agent相关的键: {keys_to_delete}")
                    await redis_pool.delete(*keys_to_delete)

        # 获取工作流
        workflow = workflow_service.get_workflow_by_pid(search.pid)
        if not workflow:
            return R.jsonify(CodeEnum.NOT_FOUND, "未找到可用的工作流")

        # 获取工作流关联的agents
        agents = workflow_service.get_workflow_agents(workflow.id)
        if search.audio_model_id:
            search.query = f"帮我将文案生成音频"
        elif search.voice_upload_url:
            search.query = f"帮我将文案生成视频"
        elif search.video_model_id:
            search.query = f"帮我将文案生成视频"
            # 获取历史记录中的 audio_url
            history_response = AgentsHistoryService(db).get_by_pid_and_task_id(search.pid, search.task_id)
            if history_response and history_response.history:
                # 解码 bytes 为字符串
                history_str = history_response.history.decode('utf-8')
                # 解析 JSON
                history_list = json.loads(history_str)
                # 从后向前遍历找到最后一个包含 audio_url 的记录
                for record in reversed(history_list):
                    if isinstance(record, dict) and record.get("content"):
                        try:
                            # 检查content是否已经是字典类型
                            if isinstance(record["content"], dict):
                                content = record["content"]
                            else:
                                content = json.loads(record["content"])
                            if "audio_url" in content:
                                search.audio_url = content["audio_url"]
                                break
                        except json.JSONDecodeError:
                            continue

        # 构建请求对象
        request_data = CommonV2ReqAgentsSchema(
            pid=search.pid,
            task_id=search.task_id,
            query=search.query,
            language=search.language,
            doc_length=search.doc_length,
            agent_uuid=search.agent_uuid,
            audio_url=search.audio_url,
            video_url=search.video_url,
            audio_model_id=search.audio_model_id,
            video_model_id=search.video_model_id,
            is_pass=search.is_pass,
            video_model_pic_url=search.video_model_pic_url,
            voice_is_upload=search.voice_is_upload,
            voice_upload_url=search.voice_upload_url,
            is_person=search.is_person,
            is_search=search.is_search,
            style=search.style,
            read_score=search.read_score,
            workflow=WorkflowInfo(
                id=workflow.id,
                workflow_name=workflow.workflow_name,
                workflow_code=workflow.workflow_code,
                description=workflow.description,
                agents=agents
            )
        )

        try:
            # 获取远程服务基础URL
            base_url = AgentsPlatformRemoteService.context.aip.remote.agents

            try:
                # 创建消息实例
                request_data_dict = request_data.model_dump()
                aip_speech_message = AIPSpeechMessage.from_request_data(
                    task_id=search.task_id,
                    pid=search.pid,
                    request_data=request_data_dict,
                    base_url=base_url
                )

                # 推送到消息队列
                push_result = await AIPSpeechProducer.speech_push(AgentsUserContents.AIP_SPEECH_QUEUE,
                                                                  aip_speech_message)
                if not push_result:
                    logger.error("Failed to push message to queue")
                    return R.jsonify(CodeEnum.INTERNAL_SERVER_ERROR, "消息推送失败")
            except Exception as inner_e:
                logger.error(f"Error in message processing: {str(inner_e)}")
                import traceback
                logger.error(f"Traceback: {traceback.format_exc()}")
                return R.jsonify(CodeEnum.INTERNAL_SERVER_ERROR, f"消息处理失败: {str(inner_e)}")

            # 返回流式响应
            return StreamingResponse(
                continue_stream(search.task_id, redis_pool),
                headers={
                    "Content-Type": "text/event-stream",
                    "Cache-Control": "no-cache",
                    "Connection": "keep-alive",
                    "X-Accel-Buffering": "no",  # 禁用 Nginx 的缓冲
                },
                media_type="text/event-stream"
            )
        except Exception as e:
            logger.error(f"Error calling remote service: {str(e)}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")
            return R.jsonify(CodeEnum.INTERNAL_SERVER_ERROR, "远程服务调用失败")

    except Exception as e:
        logger.error(f"Error in start_creation: {str(e)}")
        return R.SERVER_ERROR()


@api.get("/agents/v3/check_add/{task_id}", tags=["口播稿", "v3"], summary="获取语音稿件新增内容接口v3")
async def check_add(
        task_id: int = Path(title="任务 ID不能为空"),
        redis_client: Session = Depends(RedisConnectionPool().get_pool),
        current_user: User = Depends(check_current_user),
):
    if not task_id:
        return R.ID_NOT_FOUND()

        # 如果任务未完成，返回流式响应
    return StreamingResponse(
        generate_stream(task_id, redis_client),
        headers={
            "Content-Type": "text/event-stream",
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "X-Accel-Buffering": "no",  # 禁用 Nginx 的缓冲
        },
        media_type="text/event-stream"
    )


@api.post("/agents/v1/all_fail_task", tags=["口播稿", "v1"],
          response_model=CommonV1ResMsgSchema,
          summary="语音稿件训练失败队列",
          description="注：此接口主要获取语音稿件执行失败的消息&错误原因进行调整后执行补偿执行提交消息流程。")
async def bge_embedding_all_fail_task(search: ReqFailSchema):
    datas = await AIPSpeechProducer.speech_all_fail_messages(AgentsUserContents.AIP_SPEECH_QUEUE,
                                                             search.pid)
    return R.SUCCESS(datas)


@api.post("/agents/v1/stop_task", tags=["口播稿", "v1"],
          summary="停止任务生成",
          description="停止指定任务的成过程")
async def stop_task(
        request: CommonV1ReqAgentsSchema,
        redis_client: Session = Depends(RedisConnectionPool().get_pool),
        current_user: User = Depends(check_current_user)
):
    try:
        if not request.task_id:
            return R.ID_NOT_FOUND()

        # 构造停止标记的key
        stop_key = AgentsContents.SPEECH_STOP_LABEL.format(task_id=request.task_id)

        # 设置停止标记，有效期1小时
        await redis_client.lpush(stop_key, "true")

        logger.info(f"已设置任务 {request.task_id} 的停止标记")
        return R.SUCCESS({"message": f"已发送停止信号给任务 {request.task_id}"})

    except Exception as e:
        logger.error(f"设置任务停止标记失败: {str(e)}")
        return R.SERVER_ERROR()

