# !/usr/bin/python3
# -*- coding: utf-8 -*-

"""
任务控制器
----------------------------------------------------
@Project :   xinhou-openai-admin
@File    :   TaskController.py
@Contact :   <EMAIL>

@Modify Time      <AUTHOR>    @Desciption
------------      -------    --------    -----------
2023/07/18 17:32  ChatGPT    v1.0.0      None
2024/07/18 18:30  ChatGPT    v1.1.0      添加CRUD操作
"""

import typing as t

from fastapi import APIRouter, Depends
from sqlalchemy.orm import Session
from xinhou_openai_framework.core.db.DatabaseManager import DatabaseManager
from xinhou_openai_framework.core.reponse.R import R
from xinhou_openai_framework.core.reponse.ResModel import ResModel

from apps.admin.schema.ReqRpaTaskSchema import (
    ReqCreateRpaTaskSchema, ReqRpaTaskQuerySchema, ReqRpaTaskResultSchema
)
from common.contents.RpaTaskEnums import RobotStatusEnum
from common.service.RpaRobotService import RpaRobotService
from common.service.RpaTaskService import RpaTaskService

api = APIRouter()


@api.post("/admin/rpa/create_task", tags=["任务", "v1"],
          response_model=ResModel,
          summary="[v1][rpa_task][create]创建rpa任务接口。",
          description="创建rpa任务接口")
async def create_rpa_task(req: ReqCreateRpaTaskSchema,
                          db: Session = Depends(DatabaseManager().get_session)):
    rap_robot_service = RpaRobotService(db)  # noqa
    robot_id: t.Optional[int] = await rap_robot_service.get_robot_id_by_name(req.robot_name)
    if robot_id is None:
        return R.PARAMETER_ERR()
    result = await RpaTaskService(db).add_task(robot_id, req.robot_params)
    return R.SUCCESS(result)


@api.post("/admin/rpa/todo_list", tags=["任务", "v1"],
          response_model=ResModel,
          summary="[v1][rpa_task][query]获取待做列表",
          description="获取待做列表")
async def get_todo_list(req: ReqRpaTaskQuerySchema,
                        db: Session = Depends(DatabaseManager().get_session)):
    rap_robot_service = RpaRobotService(db)  # noqa
    robot_id: t.Optional[int] = await rap_robot_service.get_robot_id_by_name(req.robot_name)
    if robot_id is None:
        return R.PARAMETER_ERR()
    result = await RpaTaskService(db).get_todo_list(robot_id)
    return R.SUCCESS(result)


@api.post("/admin/rpa/task_result", tags=["任务", "v1"],
          response_model=ResModel,
          summary="[v1][rpa_task][create]rpa任务结束 上传结果",
          description="rpa任务结束 同步任务状态")
async def upload_task_result(req: ReqRpaTaskResultSchema,
                             db: Session = Depends(DatabaseManager().get_session)):
    if req.robot_status not in RobotStatusEnum.__members__.keys():
        return R.PARAMETER_ERR()
    result = await RpaTaskService(db).update_task_result(req)
    return R.SUCCESS(result)
