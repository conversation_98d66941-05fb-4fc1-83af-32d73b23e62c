from typing import List

from fastapi import APIRouter, Depends, Path, Body
from loguru import logger
from sqlalchemy.orm import Session
from xinhou_openai_framework.core.db.DatabaseManager import DatabaseManager
from xinhou_openai_framework.core.exception.GlobalExceptionType import ParameterException
from xinhou_openai_framework.core.reponse.R import R
from xinhou_openai_framework.core.reponse.ResModel import ResModel

from apps.admin.controller.UserController import check_current_user
from apps.admin.schema.CommentBase import ReqCommentCreateSchema, ReqCommentUpdateSchema, \
    CommentFindAllRequest, ResCommentDetailSchema
from common.entity.Comment import Comment
from common.entity.User import User
from common.service.CommentService import CommentService

api = APIRouter()


@api.post('/admin/comment/findAll', 
          tags=["神评论", "查询", "v1"],
          response_model=ResModel[List[ResCommentDetailSchema]],
          summary="[v1]查询所有神评论接口",
          description="查询所有符合条件的神评论信息")
async def find_all(
        req: CommentFindAllRequest = Body(..., description="查询参数"),
        db: Session = Depends(DatabaseManager().get_session)
):
    logger.info(f"[find_all][request]: {req}")

    # 创建一个 Comment 对象，而不是字典
    search = Comment()
    if req.task_id is not None:
        search.task_id = req.task_id
    if req.comment_type is not None:
        search.comment_type = req.comment_type

    results = CommentService(db).find_all(search)

    return R.SUCCESS(results)


@api.post('/admin/comment/save', tags=["神评论", "v1"],
          response_model=ResModel[ResCommentDetailSchema],
          summary="[v1]保存神评论接口",
          description="保存新的神评论信息")
async def save(comment: ReqCommentCreateSchema,
               current_user: User = Depends(check_current_user),
               db: Session = Depends(DatabaseManager().get_session)):
    logger.info(f"[save][request]: {comment.model_dump()}")
    new_comment = Comment(**comment.model_dump())
    result = CommentService(db).save(new_comment)
    return R.SUCCESS(result)


@api.post('/admin/comment/update', tags=["神评论", "v1"],
          response_model=ResModel[ResCommentDetailSchema],
          summary="[v1]更新神评论接口",
          description="更新已存在的神评论信息")
async def update(comment: ReqCommentUpdateSchema,
                 current_user: User = Depends(check_current_user),
                 db: Session = Depends(DatabaseManager().get_session)):
    logger.info(f"[update][request]: {comment.model_dump()}")
    if comment.id is None:
        raise ParameterException("Comment ID is required")
    existing_comment = Comment(**comment.model_dump())
    result = CommentService(db).update(existing_comment)
    if result is None:
        raise ParameterException("Comment not found")
    return R.SUCCESS(result)


@api.delete('/admin/comment/delete/{id}', tags=["神评论", "v1"],
            response_model=ResModel,
            summary="[v1]删除神评论接口",
            description="根据ID删除神评论")
async def delete(id: int = Path(..., description="神评论ID"),
                 current_user: User = Depends(check_current_user),
                 db: Session = Depends(DatabaseManager().get_session)):
    logger.info(f"[delete][request]: id={id}")
    comment = Comment(id=id)
    CommentService(db).delete(comment)
    return R.SUCCESS("Comment deleted successfully")


@api.get('/admin/comment/{id}', 
         tags=["神评论", "查询", "v1"],
         response_model=ResModel[ResCommentDetailSchema],
         summary="[v1]查询单个神评论接口",
         description="根据ID查询单个神评论信息")
async def find_by_id(id: int = Path(..., description="神评论ID"),
                     current_user: User = Depends(check_current_user),
                     db: Session = Depends(DatabaseManager().get_session)):
    logger.info(f"[find_by_id][request]: id={id}")
    comment = Comment(id=id)
    result = CommentService(db).find_by_id(comment)
    if result is None:
        raise ParameterException("Comment not found")
    return R.SUCCESS(result)
