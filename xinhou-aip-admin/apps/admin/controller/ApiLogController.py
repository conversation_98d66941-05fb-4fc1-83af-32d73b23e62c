# !/usr/bin/python3
# -*- coding: utf-8 -*-

"""
API日志控制器
----------------------------------------------------
@Project :   xinhou-openai-admin
@File    :   ApiLogController.py
@Contact :   <EMAIL>

@Modify Time      <AUTHOR>    @Desciption
------------      -------    --------    -----------
2023/08/01 10:00  peng.shen   v1.0.0     None
"""

from datetime import datetime
from typing import Optional, List

from fastapi import APIRouter, Depends, Query
from loguru import logger
from sqlalchemy.orm import Session
from xinhou_openai_framework.core.db.DatabaseManager import DatabaseManager
from xinhou_openai_framework.core.exception.GlobalBusinessException import GlobalBusinessException
from xinhou_openai_framework.core.reponse.R import R
from xinhou_openai_framework.core.reponse.ResModel import ResModel
from xinhou_openai_framework.pages.PageHelper import <PERSON>Hel<PERSON>, PageResultHelper
from pydantic import BaseModel, Field

from common.entity.ApiLog import ApiLog
from common.entity.User import User
from common.service.ApiLogService import ApiLogService
from apps.admin.controller.UserController import check_current_user

api = APIRouter()


class ApiLogQuerySchema(BaseModel):
    """
    API日志查询模型
    """
    user_id: Optional[int] = Field(None, title="用户ID")
    request_url: Optional[str] = Field(None, title="请求URL")
    module: Optional[str] = Field(None, title="所属模块")
    status: Optional[int] = Field(None, title="状态(1=成功,0=失败)")
    start_time: Optional[datetime] = Field(None, title="开始时间")
    end_time: Optional[datetime] = Field(None, title="结束时间")


class ApiLogDetailSchema(BaseModel):
    """
    API日志详情模型
    """
    id: int = Field(..., title="日志ID")
    trace_id: Optional[str] = Field(None, title="请求追踪ID")
    user_id: Optional[int] = Field(None, title="调用用户ID")
    user_name: Optional[str] = Field(None, title="调用用户名称")
    client_ip: Optional[str] = Field(None, title="客户端IP")
    request_url: str = Field(..., title="请求URL")
    request_method: str = Field(..., title="请求方法")
    request_params: Optional[str] = Field(None, title="请求参数")
    request_body: Optional[str] = Field(None, title="请求体")
    response_code: Optional[int] = Field(None, title="响应状态码")
    response_data: Optional[str] = Field(None, title="响应数据")
    error_message: Optional[str] = Field(None, title="错误信息")
    execution_time: Optional[int] = Field(None, title="执行时间(毫秒)")
    api_description: Optional[str] = Field(None, title="API描述")
    module: Optional[str] = Field(None, title="所属模块")
    status: Optional[int] = Field(None, title="状态(1=成功,0=失败)")
    created_at: datetime = Field(..., title="创建时间")


@api.post("/admin/api-log/page", tags=["API日志", "v1"],
          response_model=ResModel[PageResultHelper[ApiLogDetailSchema]],
          summary="[v1]分页查询API日志",
          description="分页查询API日志记录")
async def page(
        page_helper: PageHelper,
        query: ApiLogQuerySchema,
        current_user: User = Depends(check_current_user),
        db: Session = Depends(DatabaseManager().get_session)
):
    logger.info("[page][request]: {}".format(query.model_dump_json()))
    
    # 构建查询条件
    conditions = {}
    if query.user_id:
        conditions["user_id"] = query.user_id
    if query.request_url:
        conditions["request_url"] = query.request_url
    if query.module:
        conditions["module"] = query.module
    if query.status is not None:
        conditions["status"] = query.status
    
    # 创建查询对象
    api_log = ApiLog(**conditions)
    
    # 执行分页查询
    result = ApiLogService(db).page(api_log, page_helper)
    
    return R.SUCCESS(result)


@api.get("/admin/api-log/{id}", tags=["API日志", "v1"],
         response_model=ResModel[ApiLogDetailSchema],
         summary="[v1]获取API日志详情",
         description="根据ID获取API日志详情")
async def get_by_id(
        id: int,
        current_user: User = Depends(check_current_user),
        db: Session = Depends(DatabaseManager().get_session)
):
    logger.info("[get_by_id][request]: id={}".format(id))
    
    api_log = ApiLogService(db).find_by_id(ApiLog(id=id))
    if not api_log:
        raise GlobalBusinessException(404, "API日志不存在")
    
    return R.SUCCESS(api_log)


@api.get("/admin/api-log/modules", tags=["API日志", "v1"],
         response_model=ResModel[List[str]],
         summary="[v1]获取所有模块列表",
         description="获取系统中所有的API模块列表")
async def get_modules(
        current_user: User = Depends(check_current_user),
        db: Session = Depends(DatabaseManager().get_session)
):
    logger.info("[get_modules][request]")
    
    # 这里需要自定义SQL查询，获取所有不同的模块名称
    # 由于BaseServiceImpl可能没有提供这样的方法，这里只是一个示例
    # 实际实现可能需要根据你的ORM框架进行调整
    modules = db.query(ApiLog.module).distinct().filter(ApiLog.module != None).all()
    module_list = [module[0] for module in modules if module[0]]
    
    return R.SUCCESS(module_list)


@api.delete("/admin/api-log/{id}", tags=["API日志", "v1"],
            response_model=ResModel,
            summary="[v1]删除API日志",
            description="根据ID删除API日志")
async def delete(
        id: int,
        current_user: User = Depends(check_current_user),
        db: Session = Depends(DatabaseManager().get_session)
):
    logger.info("[delete][request]: id={}".format(id))
    
    api_log = ApiLogService(db).find_by_id(ApiLog(id=id))
    if not api_log:
        raise GlobalBusinessException(404, "API日志不存在")
    
    ApiLogService(db).delete_by_id(id)
    
    return R.SUCCESS({"message": "删除成功"}) 