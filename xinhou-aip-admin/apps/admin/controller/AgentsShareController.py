import logging

from fastapi import APIRouter, Depends
from sqlalchemy.orm import Session
from xinhou_openai_framework.core.db.DatabaseManager import DatabaseManager
from xinhou_openai_framework.core.exception.CodeEnum import CodeEnum
from xinhou_openai_framework.core.reponse.R import R
from xinhou_openai_framework.core.reponse.ResModel import ResModel

from apps.admin.controller.UserController import check_current_user
from apps.admin.schema.AgentsShareSchema import (
    ReqAgentsShareCreateSchema,
    ReqAgentsShareUpdateSchema,
    ResAgentsShareSchema
)
from common.entity.AgentsShare import AgentsShare
from common.entity.User import User
from common.service.AgentsHistoryService import AgentsHistoryService
from common.service.AgentsShareService import AgentsShareService

api = APIRouter()

logger = logging.getLogger(__name__)


@api.post("/admin/agents-share/create",
          response_model=ResModel[ResAgentsShareSchema],
          tags=["Agent任务分享", "v1"],
          summary="创建Agent任务分享链接")
async def create_share(
        model: ReqAgentsShareCreateSchema,
        current_user: User = Depends(check_current_user),
        db: Session = Depends(DatabaseManager().get_session)
):
    try:

        # 查询分享记录
        is_exist = AgentsShareService(db).get_by_task_id(model.task_id)
        if is_exist:
            return R.SUCCESS(is_exist)
        # 创建分享实体
        agents_share = AgentsShare(**model.model_dump())
        agents_share.create_by = current_user.login_name

        # 保存分享记录
        result = AgentsShareService(db).create(agents_share)
        return R.SUCCESS(result)
    except Exception as e:
        logger.error(f"创建分享链接失败: {str(e)}")
        return R.jsonify(CodeEnum.BAD_REQUEST, f"创建失败: {str(e)}")


@api.post("/admin/agents-share/update",
          response_model=ResModel[ResAgentsShareSchema],
          tags=["Agent任务分享", "v1"],
          summary="更新Agent任务分享状态")
async def update_share(
        model: ReqAgentsShareUpdateSchema,
        current_user: User = Depends(check_current_user),
        db: Session = Depends(DatabaseManager().get_session)
):
    try:
        # 更新分享记录
        result = AgentsShareService(db).update(
            id=model.id,
            is_public=model.is_public,
            remark=model.remark,
            update_by=current_user.login_name
        )
        return R.SUCCESS(result)
    except Exception as e:
        logger.error(f"更新分享状态失败: {str(e)}")
        return R.jsonify(CodeEnum.BAD_REQUEST, f"更新失败: {str(e)}")


@api.get("/admin/agents-share/query",
         response_model=ResModel[ResAgentsShareSchema],
         tags=["Agent任务分享", "查询", "v1"],
         summary="查询Agent任务分享信息")
async def query_share(
        task_id: int,
        db: Session = Depends(DatabaseManager().get_session)
):
    try:
        # 查询分享记录
        result = AgentsShareService(db).get_by_task_id(task_id)
        if not result:
            return R.jsonify(CodeEnum.NOT_FOUND, "分享链接不存在")
        return R.SUCCESS(result)
    except Exception as e:
        logger.error(f"查询分享信息失败: {str(e)}")
        return R.jsonify(CodeEnum.BAD_REQUEST, f"查询失败: {str(e)}")


@api.get("/admin/agents-share/get-history",
         response_model=ResModel,
         tags=["Agent任务分享", "查询", "v1"],
         summary="获取Agent任务分享的历史记录")
async def get_share_history(
        share_code: str,
        db: Session = Depends(DatabaseManager().get_session)
):
    try:
        # 通过分享码获取历史记录
        service = AgentsShareService(db)
        history = service.get_history_by_share_code(share_code)

        if not history:
            return R.jsonify(CodeEnum.NOT_FOUND, "分享链接不存在或未公开")

        # 解析历史记录并返回
        history_data = AgentsHistoryService.parse_history_data(history)
        return R.SUCCESS(history_data)
    except Exception as e:
        logger.error(f"获取分享历史记录失败: {str(e)}")
        return R.jsonify(CodeEnum.BAD_REQUEST, f"获取失败: {str(e)}")
