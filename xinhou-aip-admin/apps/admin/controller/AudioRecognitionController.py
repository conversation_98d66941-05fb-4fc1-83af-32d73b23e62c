#!/usr/bin/python3
# -*- coding: utf-8 -*-
"""
音视频文件识别控制器
----------------------------------------------------
@Project :   xinhou-openai-admin
@File    :   AudioRecognitionController.py
@Contact :   <EMAIL>

@Modify Time      <AUTHOR>    @Desciption
------------      -------    --------    -----------
2023/6/14 16:52   logic      1.0         音视频文件识别控制器
"""
from typing import Optional

from fastapi import APIRouter, Depends, HTTPException, Form
from loguru import logger
from sqlalchemy.orm import Session
from xinhou_openai_framework.core.db.DatabaseManager import DatabaseManager
from xinhou_openai_framework.core.reponse.R import R

from apps.admin.queue.message.AIPAudioRecognitionMessage import (
    AIPAudioRecognitionMessage,
)
from apps.admin.queue.producer.AIPAudioRecognitionProducer import (
    AIPAudioRecognitionProducer,
)
from apps.admin.schema.CommentBase import ReqFailSchema
from apps.admin.schema.ReqV1AudioRecognitionSchema import (
    ResV1AudioRecognitionSchema,
    RecognitionTaskResult,
)
from common.contents.AgentsUserContents import AgentsUserContents
from common.service.AudioRecognitionService import AudioRecognitionService

api = APIRouter()


@api.post(
    "/audio/v1/recognize/async/oss",
    tags=["音视频识别"],
    response_model=ResV1AudioRecognitionSchema,
    summary="音视频文件识别接口（异步方式-OSS链接）",
    description="此接口用于异步识别音视频文件，接收逗号分隔的OSS URL列表，将任务添加到队列中进行处理。",
)
async def audio_v1_recognize_async_oss(
    oss_urls: str = Form(...),  # 逗号分隔的OSS URLs字符串
    pid: Optional[int] = Form(None),
    knowledge_id: Optional[int] = Form(None),
    enable_words: Optional[bool] = Form(False),
    auto_split: Optional[bool] = Form(False),
    new_aweme_ids: Optional[str] = Form(None),
    author_name: Optional[str] = Form(None),
    db: Session = Depends(DatabaseManager().get_session),
):
    try:
        # 检查oss_urls是否为空
        if not oss_urls or oss_urls.strip() == "":
            raise HTTPException(status_code=400, detail="未提供有效的OSS URL")

        # 解析逗号分隔的URL列表
        url_list = [url.strip() for url in oss_urls.split(",") if url.strip()]

        if not url_list:
            raise HTTPException(status_code=400, detail="未提供有效的OSS URL")

        # 保存每个任务的ID，用于返回
        task_ids = []

        # 创建音频识别服务实例 - 仅用于记录日志
        audio_service = AudioRecognitionService()

        for url in url_list:
            # 直接验证URL是否有效
            url_parts = url.split("@@@")
            if len(url_parts) != 5:
                raise HTTPException(
                    status_code=400,
                    detail=f"URL格式错误，应为'ID@@@URL@@@封面url@@@标题@@头像url'格式: {url}",
                )

            file_id = url_parts[0]
            file_url = url_parts[1]
            cover_url = url_parts[2]
            title = url_parts[3]
            avatar_url = url_parts[4]

            # 测试任务提交是否有效
            task_result = audio_service.submit_task(
                file_url, enable_words=bool(enable_words), auto_split=bool(auto_split)
            )
            if task_result is None:
                logger.warning(
                    f"提交音频识别任务失败，可能是配额超限或服务不可用: {file_url}"
                )
                # 继续处理，让消费者稍后重试

            # 发送消息到队列
            aip_recognition_message = AIPAudioRecognitionMessage(
                file_id=int(file_id),
                task_id=task_result,
                pid=pid,
                knowledge_id=knowledge_id,
                file_url=file_url,
                new_aweme_ids=new_aweme_ids,
                author_name=author_name,
                enable_words=bool(enable_words),
                auto_split=bool(auto_split),
                cover_url=cover_url,
                title=title,
                avatar_url=avatar_url,
            )
            await AIPAudioRecognitionProducer.recognition_push(
                AgentsUserContents.AIP_AUDIO_RECOGNITION_QUEUE, aip_recognition_message
            )

            # 添加任务ID到列表
            task_ids.append(file_id)

        # 构建响应数据 - 返回所有任务ID，用逗号连接
        response_data = RecognitionTaskResult(
            task_id=",".join(task_ids), status="SUBMITTED", text=None
        )

        return R.SUCCESS(response_data)
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"提交OSS链接音视频识别任务异常: {e}")
        raise HTTPException(
            status_code=500, detail=f"提交OSS链接音视频识别任务失败: {str(e)}"
        )


@api.post(
    "/audio/v1/all_fail_task",
    tags=["音视频识别"],
    summary="音视频识别失败任务列表",
    description="获取音视频识别执行失败的消息列表，业务侧可以根据获取的数据和错误原因进行调整后，执行补偿执行提交消息流程。",
)
async def audio_v1_all_fail_task(search: ReqFailSchema):
    datas = await AIPAudioRecognitionProducer.recognition_all_fail_messages(
        AgentsUserContents.AIP_AUDIO_RECOGNITION_QUEUE, search.pid
    )
    return R.SUCCESS(datas)
