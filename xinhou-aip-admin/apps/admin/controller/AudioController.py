from aioredis import Redis
from fastapi import APIRouter, Body, Depends, HTTPException, Form
from loguru import logger
from sqlalchemy.orm import Session
from xinhou_openai_framework.core.cache.RedisConnectionPool import RedisConnectionPool
from xinhou_openai_framework.core.db.DatabaseManager import DatabaseManager
from xinhou_openai_framework.core.reponse.R import R
from xinhou_openai_framework.core.reponse.ResModel import ResModel

from apps.admin.schema.AudioSchema import ReqAudioSpeedControlSchema, ReqUpdateVoiceModelsSchema, \
    ResAudioSpeedControlSchema, ResUpdateVoiceModelsSchema, \
    ResV1AudioGenerateSchema, ResV1AudioCloneSchema, ReqV1AudioQuerySchema, ReqV1FishAudioCloneSchema, \
    ResV1FishAudioCloneSchema, ReqFishAudioGenerateSchema
from apps.admin.schema.F5TTSSchema import ReqF5TTSGenerateSchema, ResF5TTSGenerateSchema, ReqF5TTSTranscribeSchema, \
    ResF5TTSTranscribeSchema
from common.entity.EmbeddingFile import EmbeddingFile
from common.entity.VoiceModel import VoiceModel
from common.remote.AudioRemoteService import FishRemoteService
from common.service.AudioService import AudioService
from common.service.EmbeddingFileService import EmbeddingFileService
from common.service.VoiceModelService import VoiceModelService
from common.service.WorkService import WorkService

api = APIRouter()


@api.post('/audio/fish/clone', tags=["生成声音", "v2"],
          response_model=ResModel[ResV1FishAudioCloneSchema],
          summary="[v1]Fish复刻声音",
          description="Fish复刻声音")
async def clone_fish_voice(
        req: ReqV1FishAudioCloneSchema = Depends(ReqV1FishAudioCloneSchema.as_form),
        db: Session = Depends(DatabaseManager().get_session)
):
    logger.info(f"[克隆Fish声音][请求参数]: title={req.title}, file={req.voice_file.filename}, pid={req.pid}")
    try:
        audio_service = AudioService(db)
        voice_file = await req.voice_file.read()
        result = await audio_service.clone_fish_voice(
            title=req.title,
            voice_file=voice_file,
            filename=req.voice_file.filename,
            content_type=req.voice_file.content_type,
            pid=req.pid
        )
        return R.SUCCESS(result)
    except Exception as e:
        logger.error(f"克隆Fish声音时发生错误: {str(e)}")
        logger.exception("详细错误信息")
        raise HTTPException(status_code=500, detail=f"克隆声音失败: {str(e)}")


@api.post('/audio/fish/generate/v2', tags=["生成声音", "v2"],
          response_model=ResModel[ResV1AudioGenerateSchema],
          summary="[v2]Fish合成音频(用于视频合成)",
          description="使用Fish服务合成音频，支持更多参数")
async def generate_fish_audio_v2(
        req: ReqFishAudioGenerateSchema,
        db: Session = Depends(DatabaseManager().get_session)
):
    logger.info(f"[Fish合成音频V2][请求参数]: {req.model_dump()}")
    try:
        audio_service = AudioService(db)
        voice_model_service = VoiceModelService(db)
        voice_model = voice_model_service.find_by_id(VoiceModel(id=req.voice_id))
        if not voice_model:
            raise HTTPException(status_code=404, detail="未找到指定的声音模型")

        # 生成音频
        clone_name = voice_model.clone_name
        voice_name = voice_model.voice_name
        result = await audio_service.generate_fish_audio(
            task_id=req.task_id,
            pid=req.pid,
            content=req.content,
            voice_name=clone_name,
            voice_id=req.voice_id
        )

        # 调整音频速度
        speed = 1.2  # 可以考虑将此参数作为请求参数传入
        remote_data = {
            "input_url": result["audio_url"],
            "speed": speed
        }
        speed_result = FishRemoteService.control_audio_speed(json_data=remote_data)
        audio_duratio = FishRemoteService.get_audio_duration(json_data=remote_data)
        audio_result = await WorkService(db).query_work_by_type(req.task_id, 2)
        if audio_result and audio_result.file_id:
            audio_info = EmbeddingFileService(db).find_by_id(EmbeddingFile(id=audio_result.file_id))
            if audio_info:
                audio_info.duration = audio_duratio["duration"]
                EmbeddingFileService(db).update(audio_info)

        # 更新结果
        result["new_audio_url"] = speed_result["output_url"]
        result["speed"] = speed
        result["voice_model_name"] = voice_name
        result["audio_duratio"] = audio_duratio["duration"]

        return R.SUCCESS(result)
    except Exception as e:
        logger.error(f"Fish合成音频时发生错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Fish合成音频失败: {str(e)}")


@api.post('/audio/speed', tags=["音频处理", "v1"],
          response_model=ResModel[ResAudioSpeedControlSchema],
          summary="控制音频速度",
          description="调整音频播放速度")
async def control_audio_speed(
        req: ReqAudioSpeedControlSchema,
        db: Session = Depends(DatabaseManager().get_session),
        redis_client: Redis = Depends(RedisConnectionPool().get_pool)
):
    """
    控制音频速度
    :param req: 请求参数，包含输入音频URL、目标速度、任务ID和UUID
    :param db: 数据库会话
    :param redis_client: Redis客户端
    :return: 处理后的音频信息
    """
    logger.info(f"[控制音频速度][请求参数]: {req.model_dump()}")
    try:
        audio_service = AudioService(db)
        result = await audio_service.control_audio_speed(
            input_url=req.input_url,
            speed=req.speed,
            pid=req.pid,
            task_id=req.task_id,
            uuid=req.uuid,
            redis_client=redis_client
        )
        return R.SUCCESS(data=result)
    except Exception as e:
        logger.error(f"控制音频速度时发生错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"控制音频速度失败: {str(e)}")


@api.post('/audio/query', tags=["查询音频生成结果", "v1"],
          response_model=ResModel[ResV1AudioCloneSchema],
          summary="[v1]查询音频生成结果",
          description="查询音频生成结果")
async def query_audio_generate(req: ReqV1AudioQuerySchema,
                               db: Session = Depends(DatabaseManager().get_session)):
    work = await WorkService(db).query_work_by_type(req.task_id, req.work_type)
    if not work:
        raise HTTPException(status_code=400, detail="音频任务暂未生成完成!")
    embedding_file_info = EmbeddingFileService(db).find_by_id(EmbeddingFile(id=work.file_id))
    return R.SUCCESS(data={"file_url": embedding_file_info.file_url})


@api.post('/audio/update-voice-models', tags=["生成声音", "v2"],
          response_model=ResModel[ResUpdateVoiceModelsSchema],
          summary="[v1]更新声音模型",
          description="更新现有声音模型，使用Fish服务进行克隆")
async def update_voice_models(
        req: ReqUpdateVoiceModelsSchema = Body(...),
        db: Session = Depends(DatabaseManager().get_session)
):
    logger.info(f"[更新声音模型][请求参数]: {req.model_dump()}")
    try:
        audio_service = AudioService(db)
        result = await audio_service.update_voice_models(req.pid)
        return R.SUCCESS(result)
    except Exception as e:
        logger.error(f"更新声音模型时发生错误: {str(e)}")
        logger.exception("详细错误信息")
        raise HTTPException(status_code=500, detail=f"更新声音模型失败: {str(e)}")


@api.post('/audio/f5tts/generate', tags=["生成声音", "v2"],
          response_model=ResModel[ResF5TTSGenerateSchema],
          summary="[v2]F5-TTS音频生成",
          description="使用F5-TTS服务生成音频，通过voice_id获取参考音频，通过task_id获取待生成文本")
async def generate_f5tts_audio(
        req: ReqF5TTSGenerateSchema,
        db: Session = Depends(DatabaseManager().get_session)
):
    logger.info(f"[F5-TTS生成音频][请求参数]: {req.model_dump()}")
    try:
        # 1. 获取voice_model信息
        voice_model = VoiceModelService(db).find_by_id(VoiceModel(id=req.voice_id))
        if not voice_model:
            raise HTTPException(status_code=404, detail="未找到指定的声音模型")

        # 2. 调用音频生成服务
        audio_service = AudioService(db)
        result = await audio_service.generate_f5tts_audio(
            task_id=req.task_id,
            pid=req.pid,
            ref_audio_url=voice_model.voice_url,  # 使用voice_model中的音频URL
            ref_text=req.ref_text,  # 可选参数
            voice_id=req.voice_id
        )
        return R.SUCCESS(result)
    except Exception as e:
        logger.error(f"F5-TTS生成音频时发生错误: {str(e)}")
        logger.exception("详细错误信息")
        raise HTTPException(status_code=500, detail=f"F5-TTS生成音频失败: {str(e)}")


@api.post('/audio/f5tts/transcribe', tags=["生成声音", "v2"],
          response_model=ResModel[ResF5TTSTranscribeSchema],
          summary="[v2]F5-TTS音频转写",
          description="使用F5-TTS服务进行音频转写")
async def transcribe_f5tts_audio(
        req: ReqF5TTSTranscribeSchema,
        db: Session = Depends(DatabaseManager().get_session)
):
    logger.info(f"[F5-TTS音频转写][请求参数]: {req.model_dump()}")
    try:
        audio_service = AudioService(db)
        result = await audio_service.transcribe_f5tts_audio(
            audio_url=req.audio_url,
            language=req.language
        )
        return R.SUCCESS(result)
    except Exception as e:
        logger.error(f"F5-TTS音频转写时发生错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"F5-TTS音频转写失败: {str(e)}")


@api.post('/audio/duration', tags=["音频处理", "v1"],
          response_model=ResModel[ResAudioSpeedControlSchema],
          summary="获取音频时长",
          description="获取音频时长")
async def get_audio_duration(
        req: ReqAudioSpeedControlSchema,
        db: Session = Depends(DatabaseManager().get_session),
        redis_client: Redis = Depends(RedisConnectionPool().get_pool)
):
    """
    控制音频速度
    :param req: 请求参数，包含输入音频URL、目标速度、任务ID和UUID
    :param db: 数据库会话
    :param redis_client: Redis客户端
    :return: 处理后的音频信息
    """
    logger.info(f"[控制音频速度][请求参数]: {req.model_dump()}")
    try:
        audio_service = AudioService(db)
        result = await audio_service.get_audio_duration(
            input_url=req.input_url
        )
        return R.SUCCESS(data=result)
    except Exception as e:
        logger.error(f"控制音频速度时发生错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"控制音频速度失败: {str(e)}")


@api.post('/audio/fish/clone_by_url', tags=["生成声音", "v2"],
          response_model=ResModel[ResV1FishAudioCloneSchema],
          summary="[v1]通过OSS URL进行Fish复刻声音",
          description="通过已有的OSS URL进行Fish复刻声音，支持逗号分隔的多个URL")
async def clone_fish_voice_by_url(
        title: str = Form(..., description="声音标题"),
        oss_url: str = Form(..., description="OSS音频文件URL，多个URL用逗号分隔"),
        pid: int = Form(..., description="IP ID"),
        db: Session = Depends(DatabaseManager().get_session)
):
    logger.info(f"[通过URL克隆Fish声音][请求参数]: title={title}, oss_url={oss_url}, pid={pid}")
    try:
        # 分割URL字符串并去除空白
        url_list = [url.strip() for url in oss_url.split(',') if url.strip()]
        
        if not url_list:
            raise HTTPException(status_code=400, detail="未提供有效的OSS URL")
        
        results = []
        audio_service = AudioService(db)
        
        for i, url in enumerate(url_list):
            # 如果有多个URL，为每个URL添加序号后缀
            current_title = title if len(url_list) == 1 else f"{title}_{i+1}"
            result = await audio_service.clone_fish_voice_by_url(
                title=current_title,
                voice_url=url,
                pid=pid
            )
            results.append(result)
        
        # 返回结果列表或第一个结果
        return R.SUCCESS(results[0] if len(results) == 1 else results)
    except Exception as e:
        logger.error(f"通过URL克隆Fish声音时发生错误: {str(e)}")
        logger.exception("详细错误信息")
        raise HTTPException(status_code=500, detail=f"克隆声音失败: {str(e)}")
