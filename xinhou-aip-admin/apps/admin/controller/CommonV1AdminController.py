#!/usr/bin/python3
# -*- coding: utf-8 -*-
"""
langchainOpenAI&QA功能描述（Milvus）
----------------------------------------------------
@Project :   xinhou-openai-admin
@File    :   OpenAiV5Controller.py
@Contact :   <EMAIL>

@Modify Time      <AUTHOR>    @Desciption
------------      -------    --------    -----------
2023/6/5 10:47   logic   1.0         None
"""
import io
import unicodedata
from typing import List, Optional

from fastapi import APIRouter, Depends, UploadFile, HTTPException, Form, File
from langchain_community.document_loaders import RecursiveUrlLoader
from loguru import logger
from sqlalchemy.orm import Session
from xinhou_openai_framework.core.db.DatabaseManager import DatabaseManager
from xinhou_openai_framework.core.reponse.R import R

from apps.admin.queue.message.AIPAnalysisMessage import AIPAnalysisMessage
from apps.admin.queue.message.AIPEmbeddingMessage import AIPEmbeddingMessage
from apps.admin.queue.producer.AIPAnalysisProducer import AIPAnalysisProducer
from apps.admin.queue.producer.AIPEmbeddingProducer import AIPEmbeddingProducer
from apps.admin.schema.CommentBase import CommonV1ResMsgSchema, ReqFailSchema
from apps.admin.schema.ReqV1TrainingFileUploadSchema import ResV1TrainingFileUploadSchema
from common.contents.AgentsUserContents import AgentsUserContents
from common.entity.EmbeddingFile import EmbeddingFile
from common.entity.Ip import Ip
from common.entity.IpKnowledge import IpKnowledge
from common.service.EmbeddingFileService import EmbeddingFileService
from common.service.IpService import IpService
from common.service.KnowledgeService import KnowledgeService
from common.utils.FilesUtils import save_files_v1_oss
import base64
from apps.admin.schema.ReqV1Base64UploadSchema import ReqV1Base64UploadSchema

api = APIRouter()


@api.post('/common/v1/file/upload', tags=["通用", "v1"],
          response_model=ResV1TrainingFileUploadSchema,
          summary="上传通用待训练文件接口(支持批量上传)",
          description="注：此接口逻辑已重构，本接口只接收文件流存储&上传oss服务，并返回持久化后的数据结果。")
async def common_v1_file_upload(
        files: List[UploadFile] = File(...),
        pid: str = Form(...),
        emb_type: int = Form(...),
        db: Session = Depends(DatabaseManager().get_session)
):
    try:
        file_infos = await save_files_v1_oss(files)
        datas = []
        embedding_file_service = EmbeddingFileService(db)

        for vo in file_infos:
            file_data = EmbeddingFile(
                pid=pid,
                emb_type=emb_type,
                file_name=vo['file_name'],
                file_name_uuid=vo['file_name_uuid'],
                file_type=vo['file_type'],
                file_size=vo['file_size'],
                file_path=vo['file_path'],
                file_url=vo['file_url'],
                file_review_pic=vo['file_review_pic'],  # 使用 OSS 上传时生成的预览图 URL
                create_by="admin",
                update_by="admin",
                is_delete=0
            )
            saved_file = embedding_file_service.save(file_data)
            datas.append(saved_file)

        return R.SUCCESS(datas)
    except Exception as e:
        db.rollback()
        logger.error(f"An error occurred while uploading files: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to upload files: {str(e)}")


@api.post('/common/v2/file/upload', tags=["知识库"],
          response_model=ResV1TrainingFileUploadSchema,
          summary="上传获取用户账号下的视频解析文本",
          description="上传获取用户账号下的视频解析文本，写入多个字段")
async def common_v2_file_upload(
        files: List[UploadFile] = File(...),
        pid: int = Form(...),
        knowledge_id: int = Form(...),
        emb_type: int = Form(...),
        o_url: Optional[str] = Form(default=None),
        file_review_pic: Optional[str] = Form(default=None),
        source: Optional[str] = Form(...),
        extra: Optional[str] = Form(default=None),
        db: Session = Depends(DatabaseManager().get_session)
):
    try:
        # 上传文件到OSS
        file_infos = await save_files_v1_oss(files)

        # 初始化服务
        embedding_file_service = EmbeddingFileService(db)
        knowledge_service = KnowledgeService(db)

        # 初始化一个列表来存储保存的文件数据
        saved_files = []

        # 使用提供的 knowledge_id 查找知识库记录
        knowledge = knowledge_service.find_by_id(IpKnowledge(id=knowledge_id))
        if not knowledge:
            logger.warning(f"No knowledge found for ID: {knowledge_id}")
            raise HTTPException(status_code=404, detail="Knowledge not found")

        for vo in file_infos:
            file_data = EmbeddingFile(
                pid=pid,
                emb_type=emb_type,
                file_name=vo['file_name'],
                file_name_uuid=vo['file_name_uuid'],
                file_type=vo['file_type'],
                file_size=vo['file_size'],
                file_path=vo['file_path'],
                file_url=vo['file_url'],
                create_by="admin",
                update_by="admin",
                is_delete=0,
                o_url=o_url,
                file_review_pic=file_review_pic,
                source=source,
                extra=extra
            )
            saved_file = embedding_file_service.save(file_data)

            # 直接使用返回的字典，不需要调用 to_dict()
            saved_files.append(saved_file)

            # 更新知识库记录
            knowledge_service.add_file_to_knowledge(knowledge.id, saved_file['id'])
            # 如果文件不属于 txt那么发起文件转发消息
            if file_data.file_type != 'txt':
                aip_analysis_message = AIPAnalysisMessage(
                    **{"id": saved_file['id'], "pid": pid, "file_url": saved_file['file_url']
                       })
                await AIPAnalysisProducer.analysis_push(AgentsUserContents.AIP_ANALYSIS_QUEUE, aip_analysis_message)
            else:
                aip_embedding_message = AIPEmbeddingMessage(
                    **{"id": saved_file['id'], "pid": pid, "file_url": saved_file['file_url']
                       })
                await AIPEmbeddingProducer.embedding_push(AgentsUserContents.AIP_EMBEDDING_QUEUE, aip_embedding_message)
        return R.SUCCESS(saved_files)
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"An error occurred while uploading files: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to upload files: {str(e)}")


@api.post('/common/v1/url/upload', tags=["通用", "v1"],
          response_model=ResV1TrainingFileUploadSchema,
          summary="上传网页训练文件接口",
          description="注：此接口逻辑已重构，本接口只接收文件流存储&上传oss服务，并返回持久化后的数据结果。")
async def common_v1_url_upload(
        urls: List[str] = Form(...),
        ip_name: str = Form(...),
        emb_type: int = Form(...),
        db: Session = Depends(DatabaseManager().get_session)
):
    try:
        ip_results = IpService(db).find_all(Ip(**{"ip_name": ip_name}))
        if not ip_results:
            raise HTTPException(status_code=404, detail=f"No IP found with name: {ip_name}")
        ip = ip_results[0]

        # 初始化一个列表来存储保存的文件数据
        saved_files = []

        for url in urls:
            loader = RecursiveUrlLoader(url=url)
            loaded_data = loader.load_and_split()
            if not loaded_data:
                logger.warning(f"No content loaded from URL: {url}")
                continue

            text = loaded_data[0].page_content
            info = loaded_data[0].metadata['title'] + '.txt'
            file = create_upload_file(text, info)

            result = await save_files_v1_oss([file])  # Assuming save_files_v1_oss expects a list

            file_data = EmbeddingFile(
                o_url=url,
                pid=ip.id,
                emb_type=emb_type,
                file_name=result[0]['file_name'],
                file_name_uuid=result[0]['file_name_uuid'],
                file_type=result[0]['file_type'],
                file_size=result[0]['file_size'],
                file_path=result[0]['file_path'],
                file_url=result[0]['file_url'],
                create_by="admin",
                update_by="admin"
            )
            saved_file = EmbeddingFileService(db).save(file_data)
            saved_files.append(saved_file)
            # 如果文件不等于 txt那么发起文件转发消息
            aip_analysis_message = AIPAnalysisMessage(
                **{"id": saved_file['id'], "pid": ip.id, "file_url": saved_file['file_url']
                   })
            await AIPAnalysisProducer.analysis_push(AgentsUserContents.AIP_ANALYSIS_QUEUE, aip_analysis_message)
        return R.SUCCESS(saved_files)
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"An error occurred while uploading files: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to upload files: {str(e)}")


def create_upload_file(content: str, filename: str) -> UploadFile:
    """
    将字符串内容转化为UploadFile对象。

    :param content: 字符串内容，你想要转化为文件的内容。
    :param filename: 字符串，表示文件的名称。
    :return: UploadFile对象。
    """
    # 将字符串转化为字节流
    content_bytes = content.encode("utf-8")
    # 使用BytesIO建一个内存中的文件对象
    content_io = io.BytesIO(content_bytes)
    # 创建UploadFile对象
    upload_file = UploadFile(file=content_io, filename=filename)
    return upload_file


def sanitize_filename(filename):
    # 规范化Unicode字符
    filename = unicodedata.normalize('NFKD', filename)
    # 移除非ASCII字符
    filename = filename.encode('ASCII', 'ignore').decode('ASCII')
    return filename


@api.post("/common/v1/all_fail_task", tags=["通用", "v1"],
          response_model=CommonV1ResMsgSchema,
          summary="文件转化失败队列",
          description="注：此接口主要获取文件转化执行失败的消息列表，业务侧可以根据获取的数据&错误原因进行调整后，执行补偿执行提交消息流程。")
async def bge_embedding_all_fail_task(search: ReqFailSchema):
    datas = await AIPAnalysisProducer.analysis_all_fail_messages(AgentsUserContents.AIP_ANALYSIS_QUEUE,
                                                                 search.pid)
    return R.SUCCESS(datas)


@api.post('/common/v3/file/upload', tags=["通用", "v3"],
          response_model=ResV1TrainingFileUploadSchema,
          summary="文件直接存储到 oos",
          description="注：此接口逻辑已重构，本接口只接收文件流存储&上传oss服务，并返回oss结果。")
async def common_v3_file_upload(
        files: List[UploadFile] = File(...)
):
    try:
        file_infos = await save_files_v1_oss(files)
        return R.SUCCESS(file_infos)
    except Exception as e:
        logger.error(f"An error occurred while uploading files: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to upload files: {str(e)}")


@api.post('/common/v4/base64/upload', tags=["通用", "v4"],
          response_model=ResV1TrainingFileUploadSchema,
          summary="上传base64格式文件到OSS",
          description="接收base64编码的文件数据，解码后上传到OSS服务，并返回OSS结果。")
async def common_v4_base64_upload(
        request: ReqV1Base64UploadSchema
):
    try:
        # 解码base64数据
        file_data = base64.b64decode(request.base64_data)
        
        # 创建临时文件对象
        file_io = io.BytesIO(file_data)
        
        # 创建UploadFile对象
        upload_file = UploadFile(
            file=file_io, 
            filename=request.filename,
            content_type=request.content_type
        )
        
        # 上传到OSS
        file_infos = await save_files_v1_oss([upload_file])
        
        return R.SUCCESS(file_infos)
    except Exception as e:
        logger.error(f"An error occurred while uploading base64 file: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to upload base64 file: {str(e)}")
