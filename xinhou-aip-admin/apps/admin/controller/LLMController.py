from typing import List

from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from xinhou_openai_framework.core.db.DatabaseManager import DatabaseManager
from xinhou_openai_framework.core.reponse.R import R

from apps.admin.schema.LLMSchema import *
from common.entity.LLM import LLM
from common.service.LLMService import LLMService

api = APIRouter()


@api.post("/admin/llm/create", tags=["LLM管理"],
          response_model=ResLLMSchema,
          summary="创建LLM模型",
          description="创建新的LLM模型配置")
async def create_llm(
        req: ReqLLMCreateSchema,
        db: Session = Depends(DatabaseManager().get_session)
):
    if not req.llm_name or not req.llm_code or not req.model_name or not req.api_url:
        raise HTTPException(status_code=400, detail="必填字段不能为空")
        
    llm = LLM(**req.dict())
    result = LLMService(db).create(llm)
    return R.SUCCESS(result)


@api.post("/admin/llm/update", tags=["LLM管理"],
          response_model=ResLLMSchema,
          summary="更新LLM模型",
          description="更新已存在的LLM模型配置")
async def update_llm(
        req: ReqLLMUpdateSchema,
        db: Session = Depends(DatabaseManager().get_session)
):
    if not req.id or req.id <= 0:
        raise HTTPException(status_code=400, detail="无效的LLM ID")

    update_data = {
        k: v for k, v in req.dict().items()
        if v is not None or k in ['api_config']  # api_config允许为空字典
    }
    
    if len(update_data) <= 1:  # 只有id字段
        raise HTTPException(status_code=400, detail="没有需要更新的字段")
        
    llm = LLM(**update_data)
    result = LLMService(db).update(llm)
    return R.SUCCESS(result)


@api.post("/admin/llm/delete", tags=["LLM管理"],
          summary="删除LLM模型",
          description="软删除指定的LLM模型")
async def delete_llm(
        req: ReqLLMDeleteSchema,
        db: Session = Depends(DatabaseManager().get_session)
):
    if not req.id or req.id <= 0:
        raise HTTPException(status_code=400, detail="无效的LLM ID")
        
    result = LLMService(db).delete(req.id)
    return R.SUCCESS(result)


@api.get("/admin/llm/list", tags=["LLM管理"],
         response_model=List[ResLLMSchema],
         summary="获取LLM模型列表",
         description="获取所有可用的LLM模型列表")
async def list_llms(
        status: Optional[int] = None,
        db: Session = Depends(DatabaseManager().get_session)
):
    if status is not None and status not in [1, 2]:
        raise HTTPException(status_code=400, detail="无效的状态值")
        
    result = LLMService(db).list_all(status)
    return R.SUCCESS(result)
