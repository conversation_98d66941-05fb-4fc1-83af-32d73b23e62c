from typing import List

from fastapi import APIRouter, Depends, Path
from sqlalchemy.orm import Session
from xinhou_openai_framework.core.db.DatabaseManager import DatabaseManager
from xinhou_openai_framework.core.exception.GlobalExceptionType import ParameterException
from xinhou_openai_framework.core.reponse.R import R
from xinhou_openai_framework.core.reponse.ResModel import ResModel
from xinhou_openai_framework.pages.PageHelper import PageResultHelper, PageHelper

from apps.admin.controller.UserController import check_current_user
from apps.admin.schema.ProductSchema import (
    ReqProductFindSchema, ResProductDetailSchema, ReqProductSaveSchema,
    ReqProductUpdateSchema
)
from common.entity.Product import Product
from common.entity.User import User
from common.service.ProductService import ProductService

api = APIRouter()


@api.post('/admin/product/find', tags=["产品", "查询", "v1"],
          response_model=ResModel[PageResultHelper[ResProductDetailSchema]],
          summary="[v1][product][find]查询产品信息表信息带分页接口",
          description="通过参数模型传递条件查询")
async def find(search: PageHelper[ReqProductFindSchema],
               current_user: User = Depends(check_current_user),
               db: Session = Depends(DatabaseManager().get_session)):
    return R.SUCCESS(ProductService(db).find_by(search))


@api.post('/admin/product/findAll', tags=["产品", "查询", "v1"],
          response_model=ResModel[List[ResProductDetailSchema]],
          summary="[v1][product][findAll]查询产品信息表信息接口",
          description="通过参数模型传递条件查询")
async def find_all(search: ReqProductFindSchema,
                   db: Session = Depends(DatabaseManager().get_session)):
    return R.SUCCESS(ProductService(db).find_all(search))


@api.post("/admin/product/save", tags=["产品", "v1"],
          response_model=ResModel[ResProductDetailSchema],
          summary="[v1][product][save]保存产品信息表信息接口",
          description="通过参数模型保存数据")
async def save(model: ReqProductSaveSchema,
               current_user: User = Depends(check_current_user),
               db: Session = Depends(DatabaseManager().get_session)):
    product = ProductService(db).save(Product(**model.model_dump(exclude_unset=True)))
    return R.SUCCESS(product)


@api.get("/admin/product/delete/{id}", tags=["产品", "v1"],
         response_model=ResModel,
         summary="[v1][product][delete]删除产品信息表信息接口",
         description="根据ID删除数据")
async def delete(id: int = Path(title="ID不能为空"),
                 current_user: User = Depends(check_current_user),
                 db: Session = Depends(DatabaseManager().get_session)):
    if id is None:
        raise ParameterException()
    return R.SUCCESS(ProductService(db).delete(Product(id=id)))


@api.post("/admin/product/update", tags=["产品", "v1"],
          response_model=ResModel[ResProductDetailSchema],
          summary="[v1][product][update]更新产品信息表信息接口",
          description="根据ID更新数据")
async def update(model: ReqProductUpdateSchema,
                 current_user: User = Depends(check_current_user),
                 db: Session = Depends(DatabaseManager().get_session)):
    if model.id is None:
        raise ParameterException()
    return R.SUCCESS(ProductService(db).update(Product(**model.model_dump(exclude_unset=True))))


@api.get("/admin/product/detail/{id}", tags=["产品", "查询", "v1"],
         response_model=ResModel[ResProductDetailSchema],
         summary="[v1][product][detail]获取产品信息表详情接口",
         description="根据ID获取数据")
async def detail(id: int = Path(title="ID不能为空"),
                 current_user: User = Depends(check_current_user),
                 db: Session = Depends(DatabaseManager().get_session)):
    if id is None:
        raise ParameterException()
    return R.SUCCESS(ProductService(db).find_by_id(Product(id=id)))
