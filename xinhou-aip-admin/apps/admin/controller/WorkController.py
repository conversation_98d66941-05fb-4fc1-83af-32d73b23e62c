from datetime import datetime
from typing import List

from aioredis import Redis
from fastapi import APIRouter, Depends, Path, UploadFile, File, HTTPException
from loguru import logger
import requests
from sqlalchemy.orm import Session
from xinhou_openai_framework.core.cache.RedisConnectionPool import RedisConnectionPool
from xinhou_openai_framework.core.db.DatabaseManager import DatabaseManager
from xinhou_openai_framework.core.exception.GlobalExceptionType import ParameterException
from xinhou_openai_framework.core.reponse.R import R
from xinhou_openai_framework.core.reponse.ResModel import ResModel
from xinhou_openai_framework.pages.PageHelper import PageResultHelper
from xinhou_openai_framework.utils.PathUtil import PathUtil

from apps.admin.controller.UserController import check_current_user
from apps.admin.schema.WorkSchema import ResWorkDetailSchema, ReqWorkFindSchema, ReqWorkSaveSchema, ReqWorkUpdateSchema, \
    ReqWorkUserUpdateSchema
from apps.admin.schema.WorkWithTaskSchema import ResWorkWithTaskDetailSchema
from common.entity.EmbeddingFile import EmbeddingFile
from common.entity.Task import Task
from common.entity.User import User
from common.entity.Work import Work
from common.entity.AgentsHistory import AgentsHistory
from common.service.EmbeddingFileService import EmbeddingFileService
from common.service.TaskService import TaskService
from common.service.WorkService import WorkService
from common.service.AgentsHistoryService import AgentsHistoryService

api = APIRouter()


def serialize_datetime(dt: datetime) -> str:
    return dt.isoformat() if dt else None


@api.post('/admin/work/findAll', tags=["作品", "v1"],
          response_model=ResModel[List[ResWorkDetailSchema]],
          summary="[v1]查询作品信息接口",
          description="根据条件查询作品信息")
async def find_all(search: ReqWorkFindSchema,
                   db: Session = Depends(DatabaseManager().get_session)):
    logger.info("[find_all][request]:{}".format(search.model_dump()))
    if search.pid is None:
        raise ParameterException("PID is required")

    results = WorkService(db).find_all(search)

    return R.SUCCESS(results)


@api.get("/admin/work/delete/{id}", tags=["作品", "v1"],
         response_model=ResModel,
         summary="[v1]删除作品信息接口",
         description="根据ID删除作品")
async def delete(id: int = Path(title="ID不能为空"),
                 current_user: User = Depends(check_current_user),
                 db: Session = Depends(DatabaseManager().get_session)):
    logger.info("[delete][request]:{}".format({"id": id}))
    if id is None:
        raise ParameterException()
    result = await WorkService(db).delete(Work(id=id))
    return R.SUCCESS(result)


@api.post("/admin/work/update", tags=["作品", "v1"],
          response_model=ResModel[ResWorkDetailSchema],
          summary="[v1]更新作品信息接口",
          description="根据task_id和uuid更新作品信息")
async def update(model: ReqWorkUpdateSchema,
                 current_user: User = Depends(check_current_user),
                 db: Session = Depends(DatabaseManager().get_session),
                 redis_client: Redis = Depends(RedisConnectionPool().get_pool)):
    logger.info("[update][request]:{}".format(model.model_dump()))

    # 检查必传参数
    if not model.pid:
        raise ParameterException("pid 不能为空")
    if not model.task_id:
        raise ParameterException("task_id 不能为空")
    if not model.uuid:
        raise ParameterException("uuid 不能为空")

    result = await WorkService(db).update(model, redis_client)
    return R.SUCCESS(result)


@api.post("/admin/work/userUpdate", tags=["作品", "v1"],
          response_model=ResModel[ResWorkDetailSchema],
          summary="[v1]用户手动更新口播稿接口",
          description="根据task_id和 task_type更新作品信息")
async def update(model: ReqWorkUserUpdateSchema,
                 current_user: User = Depends(check_current_user),
                 db: Session = Depends(DatabaseManager().get_session)):
    logger.info("[update][request]:{}".format(model.model_dump()))
    if model.task_id is None:
        raise ParameterException()
    result = await WorkService(db).userUpdate(model)
    return R.SUCCESS(result)


@api.post("/admin/work/save", tags=["作品", "v1"],
          response_model=ResModel[ResWorkDetailSchema],
          summary="[v1]保存作品信息接口",
          description="保存作品信息")
async def save(model: ReqWorkSaveSchema,
               current_user: User = Depends(check_current_user),
               db: Session = Depends(DatabaseManager().get_session)):
    logger.info("[save][request]:{}".format(model.model_dump()))
    result = await WorkService(db).save(model)
    return R.SUCCESS(result)


@api.post('/admin/work/findWorkWithTask', tags=["作品", "v1"],
          response_model=ResModel[PageResultHelper[ResWorkWithTaskDetailSchema]],
          summary="[v1]查询作品及其任务信息接口",
          description="根据条件查询作品及其关联的任务信息")
async def find_work_with_task(search: ReqWorkFindSchema,
                              current_user: User = Depends(check_current_user),
                              db: Session = Depends(DatabaseManager().get_session)):
    result = await WorkService(db).find_work_with_task(search)
    return R.SUCCESS(result)


@api.post("/admin/work/uploadAudio", tags=["作品", "v1"],
          response_model=ResModel[ResWorkDetailSchema],
          summary="[v1]上传音频作品",
          description="上传音频作品")
async def upload_audio(
        task_id: int,
        pid: int,
        file: UploadFile = File(...),
        current_user: User = Depends(check_current_user),
        db: Session = Depends(DatabaseManager().get_session)):
    result = await WorkService(db).upload_audio(task_id, pid, file, db)
    task = TaskService(db).find_by_id(Task(id=task_id))
    task.progress = 5
    task.remark = ""
    TaskService(db).save(task)
    return R.SUCCESS(result)


@api.post("/admin/work/queryResult", tags=["作品", "v1"],
          response_model=ResModel[ResWorkDetailSchema],
          summary="[v1]查询生成的音视频产出物",
          description="查询生成的音视频产出物")
async def query_result(
        task_id: int,
        current_user: User = Depends(check_current_user),
        db: Session = Depends(DatabaseManager().get_session)):
    task = TaskService(db).find_by_id(Task(id=task_id))
    if task is None:
        raise ParameterException("Task not found")

    # Initialize response data structure
    response_data = {
        "status_code": 200,
        "message": "",
        "audio_info": "",
        "video_info": "",
        "audio_create_time": None,
        "video_create_time": None
    }

    # Get audio and video results if they exist
    audio_result = await WorkService(db).query_work_by_type(task_id, 2)
    video_result = await WorkService(db).query_work_by_type(task_id, 3)

    # Handle different progress states
    match task.progress:
        case 0:  # 口播稿初始化
            response_data.update({
                "status_code": 100,
                "message": "口播稿初始化中"
            })
        case 1:  # 生成口播稿中
            response_data.update({
                "status_code": 101,
                "message": "正在生成口播稿"
            })
        case 2:  # 完成口播稿
            response_data.update({
                "status_code": 102,
                "message": "口播稿已完成，等待音频制作"
            })
        case 3:  # 口播稿制作失败
            response_data.update({
                "status_code": 403,
                "message": f"口播稿制作失败：{task.remark or '请联系技术支持'}"
            })
        case 4:  # 音频生成中
            response_data.update({
                "status_code": 201,
                "message": "音频合成中..."
            })
        case 5:  # 完成音频
            if audio_result and audio_result.file_id:
                audio_info = EmbeddingFileService(db).find_by_id(EmbeddingFile(id=audio_result.file_id))
                response_data.update({
                    "status_code": 202,
                    "message": "音频制作完成，等待视频制作",
                    "audio_info": audio_info.file_url,
                    "audio_create_time": audio_result.updated_at
                })
        case 6:  # 音频制作失败
            response_data.update({
                "status_code": 406,
                "message": f"音频制作失败：{task.remark or '请联系技术支持'}"
            })
        case 7:  # 视频生成中
            if audio_result and audio_result.file_id:
                audio_info = EmbeddingFileService(db).find_by_id(EmbeddingFile(id=audio_result.file_id))
                response_data.update({
                    "status_code": 203,
                    "message": "视频制作中...",
                    "audio_info": audio_info.file_url,
                    "audio_create_time": audio_result.updated_at
                })
        case 8:  # 视频制作完成
            if audio_result and video_result and audio_result.file_id and video_result.file_id:
                audio_info = EmbeddingFileService(db).find_by_id(EmbeddingFile(id=audio_result.file_id))
                video_info = EmbeddingFileService(db).find_by_id(EmbeddingFile(id=video_result.file_id))
                response_data.update({
                    "status_code": 200,
                    "message": "制作完成",
                    "audio_info": audio_info.file_url,
                    "audio_create_time": audio_result.updated_at,
                    "video_info": video_info.file_url,
                    "video_create_time": video_result.updated_at
                })
        case 9:  # 视频制作此时失败
            if audio_result and audio_result.file_id:
                audio_info = EmbeddingFileService(db).find_by_id(EmbeddingFile(id=audio_result.file_id))
                response_data.update({
                    "status_code": 203,
                    "message": "视频制作中...",
                    "audio_info": audio_info.file_url,
                    "audio_create_time": audio_result.updated_at
                })
        case 10:  # 视频制作此时失败
            if audio_result and audio_result.file_id:
                audio_info = EmbeddingFileService(db).find_by_id(EmbeddingFile(id=audio_result.file_id))
                response_data.update({
                    "status_code": 409,
                    "message": f"视频制作失败：{task.remark or '请联系技术支持'}",
                    "audio_info": audio_info.file_url,
                    "audio_create_time": audio_result.updated_at
                })
        case _:
            response_data.update({
                "status_code": 500,
                "message": "未知状态，请联系技术支持"
            })
    return R.SUCCESS(data=response_data)


@api.get("/admin/work/downloadAll/{task_id}", tags=["作品", "v1"],
         response_model=ResModel,
         summary="[v1]下载任务相关的所有文件",
         description="将任务所有相关文件打包下载")
async def download_all_files(
        task_id: int = Path(..., title="任务ID"),
        current_user: User = Depends(check_current_user),
        db: Session = Depends(DatabaseManager().get_session)):
    """
    下载与指定任务关联的所有文件
    
    Args:
        task_id: 要下载文件的任务ID
        
    Returns:
        打包好的ZIP文件的下载URL
    """
    from fastapi.responses import FileResponse
    import tempfile
    import os
    import zipfile
    import time
    import requests
    import json
    from urllib.parse import urlparse

    # 检查任务是否存在
    task = TaskService(db).find_by_id(Task(id=task_id))
    if task is None:
        raise ParameterException("Task not found")

    # 查询该任务的所有作品
    works = await WorkService(db).query_all_works_by_task_id(task_id)
    if not works:
        raise ParameterException("No works found for this task")

    # 准备zip文件
    temp_dir = f"{PathUtil.get_root_path()}/static/temp"
    PathUtil.not_exists_mkdir(temp_dir)
    timestamp = int(time.time())
    zip_path = f"{temp_dir}/task_{task_id}_files_{timestamp}.zip"
    temp_files = []  # 存储临时文件路径，用于后续清理
    
    try:
        with zipfile.ZipFile(zip_path, 'w') as zip_file:
            # 处理每个作品
            for work in works:
                try:
                    # 根据work_type决定如何处理
                    if work.work_type == 1:  # 文本类型
                        if work.content:
                            # 创建临时文件
                            fd, temp_path = tempfile.mkstemp(suffix='.txt')
                            temp_files.append(temp_path)
                            
                            # 处理content内容（可能是二进制blob）
                            content_data = work.content
                            if isinstance(content_data, bytes):
                                try:
                                    content_text = content_data.decode('utf-8')
                                except UnicodeDecodeError:
                                    # 如果不是UTF-8编码，尝试其他常见编码
                                    encodings = ['latin1', 'gbk', 'gb2312', 'big5']
                                    for encoding in encodings:
                                        try:
                                            content_text = content_data.decode(encoding)
                                            break
                                        except UnicodeDecodeError:
                                            continue
                                    else:
                                        logger.error(f"无法解码文本内容: work_id={work.id}")
                                        content_text = f"[无法解码的内容] work_id={work.id}"
                            else:
                                content_text = str(content_data)
                            
                            # 提取"</think>"后面的内容
                            think_end_tag = "</think>"
                            if think_end_tag in content_text:
                                content_text = content_text.split(think_end_tag, 1)[1]
                            
                            # 写入临时文件
                            with os.fdopen(fd, 'w', encoding='utf-8') as tmp:
                                tmp.write(content_text)
                            
                            # 添加到zip文件
                            file_name = f"{task_id}_文本_{work.id}.txt"
                            with open(temp_path, 'rb') as f:
                                zip_file.writestr(file_name, f.read())
                            
                            logger.info(f"Added text content from work {work.id} to zip")
                    else:  # 其他类型 (音频、视频等)
                        if work.file_id:
                            # 获取文件URL
                            file_url = EmbeddingFileService(db).get_download_url(work.file_id)
                            if file_url:
                                # 获取文件信息
                                file_info = EmbeddingFileService(db).find_by_id(EmbeddingFile(id=work.file_id))
                                if file_info:
                                    # 确定文件类型和名称
                                    work_type_map = {1: "文本", 2: "音频", 3: "视频", 4: "选题", 5: "附件"}
                                    work_type_str = work_type_map.get(work.work_type, "其他")
                                    
                                    # 根据文件类型获取正确的后缀名
                                    file_ext = None
                                    if work.work_type == 2:  # 音频文件
                                        file_ext = "mp3"
                                    elif work.work_type == 3:  # 视频文件
                                        file_ext = "mp4"
                                    else:
                                        file_ext = file_info.file_type if file_info.file_type else "dat"
                                    
                                    file_name = f"{task_id}_{work_type_str}_{work.id}.{file_ext}"
                                    
                                    # 下载文件并添加到zip
                                    try:
                                        response = requests.get(file_url, stream=True)
                                        response.raise_for_status()
                                        zip_file.writestr(file_name, response.content)
                                        logger.info(f"Added file for work {work.id} to zip")
                                    except Exception as e:
                                        logger.error(f"下载文件失败: {file_url}, 错误: {str(e)}")
                except Exception as e:
                    logger.error(f"处理作品 {work.id} 时出错: {str(e)}")
            
            # 处理搜索专家的热点素材图片
            try:
                # 查询历史记录
                agents_history = AgentsHistoryService(db).get_by_pid_and_task_id(task.pid, task_id)
                if agents_history and agents_history.history:
                    # 解析历史记录数据
                    history_data = AgentsHistoryService.parse_history_data(agents_history)
                    
                    # 遍历历史记录，查找SEARCH3类型的数据
                    for history_item in history_data.get("history", []):
                        if (history_item.get("agent_action") == "SEARCH3" and 
                            history_item.get("content")):
                            try:
                                # 解析content字段
                                content_str = history_item["content"]
                                content_data = json.loads(content_str)
                                
                                # 提取image_urls
                                image_urls = content_data.get("image_urls", [])
                                
                                # 下载每个图片
                                for image_url in image_urls:
                                    try:
                                        # 从URL中提取文件名
                                        parsed_url = urlparse(image_url)
                                        path_parts = parsed_url.path.split('/')
                                        
                                        # 查找uploads/后面的部分
                                        filename = None
                                        for i, part in enumerate(path_parts):
                                            if part == "uploads" and i + 1 < len(path_parts):
                                                filename = path_parts[i + 1]
                                                break
                                        
                                        if not filename:
                                            # 如果没找到uploads，使用最后一部分作为文件名
                                            filename = path_parts[-1] if path_parts else "unknown.jpg"
                                        
                                        # 确保文件名有正确的扩展名
                                        if not filename.endswith(('.jpg', '.jpeg', '.png', '.gif', '.bmp')):
                                            filename += '.jpg'
                                        
                                        # 添加前缀以标识这是热点素材
                                        zip_filename = f"{task_id}_热点素材_{filename}"
                                        
                                        # 下载图片
                                        response = requests.get(image_url, stream=True, timeout=10)
                                        response.raise_for_status()
                                        
                                        # 添加到zip文件
                                        zip_file.writestr(zip_filename, response.content)
                                        logger.info(f"Added hot material image {filename} to zip")
                                        
                                    except Exception as e:
                                        logger.error(f"下载热点素材图片失败: {image_url}, 错误: {str(e)}")
                                        
                            except Exception as e:
                                logger.error(f"解析SEARCH3内容时出错: {str(e)}")
                                
            except Exception as e:
                logger.error(f"处理热点素材图片时出错: {str(e)}")
                    
        # 检查zip文件是否为空
        if os.path.getsize(zip_path) == 0:
            raise ParameterException("No content was added to the zip file")
            
        # 返回文件响应
        response = FileResponse(
            path=zip_path,
            filename=f"task_{task_id}_files.zip",
            media_type="application/zip"
        )
        
        return response
        
    except Exception as e:
        logger.error(f"创建zip文件时出错: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to create zip file: {str(e)}")
        
    finally:
        # 清理临时文件
        for temp_file in temp_files:
            try:
                if os.path.exists(temp_file):
                    os.remove(temp_file)
            except Exception as e:
                logger.error(f"清理临时文件失败: {temp_file}, 错误: {str(e)}")
