import json
import logging
from typing import List, Dict

from aioredis import Redis
from fastapi import APIRouter, Depends
from sqlalchemy.orm import Session
from xinhou_openai_framework.core.cache.RedisConnectionPool import RedisConnectionPool
from xinhou_openai_framework.core.db.DatabaseManager import DatabaseManager
from xinhou_openai_framework.core.decorator.NoLogger import <PERSON><PERSON><PERSON><PERSON>
from xinhou_openai_framework.core.exception.CodeEnum import CodeEnum
from xinhou_openai_framework.core.reponse.R import R
from xinhou_openai_framework.core.reponse.ResModel import ResModel

from apps.admin.controller.UserController import check_current_user
from apps.admin.schema.AgentsHistorySchema import (
    ReqAgentsHistoryCreateSchema,
    ResAgentsHistorySchema,
    ReqAgentsHistoryUpdateSchema
)
from common.entity.AgentsHistory import AgentsHistory
from common.entity.User import User
from common.service.AgentsHistoryService import AgentsHistoryService
from common.utils.RedisHistoryUtil import RedisHistoryUtil

api = APIRouter()

logger = logging.getLogger(__name__)


@api.post("/admin/agents-history/create",
          response_model=ResModel[ResAgentsHistorySchema],
          tags=["Agent历史记录", "v1"],
          summary="创建Agent执行历史记录")
async def create(
        model: ReqAgentsHistoryCreateSchema,
        current_user: User = Depends(check_current_user),
        db: Session = Depends(DatabaseManager().get_session)
):
    agents_history = AgentsHistory(**model.model_dump())
    result = AgentsHistoryService(db).create(agents_history)
    return R.SUCCESS(result)


@api.get("/admin/agents-history/task/{pid}/{task_id}",
         response_model=ResModel[ResAgentsHistorySchema],
         tags=["Agent历史记录", "查询", "v1"],
         summary="获取指定任务的历史记录")
@NoLogger
async def get_by_task_id(
        pid: int,
        task_id: int,
        current_user: User = Depends(check_current_user),
        db: Session = Depends(DatabaseManager().get_session)
):
    result = AgentsHistoryService(db).get_by_pid_and_task_id(pid, task_id)
    return R.SUCCESS(result)


@api.post("/admin/agents-history/update",
          response_model=ResModel[ResAgentsHistorySchema],
          tags=["Agent历史记录", "v1"],
          summary="修改Agent执行历史记录")
async def update(
        model: ReqAgentsHistoryUpdateSchema,
        current_user: User = Depends(check_current_user),
        db: Session = Depends(DatabaseManager().get_session),
        redis_client: Redis = Depends(RedisConnectionPool().get_pool)
):
    try:
        # 设置更新人
        model.update_by = current_user.login_name

        # 如果history是列表，转换为JSON字符串，然后转换为bytes
        if isinstance(model.history, list):
            history_json = json.dumps(model.history, ensure_ascii=False)
            model.history = history_json.encode('utf-8')
        # 如果history已经是字符串，直接转换为bytes
        elif isinstance(model.history, str):
            model.history = model.history.encode('utf-8')
        # 如果history已经是bytes，保持不变
        elif not isinstance(model.history, bytes):
            raise ValueError(f"不支持的history类型: {type(model.history)}")

        result = await AgentsHistoryService(db).update(model)

        # 如果更新成功且有history字段，同步更新Redis缓存
        if result and result.history:
            try:
                # 解析history字段
                history_str = result.history.decode('utf-8')
                # 处理可能的转义字符
                if history_str.startswith('"') and history_str.endswith('"'):
                    history_str = history_str[1:-1].encode().decode('unicode_escape')

                history_list = json.loads(history_str)
                await RedisHistoryUtil.sync_history_to_redis(redis_client, result.task_id, history_list)
            except Exception as e:
                # 记录错误但不影响主流程
                logger.error(f"同步Redis缓存失败: {str(e)}")

        return R.SUCCESS(result)
    except Exception as e:
        logger.error(f"更新历史记录失败: {str(e)}")
        raise

@api.post("/admin/agents-history/sync-all-to-redis",
          response_model=ResModel,
          tags=["Agent历史记录", "同步", "v1"],
          summary="全量同步所有历史记录到Redis")
async def sync_all_history_to_redis(
        batch_size: int = 100,
        current_user: User = Depends(check_current_user),
        db: Session = Depends(DatabaseManager().get_session),
        redis_client: Redis = Depends(RedisConnectionPool().get_pool)
):
    try:
        result = await RedisHistoryUtil.sync_all_to_redis(redis_client, db, batch_size)

        if result["failed"] == 0:
            return R.SUCCESS(result)
        else:
            return R.SUCCESS(result)
    except Exception as e:
        logger.error(f"全量同步历史记录到Redis失败: {str(e)}")
        return R.jsonify(CodeEnum.BAD_REQUEST, f"同步失败: {str(e)}")


@api.post("/admin/agents-history/sync-all-to-redis-concurrent",
          response_model=ResModel,
          tags=["Agent历史记录", "同步", "v1"],
          summary="并发全量同步所有历史记录到Redis")
async def sync_all_history_to_redis_concurrent(
        batch_size: int = 100,
        max_concurrency: int = 25,
        current_user: User = Depends(check_current_user),
        db: Session = Depends(DatabaseManager().get_session),
        redis_client: Redis = Depends(RedisConnectionPool().get_pool)
):
    try:
        result = await RedisHistoryUtil.sync_all_to_redis_concurrent(
            redis_client,
            db,
            batch_size,
            max_concurrency
        )

        if result["failed"] == 0:
            return R.SUCCESS(result)
        else:
            return R.SUCCESS(result)
    except Exception as e:
        logger.error(f"并发全量同步历史记录到Redis失败: {str(e)}")
        return R.jsonify(CodeEnum.BAD_REQUEST, f"同步失败: {str(e)}")


@api.post("/admin/agents-history/sync-to-redis-v2/{task_id}",
          response_model=ResModel,
          tags=["Agent历史记录", "同步", "v1"],
          summary="同步指定任务的历史记录到Redis（新版本）")
async def sync_history_to_redis_v2(
        task_id: int,
        current_user: User = Depends(check_current_user),
        db: Session = Depends(DatabaseManager().get_session),
        redis_client: Redis = Depends(RedisConnectionPool().get_pool)
):
    try:
        await RedisHistoryUtil.sync_db_to_redis_v2(redis_client, db, task_id)
        return R.SUCCESS("同步成功")
    except Exception as e:
        logger.error(f"同步历史记录到Redis失败: {str(e)}")
        return R.jsonify(CodeEnum.BAD_REQUEST, f"同步失败: {str(e)}")
