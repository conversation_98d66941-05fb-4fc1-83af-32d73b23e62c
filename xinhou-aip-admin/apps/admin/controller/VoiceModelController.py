# !/usr/bin/python3
# -*- coding: utf-8 -*-

"""
声音模型信息表控制器类
----------------------------------------------------
@Project :   xinhou-openai-admin
@File    :   VoiceModel.py
@Contact :   <EMAIL>

@Modify Time      <AUTHOR>    @Desciption
------------      -------    --------    -----------
2023/08/15 10:00  peng.shen   v1.0.0     None
"""

from typing import Any, Dict

from fastapi import APIRouter, Body, Depends, HTTPException
from fastapi.params import Path
from loguru import logger
from sqlalchemy.orm import Session
from xinhou_openai_framework.core.db.DatabaseManager import DatabaseManager
from xinhou_openai_framework.core.exception.GlobalExceptionType import ParameterException
from xinhou_openai_framework.core.reponse.R import R
from xinhou_openai_framework.core.reponse.ResModel import <PERSON>sModel
from xinhou_openai_framework.pages.PageHelper import <PERSON>Result<PERSON>elper, PageHelper
from xinhou_openai_framework.core.cache.RedisConnectionPool import RedisConnectionPool
from aioredis import Redis

from apps.admin.controller.UserController import check_current_user
from apps.admin.schema.VoiceModelSchema import ReqVideoTranscodeSchema, ResVoiceModelDetailSchema, \
    ReqVoiceModelFindSchema, \
    ReqVoiceModelSaveSchema, ReqVoiceModelUpdateSchema, ReqVoiceModelSaveHistorySchema
from common.entity.AgentsHistory import AgentsHistory
from common.entity.User import User
from common.entity.VoiceModel import VoiceModel
from common.remote.VideoRemoteService import VideoRemoteService
from common.service.AgentsHistoryService import AgentsHistoryService
from common.service.VoiceModelService import VoiceModelService
from common.utils.RedisHistoryUtil import RedisHistoryUtil

api = APIRouter()


@api.post('/admin/voice/model/find', tags=["声音模型", "v1"],
          response_model=ResModel[PageResultHelper[ResVoiceModelDetailSchema]],
          summary="[v1]查询声音模型信息表信息带分页接口",
          description="通过参数模型传递条件查询")
async def find(search: PageHelper[ReqVoiceModelFindSchema],
               current_user: User = Depends(check_current_user),
               db: Session = Depends(DatabaseManager().get_session)):
    logger.info("[find][request]:{}".format(search.model_dump()))
    return R.SUCCESS(VoiceModelService(db).find_by(search))


@api.post('/admin/voice/model/findAll', tags=["声音模型", "v1"],
          response_model=ResModel[PageResultHelper[ResVoiceModelDetailSchema]],
          summary="[v1]查询声音模型信息表信息接口",
          description="通过参数模型传递条件查询")
async def find_all(search: ReqVoiceModelFindSchema,
                   db: Session = Depends(DatabaseManager().get_session)):
    logger.info("[find][request]:{}".format(search.model_dump()))
    return R.SUCCESS(VoiceModelService(db).find_all(search))


@api.post("/admin/voice/model/save", tags=["声音模型", "v1"],
          response_model=ResModel[ResVoiceModelDetailSchema],
          summary="[v1]保存声音模型信息表信息接口",
          description="通过参数模型保存数据")
async def save(model: ReqVoiceModelSaveSchema,
               current_user: User = Depends(check_current_user),
               db: Session = Depends(DatabaseManager().get_session)):
    logger.info("[save][request]:{}".format(model.model_dump_json()))
    return R.SUCCESS(VoiceModelService(db).save(VoiceModel(**model.model_dump(exclude_unset=True))))


@api.post("/admin/voice/model/save_history", tags=["声音模型", "v1"],
          response_model=ResModel[ResVoiceModelDetailSchema],
          summary="[v1]保存声音模型信息表，并更新历史记录信息接口",
          description="通过参数模型保存数据，并更新历史记录")
async def save_history(
        model: ReqVoiceModelSaveHistorySchema,
        current_user: User = Depends(check_current_user),
        db: Session = Depends(DatabaseManager().get_session),
        redis_client: Redis = Depends(RedisConnectionPool().get_pool)
):
    logger.info("[save_history][request]:{}".format(model.model_dump_json()))
    try:
        # 2. 如果存在 uuid，更新 AgentsHistory
        if model.uuid and model.task_id and model.pid:
            # 获取 AgentsHistory
            agents_history = AgentsHistoryService(db).get_by_pid_and_task_id(model.pid, model.task_id)
            if agents_history:
                try:
                    # 解析 history 字段
                    history_str = agents_history.history.decode('utf-8')
                    # 处理可能的转义字符
                    if history_str.startswith('"') and history_str.endswith('"'):
                        history_str = history_str[1:-1].encode().decode('unicode_escape')

                    import json
                    from datetime import datetime

                    try:
                        # 解析历史记录，添加错误处理
                        history_list = json.loads(history_str)
                        if not isinstance(history_list, list):
                            logger.error(f"历史记录格式错误，应为列表类型: {history_str}")
                            raise ValueError("历史记录格式错误，应为列表类型")

                        # 创建音频专家信息
                        audio_expert = {
                            "audio_name": model.voice_name,
                            "audio_url": model.voice_url,
                            "new_audio_url": model.new_voice_url,
                            "created_at": datetime.now().isoformat(),
                            "updated_at": datetime.now().isoformat()
                        }

                        # 查找并更新指定 uuid 的记录
                        updated = False
                        for item in history_list:
                            if not isinstance(item, dict):
                                logger.warning(f"跳过非字典类型的历史记录项: {item}")
                                continue

                            if item.get('uuid') == model.uuid:
                                # 初始化或更新 content 列表
                                if 'content' not in item:
                                    item['content'] = "{}"

                                item['content'] = json.dumps(audio_expert)
                                updated = True
                                break

                        if not updated:
                            logger.warning(f"未找到匹配的 uuid: {model.uuid}")
                        else:
                            # 将数据转换为 UTF-8 格式存储
                            history_json = json.dumps(history_list, ensure_ascii=False)
                            history_bytes = history_json.encode('utf-8')

                            # 更新 history 字段
                            db.query(AgentsHistory).filter(
                                AgentsHistory.id == agents_history.id
                            ).update({
                                'history': history_bytes,
                                'updated_at': datetime.now()
                            })

                            db.commit()

                            # 同步更新Redis缓存
                            await RedisHistoryUtil.sync_history_to_redis(redis_client, model.task_id, history_list)

                    except json.JSONDecodeError as je:
                        logger.error(f"JSON 解析失败: {str(je)}, 原始数据: {history_str}")
                        raise HTTPException(status_code=500, detail=f"历史记录格式错误: {str(je)}")

                except Exception as e:
                    logger.error(f"更新 AgentsHistory 失败: {str(e)}")
                    db.rollback()
                    raise HTTPException(status_code=500, detail=f"更新 AgentsHistory 失败: {str(e)}")

        return R.SUCCESS(model.uuid)

    except Exception as e:
        db.rollback()
        logger.error(f"保存音频模型失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"保存音频模型失败: {str(e)}")


@api.get("/admin/voice/model/delete/{id}", tags=["声音模型", "v1"],
         response_model=ResModel,
         summary="[v1]删除声音模型信息表信息接口",
         description="根据ID删除数据")
async def delete(id: int = Path(title="ID不能为空"),
                 current_user: User = Depends(check_current_user),
                 db: Session = Depends(DatabaseManager().get_session)):
    logger.info("[detail][request]:{}".format({"id": id}))
    if id is None:
        raise ParameterException()
    VoiceModelService(db).delete(VoiceModel(id=id))
    return R.SUCCESS()


@api.post("/admin/voice/model/update", tags=["声音模型", "v1"],
          response_model=ResModel[ResVoiceModelDetailSchema],
          summary="[v1]更新声音模型信息表信息接口",
          description="根据ID更新数据")
async def update(model: ReqVoiceModelUpdateSchema,
                 current_user: User = Depends(check_current_user),
                 db: Session = Depends(DatabaseManager().get_session)):
    logger.info("[save][request]:{}".format(model.model_dump_json()))
    if model.id is None:
        raise ParameterException()
    return R.SUCCESS(VoiceModelService(db).update(VoiceModel(**model.model_dump(exclude_unset=True))))


@api.get("/admin/voice/model/detail/{id}", tags=["声音模型", "查询", "v1"],
         response_model=ResModel[ResVoiceModelDetailSchema],
         summary="[v1]获取声音模型信息表详情接口",
         description="根据ID获取数据")
async def detail(id: int = Path(title="ID不能为空"),
                 current_user: User = Depends(check_current_user),
                 db: Session = Depends(DatabaseManager().get_session)):
    logger.info("[detail][request]:{}".format({"id": id}))
    if id is None:
        raise ParameterException()
    return R.SUCCESS(VoiceModelService(db).find_by_id(VoiceModel(id=id)))


@api.post("/video/transcode", tags=["生成视频", "v1"],
          response_model=ResModel[Dict[str, Any]],
          summary="[v1]视频转码接口",
          description="将视频转换为标准格式")
async def transcode_video(
        req: ReqVideoTranscodeSchema = Body(..., description="转码请求参数"),
        current_user: User = Depends(check_current_user),
        db: Session = Depends(DatabaseManager().get_session)):
    logger.info("[transcode_video][request]: {}".format(req))

    if not req.input_url:
        raise ParameterException("输入视频URL不能为空")

    try:
        remote_data = {
            "input_url": req.input_url
        }

        result = VideoRemoteService.transcode_video(json_data=remote_data)
        if not result.get("output_url"):
            raise ValueError("未获取到转码后的视频URL")

        return R.SUCCESS({
            "output_url": result["output_url"]
        })

    except Exception as e:
        logger.error(f"视频转码失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"视频转码失败: {str(e)}")
