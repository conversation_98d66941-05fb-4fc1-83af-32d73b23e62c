from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from xinhou_openai_framework.core.db.DatabaseManager import DatabaseManager
from xinhou_openai_framework.core.reponse.R import R

from apps.admin.schema.AgentSchema import *
from common.entity.Agent import Agent
from common.service.AgentService import AgentService

api = APIRouter()


@api.post("/admin/agent/create", tags=["Agent管理"],
          response_model=ResAgentSchema,
          summary="创建Agent",
          description="创建新的Agent配置")
async def create_agent(
        req: ReqAgentCreateSchema,
        db: Session = Depends(DatabaseManager().get_session)
):
    if not req.agent_name_cn or not req.agent_name_en or not req.agent_code:
        raise HTTPException(status_code=400, detail="必填字段不能为空")
        
    agent = Agent(**req.dict())
    result = AgentService(db).create(agent)
    return R.SUCCESS(result)


@api.post("/admin/agent/update", tags=["Agent管理"],
          response_model=ResAgentSchema,
          summary="更新Agent",
          description="更新已存在的Agent配置")
async def update_agent(
        req: ReqAgentUpdateSchema,
        db: Session = Depends(DatabaseManager().get_session)
):
    if not req.id or req.id <= 0:
        raise HTTPException(status_code=400, detail="无效的Agent ID")

    update_data = {
        k: v for k, v in req.dict().items()
        if v is not None or k in ['influence_scope']  # influence_scope允许为空列表
    }
    
    if len(update_data) <= 1:  # 只有id字段
        raise HTTPException(status_code=400, detail="没有需要更新的字段")
        
    agent = Agent(**update_data)
    result = AgentService(db).update(agent)
    return R.SUCCESS(result)


@api.post("/admin/agent/delete", tags=["Agent管理"],
          summary="删除Agent",
          description="软删除指定的Agent")
async def delete_agent(
        req: ReqAgentDeleteSchema,
        db: Session = Depends(DatabaseManager().get_session)
):
    if not req.id or req.id <= 0:
        raise HTTPException(status_code=400, detail="无效的Agent ID")
        
    result = AgentService(db).delete(req.id)
    return R.SUCCESS(result)


@api.get("/admin/agent/get/{id}", tags=["Agent管理"],
         response_model=ResAgentSchema,
         summary="获取Agent详情",
         description="根据ID获取Agent详细信息")
async def get_agent(
        id: int,
        db: Session = Depends(DatabaseManager().get_session)
):
    if not id or id <= 0:
        raise HTTPException(status_code=400, detail="无效的Agent ID")
        
    result = AgentService(db).get_by_id(id)
    if not result:
        raise HTTPException(status_code=404, detail="Agent不存在")
    return R.SUCCESS(result)


@api.get("/admin/agent/list", 
         tags=["Agent管理", "查询"],
         response_model=List[ResAgentSchema],
         summary="获取Agent列表")
async def list_agents(
        status: Optional[int] = None,
        db: Session = Depends(DatabaseManager().get_session)
):
    if status is not None and status not in [1, 2]:
        raise HTTPException(status_code=400, detail="无效的状态值")
        
    result = AgentService(db).list_all(status)
    return R.SUCCESS(result)


@api.get("/admin/agent/list/type/{agent_type}", 
         tags=["Agent管理", "查询"],
         response_model=List[ResAgentSchema],
         summary="获取指定类型的Agent列表")
async def list_agents_by_type(
        agent_type: int,
        status: Optional[int] = None,
        db: Session = Depends(DatabaseManager().get_session)
):
    if agent_type not in [1, 2]:
        raise HTTPException(status_code=400, detail="无效的Agent类型")
        
    result = AgentService(db).list_by_type(agent_type, status)
    return R.SUCCESS(result)


@api.get("/admin/agent/list/role/{agent_role}", 
         tags=["Agent管理", "查询"],
         response_model=List[ResAgentSchema],
         summary="获取指定角色的Agent列表")
async def list_agents_by_role(
        agent_role: int,
        status: Optional[int] = None,
        db: Session = Depends(DatabaseManager().get_session)
):
    if agent_role not in [1, 2, 3, 4, 5]:
        raise HTTPException(status_code=400, detail="无效的Agent角色")
        
    result = AgentService(db).list_by_role(agent_role, status)
    return R.SUCCESS(result)
