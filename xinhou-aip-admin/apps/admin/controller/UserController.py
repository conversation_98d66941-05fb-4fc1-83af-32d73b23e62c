# !/usr/bin/python3
# -*- coding: utf-8 -*-

"""
用户信息表控制器类
----------------------------------------------------
@Project :   xinhou-openai-admin
@File    :   User.py
@Contact :   <EMAIL>

@Modify Time      <AUTHOR>    @Desciption
------------      -------    --------    -----------
2023/04/16 22:04  peng.shen   v1.0.0     None
"""

from fastapi import APIRouter, Depends
from fastapi.params import Path
from fastapi.security import OAuth2PasswordBearer
from loguru import logger
from sqlalchemy.orm import Session
from xinhou_openai_framework.core.cache.RedisConnectionPool import RedisConnectionPool
from xinhou_openai_framework.core.db.DatabaseManager import DatabaseManager
from xinhou_openai_framework.core.exception.CodeEnum import CodeEnum
from xinhou_openai_framework.core.exception.GlobalBusinessException import GlobalBusinessException
from xinhou_openai_framework.core.exception.GlobalExceptionType import ParameterException
from xinhou_openai_framework.core.reponse.R import R
from xinhou_openai_framework.core.reponse.ResModel import ResModel
from xinhou_openai_framework.pages.PageHelper import PageResultHelper, PageHelper
from xinhou_openai_framework.utils.Md5Util import Md5Util

from apps.admin.schema.UserSchema import ResUserDetailSchema, ReqUserFindSchema, ReqUserSaveSchema, \
    ReqUserUpdateSchema, ReqUserLoginSchema, ReqChangePasswordSchema, ReqUserMobileLoginSchema, \
    ReqUserMobileRegisterSchema, ReqAllocatePointsSchema, ReqChangePasswordByMobileSchema
from common.entity.User import User
from common.service.IpService import IpService
from common.service.UserService import UserService

api = APIRouter()


async def check_current_user(token: str = Depends(OAuth2PasswordBearer(tokenUrl="token")),
                             db: Session = Depends(DatabaseManager().get_session)):
    user = await UserService(db).get_user_by_token(token)
    if not user:
        raise GlobalBusinessException(401, "无效的身份验证凭证")
    return user


@api.post('/admin/user/find', tags=["用户", "v1"],
          response_model=ResModel[PageResultHelper[ResUserDetailSchema]],
          summary="[v1]查询用户信息表信息带分页接口",
          description="通过参数模型传递条件查询")
async def find(search: PageHelper[ReqUserFindSchema], current_user: User = Depends(check_current_user),
               db: Session = Depends(DatabaseManager().get_session)):
    logger.info("[find][request]:{}".format(search.model_dump()))
    return R.SUCCESS(UserService(db).find_by(search))


@api.post('/admin/user/findAll', tags=["用户", "v1"],
          response_model=ResModel[PageResultHelper[ResUserDetailSchema]],
          summary="[v1]查询用户信息表信息接口",
          description="通过参数模型传递条件查询")
async def find_all(search: ReqUserFindSchema,
                   db: Session = Depends(DatabaseManager().get_session)):
    logger.info("[find][request]:{}".format(search.model_dump()))
    return R.SUCCESS(UserService(db).find_all(search))


@api.post("/admin/user/save", tags=["用户", "v1"],
          response_model=ResModel[ResUserDetailSchema],
          summary="[v1]保存用户信息表信息接口",
          description="通过参数模型保存数据")
async def save(model: ReqUserSaveSchema, current_user: User = Depends(check_current_user),
               db: Session = Depends(DatabaseManager().get_session)):
    logger.info("[save][request]:{}".format(model.model_dump_json()))
    model.login_pwd = Md5Util.md5_string(model.login_pwd)

    try:
        user = UserService(db).save(User(**model.model_dump(exclude_unset=True)))
        return R.SUCCESS(user)
    except Exception as e:
        # Log the actual exception for debugging
        logger.error(f"Error saving user: {str(e)}")
        # Raise a GlobalBusinessException with a specific error message
        raise GlobalBusinessException(500, "保存用户时发生错误")


@api.get("/admin/user/delete/{id}", tags=["用户", "v1"],
         response_model=ResModel,
         summary="[v1]删除用户信息表信息接口",
         description="根据ID删除数据")
async def delete(id: int = Path(title="ID不能为空"), current_user: User = Depends(check_current_user),
                 db: Session = Depends(DatabaseManager().get_session)):
    logger.info("[detail][request]:{}".format({"id": id}))
    if id is None:
        raise ParameterException()
    UserService(db).delete(User(id=id))
    return R.SUCCESS()


@api.post("/admin/user/update", tags=["用户", "v1"],
          response_model=ResModel[ResUserDetailSchema],
          summary="[v1]更新用户信息表信息接口",
          description="根据ID更新数据")
async def update(model: ReqUserUpdateSchema, current_user: User = Depends(check_current_user),
                 db: Session = Depends(DatabaseManager().get_session)):
    logger.info("[save][request]:{}".format(model.json()))
    if model.id is None:
        raise ParameterException()
    return R.SUCCESS(UserService(db).update(User(**model.model_dump(exclude_unset=True))))


@api.get("/admin/user/detail/{id}", tags=["用户", "v1"],
         response_model=ResModel[ResUserDetailSchema],
         summary="[v1]获取用户信息表详情接口",
         description="根据ID获取数据")
async def detail(id: int = Path(title="ID不能为空"), current_user: User = Depends(check_current_user),
                 db: Session = Depends(DatabaseManager().get_session)):
    logger.info("[detail][request]:{}".format({"id": id}))
    if id is None:
        raise ParameterException()
    return R.SUCCESS(UserService(db).find_by_id(User(id=id)))


@api.post("/admin/user/login", tags=["用户", "v1"],
          summary="[v1]用户登录",
          description="通过参数模型登录")
async def login(search: ReqUserLoginSchema, db: Session = Depends(DatabaseManager().get_session)):
    logger.info("[find][request]:{}".format(search.model_dump()))
    try:
        rsp = await UserService(db).login(User(**search.model_dump(exclude_unset=True)))
        return R.SUCCESS(rsp)
    except Exception as e:
        logger.error(f"Error login: {str(e)}")
        return R.jsonify(CodeEnum.LOGIN_ERR_PWD)


@api.get("/admin/user/me", tags=["用户", "v1"],
         summary="[v1]获取当前用户详细信息",
         description="检索当前已验证用户的详细信息")
async def get_current_user(token: str = Depends(OAuth2PasswordBearer(tokenUrl="token")),
                           db: Session = Depends(DatabaseManager().get_session)):
    user = await UserService(db).get_user_by_token(token)
    return R.SUCCESS(user)


@api.post("/admin/user/change-password", tags=["用户", "v1"],
          response_model=ResModel,
          summary="[v1]修改用户密码",
          description="验证原密码并修改为新密码")
async def change_password(
        model: ReqChangePasswordSchema,
        current_user: User = Depends(check_current_user),
        db: Session = Depends(DatabaseManager().get_session)
):
    logger.info("[change_password][request]: User ID: {}".format(current_user.id))

    success = await UserService(db).change_password(
        current_user.id,
        model.old_password,
        model.new_password
    )

    if success:
        return R.SUCCESS({"message": "密码修改成功"})
    else:
        raise GlobalBusinessException(500, "原密码不正确")


@api.post("/admin/user/change-password-by-mobile", tags=["用户", "v1"],
          response_model=ResModel,
          summary="[v1]通过手机号和验证码修改密码",
          description="通过手机号和验证码验证身份后修改密码")
async def change_password_by_mobile(
        model: ReqChangePasswordByMobileSchema,
        db: Session = Depends(DatabaseManager().get_session)
):
    logger.info("[change_password_by_mobile][request]: Mobile: {}".format(model.mobile))

    redis_pool = await RedisConnectionPool().get_pool()
    success, message = await UserService(db).change_password_by_mobile(
        model.mobile,
        model.verify_code,
        model.new_password,
        redis_pool
    )

    if success:
        return R.SUCCESS({"message": message})
    else:
        raise GlobalBusinessException(500, message)


@api.post("/admin/user/mobile-login", tags=["用户", "v1"],
          summary="[v1]手机验证码登录",
          description="通过手机号和验证码登录")
async def mobile_login(model: ReqUserMobileLoginSchema,
                       db: Session = Depends(DatabaseManager().get_session),
                       redis_pool: Session = Depends(RedisConnectionPool().get_pool)
                       ):
    logger.info("[mobile_login][request]:{}".format(model.model_dump()))
    is_success, info = await UserService(db).mobile_login(model.mobile, model.verify_code, redis_pool)
    if not is_success:
        return R.PARAMETER_ERR(data={"message": info})
    return R.SUCCESS(info)


@api.post("/admin/user/mobile-register", tags=["用户", "v1"],
          summary="[v1]手机号注册并登录",
          description="通过手机号注册新用户并登录")
async def mobile_register(model: ReqUserMobileRegisterSchema,
                          db: Session = Depends(DatabaseManager().get_session),
                          redis_pool: Session = Depends(RedisConnectionPool().get_pool)
                          ):
    logger.info("[mobile_register][request]:{}".format(model.model_dump()))
    is_success, info = await UserService(db).mobile_register(model, redis_pool)
    if not is_success:
        return R.PARAMETER_ERR(data={"message": info})

    return R.SUCCESS(info)


@api.post("/admin/user/send-verify-code", tags=["用户", "v1"],
          response_model=ResModel,
          summary="[v1]发送手机验证码",
          description="向指定手机号发送验证码")
async def send_verify_code(mobile: str,
                           db: Session = Depends(DatabaseManager().get_session)):
    logger.info("[send_verify_code][request]: mobile={}".format(mobile))

    redis_pool = await RedisConnectionPool().get_pool()
    is_send_sms, info = await UserService(db).send_verify_code(mobile, redis_pool)
    if not is_send_sms:
        return R.PARAMETER_ERR(data={"message": info})
    return R.SUCCESS({"message": info})

@api.post("/admin/user/v2/send-verify-code", tags=["用户", "v2"],
          response_model=ResModel,
          summary="[v2]发送手机验证码",
          description="向指定手机号发送验证码")
async def send_verify_code(mobile: str,
                           db: Session = Depends(DatabaseManager().get_session)):
    logger.info("[send_verify_code][request]: mobile={}".format(mobile))

    redis_pool = await RedisConnectionPool().get_pool()
    is_send_sms, info = await UserService(db).send_verify_code_v2(mobile, redis_pool)
    if not is_send_sms:
        return R.PARAMETER_ERR(data={"message": info})
    return R.SUCCESS({"message": info})

@api.post("/admin/user/recharge", tags=["用户", "v1"],
          response_model=ResModel[ResUserDetailSchema],
          summary="[v1]用户充值",
          description="增加用户剩余点数")
async def recharge_points(
    user_id: int,
    points: float,
    current_user: User = Depends(check_current_user),
    db: Session = Depends(DatabaseManager().get_session)
):
    if current_user.user_type != 1:  # 检查是否是管理员
        raise GlobalBusinessException(403, "没有充值权限")
        
    try:
        updated_user = UserService(db).update_remain_point(user_id, points)
        if not updated_user:
            return R.PARAMETER_ERR(data={"message": "用户不存在"})
        return R.SUCCESS(updated_user)
    except Exception as e:
        logger.error(f"充值失败: {str(e)}")
        raise GlobalBusinessException(500, "充值失败")


@api.post("/admin/user/allocate-points", tags=["用户", "v1"],
          response_model=ResModel[ResUserDetailSchema],
          summary="[v1]用户分配点数给IP",
          description="将用户的点数分配给指定的IP")
async def allocate_points(
    model: ReqAllocatePointsSchema,
    current_user: User = Depends(check_current_user),
    db: Session = Depends(DatabaseManager().get_session)
):
    logger.info(f"[allocate_points][request]: {model.model_dump()}")
    
    # 检查并扣除用户点数
    user_service = UserService(db)
    if not user_service.check_and_deduct_points(current_user.id, model.points):
        return R.PARAMETER_ERR(data={"message": "用户点数不足"})
    
    # 为IP增加点数
    try:
        ip_service = IpService(db)
        updated_ip = ip_service.add_points(model.pid, model.points, str(current_user.id))
        if not updated_ip:
            # 如果IP不存在，回滚用户点数
            user_service.update_remain_point(current_user.id, model.points)
            return R.PARAMETER_ERR(data={"message": "IP不存在"})
            
        return R.SUCCESS({"message": f"成功分配 {model.points} 点数给IP"})
    except Exception as e:
        # 发生错误时回滚用户点数
        user_service.update_remain_point(current_user.id, model.points)
        logger.error(f"分配点数时发生错误: {str(e)}")
        raise GlobalBusinessException(500, "分配点数失败")
