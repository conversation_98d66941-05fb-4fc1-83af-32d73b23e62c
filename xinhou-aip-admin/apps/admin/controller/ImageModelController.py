from fastapi import APIRouter, Depends
from fastapi.params import Path
from loguru import logger
from sqlalchemy.orm import Session
from xinhou_openai_framework.core.context.model.AppContext import AppContext
from xinhou_openai_framework.core.context.model.SystemContext import ctx
from xinhou_openai_framework.core.db.DatabaseManager import DatabaseManager
from xinhou_openai_framework.core.exception.GlobalExceptionType import ParameterException
from xinhou_openai_framework.core.reponse.R import R
from xinhou_openai_framework.core.reponse.ResModel import ResModel
from xinhou_openai_framework.pages.PageHelper import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, PageHelper

from apps.admin.controller.UserController import check_current_user
from apps.admin.schema.ImageModelSchema import (
    ImageModelDetailSchema, ImageModelFindSchema,
    ImageModelSaveSchema, ImageModelUpdateSchema
)
from common.entity.ImageModel import ImageModel
from common.entity.Ip import Ip
from common.entity.User import User
from common.service import WxNotificationService
from common.service.ImageModelService import ImageModelService
from common.service.IpService import IpService

api = APIRouter()


@api.post('/admin/image-model/findAll', tags=["图片制作专属模型", "v1"],
          response_model=ResModel[list[ImageModelDetailSchema]],
          summary="查询所有图片制作专属模型信息接口")
async def find_all(search: ImageModelFindSchema,
                   current_user: User = Depends(check_current_user),
                   db: Session = Depends(DatabaseManager().get_session)):
    logger.info("[findAll][request]:{}".format(search.model_dump()))
    return R.SUCCESS(ImageModelService(db).find_all(search))


@api.post('/admin/image-model/find', tags=["图片制作专属模型", "v1"],
          response_model=ResModel[PageResultHelper[ImageModelDetailSchema]],
          summary="查询图片制作专属模型信息带分页接口")
async def find(search: PageHelper[ImageModelFindSchema],
               current_user: User = Depends(check_current_user),
               db: Session = Depends(DatabaseManager().get_session)):
    logger.info("[find][request]:{}".format(search.model_dump()))
    return R.SUCCESS(ImageModelService(db).find_by(search))


@api.post('/admin/image-model/save', tags=["图片制作专属模型", "v1"],
          response_model=ResModel[ImageModelDetailSchema],
          summary="保存图片制作专属模型信息接口")
async def save(model: ImageModelSaveSchema,
               current_user: User = Depends(check_current_user),
               db: Session = Depends(DatabaseManager().get_session)):
    context: AppContext = ctx.__getattr__("context")
    logger.info("[save][request]:{}".format(model.model_dump()))
    result = ImageModelService(db).save(ImageModel(**model.model_dump()))
    ip = IpService(db).find_by_id(Ip(id=model.pid))
    message = f"[撒花]{current_user.phone}\n（{ip.ip_name}）于{result.created_at.isoformat()}，提交【图片制作专属模型】~\n图片URL：{result.image_url}；专属模型类型：{result.model_type}；口播风格：{result.speech_style}；访谈风格：{result.interview_style}；模型时长：{result.duration}；形象要求备注：{result.appearance_note}；提交状态：{'已提交' if result.submit_status == 1 else '已上传'}~";
    await WxNotificationService.sendImages(message, context)
    return R.SUCCESS(result)


@api.post('/admin/image-model/update', tags=["图片制作专属模型", "v1"],
          response_model=ResModel[ImageModelDetailSchema],
          summary="更新图片制作专属模型信息接口")
async def update(model: ImageModelUpdateSchema,
                 current_user: User = Depends(check_current_user),
                 db: Session = Depends(DatabaseManager().get_session)):
    logger.info("[update][request]:{}".format(model.model_dump()))
    if not model.id:
        raise ParameterException()
    return R.SUCCESS(ImageModelService(db).update_by_id(model.id, model.model_dump(exclude={'id'})))


@api.get('/admin/image-model/delete/{id}', tags=["图片制作专属模型", "v1"],
         response_model=ResModel,
         summary="删除图片制作专属模型信息接口")
async def delete(id: int = Path(..., title="ID"),
                 current_user: User = Depends(check_current_user),
                 db: Session = Depends(DatabaseManager().get_session)):
    logger.info("[delete][request]:{}".format({"id": id}))
    if not id:
        raise ParameterException()
    ImageModelService(db).delete(ImageModel(id=id))
    return R.SUCCESS()


@api.get('/admin/image-model/detail/{id}', tags=["图片制作专属模型", "v1"],
         response_model=ResModel[ImageModelDetailSchema],
         summary="获取图片制作专属模型详情接口")
async def detail(id: int = Path(..., title="ID"),
                 current_user: User = Depends(check_current_user),
                 db: Session = Depends(DatabaseManager().get_session)):
    logger.info("[detail][request]:{}".format({"id": id}))
    if not id:
        raise ParameterException()
    return R.SUCCESS(ImageModelService(db).find_by_id(ImageModel(id=id)))
