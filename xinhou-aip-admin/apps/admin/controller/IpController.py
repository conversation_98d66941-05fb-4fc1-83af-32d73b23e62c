# !/usr/bin/python3
# -*- coding: utf-8 -*-

"""
IP信息表控制器类
----------------------------------------------------
@Project :   xinhou-openai-admin
@File    :   Ip.py
@Contact :   <EMAIL>

@Modify Time      <AUTHOR>    @Desciption
------------      -------    --------    -----------
2023/04/16 22:04  peng.shen   v1.0.0     None
"""

from datetime import datetime

from fastapi import APIRouter, Depends
from fastapi import Query
from fastapi.params import Path
from loguru import logger
from sqlalchemy.orm import Session
from xinhou_openai_framework.core.db.DatabaseManager import DatabaseManager
from xinhou_openai_framework.core.exception.CodeEnum import CodeEnum
from xinhou_openai_framework.core.exception.GlobalExceptionType import ParameterException
from xinhou_openai_framework.core.reponse.R import R
from xinhou_openai_framework.core.reponse.ResModel import ResModel
from xinhou_openai_framework.pages.PageHelper import PageResultHelper, PageHelper

from apps.admin.controller.UserController import check_current_user
from apps.admin.schema.IpSchema import IpBindSchema, IpUnbindSchema, IpBoundInfoSchema, IpResponseSchema
from apps.admin.schema.IpSchema import ResIpDetailSchema, ReqIpFindSchema, ReqIpSaveSchema, ReqIpUpdateSchema
from common.entity.Ip import Ip
from common.entity.User import User
from common.service.IpService import IpService

api = APIRouter()


@api.post('/admin/ip/find', tags=["IP", "v1"],
          response_model=ResModel[PageResultHelper[ResIpDetailSchema]],
          summary="[v1]查询IP信息表信息带分页接口",
          description="通过参数模型传递条件查询")
async def find(search: PageHelper[ReqIpFindSchema],
               current_user: User = Depends(check_current_user),
               db: Session = Depends(DatabaseManager().get_session)):
    logger.info("[find][request]:{}".format(search.model_dump()))
    return R.SUCCESS(IpService(db).find_by(search))


@api.post('/admin/ip/findAll', tags=["IP", "v1"],
          response_model=ResModel[PageResultHelper[ResIpDetailSchema]],
          summary="[v1]查询IP信息表信息接口",
          description="通过参数模型传递条件查询")
async def find_all(search: ReqIpFindSchema,
                   db: Session = Depends(DatabaseManager().get_session)):
    logger.info("[find][request]:{}".format(search.model_dump()))
    return R.SUCCESS(IpService(db).find_all(search))


@api.post("/admin/ip/save", tags=["IP", "v1"],
          response_model=ResModel[ResIpDetailSchema],
          summary="[v1]保存IP信息表信息接口",
          description="通过参数模型保存数据")
async def save(model: ReqIpSaveSchema,
               current_user: User = Depends(check_current_user),
               db: Session = Depends(DatabaseManager().get_session)):
    logger.info("[save][request]:{}".format(model.model_dump_json()))
    model.uid = current_user.id
    ip = IpService(db).save(Ip(**model.model_dump(exclude_unset=True)))
    model.id = ip.id
    return R.SUCCESS(model)


@api.get("/admin/ip/delete/{id}", tags=["IP", "v1"],
         response_model=ResModel,
         summary="[v1]删除IP信息表信息接口",
         description="根据ID删除数据")
async def delete(id: int = Path(title="ID不能为空"),
                 current_user: User = Depends(check_current_user),
                 db: Session = Depends(DatabaseManager().get_session)):
    logger.info("[detail][request]:{}".format({"id": id}))
    if id is None:
        raise ParameterException()
    IpService(db).delete(Ip(id=id))
    return R.SUCCESS()


@api.post("/admin/ip/update", tags=["IP", "v1"],
          response_model=ResModel[ResIpDetailSchema],
          summary="[v1]更新IP信息表信息接口",
          description="根据ID更新数据")
async def update(model: ReqIpUpdateSchema,
                 current_user: User = Depends(check_current_user),
                 db: Session = Depends(DatabaseManager().get_session)):
    logger.info("[save][request]:{}".format(model.json()))
    if model.id is None:
        raise ParameterException()
    return R.SUCCESS(IpService(db).update(Ip(**model.model_dump(exclude_unset=True))))


@api.get("/admin/ip/detail/{id}", tags=["IP", "v1"],
         response_model=ResModel[ResIpDetailSchema],
         summary="[v1]获取IP信息表详情接口",
         description="根据ID获取数据")
async def detail(id: int = Path(title="ID不能为空"),
                 current_user: User = Depends(check_current_user),
                 db: Session = Depends(DatabaseManager().get_session)):
    logger.info("[detail][request]:{}".format({"id": id}))
    if id is None:
        raise ParameterException()
    return R.SUCCESS(IpService(db).find_by_id(Ip(id=id)))


@api.post("/ip/bind", tags=["微信机器人"],
          response_model=IpResponseSchema,
          summary="微信机器人端执行绑定知识库操作", )
async def bind_knowledge_base(request: IpBindSchema, db: Session = Depends(DatabaseManager().get_session)):
    service = IpService(db)
    success, message = service.bind_knowledge_base(request.src_name, request.msg_id, request.ip_name)
    return R.SUCCESS({"success": success, "message": message})


@api.post("/ip/unbind", tags=["微信机器人"],
          response_model=IpResponseSchema,
          summary="微信机器人端执行解绑知识库操作")
async def unbind_knowledge_base(request: IpUnbindSchema, db: Session = Depends(DatabaseManager().get_session)):
    service = IpService(db)
    success, message = service.unbind_knowledge_base(request.msg_id)
    return R.SUCCESS({"success": success, "message": message})


@api.post("/ip/bound_info", tags=["微信机器人"],
          response_model=IpResponseSchema,
          summary="微信机器人查看是否执行绑定知识库操作")
async def get_bound_knowledge_base(request: IpBoundInfoSchema, db: Session = Depends(DatabaseManager().get_session)):
    service = IpService(db)
    success, ip_name = service.get_bound_knowledge_base(request.msg_id)
    message = ip_name if success else "未找到绑定的知识库"
    return R.SUCCESS({"success": success, "message": message})


@api.post("/admin/ip/update_by_name", tags=["IP", "v1"],
          response_model=ResModel[ResIpDetailSchema],
          summary="[v1]通过IP名称更新积分和到期时间",
          description="通过IP名称更新用户的剩余积分和到期时间")
async def update_by_name(
        ip_name: str = Query(..., description="IP名称"),
        remain_point: int = Query(..., description="剩余积分(秒)"),
        expire_time: datetime = Query(..., description="到期时间"),
        current_user: User = Depends(check_current_user),
        db: Session = Depends(DatabaseManager().get_session)
):
    logger.info(f"[update_by_name][request]: ip_name={ip_name}, remain_point={remain_point}, expire_time={expire_time}")

    # 调用service层方法
    result = IpService(db).update_by_name(ip_name, remain_point, expire_time, current_user.login_name)
    if not result:
        # 使用R.FAIL返回错误信息
        return R.jsonify(CodeEnum.ID_NOT_FOUND, f"IP未找到")

    return R.SUCCESS(result)


@api.get("/admin/ip/get_by_name", tags=["IP", "查询", "v1"],
         response_model=ResModel[ResIpDetailSchema],
         summary="[v1]通过IP名称查询IP信息",
         description="通过IP名称查询IP的详细信息，包括剩余积分和到期时间")
async def get_by_name(
        ip_name: str = Query(..., description="IP名称"),
        current_user: User = Depends(check_current_user),
        db: Session = Depends(DatabaseManager().get_session)
):
    logger.info(f"[get_by_name][request]: ip_name={ip_name}")

    # 调用service层方法查询IP信息
    result = IpService(db).get_by_name(ip_name)
    if not result:
        return R.jsonify(CodeEnum.ID_NOT_FOUND, f"IP未找到")
    return R.SUCCESS(result)
