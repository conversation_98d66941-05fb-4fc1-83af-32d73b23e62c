from typing import List

from fastapi import APIRouter, Depends, File, Form, UploadFile
from loguru import logger
from sqlalchemy.orm import Session
from xinhou_openai_framework.core.db.DatabaseManager import DatabaseManager
from xinhou_openai_framework.core.reponse.R import R

from apps.admin.controller.UserController import check_current_user
from apps.admin.schema.ReqRagSchema import ReqHybridSearchSchema, ReqDeleteEmbeddingSchema
from apps.admin.schema.ReqV1TrainingFileUploadSchema import ResV1TrainingFileUploadSchema
from common.entity.User import User
from common.service.RagService import RagService
from common.utils.FilesUtils import save_files_v1_oss

api = APIRouter()


@api.post("/rag/v1/hybrid_search", tags=["RAG"], summary="[RAG]混合向量搜索")
async def hybrid_search(
        search: ReqHybridSearchSchema,
        db: Session = Depends(DatabaseManager().get_session)
):
    try:
        logger.info(f"[hybrid_search] 请求参数: {search.model_dump_json()}")
        rag_service = RagService(db)
        results = await rag_service.hybrid_search(search.model_dump())
        return R.SUCCESS(results)
    except Exception as e:
        logger.error(f"混合搜索发生错误: {str(e)}")
        return R.SERVER_ERROR()


@api.post("/rag/v1/delete_embedding", tags=["RAG"], summary="[RAG]删除嵌入数据",
          description="根据pid和file_id删除嵌入数据")
async def delete_embedding(
        req: ReqDeleteEmbeddingSchema,
        db: Session = Depends(DatabaseManager().get_session),
        current_user: User = Depends(check_current_user)
):
    try:
        logger.info(f"[delete_embedding] 请求参数: {req.model_dump_json()}")
        rag_service = RagService(db)
        result = await rag_service.delete_embedding(req.pid, req.file_id)
        return R.SUCCESS(result)
    except ValueError as e:
        logger.error(f"删除嵌入数据失败: {str(e)}")
        return R.PARAMETER_ERR()
    except Exception as e:
        logger.error(f"删除嵌入数据发生错误: {str(e)}")
        db.rollback()
        return R.SERVER_ERROR()


@api.post('/taskfile/upload', tags=["RAG"],
          response_model=ResV1TrainingFileUploadSchema,
          summary="上传工作流RAG文件",
          description="用于上传文件至工作流RAG")
async def task_file_upload(
        files: List[UploadFile] = File(...),
        pid: int = Form(...),
        emb_type: int = Form(...),
        task_id: int = Form(...),
        knowledge_id: int = Form(...),
        db: Session = Depends(DatabaseManager().get_session)
):
    try:
        file_infos = await save_files_v1_oss(files)
        rag_service = RagService(db)
        datas = await rag_service.upload_file(file_infos, pid, emb_type, task_id, knowledge_id)
        return R.SUCCESS(datas)
    except ValueError as e:
        logger.error(f"上传文件失败: {str(e)}")
        return R.PARAMETER_ERR(msg=str(e))
    except Exception as e:
        db.rollback()
        logger.error(f"上传文件发生错误: {e}")
        return R.SERVER_ERROR()


@api.post('/knowledgefile/upload', tags=["RAG"],
          response_model=ResV1TrainingFileUploadSchema,
          summary="上传个人知识库RAG文件",
          description="用于上传文件至个人知识库RAG")
async def knowledge_file_upload(
        files: List[UploadFile] = File(...),
        pid: int = Form(...),
        emb_type: int = Form(...),
        knowledge_id: int = Form(None),
        db: Session = Depends(DatabaseManager().get_session)
):
    try:
        file_infos = await save_files_v1_oss(files)
        rag_service = RagService(db)
        datas = await rag_service.upload_file(file_infos, pid, emb_type, None, knowledge_id)
        return R.SUCCESS(datas)
    except ValueError as e:
        logger.error(f"上传文件失败: {str(e)}")
        return R.PARAMETER_ERR(msg=str(e))
    except Exception as e:
        db.rollback()
        logger.error(f"上传文件发生错误: {e}")
        return R.SERVER_ERROR()
