# !/usr/bin/python3
# -*- coding: utf-8 -*-

"""
任务控制器
----------------------------------------------------
@Project :   xinhou-openai-admin
@File    :   TaskController.py
@Contact :   <EMAIL>

@Modify Time      <AUTHOR>    @Desciption
------------      -------    --------    -----------
2023/07/18 17:32  ChatGPT    v1.0.0      None
2024/07/18 18:30  ChatGPT    v1.1.0      添加CRUD操作
"""
from typing import List

from fastapi import APIRouter, Depends, HTTPException, Path
from loguru import logger
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.orm import Session
from xinhou_openai_framework.core.db.DatabaseManager import DatabaseManager
from xinhou_openai_framework.core.decorator.NoLogger import NoLogger
from xinhou_openai_framework.core.exception.GlobalExceptionType import ParameterException
from xinhou_openai_framework.core.reponse.R import R
from xinhou_openai_framework.core.reponse.ResModel import ResModel
from xinhou_openai_framework.pages.PageHelper import PageResultHelper, PageHelper

from apps.admin.controller.UserController import check_current_user
from apps.admin.schema.TaskSchema import (
    ReqTaskQuerySchema, ResTaskDetailSchema, ReqTaskFindSchema, ReqTaskSaveSchema,
    ReqTaskUpdateSchema, ResTaskStatusSchema
)
from common.entity.Task import Task
from common.entity.User import User
from common.service.TaskService import TaskService

api = APIRouter()


@api.get("/admin/task/latest/{pid}", tags=["任务", "v1"],
         response_model=ResModel,
         summary="[v1][task][latest]查询最新任务列表接口",
         description="查询24小时内最新的5条记录")
@NoLogger
async def get_latest_tasks(
        pid: int = Path(title="PID不能为空"),
        current_user: User = Depends(check_current_user),
        db: Session = Depends(DatabaseManager().get_session)):
    try:
        task_service = TaskService(db)
        result = await task_service.get_latest_tasks(pid)
        return R.SUCCESS(result)
    except SQLAlchemyError as e:
        logger.error(f"Database error: {str(e)}")
        return R.SAVE_ERROR()
    except Exception as e:
        logger.error(f"Unexpected error: {str(e)}")
        return R.SERVER_ERROR()
    return R.SUCCESS(result)


@api.post("/admin/task/query", tags=["任务", "v1"],
          response_model=ResModel,
          summary="[v1][task][query]查询任务列表接口",
          description="根据日期和PID查询任务列表")
async def query(req: ReqTaskQuerySchema,
                current_user: User = Depends(check_current_user),
                db: Session = Depends(DatabaseManager().get_session)):
    task_service = TaskService(db)
    result = await task_service.query_tasks(req.query_date, req.pid)
    return R.SUCCESS(result)


@api.post('/admin/task/find', tags=["任务", "v1"],
          response_model=ResModel[PageResultHelper[ResTaskDetailSchema]],
          summary="[v1][task][find]查询任务信息表信息带分页接口",
          description="通过参数模型传递条件查询")
async def find(search: PageHelper[ReqTaskFindSchema],
               current_user: User = Depends(check_current_user),
               db: Session = Depends(DatabaseManager().get_session)):
    return R.SUCCESS(TaskService(db).find_by(search))


@api.post('/admin/task/findAll', tags=["任务", "v1"],
          response_model=ResModel[List[ResTaskDetailSchema]],
          summary="[v1][task][findAll]查询任务信息表信息接口",
          description="通过参数模型传递条件查询")
async def find_all(search: ReqTaskFindSchema,
                   db: Session = Depends(DatabaseManager().get_session)):
    return R.SUCCESS(TaskService(db).find_all(search))


@api.post("/admin/task/save", tags=["任务", "v1"],
          response_model=ResModel[ResTaskDetailSchema],
          summary="[v1][task][save]保存任务信息表信息接口",
          description="通过参数模型保存数据，支持音频和视频模型ID")
async def save(model: ReqTaskSaveSchema,
               current_user: User = Depends(check_current_user),
               db: Session = Depends(DatabaseManager().get_session)):
    task = TaskService(db).save(Task(**model.model_dump(exclude_unset=True)))
    return R.SUCCESS(task)


@api.get("/admin/task/delete/{id}", tags=["任务", "v1"],
         response_model=ResModel,
         summary="[v1][task][delete]删除任务信息表信息接口",
         description="根据ID删除数据")
async def delete(id: int = Path(title="ID不能为空"),
                 current_user: User = Depends(check_current_user),
                 db: Session = Depends(DatabaseManager().get_session)):
    if id is None:
        raise ParameterException()
    return R.SUCCESS(TaskService(db).delete(Task(id=id)))


@api.post("/admin/task/update", tags=["任务", "v1"],
          response_model=ResModel[ResTaskDetailSchema],
          summary="[v1][task][update]更新任务信息表信息接口",
          description="根据ID更新数据，支持音频和视频模型ID")
async def update(model: ReqTaskUpdateSchema,
                 current_user: User = Depends(check_current_user),
                 db: Session = Depends(DatabaseManager().get_session)):
    if model.id is None:
        raise ParameterException()
    return R.SUCCESS(TaskService(db).update(Task(**model.model_dump(exclude_unset=True))))


@api.get("/admin/task/detail/{id}",
         tags=["任务", "查询", "v1"],
         response_model=ResModel[ResTaskDetailSchema],
         summary="[v1][task][detail]获取任务信息表详情接口",
         description="根据ID获取数据")
async def detail(id: int = Path(title="ID不能为空"),
                 db: Session = Depends(DatabaseManager().get_session)):
    if id is None:
        raise ParameterException()
    return R.SUCCESS(TaskService(db).find_by_id(Task(id=id)))


@api.get("/admin/task/status/{task_id}",
         tags=["任务", "查询", "v1"],
         response_model=ResModel[ResTaskStatusSchema],
         summary="[v1]查询任务状态及相关信息",
         description="根据任务ID查询任务状态、模型信息及生成文件信息")
async def get_task_status(
        task_id: int = Path(..., title="任务ID"),
        db: Session = Depends(DatabaseManager().get_session)
):
    try:
        task_service = TaskService(db)
        result = await task_service.get_task_status(task_id)
        return R.SUCCESS(result)
    except Exception as e:
        logger.error(f"查询任务状态失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))
