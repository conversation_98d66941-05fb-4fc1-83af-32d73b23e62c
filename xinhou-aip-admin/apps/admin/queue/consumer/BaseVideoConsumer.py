import asyncio
from typing import Optional

from loguru import logger
from sqlalchemy.orm import Session
from xinhou_openai_framework.core.db.DatabaseManager import DatabaseManager
from xinhou_openai_framework.core.queue.consumer.RedisConsumerSupport import ConsumerServiceSupport
from xinhou_openai_framework.core.queue.message.RedisMessageModel import RedisMessageModel

from common.service import WxNotificationService


class BaseVideoConsumer(ConsumerServiceSupport):
    """视频处理消费者基类"""

    RETRY_INTERVAL = 20  # 重试间隔（秒）

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.db: Optional[Session] = None
        self.db_manager = DatabaseManager.get_instance()

    def _get_db_session(self) -> Session:
        """
        获取数据库会话
        Returns:
            Session: SQLAlchemy会话对象
        """
        try:
            return next(self.db_manager.get_session())
        except Exception as e:
            logger.error(f"获取数据库会话失败: {str(e)}")
            raise

    async def _notify_error(self, error_msg: str):
        """
        发送错误通知
        Args:
            error_msg: 错误信息
        """
        try:
            # 格式化错误消息
            formatted_message = (
                f"项目名: xinhou-aip\n"
                f"服务名: xinhou-aip-admin\n"
                f"请求方法: {self.__class__.__name__}.process_message\n"
                f"等级: P1\n"
                f"异常内容: {error_msg}"
            )

            # 发送通知
            await WxNotificationService.sendImages(formatted_message)

        except Exception as e:
            logger.error(f"发送错误通知失败: {str(e)}")

    async def handler(self):
        """重写处理方法，增加重试逻辑"""
        while True:
            try:
                # 尝试获取锁
                if not await self.acquire_lock():
                    await asyncio.sleep(1)
                    continue

                try:
                    # 获取消息
                    message = await self.get_message()
                    if not message:
                        await asyncio.sleep(1)
                        continue

                    # 处理消息
                    should_remove = await self.process_message(message)

                    if should_remove:
                        # 如果返回 True，从队列中移除消息
                        await self.remove_message(message)
                    else:
                        # 如果返回 False，等待一段时间后重试
                        logger.info(f"Message processing not complete, will retry in {self.RETRY_INTERVAL} seconds")
                        # 先将消息重新放入队列尾部
                        await self.requeue_message(message)
                        # 从当前位置移除消息
                        await self.remove_message(message)
                        # 等待重试间隔
                        await asyncio.sleep(self.RETRY_INTERVAL)

                finally:
                    # 释放锁
                    await self.release_lock()
                    # 清理数据库连接
                    self.cleanup()

            except Exception as e:
                logger.error(f"Error in message handler: {str(e)}")
                await asyncio.sleep(1)

    async def requeue_message(self, message: RedisMessageModel):
        """将消息重新放入队列尾部"""
        try:
            await self.redis.lpush(self.queue_name, message.json())
            logger.debug(f"Message requeued to {self.queue_name}: {message}")
        except Exception as e:
            logger.error(f"Failed to requeue message: {str(e)}")
            raise

    async def get_message(self):
        """获取消息"""
        try:
            message = await self.redis.rpop(self.queue_name)
            if message:
                return RedisMessageModel.parse_raw(message)
            return None
        except Exception as e:
            logger.error(f"Error getting message: {str(e)}")
            return None

    async def remove_message(self, message: RedisMessageModel):
        """从队列中移除消息"""
        try:
            # 实际上不需要做任何事情，因为消息已经通过 LPOP 移除
            pass
        except Exception as e:
            logger.error(f"Error removing message: {str(e)}")

    def cleanup(self):
        """清理资源"""
        if self.db:
            try:
                self.db.close()
            except Exception as e:
                logger.error(f"关闭数据库连接失败: {str(e)}")
            finally:
                self.db = None

    def __del__(self):
        """析构函数，确保资源被正确释放"""
        self.cleanup()
