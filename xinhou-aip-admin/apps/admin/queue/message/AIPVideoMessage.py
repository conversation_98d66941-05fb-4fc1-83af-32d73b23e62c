from typing import Optional, Union

from pydantic import BaseModel, Field


class AIPVideoMessage(BaseModel):
    """
    视频处理消息模型
    """
    task_id: int = Field(..., title="任务ID")
    video_job_id: Union[str, int] = Field(..., title="视频任务ID")
    pid: Optional[int] = Field(default=None, title="项目ID")
    title: Optional[str] = Field(default=None, title="标题")
    url: str = Field(..., title="视频服务地址")
    uuid: str = Field(..., title="视频专家 uuid")
    redis_key: Optional[str] = Field(default=None, title="redis key")