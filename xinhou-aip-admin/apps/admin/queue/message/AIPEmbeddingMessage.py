# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
功能描述
----------------------------------------------------
@Project :   xinhou-openai-admin
@File    :   V4ReqTrainingChatSchema.py
@Contact :   <EMAIL>

@Modify Time      <AUTHOR>    @Desciption
------------      -------    --------    -----------
2023/6/13 21:52   logic   1.0         None
"""
from typing import Optional

from pydantic import BaseModel, Field
from pydantic.v1 import Required


class AIPEmbeddingMessage(BaseModel):
    """
    Summary 总结入参模型
    """

    id: int = Field(
        default=Required, title="文件 id"
    )

    pid: Optional[int] = Field(
        default=None, title="ipid"
    )

    task_id: Optional[int] = Field(
        default=None, title="任务id"
    )

    file_url: Optional[str] = Field(
        default=None, title="文件下载地址",
        description="文件下载地址"
    )

    callback_url: Optional[str] = Field(
        default=None, title="业务处理完成后的结果回调地址",
        description="需要外网访问地址"
    )
