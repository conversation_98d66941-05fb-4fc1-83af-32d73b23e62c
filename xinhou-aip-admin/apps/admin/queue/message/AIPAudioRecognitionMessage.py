#!/usr/bin/python3
# -*- coding: utf-8 -*-
"""
功能描述：音视频文件识别消息模型
----------------------------------------------------
@Project :   xinhou-openai-admin
@File    :   AIPAudioRecognitionMessage.py
@Contact :   <EMAIL>

@Modify Time      <AUTHOR>    @Desciption
------------      -------    --------    -----------
2023/6/14 16:52   logic      1.0         音视频文件识别消息模型
"""
from typing import Optional

from pydantic import BaseModel, Field
from pydantic.v1 import Required


class AIPAudioRecognitionMessage(BaseModel):
    """
    音视频文件识别消息模型，用于异步处理
    """
    file_id: int = Field(
        default=Required, title="文件 id"
    )
    task_id: str = Field(
        default=Required, title="语音任务 id"
    )
    pid: Optional[int] = Field(
        default=None, title="ipid"
    )
    knowledge_id: Optional[int] = Field(
        default=None, title="知识库ID",
        description="关联的知识库ID"
    )
    file_url: str = Field(
        default=Required, title="文件下载地址",
        description="文件下载地址"
    )
    file_format: Optional[str] = Field(
        default="wav", title="文件格式",
        description="音视频文件格式：wav、mp3等"
    )
    sample_rate: Optional[int] = Field(
        default=16000, title="采样率",
        description="音频采样率，默认16000Hz"
    )
    enable_words: Optional[bool] = Field(
        default=False, title="是否输出词信息",
        description="是否启用词级别时间戳"
    )
    auto_split: Optional[bool] = Field(
        default=False, title="是否启用智能分轨",
        description="是否启用智能分轨功能"
    )
    callback_url: Optional[str] = Field(
        default=None, title="业务处理完成后的结果回调地址",
        description="需要外网访问地址"
    )
    new_aweme_ids: Optional[str] = Field(
        default=None, title="新抖音视频ID",
        description="新抖音视频ID"
    )
    cover_url: Optional[str] = Field(
        default=None, title="封面",
        description="封面"
    )
    title: Optional[str] = Field(
        default=None, title="视频标题",
        description="视频标题"
    )
    avatar_url: Optional[str] = Field(
        default=None, title="作者头像",
        description="作者头像"
    )
    author_name: Optional[str] = Field(
        default=None, title="作者名称",
        description="作者名称"
    )
