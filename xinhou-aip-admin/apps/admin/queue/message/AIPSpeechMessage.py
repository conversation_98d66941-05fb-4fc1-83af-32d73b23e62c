# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
语音合成消息类
----------------------------------------------------
@Project :   xinhou-aip-admin
@File    :   AIPSpeechMessage.py
@Contact :   <EMAIL>

@Modify Time      <AUTHOR>    @Desciption
------------      -------    --------    -----------
2024/03/21 10:04   chenlong   v1.0.0     None
"""
import json
from dataclasses import dataclass, asdict
from typing import Optional, Dict

from apps.admin.schema.CommonV1ReqAgentsSchema import WorkflowInfo


@dataclass
class AIPSpeechMessage:
    """语音合成消息类"""
    task_id: int  # task id
    pid: int  # project id
    is_pass: Optional[int] = 0
    query: Optional[str] = None  # 查询内容
    language: Optional[str] = None  # 语言
    doc_length: Optional[int] = None  # 文档长度
    agent_uuid: Optional[str] = None  # agent uuid
    audio_url: Optional[str] = None  # 音频URL
    video_url: Optional[str] = None  # 视频URL
    audio_model_id: Optional[int] = None  # 音频模型ID
    video_model_id: Optional[int] = None  # 视频模型ID
    video_model_pic_url: Optional[str] = None
    voice_is_upload: Optional[int] = None
    voice_upload_url: Optional[str] = None
    workflow: Optional[WorkflowInfo] = None  # 工作流信息
    base_url: Optional[str] = None  # 远程服务基础URL
    request_data: Optional[Dict] = None  # 原始请求数据
    is_person: Optional[int] = None
    is_search: Optional[int] = None
    is_rag: Optional[int] = None
    is_knowledge: Optional[int] = None
    style: Optional[str] = None
    read_score: Optional[int] = None

    def to_dict(self) -> dict:
        """将对象转换为字典"""
        data = asdict(self)
        # 处理 workflow 对象
        if self.workflow:
            if hasattr(self.workflow, 'model_dump'):
                data['workflow'] = self.workflow.model_dump()
            elif hasattr(self.workflow, 'model_dump_json'):
                data['workflow'] = json.loads(self.workflow.model_dump_json())
            elif hasattr(self.workflow, 'dict') and callable(self.workflow.dict):
                data['workflow'] = self.workflow.dict()
            elif hasattr(self.workflow, '__dict__'):
                data['workflow'] = {k: v for k, v in self.workflow.__dict__.items() if not k.startswith('_')}
        return data

    @classmethod
    def from_request_data(cls, task_id: int, pid: int, request_data: dict,
                          base_url: str) -> 'AIPSpeechMessage':
        """从请求数据创建消息实例"""
        workflow_data = request_data.get('workflow')
        if workflow_data and isinstance(workflow_data, dict):
            workflow = WorkflowInfo(**workflow_data)
        else:
            workflow = None

        return cls(
            task_id=task_id,
            pid=pid,
            query=request_data.get('query'),
            is_pass=request_data.get('is_pass'),
            language=request_data.get('language'),
            doc_length=request_data.get('doc_length'),
            agent_uuid=request_data.get('agent_uuid'),
            audio_url=request_data.get('audio_url'),
            video_url=request_data.get('video_url'),
            audio_model_id=request_data.get('audio_model_id'),
            video_model_id=request_data.get('video_model_id'),
            video_model_pic_url=request_data.get('video_model_pic_url'),
            voice_is_upload=request_data.get('voice_is_upload'),
            voice_upload_url=request_data.get('voice_upload_url'),
            is_person=request_data.get('is_person'),
            is_search=request_data.get('is_search'),
            is_rag=request_data.get('is_rag'),
            style=request_data.get('style'),
            read_score=request_data.get('read_score'),
            is_knowledge = request_data.get('is_knowledge'),
            workflow=workflow,
            base_url=base_url,
            request_data=request_data
        )
