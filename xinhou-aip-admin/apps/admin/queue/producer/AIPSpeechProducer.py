# !/usr/bin/python3
# -*- coding: utf-8 -*-

"""
语音合成生产者
----------------------------------------------------
@Project :   xinhou-aip-admin
@File    :   AIPSpeechProducer.py
@Contact :   <EMAIL>

@Modify Time      <AUTHOR>    @Desciption
------------      -------    --------    -----------
2024/03/21 10:04   chenlong   v1.0.0     None
"""

import json
from typing import Optional, Any
from dataclasses import asdict
from datetime import datetime, date

from loguru import logger
from xinhou_openai_framework.core.cache.RedisConnectionPool import RedisConnectionPool

from apps.admin.queue.message.AIPSpeechMessage import AIPSpeechMessage


class CustomJSONEncoder(json.JSONEncoder):
    """自定义JSON编码器"""
    def default(self, obj):
        if hasattr(obj, 'model_dump'):
            # 处理 Pydantic 模型
            return obj.model_dump()
        elif hasattr(obj, 'model_dump_json'):
            # 处理 Pydantic 模型（另一种方法）
            return json.loads(obj.model_dump_json())
        elif hasattr(obj, 'dict') and callable(obj.dict):
            # 处理有dict方法的对象
            return obj.dict()
        elif hasattr(obj, 'to_dict') and callable(obj.to_dict):
            # 处理有to_dict方法的对象
            return obj.to_dict()
        elif hasattr(obj, '__dict__'):
            # 处理普通对象
            return {k: v for k, v in obj.__dict__.items() if not k.startswith('_')}
        elif isinstance(obj, bytes):
            # 处理字节类型
            return obj.decode('utf-8')
        elif isinstance(obj, (datetime, date)):
            # 处理日期时间类型
            return obj.isoformat()
        return super().default(obj)


class AIPSpeechProducer:
    """语音合成生产者"""

    @staticmethod
    async def speech_push(queue_name: str, message: AIPSpeechMessage) -> bool:
        """
        推送消息到队列
        :param queue_name: 队列名称
        :param message: 消息内容
        :return: 是否成功
        """
        try:
            redis_client = await RedisConnectionPool().get_pool()
            
            # 使用消息对象的to_dict方法转换为字典
            message_dict = message.to_dict()
            
            # 使用自定义编码器进行JSON序列化
            message_str = json.dumps(message_dict, cls=CustomJSONEncoder)
            
            # 推送到Redis队列
            await redis_client.lpush(queue_name, message_str)
            logger.info(f"Successfully pushed message to queue {queue_name}: {message.task_id}")
            return True
        except Exception as e:
            logger.error(f"Error pushing message to queue {queue_name}: {str(e)}")
            return False

    @staticmethod
    async def speech_all_fail_messages(queue_name: str, pid: Optional[int] = None) -> list:
        """
        获取所有失败的消息
        :param queue_name: 队列名称
        :param pid: 项目ID
        :return: 失败消息列表
        """
        try:
            redis_client = await RedisConnectionPool().get_pool()
            fail_queue = f"{queue_name}:fail"
            
            # 获取所有失败消息
            messages = []
            while True:
                message = await redis_client.rpop(fail_queue)
                if not message:
                    break
                    
                # 解码消息内容
                if isinstance(message, bytes):
                    message = message.decode('utf-8')
                message_dict = json.loads(message)
                
                if pid is None or message_dict.get('pid') == pid:
                    messages.append(message_dict)
                else:
                    # 如果不匹配pid，将消息放回队列
                    await redis_client.lpush(fail_queue, message)
                    
            return messages
        except Exception as e:
            logger.error(f"Error getting fail messages from queue {queue_name}: {str(e)}")
            return []
