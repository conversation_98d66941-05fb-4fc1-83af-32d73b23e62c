#!/usr/bin/python3
# -*- coding: utf-8 -*-
"""
音视频文件识别功能描述
----------------------------------------------------
@Project :   xinhou-openai-admin
@File    :   AIPAudioRecognitionProducer.py
@Contact :   <EMAIL>

@Modify Time      <AUTHOR>    @Desciption
------------      -------    --------    -----------
2023/6/14 16:52   logic      1.0         音视频文件识别生产者
"""
import logging
from typing import List

from xinhou_openai_framework.core.queue.message.RedisMessageModel import RedisMessageModel
from xinhou_openai_framework.core.queue.producer.RedisProducerSupport import ProducerServiceSupport
from xinhou_openai_framework.utils.DateUtil import DateUtil
from xinhou_openai_framework.utils.IdUtil import IdUtil

from apps.admin.queue.message.AIPAudioRecognitionMessage import AIPAudioRecognitionMessage


class AIPAudioRecognitionProducer(ProducerServiceSupport):

    @staticmethod
    async def recognition_push(queue_name, content: AIPAudioRecognitionMessage):
        """
        业务方法，发送音视频识别执行消息
        """
        await AIPAudioRecognitionProducer.process_push(queue_name, [
            RedisMessageModel(key=IdUtil.uuid_32(), content=content,
                              timestamp=DateUtil.get_current_timestamp())
        ])
        logging.info(f"发送到队列的音视频识别信息为{content}")

    @staticmethod
    async def recognition_all_fail_messages(queue_name, pid) -> List[RedisMessageModel]:
        """
        根据租户ID获取该租户下的所有失败的音视频识别请求消息队列列表
        """
        all_fail_messages = await AIPAudioRecognitionProducer.get_all_fail_messages(queue_name)
        fail_messages: List[RedisMessageModel] = []
        for message in all_fail_messages:
            recognition_message = AIPAudioRecognitionMessage(**message.content)
            if pid:
                if recognition_message.pid == pid:
                    fail_messages.append(message)
            else:
                fail_messages.append(message)
        return fail_messages 