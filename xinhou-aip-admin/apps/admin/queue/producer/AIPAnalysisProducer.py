# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
OpenAI文档训练&QA功能描述（Milvus）
----------------------------------------------------
@Project :   xinhou-openai-admin
@File    :   AIPAnalysisProducer.py
@Contact :   <EMAIL>

@Modify Time      <AUTHOR>    @Desciption
------------      -------    --------    -----------
2023/6/5 10:47   logic   1.0         None
"""
import logging
from typing import List

from xinhou_openai_framework.core.queue.message.RedisMessageModel import RedisMessageModel
from xinhou_openai_framework.core.queue.producer.RedisProducerSupport import ProducerServiceSupport
from xinhou_openai_framework.utils.DateUtil import DateUtil
from xinhou_openai_framework.utils.IdUtil import IdUtil

from apps.admin.queue.message.AIPAnalysisMessage import AIPAnalysisMessage


class AIPAnalysisProducer(ProducerServiceSupport):

    @staticmethod
    async def analysis_push(queue_name, content: AIPAnalysisMessage):
        """
        业务方法，发送总结执行消息
        """
        await AIPAnalysisProducer.process_push(queue_name, [
            RedisMessageModel(key=IdUtil.uuid_32(), content=content,
                              timestamp=DateUtil.get_current_timestamp())
        ])
        logging.info(f"发送到队伍的信息为{content}")

    @staticmethod
    async def analysis_all_fail_messages(queue_name, pid) -> List[RedisMessageModel]:
        """
        根据租户ID获取 该租户下的所有 失败的总结请求消息队列列表
        """
        all_fail_messages = await AIPAnalysisProducer.get_all_fail_messages(queue_name)
        fail_messages: List[RedisMessageModel] = []
        for message in all_fail_messages:
            analysis_message = AIPAnalysisMessage(**message.content)
            if pid:
                if analysis_message.pid == pid:
                    fail_messages.append(message)
            else:
                fail_messages.append(message)
        return fail_messages
