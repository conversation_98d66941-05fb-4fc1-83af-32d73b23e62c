from typing import List

from xinhou_openai_framework.core.queue.message.RedisMessageModel import RedisMessageModel
from xinhou_openai_framework.core.queue.producer.RedisProducerSupport import ProducerServiceSupport
from xinhou_openai_framework.utils.DateUtil import DateUtil
from xinhou_openai_framework.utils.IdUtil import IdUtil

from apps.admin.queue.message.AIPVideoMessage import AIPVideoMessage


class AIPVideoProducer(ProducerServiceSupport):
    @staticmethod
    async def video_push(queue_name, content: AIPVideoMessage):
        """
        发送视频处理消息
        """
        await AIPVideoProducer.process_push(queue_name, [
            RedisMessageModel(
                key=IdUtil.uuid_32(),
                content=content,
                timestamp=DateUtil.get_current_timestamp()
            )
        ])

    @staticmethod
    async def video_all_fail_messages(queue_name, pid) -> List[RedisMessageModel]:
        """
        获取指定租户下所有失败的视频处理消息
        """
        all_fail_messages = await AIPVideoProducer.get_all_fail_messages(queue_name)
        fail_messages: List[RedisMessageModel] = []
        for message in all_fail_messages:
            video_message = AIPVideoMessage(**message.content)
            if pid:
                if video_message.pid == pid:
                    fail_messages.append(message)
            else:
                fail_messages.append(message)
        return fail_messages 