# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
OpenAI文档训练&QA功能描述（Milvus）
----------------------------------------------------
@Project :   xinhou-openai-admin
@File    :   AIPEmbeddingProducer.py
@Contact :   <EMAIL>

@Modify Time      <AUTHOR>    @Desciption
------------      -------    --------    -----------
2023/6/5 10:47   logic   1.0         None
"""
from typing import List

from xinhou_openai_framework.core.queue.message.RedisMessageModel import RedisMessageModel
from xinhou_openai_framework.core.queue.producer.RedisProducerSupport import ProducerServiceSupport
from xinhou_openai_framework.utils.DateUtil import DateUtil
from xinhou_openai_framework.utils.IdUtil import IdUtil

from apps.admin.queue.message.AIPEmbeddingMessage import AIPEmbeddingMessage


class AIPEmbeddingProducer(ProducerServiceSupport):
    @classmethod
    async def embedding_push(cls, queue_name: str, content: AIPEmbeddingMessage):
        """
        发送 embedding 执行消息到队列
        """
        await AIPEmbeddingProducer.process_push(queue_name, [
            RedisMessageModel(key=IdUtil.uuid_32(), content=content.dict(),
                              timestamp=DateUtil.get_current_timestamp())
        ])

    @staticmethod
    async def embedding_all_fail_messages(queue_name, pid) -> List[AIPEmbeddingMessage]:
        """
        根据租户ID获取 该租户下的所有 失败的总结请求消息队列列表
        """
        all_fail_messages = await AIPEmbeddingProducer.get_all_fail_messages(queue_name)
        fail_messages: List[AIPEmbeddingMessage] = []
        for message in all_fail_messages:
            embedding_message = AIPEmbeddingMessage(**message.content)
            if embedding_message.pid == pid:
                fail_messages.append(embedding_message)
        return fail_messages
