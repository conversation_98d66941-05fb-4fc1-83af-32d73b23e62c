# !/usr/bin/python3
# -*- coding: utf-8 -*-

from typing import Optional

from pydantic import BaseModel, Field


class ReqV1AdminDocumentQuerySchema(BaseModel):
    query: Optional[str] = Field(..., title="查询内容")
    ip_name: Optional[str] = Field(default=None, title="IP名称")
    content_classify: Optional[str] = Field(default=None, title="内容分类")
    top_k: Optional[int] = Field(default=None, title="查询数量")
    cosine_thresh: Optional[float] = Field(default=None, title="余弦相似度阈值")


class ResV1AdminDocumentQuerySchema(BaseModel):
    """
    出参模型
    """
    doc: str = Field(default=None, title="文件名称")
    vec: str = Field(default=None, title="文件向量")
