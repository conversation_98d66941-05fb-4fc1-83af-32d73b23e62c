from datetime import datetime
from typing import Optional

from pydantic import BaseModel, Field


class ResIpDetailSchema(BaseModel):
    id: int = Field(..., title="ID")
    uid: Optional[str] = Field(None, title="用户ID")
    ip_name: Optional[str] = Field(None, title="IP名称")
    avatar: Optional[str] = Field(None, title="头像")
    status: Optional[int] = Field(1, title="状态")
    del_flag: Optional[int] = Field(1, title="删除标志")
    create_by: Optional[str] = Field(None, title="创建者")
    created_at: Optional[datetime] = Field(None, title="创建时间")
    update_by: Optional[str] = Field(None, title="更新者")
    updated_at: Optional[datetime] = Field(None, title="更新时间")
    remark: Optional[str] = Field(None, title="备注")
    group_id: Optional[str] = Field(None, title="群组ID")
    group_name: Optional[str] = Field(None, title="群组名称")

    class Config:
        from_attributes = True


class ReqIpFindSchema(BaseModel):
    uid: Optional[str] = Field(None, title="用户ID")
    ip_name: Optional[str] = Field(None, title="IP名称")
    status: Optional[int] = Field(None, title="状态")
    del_flag: Optional[int] = Field(1, title="删除标志")


class ReqIpSaveSchema(BaseModel):
    uid: str = Field(..., title="用户ID")
    id: Optional[int] = Field(None, title="id")
    ip_name: Optional[str] = Field(None, title="IP名称")
    avatar: Optional[str] = Field(None, title="头像")
    status: Optional[int] = Field(1, title="状态")
    del_flag: Optional[int] = Field(1, title="删除标志")
    create_by: Optional[str] = Field(None, title="创建者")
    remark: Optional[str] = Field(None, title="备注")
    remain_point: Optional[int] = Field(None, title="用户可使用剩余积分(s)")
    expire_time: Optional[datetime] = Field(None, title="对应IP到期时间")


class ReqIpUpdateSchema(BaseModel):
    id: int = Field(..., title="ID")
    uid: Optional[str] = Field(None, title="用户ID")
    ip_name: Optional[str] = Field(None, title="IP名称")
    avatar: Optional[str] = Field(None, title="头像")
    status: Optional[int] = Field(None, title="状态")
    del_flag: Optional[int] = Field(1, title="删除标志")
    update_by: Optional[str] = Field(None, title="更新者")
    remark: Optional[str] = Field(None, title="备注")
    remain_point: Optional[int] = Field(None, title="用户可使用剩余积分(s)")
    expire_time: Optional[datetime] = Field(None, title="对应IP到期时间")


class IpBindSchema(BaseModel):
    src_name: str
    msg_id: str
    ip_name: str


class IpUnbindSchema(BaseModel):
    msg_id: str


class IpBoundInfoSchema(BaseModel):
    msg_id: str


class IpResponseSchema(BaseModel):
    success: bool
    message: str
