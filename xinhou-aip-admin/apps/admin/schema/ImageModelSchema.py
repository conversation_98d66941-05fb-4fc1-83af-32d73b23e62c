from datetime import datetime
from typing import Optional

from pydantic import BaseModel, Field


class ImageModelDetailSchema(BaseModel):
    id: int = Field(..., title="ID")
    pid: Optional[int] = Field(None, title="父ID")
    image_url: Optional[str] = Field(None, title="图片URL")
    model_type: str = Field(..., title="专属模型类型")
    speech_style: str = Field(None, title="口播风格")
    interview_style: str = Field(None, title="访谈风格")
    duration: str = Field(..., title="模型时长")
    appearance_note: Optional[str] = Field(None, title="形象要求备注")
    status: Optional[int] = Field(1, title="状态")
    del_flag: Optional[int] = Field(1, title="删除标志")
    create_by: Optional[str] = Field(None, title="创建者")
    created_at: Optional[datetime] = Field(None, title="创建时间")
    update_by: Optional[str] = Field(None, title="更新者")
    updated_at: Optional[datetime] = Field(None, title="更新时间")
    submit_status: int = Field(1, title='状态:1=已提交,2=已上传')
    upload_time: Optional[datetime] = Field(None, title='上传时间')

    class Config:
        from_attributes = True


class ImageModelFindSchema(BaseModel):
    pid: Optional[int] = Field(None, title="父ID")
    model_type: Optional[str] = Field(None, title="专属模型类型")
    speech_style: Optional[str] = Field(None, title="口播风格")
    interview_style: Optional[str] = Field(None, title="访谈风格")
    status: Optional[int] = Field(None, title="状态")
    del_flag: Optional[int] = Field(1, title="删除标志")


class ImageModelSaveSchema(BaseModel):
    pid: Optional[int] = Field(None, title="父ID")
    image_url: str = Field(..., title="图片URL")
    model_type: str = Field(..., title="专属模型类型")
    speech_style: Optional[str] = Field(None, title="口播风格")
    interview_style: Optional[str] = Field(None, title="访谈风格")
    duration: str = Field(..., title="模型时长")
    appearance_note: Optional[str] = Field(None, title="形象要求备注")
    status: Optional[int] = Field(1, title="状态")
    submit_status: Optional[int] = Field(1, title='状态:1=已提交,2=已上传')
    client_ip: Optional[str] = Field(None, title="客户端IP")
    device: Optional[str] = Field(None, title="设备类型")


class ImageModelUpdateSchema(BaseModel):
    id: int = Field(..., title="ID")
    pid: Optional[int] = Field(None, title="父ID")
    image_url: Optional[str] = Field(None, title="图片URL")
    model_type: Optional[str] = Field(None, title="专属模型类型")
    speech_style: Optional[str] = Field(None, title="口播风格")
    interview_style: Optional[str] = Field(None, title="访谈风格")
    duration: Optional[str] = Field(None, title="模型时长")
    appearance_note: Optional[str] = Field(None, title="形象要求备注")
    status: Optional[int] = Field(None, title="状态")
