from datetime import datetime
from typing import Optional

from pydantic import BaseModel, Field


class EmbeddingFileSchema(BaseModel):
    id: int = Field(..., title="ID")
    pid: Optional[int] = Field(None, title="项目ID")
    file_name: Optional[str] = Field(None, title="文件名")
    is_delete: Optional[int] = Field(None, title="是否删除")
    file_name_uuid: Optional[str] = Field(None, title="文件UUID名")
    file_type: Optional[str] = Field(None, title="文件类型")
    file_size: Optional[int] = Field(None, title="文件大小")
    file_path: Optional[str] = Field(None, title="文件路径")
    file_url: Optional[str] = Field(None, title="文件URL")
    file_educate_status: Optional[int] = Field(None, title="文件教育状态")
    emb_type: Optional[int] = Field(None, title="嵌入类型")
    remark: Optional[str] = Field(None, title="备注")
    o_url: Optional[str] = Field(None, title="原始URL")
    file_review_pic: Optional[str] = Field(None, title="文件预览图")
    source: Optional[str] = Field(None, title="来源")
    create_by: Optional[str] = Field(None, title="创建者")
    created_at: Optional[datetime] = Field(None, title="创建时间")
    updated_at: Optional[datetime] = Field(None, title="更新时间")
    embedding_documents: Optional[dict] = Field(None, title="嵌入文档")
    extra: Optional[str] = Field(None, title="额外信息")
    file_word_num: Optional[int] = Field(None, title="文件字数")
    first_page_content: Optional[str] = Field(None, title="首页内容")

    class Config:
        from_attributes = True
        json_encoders = {
            datetime: lambda v: v.isoformat() if v else None
        }


class ReqPersonalFileListSchema(BaseModel):
    t_ip_knowledge_id: int = Field(..., title="IP知识库ID")
    file_name: Optional[str] = Field(None, title="文件名")
    page_size: int = Field(10, title="每页数量")
    page_num: int = Field(1, title="页码")
