#!/usr/bin/python3
# -*- coding: utf-8 -*-
"""
Base64文件上传请求模式
----------------------------------------------------
@Project :   xinhou-openai-admin
@File    :   ReqV1Base64UploadSchema.py
@Contact :   <EMAIL>

@Modify Time      <AUTHOR>    @Desciption
------------      -------    --------    -----------
2023/6/5 10:47   logic   1.0         None
"""
from pydantic import BaseModel, Field


class ReqV1Base64UploadSchema(BaseModel):
    """Base64文件上传请求模式"""
    base64_data: str = Field(..., description="Base64编码的文件数据")
    filename: str = Field(..., description="文件名称")
    content_type: str = Field(..., description="文件MIME类型，例如'image/jpeg'") 