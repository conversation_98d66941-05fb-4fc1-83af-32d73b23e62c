import uuid
from pydantic import BaseModel, Field
from typing import Optional

class ReqAgentsShareCreateSchema(BaseModel):
    """创建分享链接请求模型"""
    task_id: int = Field(..., description="任务ID")
    pid: int = Field(..., description="博主ID")
    is_public: bool = Field(default=False, description="是否公开分享")
    remark: Optional[str] = Field(default=None, description="备注")

class ReqAgentsShareUpdateSchema(BaseModel):
    """更新分享链接请求模型"""
    id: int = Field(..., description="分享记录ID")
    is_public: bool = Field(..., description="是否公开分享")
    remark: Optional[str] = Field(default=None, description="备注")

class ResAgentsShareSchema(BaseModel):
    """分享链接响应模型"""
    id: int
    task_id: int
    pid: int
    share_code: str
    is_public: bool
    remark: Optional[str] = None
    create_by: Optional[str] = None
    update_by: Optional[str] = None
    created_at: Optional[str] = None
    updated_at: Optional[str] = None

class ReqAgentsShareQuerySchema(BaseModel):
    """查询分享链接请求模型"""
    share_code: str = Field(..., description="分享码")