from typing import Optional

from pydantic import BaseModel


class ReqHybridSearchSchema(BaseModel):
    """混合搜索请求模型"""
    query: str
    pid: int
    task_id: Optional[int] = None
    limit: int = 5
    threshold: float = 0.1

    class Config:
        json_schema_extra = {
            "example": {
                "query": "搜索查询文本",
                "pid": 1015,
                "task_id": None,
                "limit": 5,
                "threshold": 0.5
            }
        }


class ReqDeleteEmbeddingSchema(BaseModel):
    """删除嵌入数据请求模型"""
    pid: int
    file_id: int