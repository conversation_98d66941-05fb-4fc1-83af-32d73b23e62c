# apps/admin/schema/CdkSchema.py

from datetime import datetime
from typing import Optional, List

from pydantic import BaseModel, Field


class ReqCdkCreateSchema(BaseModel):
    """创建CDK请求模型"""
    cdk_key: str = Field(..., title="CDK兑换码")
    total_uses: int = Field(1, title="可使用总次数")
    point: float = Field(..., title="固定点数值")
    used_time: int = Field(..., title="有效天数")
    status: Optional[int] = Field(1, title="状态")
    remark: Optional[str] = Field(None, title="备注")


class ReqCdkUseSchema(BaseModel):
    """使用CDK请求模型"""
    cdk_key: str = Field(..., title="CDK兑换码")
    uid: int = Field(..., title="使用者用户ID")
    pid: int = Field(..., title="使用者PID")
    ip_id: int = Field(..., title="IP ID")


class ReqCdkUpdateSchema(BaseModel):
    """更新CDK请求模型"""
    id: int
    cdk_key: Optional[str] = None
    total_uses: Optional[int] = None
    remain_point: Optional[float] = None
    expire_time: Optional[datetime] = None
    status: Optional[int] = None
    remark: Optional[str] = None


class ResCdkSchema(BaseModel):
    """CDK响应模型"""
    id: int
    cdk_key: str
    total_uses: int
    used_count: int
    remain_point: float
    expire_time: Optional[datetime]
    used_time: Optional[datetime]
    status: int
    remark: Optional[str]
    created_at: datetime
    updated_at: datetime

    class Config:
        orm_mode = True


class ReqCdkDeleteSchema(BaseModel):
    """删除CDK请求模型"""
    id: int


# CDK使用记录相关Schema
class ReqCdkUsageLogCreateSchema(BaseModel):
    """创建CDK使用记录请求模型"""
    cdk_id: int = Field(..., title="CDK ID")
    cdk_key: str = Field(..., title="CDK兑换码")
    uid: int = Field(..., title="使用者用户ID")
    pid: int = Field(..., title="使用者PID")
    used_point: float = Field(..., title="使用点数")
    status: Optional[int] = Field(1, title="状态")
    remark: Optional[str] = Field(None, title="备注")


class ResCdkUsageLogSchema(BaseModel):
    """CDK使用记录响应模型"""
    id: int
    cdk_id: int
    cdk_key: str
    uid: int
    pid: int
    used_point: float
    used_time: datetime
    status: int
    remark: Optional[str]
    created_at: datetime

    class Config:
        orm_mode = True


class ReqCdkActivateSchema(BaseModel):
    """CDK激活请求模型"""
    cdk_key: str = Field(..., title="CDK激活码")
    phone: str = Field(..., title="手机号")
    verify_code: str = Field(..., title="验证码")  # 添加验证码字段
    password: str = Field("88888888", title="密码")
    ip_name: str = Field(..., title="IP名称")
    avatar: Optional[str] = Field(None, title="头像路径")


class ResCdkActivateSchema(BaseModel):
    """CDK激活响应模型"""
    phone: str
    password: str
    uid: int
    ip_id: int


class ReqCdkBatchCreateSchema(BaseModel):
    """批量创建CDK请求模型"""
    point: float = Field(..., title="固定点数")
    used_time: int = Field(..., title="有效天数")
    count: int = Field(..., ge=1, le=1000, title="生成数量")  # 限制一次最多生成1000个
    total_uses: Optional[int] = Field(1, title="可使用次数")
    remark: Optional[str] = Field(None, title="备注")


class ResCdkBatchCreateSchema(BaseModel):
    """批量创建CDK响应模型"""
    total: int = Field(..., title="生成总数")
    cdk_list: List[str] = Field(..., title="CDK列表")
