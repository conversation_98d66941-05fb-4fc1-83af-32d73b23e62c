# !/usr/bin/python3
# -*- coding: utf-8 -*-
from datetime import date as datetime_date, datetime
from typing import Any, Dict, Optional, List

from pydantic import BaseModel, Field


class ReqTaskSaveSchema(BaseModel):
    pid: int = Field(..., title="IPID")
    task_id: Optional[int] = Field(None, title="任务ID")
    query: str = Field(..., title="主题")
    status: int = Field(default=0, title="状态")
    progress: int = Field(default=0, title="进度")
    remark: Optional[str] = Field(None, title="备注")
    audio_model_id: Optional[str] = Field(None, title="音频模型ID")
    video_model_id: Optional[str] = Field(None, title="视频模型ID")
    task_knowledge_ids: Optional[str] = Field(None, title="任务知识库ID")


class ReqTaskContenuSchema(BaseModel):
    task_id: Optional[int] = Field(None, title="任务ID")
    query: str = Field(..., title="主题")
    pid: int = Field(..., title="IPID")



class ReqTaskQuerySchema(BaseModel):
    query_date: datetime_date = Field(..., title="查询日期")
    pid: int = Field(..., title="IPID")


class TaskItem(BaseModel):
    id: int = Field(..., title="任务ID")
    pid: int = Field(..., title="IPID")
    status: int = Field(..., title="状态")
    progress: int = Field(..., title="进度")
    created_at: datetime_date = Field(..., title="创建日期")
    remark: Optional[str] = Field(None, title="备注")


class ResTaskQuerySchema(BaseModel):
    drafts: List[TaskItem] = Field(default_factory=list, title="草稿数组")
    scripts: List[TaskItem] = Field(default_factory=list, title="口播稿数组")
    videos: List[TaskItem] = Field(default_factory=list, title="短视频数组")


class ReqTaskUpdateSchema(BaseModel):
    id: int = Field(..., title="任务ID")
    pid: Optional[int] = Field(None, title="IPID")
    query: Optional[str] = Field(None, title="主题")
    remark: Optional[str] = Field(None, title="备注")
    progress: Optional[int] = Field(None, title="进度")
    status: Optional[int] = Field(None, title="状态")
    del_flag: Optional[int] = Field(1, title="删除标志")
    audio_model_id: Optional[str] = Field(None, title="音频模型ID")
    video_model_id: Optional[str] = Field(None, title="视频模型ID")


class ResTaskDetailSchema(BaseModel):
    id: int = Field(..., title="任务ID")
    pid: int = Field(..., title="IPID")
    task_id: Optional[int] = Field(None, title="任务ID")
    query: str = Field(..., title="主题")
    remark: Optional[str] = Field(None, title="备注")
    progress: int = Field(..., title="进度")
    status: int = Field(..., title="状态")
    del_flag: int = Field(..., title="删除标志")
    created_at: datetime = Field(..., title="创建时间")
    updated_at: Optional[datetime] = Field(None, title="更新时间")
    audio_model_id: Optional[str] = Field(None, title="音频模型ID")
    video_model_id: Optional[str] = Field(None, title="视频模型ID")

    class Config:
        from_attributes = True


class ReqTaskLatestSchema(BaseModel):
    pid: int = Field(..., title="IPID")


class ReqTaskFailSchema(BaseModel):
    pid: Optional[int] = Field(None, title="IPID")


class ResTaskLatestSchema(BaseModel):
    tasks: List[TaskItem] = Field(default_factory=list, title="最新任务数组")


class ReqTaskFindSchema(BaseModel):
    pid: Optional[int] = Field(None, title="IPID")
    status: Optional[int] = Field(None, title="状态")
    progress: Optional[int] = Field(None, title="进度")
    date_from: Optional[datetime] = Field(None, title="开始日期")
    date_to: Optional[datetime] = Field(None, title="结束日期")
    search_term: Optional[str] = Field(None, title="搜索词")

class ResTaskStatusSchema(BaseModel):
    status_code: int = Field(..., title="状态码")
    message: str = Field(..., title="状态描述")
    task_info: Dict[str, Any] = Field(..., title="任务信息")
    audio_info: Optional[Dict[str, Any]] = Field(None, title="音频信息")
    video_info: Optional[Dict[str, Any]] = Field(None, title="视频信息")
    audio_model_info: Optional[Dict[str, Any]] = Field(None, title="音频模型信息")
    video_model_info: Optional[Dict[str, Any]] = Field(None, title="视频模型信息")

    class Config:
        from_attributes = True