# !/usr/bin/python3
# -*- coding: utf-8 -*-

"""
知识库模型类
----------------------------------------------------
@Project :   xinhou-aip-admin
@File    :   KnowledgeSchema.py
@Contact :   <EMAIL>

@Modify Time      <AUTHOR>    @Desciption
------------      -------    --------    -----------
2024/07/18 10:15   fancy     v1.0.0      初始创建
"""

from datetime import datetime
from typing import Optional

from pydantic import BaseModel, Field


class ResPersonalKnowledgeSchema(BaseModel):
    """
    个人知识库响应模型
    """
    id: int = Field(..., title="知识ID")
    knowledge_source: str = Field(..., title="知识来源")
    knowledge_type: int = Field(..., title="知识类型")
    pid: int = Field(..., title="IP ID")
    created_at: datetime = Field(..., title="创建时间")
    updated_at: datetime = Field(..., title="更新时间")
    remark: Optional[str] = Field(None, title="备注")
    is_myself: int = Field(0, title="是否是自己的媒体账号")

    class Config:
        from_attributes = True


class ReqDetailByField(BaseModel):
    knowledge_source: str = Field(..., title="知识来源")
    knowledge_type: int = Field(..., title="知识类型")


class KnowledgeSearchSchema(BaseModel):
    knowledge_source: Optional[str] = Field(None, title="知识来源")
    knowledge_type: Optional[int] = Field(None, title="知识类型")
    pid: Optional[int] = Field(None, title="IP ID")
    id: Optional[int] = Field(None, title="id")
    is_myself: Optional[int] = Field(None, title="是否是自己的媒体账号")


class KnowledgeGraphSchema(BaseModel):
    pid: Optional[int] = Field(None, title="IP ID")
    