from datetime import datetime
from typing import Optional

from pydantic import BaseModel, Field


class AddSocialMediaAccountRequest(BaseModel):
    account_url: str = Field(..., title="社交媒体账号URL")
    pid: int = Field(..., title="IP ID")
    is_myself: int = Field(0, title="是否是自己的媒体账号")


class DeleteSocialMediaAccountResponse(BaseModel):
    success: bool = Field(..., title="操作是否成功")
    message: str = Field(..., title="响应消息")


class UpdateSocialMediaAccountRequest(BaseModel):
    id: int = Field(..., title="账号ID")
    knowledge_source: Optional[str] = Field(None, title="知识来源")
    knowledge_type: Optional[int] = Field(None, title="知识类型")
    account_url: Optional[str] = Field(None, title="账号URL")
    pid: Optional[int] = Field(None, title="IP ID")
    create_by: Optional[str] = Field(None, title="创建者")
    update_by: Optional[str] = Field(None, title="更新者")
    remark: Optional[str] = Field(None, title="备注")
    file_list: Optional[str] = Field(None, title="文件列表")
    avatar: Optional[str] = Field(None, title="头像")
    is_myself: Optional[int] = Field(None, title="是否是自己的媒体账号")

    class Config:
        extra = "forbid"


class SocialMediaAccountResponse(BaseModel):
    id: int = Field(..., title="账号ID")
    knowledge_source: str = Field(..., title="知识来源")
    knowledge_type: int = Field(..., title="知识类型")
    account_url: Optional[str] = Field(None, title="账号URL")
    pid: int = Field(..., title="IP ID")
    create_by: Optional[str] = Field(None, title="创建者")
    update_by: Optional[str] = Field(None, title="更新者")
    created_at: datetime = Field(..., title="创建时间")
    updated_at: datetime = Field(..., title="更新时间")
    remark: Optional[str] = Field(None, title="备注")
    file_list: Optional[str] = Field(None, title="文件列表")
    avatar: Optional[str] = Field(None, title="头像")
    is_myself: int = Field(0, title="是否是自己的媒体账号")

    class Config:
        from_attributes = True
