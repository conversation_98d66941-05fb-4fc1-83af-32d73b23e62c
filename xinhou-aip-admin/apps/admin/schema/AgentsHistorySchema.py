from datetime import datetime
from typing import Optional, Union, List, Dict, Any

from pydantic import BaseModel, ConfigDict


class ReqAgentsHistoryCreateSchema(BaseModel):
    """创建历史记录请求模型"""
    task_id: int
    pid: int
    status: Optional[int] = 1
    history: Union[bytes, str, List[Dict[str, Any]]]
    remark: Optional[str] = None

    model_config = ConfigDict(
        from_attributes=True,
        arbitrary_types_allowed=True,
        extra='allow',
        validate_assignment=False,
        validate_default=False,
    )


class ReqAgentsHistoryUpdateSchema(BaseModel):
    """更新历史记录请求模型"""
    id: int
    task_id: Optional[int] = None
    pid: Optional[int] = None
    status: Optional[int] = None
    history: Optional[Union[bytes, str, List[Dict[str, Any]]]] = None
    remark: Optional[str] = None
    update_by: Optional[str] = None

    model_config = ConfigDict(
        from_attributes=True,
        arbitrary_types_allowed=True,
        extra='allow',
        validate_assignment=False,
        validate_default=False,
    )


class ResAgentsHistorySchema(BaseModel):
    """历史记录响应模型"""
    id: int
    task_id: int
    pid: int
    status: int
    history: bytes
    remark: Optional[str] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    create_by: Optional[str] = None
    update_by: Optional[str] = None

    model_config = ConfigDict(
        from_attributes=True,
        arbitrary_types_allowed=True,
        extra='allow',
        validate_assignment=False,
        validate_default=False,
    ) 