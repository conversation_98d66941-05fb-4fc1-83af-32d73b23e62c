from datetime import datetime
from typing import Optional

from pydantic import BaseModel, Field


class ReqWorkFindSchema(BaseModel):
    task_id: Optional[int] = Field(None, title="任务 ID")
    work_type: Optional[int] = Field(None, title="作品类型")
    status: Optional[int] = Field(None, title="状态")
    pid: int = Field(..., title="PID")  # 使用 ... 表示必填字段
    date_from: Optional[datetime] = Field(None, title="开始日期")
    date_to: Optional[datetime] = Field(None, title="结束日期")
    search_term: Optional[str] = Field(None, title="搜索关键词")

    class Config:
        from_attributes = True
        json_encoders = {
            datetime: lambda v: v.isoformat() if v else None
        }
