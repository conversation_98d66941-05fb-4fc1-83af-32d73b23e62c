# !/usr/bin/python3
# -*- coding: utf-8 -*-


from typing import Optional

from pydantic import BaseModel, Field


class ReqCreateRpaTaskSchema(BaseModel):
    robot_name: str = Field(title="机器人名称")
    robot_params: Optional[str] = Field(default="", title="机器人参数")


class ReqRpaTaskQuerySchema(BaseModel):
    robot_name: str = Field(title="机器人名称")


class ReqRpaTaskResultSchema(BaseModel):
    id: int = Field(title="任务id")
    robot_status: str = Field(title="任务状态")
    result_info: str = Field(title="机器人输出结果信息")
    result_file: Optional[str] = Field(title="机器人产生的文件链接")
