from datetime import datetime
from typing import Optional

from pydantic import BaseModel, Field


class ResMediaModelDetailSchema(BaseModel):
    id: int = Field(..., title="ID")
    title: Optional[str] = Field(None, title="标题")
    pic_url: Optional[str] = Field(None, title="封面图")
    status: Optional[int] = Field(1, title="视频状态")
    media_url: Optional[str] = Field(None, title="媒体URL")
    del_flag: Optional[int] = Field(1, title="删除标志")
    create_by: Optional[int] = Field(None, title="创建者")
    created_at: Optional[datetime] = Field(None, title="创建时间")
    update_by: Optional[int] = Field(None, title="更新者")
    updated_at: Optional[datetime] = Field(None, title="更新时间")
    remark: Optional[str] = Field(None, title="备注")
    pid: Optional[int] = Field(None, title="IPID")

    class Config:
        from_attributes = True


class ReqMediaModelFindSchema(BaseModel):
    title: Optional[str] = Field(None, title="标题")
    status: Optional[int] = Field(None, title="视频状态")
    del_flag: Optional[int] = Field(1, title="删除标志")
    pid: Optional[int] = Field(None, title="IPID")


class ReqMediaModelSaveSchema(BaseModel):
    title: str = Field(..., title="标题")
    pic_url: Optional[str] = Field(None, title="封面图")
    status: Optional[int] = Field(1, title="视频状态")
    media_url: Optional[str] = Field(None, title="媒体URL")
    del_flag: Optional[int] = Field(1, title="删除标志")
    create_by: Optional[int] = Field(None, title="创建者")
    remark: Optional[str] = Field(None, title="备注")
    pid: int = Field(..., title="IPID")
    transcode: int = Field(..., title="转码状态")


class ReqMediaModelUpdateSchema(BaseModel):
    id: int = Field(..., title="ID")
    title: Optional[str] = Field(None, title="标题")
    pic_url: Optional[str] = Field(None, title="封面图")
    status: Optional[int] = Field(None, title="视频状态")
    media_url: Optional[str] = Field(None, title="媒体URL")
    del_flag: Optional[int] = Field(1, title="删除标志")
    update_by: Optional[int] = Field(None, title="更新者")
    remark: Optional[str] = Field(None, title="备注")
    pid: Optional[int] = Field(None, title="IPID")

class ReqMediaModelUploadSchema(BaseModel):
    pid: int = Field(..., title="IPID")
    title: str = Field(..., title="标题")

    class Config:
        json_schema_extra = {
            "example": {
                "pid": 1,
                "title": "Sample Media Title"
            }
        }