from typing import Optional, Dict

from pydantic import BaseModel


class ReqLLMCreateSchema(BaseModel):
    """创建LLM请求模型"""
    llm_name: str
    llm_code: str
    model_name: str
    api_url: str
    api_key: Optional[str] = None
    api_config: Optional[Dict] = None
    status: Optional[int] = 1
    remark: Optional[str] = None

class ReqLLMUpdateSchema(BaseModel):
    """更新LLM请求模型"""
    id: int
    llm_name: Optional[str] = None
    model_name: Optional[str] = None
    api_url: Optional[str] = None
    api_key: Optional[str] = None
    api_config: Optional[Dict] = None
    status: Optional[int] = None
    remark: Optional[str] = None

class ResLLMSchema(BaseModel):
    """LLM响应模型"""
    id: int
    llm_name: str
    llm_code: str
    model_name: str
    api_url: str
    status: int
    remark: Optional[str] = None

class ReqLLMDeleteSchema(BaseModel):
    """删除LLM请求模型"""
    id: int 