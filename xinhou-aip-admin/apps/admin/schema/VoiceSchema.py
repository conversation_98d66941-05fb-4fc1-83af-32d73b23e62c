from enum import Enum
from typing import Optional

from pydantic import BaseModel, Field


class AudioTypeEnum(Enum):
    generate = "generate"
    upload = "upload"
    fish = "fish"


class ReqCreateVideoTaskSchema(BaseModel):
    """
    Schema for creating a new video task
    """
    task_id: int
    video_url: str
    audio_url: str
    server_name: str

    class Config:
        json_schema_extra = {
            "example": {
                "video_url": "https://example.com/video.mp4",
                "audio_url": "https://example.com/audio.wav",
                "server_name": "FeiYing"
            }
        }


class ReqCreateVideoTaskV2Schema(BaseModel):
    """
    Schema for creating a new video task
    """
    task_id: int
    video_url: str
    pid: int
    audio_type: AudioTypeEnum
    voice_id: Optional[int]
    audio_url: Optional[str]

    class Config:
        json_schema_extra = {

            "example": {
                "task_id": 88,
                "pid": 88,
                "video_url": "https://static.chatonai.com/static/uploads/example.mp4",
                "audio_type": "generate",
                "voice_id": 88,
                "audio_url": ""
            }
        }


class ReqQueryVideoTaskSchema(BaseModel):
    """
    Schema for querying the status of a video task
    """
    task_id: int


class ResCreateVideoTaskSchema(BaseModel):
    """
    Schema for the response of creating a new video task
    """
    video_job_id: int

    class Config:
        json_schema_extra = {
            "example": {
                "video_job_id": 1208172
            }
        }


class ResQueryVideoTaskSchema(BaseModel):
    """
    Schema for the response of querying the status of a video task
    """
    status: int
    video_url: Optional[str] = None
    audio_url: Optional[str] = None
    message: str
    code: int

    class Config:
        json_schema_extra = {
            "example": {
                "status": 3,
                "video_url": "https://example.com/processed_video.mp4",
                "message": "",
                "code": 0
            }
        }

    @property
    def status_description(self) -> str:
        status_map = {
            1: "等待中",
            2: "处理中",
            3: "完成",
            4: "失败"
        }
        return status_map.get(self.status, "未知状态")


class ReqCreateVideoTaskV3Schema(BaseModel):
    task_id: int
    pid: int
    video_url: str
    audio_type: AudioTypeEnum
    voice_id: int
    audio_url: Optional[str] = None

    class Config:
        use_enum_values = True
        json_schema_extra = {
            "example": {
                "task_id": 1167,
                "pid": 1013,
                "video_url": "https://example.com/video.mp4",
                "audio_type": "generate",
                "voice_id": 185,
                "audio_url": ""
            }
        }


class ReqCreateVideoWithAudioSchema(BaseModel):
    """用已有音频创建视频的请求Schema"""
    task_id: int
    pid: int
    video_url: str
    audio_url: str
    video_model_id: Optional[int] = None
    uuid: str
    redis_key: Optional[str] = None

    class Config:
        json_schema_extra = {
            "example": {
                "task_id": 1167,
                "pid": 1013,
                "uuid": "1234567890",
                "video_url": "https://example.com/video.mp4",
                "audio_url": "https://example.com/audio.mp3",
                "video_model_id": 123,
                "redis_key": "1234567890"
            }
        }
