from typing import Optional

from pydantic import BaseModel, Field


class ResWorkDetailSchema(BaseModel):
    work_type: int = Field(..., title="作品类型")
    title: str = Field(..., title="标题")
    status: int = Field(..., title="状态")
    pid: int = Field(..., title="IPID")
    create_by: str = Field(..., title="创建者")
    update_by: Optional[str] = Field(None, title="更新者")
    file_id: int = Field(..., title="文件ID")
    task_id: int = Field(..., title="任务ID")
    content: Optional[str] = Field(None, title="内容")
    remark: Optional[str] = Field(None, title="备注")
    created_at: str = Field(..., title="创建时间")
    updated_at: str = Field(..., title="更新时间")
    file_url: Optional[str] = Field(None, title="文件URL")
    audio_model_id: Optional[str] = Field(None, title="音频模型ID")
    video_model_id: Optional[str] = Field(None, title="视频模型ID")

    class Config:
        from_attributes = True


class ReqWorkFindSchema(BaseModel):
    task_id: int = Field(None, title="任务ID")
    work_type: int = Field(None, title="作品类型")
    title: str = Field(None, title="标题")
    status: int = Field(None, title="状态")
    pid: int = Field(..., title="IPID")
    content: Optional[str] = Field(None, title="内容")
    date_from: Optional[str] = Field(None, title="开始日期")
    date_to: Optional[str] = Field(None, title="结束日期")
    search_term: Optional[str] = Field(None, title="搜索词")


class ReqWorkSaveSchema(BaseModel):
    task_id: int = Field(..., title="任务ID")
    work_type: int = Field(..., title="作品类型")
    title: Optional[str] = Field(None, title="标题")
    content: Optional[str] = Field(None, title="内容")
    file_id: Optional[int] = Field(None, title="文件ID")
    status: Optional[int] = Field(1, title="状态")
    create_by: Optional[str] = Field(None, title="创建者")
    remark: Optional[str] = Field(None, title="备注")
    pid: int = Field(..., title="IPID")
    audio_model_id: Optional[str] = Field(None, title="音频模型ID")
    video_model_id: Optional[str] = Field(None, title="视频模型ID")


class ReqWorkUpdateSchema(BaseModel):
    """
    作品更新请求参数
    """
    pid: int = Field(..., description="项目ID")
    task_id: int = Field(..., description="任务ID")
    uuid: str = Field(..., description="历史记录UUID")
    content: Optional[str] = Field(None, description="作品内容")
    status: Optional[int] = Field(None, description="作品状态")
    remark: Optional[str] = Field(None, description="备注")
    file_id: Optional[int] = Field(None, description="文件ID")
    audio_model_id: Optional[int] = Field(None, description="音频模型ID")
    video_model_id: Optional[int] = Field(None, description="视频模型ID")
    
    class Config:
        json_schema_extra = {
            "example": {
                "pid": 1,
                "task_id": 1,
                "uuid": "xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx",
                "content": "作品内容",
                "status": 1,
                "remark": "备注",
                "file_id": 1,
                "audio_model_id": 1,
                "video_model_id": 1
            }
        }


class ReqWorkUserUpdateSchema(BaseModel):
    task_id: Optional[int] = Field(None, title="任务ID")
    content: Optional[str] = Field(None, title="内容")
    work_type: int = Field(1, title="作品类型")
    remark: str = Field("用户手动更新", title="备注")
