from enum import Enum
from fastapi import UploadFile, Form, File
from pydantic import BaseModel, Field
from typing import Optional


class AudioServerEnum(str, Enum):
    ALi = "ALi"
    Fish = "Fish"


class ReqV1AudioQuerySchema(BaseModel):
    task_id: int
    work_type: int


class ReqV1AudioCloneSchema(BaseModel):
    voice_prefix: str
    timbre_id: int
    server_name: AudioServerEnum


class ResV1AudioCloneSchema(BaseModel):
    Message: str
    Code: int
    RequestId: str
    VoiceName: str


class ReqV1AudioGenerateSchema(BaseModel):
    task_id: int
    pid: int
    voice_name: str
    server_name: AudioServerEnum


class ReqV1FishAudioGenerateSchema(BaseModel):
    task_id: int
    pid: int
    text: str = Field(None)  
    voice_name: str
    server_name: AudioServerEnum


class ResV1AudioGenerateSchema(BaseModel):
    generate_audio_url: str

class ReqV1FishAudioCloneSchema(BaseModel):
    title: str
    pid: int
    voice_file: UploadFile

    @classmethod
    def as_form(
        cls,
        title: str = Form(..., description="声音标题"),
        pid: int = Form(..., description="人设ID"),
        voice_file: UploadFile = File(..., description="声音文件")
    ):
        return cls(title=title, pid=pid, voice_file=voice_file)

class ResV1FishAudioCloneSchema(BaseModel):
    message: str
    code: int
    voice_name: str
    clone_name: str

class ReqUpdateVoiceModelsSchema(BaseModel):
    pid: int

class ResUpdateVoiceModelsSchema(BaseModel):
    message: str
    updated_count: int
    failed_count: int
    total_count: int
    
class ReqFishAudioGenerateSchema(BaseModel):
    task_id: int = Field(..., description="任务ID")
    pid: int = Field(..., description="用户ID")
    voice_id: int = Field(..., description="声音模型ID")
    content: Optional[str] = Field(None, description="需合成音频文本")

class ReqAudioSpeedControlSchema(BaseModel):
    """音频速度控制请求Schema"""
    input_url: str = Field(..., description="输入音频URL")
    speed: float = Field(..., description="速度倍率", ge=0.5, le=2.0)
    pid: int = Field(..., description="用户ID")
    task_id: int = Field(..., description="任务ID")
    uuid: str = Field(..., description="历史记录UUID")

    class Config:
        json_schema_extra = {
            "example": {
                "input_url": "https://example.com/audio.mp3",
                "speed": 1.5,
                "pid": 12345,
                "task_id": 1000,
                "uuid": "2cee5a8a-ccf1-11ef-aea4-00163e69be68"
            }
        }

class ResAudioSpeedControlSchema(BaseModel):
    """音频速度控制响应Schema"""
    output_url: str
    speed: float