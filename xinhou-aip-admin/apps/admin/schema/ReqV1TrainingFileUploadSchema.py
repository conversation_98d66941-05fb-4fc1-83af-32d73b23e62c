# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
功能描述
----------------------------------------------------
@Project :   xinhou-openai-admin
@File    :   ReturnOpenaiSchema.py
@Contact :   <EMAIL>

@Modify Time      <AUTHOR>    @Desciption
------------      -------    --------    -----------
2023/3/26 18:06   logic   1.0         None
"""
from typing import List
from typing import Optional

from pydantic import BaseModel, Field
from pydantic.v1 import Required


class ReqV1TrainingFileUploadSchema(BaseModel):
    """
    文件上传返回数据模型
    """
    id: Optional[int] = Field(title="文件ID")
    file_name: Optional[str] = Field(title="文件名称")
    file_name_uuid: Optional[str] = Field(title="文件UUID名称")
    file_type: Optional[str] = Field(title="文件类型")
    file_size: Optional[int] = Field(title="文件大小")
    file_path: Optional[str] = Field(title="文件访问路径")
    file_educate_status: Optional[int] = Field(title="文件训练状态",
                                               description="文件训练状态:0=未训练,1=已训练,3=训练异常")
    file_url: Optional[str] = Field(title="文件下载路径")
    remark: Optional[str] = Field(title="备注", description="备注")


class ResV1TrainingFileUploadSchema(BaseModel):
    """
    通用返回模型
    """

    code: Optional[int] = Field(default=Required, title="编码", description="返回请求编码")
    msg: Optional[str] = Field(default=Required, title="消息", description="返回请求消息")
    data: Optional[List[ReqV1TrainingFileUploadSchema]] = Field(title="内容", description="返回内容")
