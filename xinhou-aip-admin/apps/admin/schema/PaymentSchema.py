# !/usr/bin/python3
# -*- coding: utf-8 -*-

"""
支付相关的数据模型
----------------------------------------------------
@Project :   xinhou-openai-admin
@File    :   PaymentSchema.py
@Contact :   <EMAIL>

@Modify Time      <AUTHOR>    @Desciption
------------      -------    --------    -----------
2024/09/18 11:00  ChatGPT    v1.0.0      None
2024/09/18 19:30  ChatGPT    v1.1.0      更新ResPaymentResultSchema以匹配实际API响应
2024/09/18 20:00  ChatGPT    v1.2.0      从ReqPaymentInitSchema中移除out_trade_no
"""
from datetime import datetime
from decimal import Decimal
from typing import Optional, Union

from pydantic import BaseModel, Field

from apps.admin.schema.ImageModelSchema import ImageModelSaveSchema
from apps.admin.schema.VirtualHumanSchema import VirtualHumanSaveSchema


class ReqPaymentInitSchema(BaseModel):
    pay_type: str
    client_ip: str = None
    device: str = None
    param: str  # JSON字符串，包含product_id等信息


class ReqPaymentInitSchemaV2(BaseModel):
    pay_type: str
    client_ip: str = None
    device: str = None
    product_id: int = None
    param: Union[ImageModelSaveSchema, VirtualHumanSaveSchema]


class ResPaymentResultSchema(BaseModel):
    code: int = Field(..., description="状态码: 1成功, 0失败")
    msg: str = Field(..., description="返回信息")
    out_trade_no: Optional[str] = Field(None, description="商户订单号")
    pay_url: Optional[str] = Field(None, description="支付URL")
    status: Optional[int] = Field(None, description="支付状态: 0未支付, 1已支付")
    created_at: Optional[datetime] = Field(None, description="订单创建时间")


class ReqPaymentQuerySchema(BaseModel):
    out_trade_no: str = Field(..., description="商户订单号")


class ReqPaymentRefundSchema(BaseModel):
    out_trade_no: str = Field(..., description="商户订单号")
    money: Decimal = Field(..., description="退款金额")


class PaymentNotifySchema(BaseModel):
    pid: str = Field(..., description="商户ID")
    trade_no: str = Field(..., description="支付平台订单号")
    out_trade_no: str = Field(..., description="商户订单号")
    type: str = Field(..., description="支付方式")
    name: str = Field(..., description="商品名称")
    money: Decimal = Field(..., description="支付金额")
    trade_status: str = Field(..., description="交易状态")
    param: Optional[str] = Field(None, description="自定义参数")
    sign: str = Field(..., description="签名")
    sign_type: str = Field(..., description="签名类型")


class ReqPaymentFindSchema(BaseModel):
    out_trade_no: Optional[str] = Field(None, description="商户订单号")
    trade_status: Optional[str] = Field(None, description="交易状态")
    payment_type: Optional[str] = Field(None, description="支付方式")
    date_from: Optional[datetime] = Field(None, description="开始日期")
    date_to: Optional[datetime] = Field(None, description="结束日期")
    customer_id: Optional[int] = Field(None, description="pid")

class ResPaymentDetailSchema(BaseModel):
    out_trade_no: Optional[str] = Field(None, description="商户订单号")
    trade_status: Optional[str] = Field(None, description="交易状态")
    payment_type: Optional[str] = Field(None, description="支付方式")
    date_from: Optional[datetime] = Field(None, description="开始日期")
    date_to: Optional[datetime] = Field(None, description="结束日期")
    customer_id: Optional[int] = Field(None, description="pid")