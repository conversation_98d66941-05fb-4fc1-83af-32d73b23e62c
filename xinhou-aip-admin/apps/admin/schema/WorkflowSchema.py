from typing import Optional, List

from pydantic import BaseModel, ConfigDict


class ReqWorkflowCreateSchema(BaseModel):
    """创建工作流请求模型"""
    workflow_name: str
    workflow_code: str
    pid: int
    description: Optional[str] = None
    agent_ids: List[int]  # 关联的Agent ID列表
    status: Optional[int] = 1
    remark: Optional[str] = None


class ReqWorkflowUpdateSchema(BaseModel):
    """更新工作流请求模型"""
    id: int
    workflow_name: Optional[str] = None
    description: Optional[str] = None
    agent_ids: Optional[List[int]] = None
    status: Optional[int] = None
    remark: Optional[str] = None


class ResWorkflowSchema(BaseModel):
    """工作流响应模型"""
    id: int
    workflow_name: str
    workflow_code: str
    pid: int
    description: Optional[str] = None
    status: int
    remark: Optional[str] = None
    is_default: int  # 新增字段


class ReqWorkflowDeleteSchema(BaseModel):
    """删除工作流请求模型"""
    id: int


class AgentInfo(BaseModel):
    id: int
    agent_name_cn: str
    agent_name_en: Optional[str]
    agent_code: str
    agent_type: int
    agent_role: int
    influence_scope: Optional[str] = None  # 使用 Any 类型
    prompt_cn: Optional[str]
    prompt_en: Optional[str]
    status: int
    llm_name: str
    model_name: str
    api_url: Optional[str]
    api_key: Optional[str]
    api_config: Optional[dict]
    agent_style: str = 'USUALLY'

    model_config = ConfigDict(
        from_attributes=True,
        arbitrary_types_allowed=True,  # 允许任意类型
        extra='allow',  # 允许额外字段
        validate_assignment=False,  # 关闭赋值验证
        validate_default=False,  # 关闭默认值验证
    )


class WorkflowInfo(BaseModel):
    id: int
    workflow_name: str
    workflow_code: str
    description: Optional[str]
    agents: List[AgentInfo]

    model_config = ConfigDict(
        from_attributes=True,
        arbitrary_types_allowed=True,
        extra='allow',
        validate_assignment=False,
        validate_default=False,
    )
