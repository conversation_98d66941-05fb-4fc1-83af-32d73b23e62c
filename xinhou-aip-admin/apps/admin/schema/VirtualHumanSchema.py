from datetime import datetime
from typing import Optional

from pydantic import BaseModel, Field


class VirtualHumanDetailSchema(BaseModel):
    id: int = Field(..., title="ID")
    pid: Optional[int] = Field(None, title="父ID")
    model_type: str = Field(..., title="超虚拟人制作类型")
    speech_style: str = Field(None, title="口播风格")
    interview_style: str = Field(None, title="访谈风格")
    duration: str = Field(..., title="模型时长")
    appearance_note: Optional[str] = Field(None, title="形象要求备注")
    status: Optional[int] = Field(1, title="状态")
    del_flag: Optional[int] = Field(1, title="删除标志")
    create_by: Optional[str] = Field(None, title="创建者")
    created_at: Optional[datetime] = Field(None, title="创建时间")
    update_by: Optional[str] = Field(None, title="更新者")
    updated_at: Optional[datetime] = Field(None, title="更新时间")
    submit_status: int = Field(1, title='状态:1=已提交,2=已上传')
    upload_time: Optional[datetime] = Field(None, title='上传时间')
    has_collection: Optional[int] = Field(0, title="状态:1=已完成采集,0=未完成采集")


    class Config:
        from_attributes = True


class VirtualHumanFindSchema(BaseModel):
    pid: Optional[int] = Field(None, title="父ID")
    model_type: Optional[str] = Field(None, title="超虚拟人制作类型")
    speech_style: Optional[str] = Field(None, title="口播风格")
    interview_style: Optional[str] = Field(None, title="访谈风格")
    status: Optional[int] = Field(None, title="状态")
    del_flag: Optional[int] = Field(1, title="删除标志")
    has_collection: Optional[int] = Field(0, title="状态:1=已完成采集,0=未完成采集")


class VirtualHumanSaveSchema(BaseModel):
    pid: Optional[int] = Field(None, title="父ID")
    model_type: Optional[str] = Field(..., title="超虚拟人制作类型")
    speech_style: Optional[str] = Field(None, title="口播风格")
    interview_style: Optional[str] = Field(None, title="访谈风格")
    duration: Optional[str] = Field(..., title="模型时长")
    appearance_note: Optional[str] = Field(None, title="形象要求备注")
    status: Optional[int] = Field(1, title="状态")
    has_collection: Optional[int] = Field(0, title="状态:1=已完成采集,0=未完成采集")


class VirtualHumanUpdateSchema(BaseModel):
    id: int = Field(..., title="ID")
    pid: Optional[int] = Field(None, title="父ID")
    model_type: Optional[str] = Field(None, title="超虚拟人制作类型")
    speech_style: Optional[str] = Field(None, title="口播风格")
    interview_style: Optional[str] = Field(None, title="访谈风格")
    duration: Optional[str] = Field(None, title="模型时长")
    appearance_note: Optional[str] = Field(None, title="形象要求备注")
    status: Optional[int] = Field(None, title="状态")
    has_collection: Optional[int] = Field(0, title="状态:1=已完成采集,0=未完成采集")
