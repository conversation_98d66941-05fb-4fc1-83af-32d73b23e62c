from datetime import datetime
from decimal import Decimal
from typing import Optional

from pydantic import BaseModel, Field


class ReqProductFindSchema(BaseModel):
    search_term: Optional[str] = None
    status: Optional[int] = None
    min_price: Optional[Decimal] = None
    max_price: Optional[Decimal] = None
    unit: Optional[str] = None


class ResProductDetailSchema(BaseModel):
    id: int
    name: str
    money: Decimal
    status: int
    remark: Optional[str] = None
    unit: Optional[str] = None
    features: Optional[str] = None
    desc: Optional[str] = None
    create_by: Optional[str] = None
    created_at: datetime
    update_by: Optional[str] = None
    updated_at: Optional[datetime] = None
    name_en: Optional[str] = None
    money_en: Optional[Decimal] = None
    unit_en: Optional[str] = None
    features_en: Optional[str] = None
    desc_en: Optional[str] = None


class ReqProductSaveSchema(BaseModel):
    name: str = Field(..., max_length=255)
    money: Decimal = Field(..., ge=0, decimal_places=2)
    status: int = Field(1, ge=1, le=2)
    remark: Optional[str] = Field(None, max_length=500)
    unit: Optional[str] = Field(None, max_length=64)
    features: Optional[str] = Field(None, max_length=500)
    desc: Optional[str] = Field(None, max_length=500)
    name_en: Optional[str] = Field(None, max_length=255)
    money_en: Decimal = Field(..., ge=0, decimal_places=2)
    unit_en: Optional[str] = Field(None, max_length=64)
    features_en: Optional[str] = Field(None, max_length=500)
    desc_en: Optional[str] = Field(None, max_length=500)


class ReqProductUpdateSchema(ReqProductSaveSchema):
    id: int
