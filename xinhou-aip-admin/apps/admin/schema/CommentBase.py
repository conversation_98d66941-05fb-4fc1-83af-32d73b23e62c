from datetime import datetime
from typing import Optional, List

from pydantic import BaseModel, Field
from pydantic.v1 import Required
from xinhou_openai_framework.core.queue.message.RedisMessageModel import RedisMessageModel


class CommentBase(BaseModel):
    task_id: int = Field(..., description="关联的任务ID")
    comment_type: int = Field(...,
                              description="评论类型:1=作者自评,2=观众-搞笑,3=观众-讽刺,4=观众-鼓励,5=朋友圈文案,6=群聊转发")
    content: str = Field(..., description="神评论内容")
    status: Optional[int] = Field(1, description="评论状态:1=正常,2=待审核,3=已删除")
    remark: Optional[str] = Field(None, description="备注")


class ReqCommentCreateSchema(CommentBase):
    pass


class ReqCommentUpdateSchema(CommentBase):
    id: int = Field(..., description="评论ID")


class ResCommentDetailSchema(CommentBase):
    id: int = Field(..., description="评论ID")
    create_by: Optional[str] = Field(None, description="创建者")
    created_at: datetime = Field(..., description="创建时间")
    update_by: Optional[str] = Field(None, description="更新者")
    updated_at: Optional[datetime] = Field(None, description="更新时间")

    class Config:
        orm_mode = True


class CommentFindAllRequest(BaseModel):
    task_id: Optional[int] = None
    comment_type: Optional[int] = None


class CommentFindByRequest(BaseModel):
    page: int = 1
    size: int = 10
    task_id: Optional[int] = None
    comment_type: Optional[int] = None


class ReqFailSchema(BaseModel):
    pid: Optional[int] = Field(None, title="IPID")


class CommonV1ResMsgSchema(BaseModel):
    """
    通用返回模型
    """

    code: Optional[int] = Field(default=Required, title="编码", description="返回请求编码")
    msg: Optional[str] = Field(default=Required, title="消息", description="返回请求消息")
    data: Optional[List[RedisMessageModel]] = Field(default=None, title="内容",
                                                    description="返回内容")
