from typing import Optional

from pydantic import BaseModel, Field, validator


class TTSFormData(BaseModel):
    ref_audio_url: str = Field(..., description="参考音频URL")
    ref_text: Optional[str] = Field(None, description="参考音频文本")
    gen_text: str = Field(..., description="需要生成的文本")
    model_name: str = Field("F5-TTS", description="模型名称")
    remove_silence: bool = Field(False, description="是否移除静音")
    speed: float = Field(0.8, description="语速")
    cross_fade_duration: float = Field(0.15, description="交叉淡入淡出时长")

class TranscribeRequest(BaseModel):
    audio_url: str = Field(..., description="需要转写的音频URL")
    language: Optional[str] = Field(None, description="语言代码")

class TTSResponse(BaseModel):
    audio: str = Field(..., description="生成的音频URL")
    sample_rate: Optional[int] = None
    spectrogram: Optional[str] = None

# 添加请求和响应的Schema
class ReqF5TTSGenerateSchema(BaseModel):
    task_id: int = Field(..., description="任务ID")
    pid: int = Field(..., description="项目ID")
    voice_id: int = Field(..., description="声音模型ID")
    ref_text: Optional[str] = Field(None, description="参考音频文本(可选)")
    
    @validator('task_id', 'pid', 'voice_id')
    def validate_ids(cls, v):
        if v <= 0:
            raise ValueError('ID must be positive')
        return v

class ResF5TTSGenerateSchema(BaseModel):
    audio_url: str = Field(..., description="生成的音频URL")

class ReqF5TTSTranscribeSchema(BaseModel):
    audio_url: str = Field(..., description="需要转写的音频URL")
    language: str = Field("chinese", description="语言类型")

class ResF5TTSTranscribeSchema(BaseModel):
    text: str = Field(..., description="转写后的文本")
  