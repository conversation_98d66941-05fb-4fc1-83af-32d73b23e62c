from enum import Enum
from typing import Optional

from pydantic import BaseModel, Field


class LanguageEnum(str, Enum):
    CHINESE = "zh"
    ENGLISH = "en"
    SPANISH = "es"
    FRENCH = "fr"
    GERMAN = "de"
    JAPANESE = "ja"


class ReqAgentsChatSchema(BaseModel):
    pid: int = Field(..., title="IPID")
    task_id: Optional[int] = Field(None, title="任务ID")
    query: Optional[str] = Field(default="根据以上附件帮我生成口播稿", title="主题")
    status: int = Field(default=0, title="状态")
    progress: int = Field(default=0, title="进度")
    remark: Optional[str] = Field(None, title="备注")
    audio_url: Optional[str] = Field(None, title="音频模型地址")
    video_url: Optional[str] = Field(None, title="视频模型地址")
    audio_model_id: Optional[int] = Field(None, title="音频模型ID")
    video_model_id: Optional[int] = Field(None, title="视频模型ID")
    task_knowledge_ids: Optional[str] = Field(None, title="任务知识库ID")
    language: Optional[LanguageEnum] = Field(default=None, title="语言")
    doc_length: Optional[int] = Field(None, title="文档长度")
    agent_uuid: Optional[str] = Field(None, title="agent_uuid")
    is_pass: Optional[int] = Field(0, title="是否跳过意图识别")
    video_model_pic_url: Optional[str] = Field(None, title="视频预览图")
    voice_is_upload: Optional[int] = Field(0, title="声音是否上传")
    voice_upload_url: Optional[str] = Field(None, title="声音地址")
    is_person: Optional[int] = Field(None, title="启动人设")
    is_search: Optional[int] = Field(None, title="启动搜索")
    is_rag: Optional[int] = Field(None, title="启动RAG")
    is_knowledge: Optional[int] = Field(None, title="启动用户知识图谱")
    style: Optional[str] = Field(None, title="设置风格")
    read_score: Optional[int] = Field(None, title="易读得分")

