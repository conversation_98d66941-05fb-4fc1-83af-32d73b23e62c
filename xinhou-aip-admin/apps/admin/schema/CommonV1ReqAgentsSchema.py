# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
功能描述
----------------------------------------------------
@Project :   xinhou-openai-embedding
@File    :   V4ReqTrainingFileSchema.py
@Contact :   <EMAIL>

@Modify Time      <AUTHOR>    @Desciption
------------      -------    --------    -----------
2023/6/5 21:26   logic   1.0         None
"""

from typing import List, Optional, Dict, Any

from pydantic import BaseModel, Field

from apps.admin.schema.AgentSchema import AgentToolSchema
from apps.admin.schema.AgentsAdminSchema import LanguageEnum


class CommonV1ReqAgentsSchema(BaseModel):
    """文本训练入参模型"""
    task_id: int = Field(default=..., title="口播稿会话 id")


class LLMInfo(BaseModel):
    """LLM信息"""
    llm_name: str
    model_name: str
    api_url: str
    api_key: Optional[str] = None
    api_config: Optional[Dict[str, Any]] = None


class AgentInfo(BaseModel):
    """Agent信息"""
    id: int
    agent_name_cn: str
    agent_name_en: str
    agent_code: str
    agent_type: int
    agent_role: int
    agent_style: str = 'USUALLY'
    agent_action: Optional[str] = None
    influence_scope: Optional[str] = None
    prompt_cn: Optional[str] = None
    prompt_en: Optional[str] = None
    status: int
    llm_name: str
    model_name: str
    api_url: str
    api_key: Optional[str] = None
    api_config: Optional[Dict[str, Any]] = None
    description: Optional[str] = None
    tool_ids: Optional[str]
    tools: Optional[List[AgentToolSchema]]


class WorkflowInfo(BaseModel):
    """工作流信息"""
    id: int
    workflow_name: str
    workflow_code: str
    description: Optional[str] = None
    agents: List[AgentInfo]


class CommonV2ReqAgentsSchema(BaseModel):
    """请求模型"""
    pid: int
    task_id: int
    task_id: Optional[int] = None
    query: str
    agent_uuid: Optional[str] = None
    is_pass: Optional[int] = Field(0, title="是否跳过意图识别")
    audio_url: Optional[str] = Field(None, title="音频模型地址")
    video_url: Optional[str] = Field(None, title="视频模型地址")
    audio_model_id: Optional[int] = Field(None, title="音频模型ID")
    video_model_id: Optional[int] = Field(None, title="视频模型ID")
    workflow: WorkflowInfo
    language: Optional[LanguageEnum] = Field(default=LanguageEnum.CHINESE, title="语言")
    doc_length: Optional[int] = Field(default=270, title="文档长度")
    video_model_pic_url: Optional[str] = Field(None, title="视频预览图")
    voice_is_upload: Optional[int] = Field(0, title="声音是否上传")
    voice_upload_url: Optional[str] = Field(None, title="声音地址")
    is_person: Optional[int] = Field(None, title="启动人设")
    is_search: Optional[int] = Field(None, title="启动搜索")
    is_rag: Optional[int] = Field(None, title="启动RAG")
    style: Optional[str] = Field(None, title="设置风格")
    read_score: Optional[int] = Field(None, title="易读得分")
    is_knowledge: Optional[int] = Field(None, title="启动用户图谱")
