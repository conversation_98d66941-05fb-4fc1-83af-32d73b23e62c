#!/usr/bin/python3
# -*- coding: utf-8 -*-
"""
功能描述：音视频文件识别模型定义
----------------------------------------------------
@Project :   xinhou-openai-admin
@File    :   ReqV1AudioRecognitionSchema.py
@Contact :   <EMAIL>

@Modify Time      <AUTHOR>    @Desciption
------------      -------    --------    -----------
2023/6/14 16:52   logic      1.0         音视频文件识别模型
"""
from typing import Optional, List, Dict, Any, Union
from pydantic import BaseModel, Field
from pydantic.v1 import Required


class ReqV1AudioRecognitionSchema(BaseModel):
    """
    音视频文件识别请求模型
    """
    file_url: str = Field(title="文件URL", description="要识别的音视频文件URL")
    knowledge_id: Optional[int] = Field(default=None, title="知识库ID", description="关联的知识库ID")
    file_format: Optional[str] = Field(default="wav", title="文件格式", description="音视频文件格式：wav、mp3等")
    sample_rate: Optional[int] = Field(default=16000, title="采样率", description="音频采样率，默认16000Hz")
    enable_words: Optional[bool] = Field(default=False, title="是否输出词信息", description="是否启用词级别时间戳")
    auto_split: Optional[bool] = Field(default=False, title="是否启用智能分轨", description="是否启用智能分轨功能")


class RecognitionTaskResult(BaseModel):
    """
    识别任务结果模型
    """
    task_id: str = Field(title="任务ID", description="阿里云识别任务ID")
    status: str = Field(title="任务状态", description="任务状态: QUEUEING、RUNNING、SUCCESS等")
    result: Optional[Dict[str, Any]] = Field(default=None, title="识别结果", description="完整的识别结果")
    text: Optional[str] = Field(default=None, title="识别文本", description="识别出的文本内容")
    sentences: Optional[List[Dict[str, Any]]] = Field(default=None, title="句子列表", description="分句识别结果")


class ResV1AudioRecognitionSchema(BaseModel):
    """
    音视频文件识别响应模型
    """
    code: Optional[int] = Field(default=Required, title="编码", description="返回请求编码")
    msg: Optional[str] = Field(default=Required, title="消息", description="返回请求消息")
    data: Optional[RecognitionTaskResult] = Field(default=None, title="内容", description="返回内容") 