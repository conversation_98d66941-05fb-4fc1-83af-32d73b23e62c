# !/usr/bin/python3
# -*- coding: utf-8 -*-

"""
用户信息表模型类
----------------------------------------------------
@Project :   xinhou-openai-admin
@File    :   User.py
@Contact :   <EMAIL>

@Modify Time      <AUTHOR>    @Desciption
------------      -------    --------    -----------
2023/04/16 22:04  peng.shen   v1.0.0     None
"""

from datetime import datetime
from typing import Optional

from pydantic import BaseModel, Field


class ReqUserFindSchema(BaseModel):
    """
    用户信息表 查询入参参数&校验模型
    """

    id: Optional[int] = Field(
        default=None, title="用户ID",
    )

    login_name: Optional[str] = Field(
        default=None, title="登录账号",
    )

    login_pwd: Optional[str] = Field(
        default=None, title="密码",
    )

    user_name: Optional[str] = Field(
        default=None, title="用户昵称",
    )

    user_type: Optional[int] = Field(
        default=None, title="用户类型:1=系统用户,2=普通用户",
    )

    email: Optional[str] = Field(
        default=None, title="用户邮箱",
    )

    phone: Optional[str] = Field(
        default=None, title="固定电话",
    )

    mobile: Optional[str] = Field(
        default=None, title="手机号码",
    )

    sex: Optional[int] = Field(
        default=None, title="用户性别:1=男,2=女,3=未知",
    )

    avatar: Optional[str] = Field(
        default=None, title="头像路径",
    )

    status: Optional[int] = Field(
        default=None, title="帐号状态:1=正常,2=禁用"
    )

    del_flag: Optional[int] = Field(
        default=None, title="删除标志:1=代表存在,2=代表删除"
    )

    create_by: Optional[str] = Field(
        default=None, title="创建者",
    )

    created_at: Optional[datetime] = Field(
        default=None, title="创建时间",
    )

    update_by: Optional[str] = Field(
        default=None, title="更新者",
    )

    updated_at: Optional[datetime] = Field(
        default=None, title="更新时间",
    )

    remark: Optional[str] = Field(
        default=None, title="备注",
    )


class ReqUserLoginSchema(BaseModel):
    """
    用户信息表 查询入参参数&校验模型
    """
    login_name: Optional[str] = Field(
        default=None, title="登录账号",
    )

    login_pwd: Optional[str] = Field(
        default=None, title="密码",
    )


class ReqUserSaveSchema(BaseModel):
    """
    用户信息表 保存入参参数&校验模型
    """

    login_name: Optional[str] = Field(
        title="登录账号",
    )

    login_pwd: Optional[str] = Field(
        title="密码",
    )

    user_name: Optional[str] = Field(
        title="用户昵称",
    )

    user_type: Optional[int] = Field(
        title="用户类型:1=系统用户,2=普通用户",
    )

    email: Optional[str] = Field(
        title="用户邮箱",
    )

    phone: Optional[str] = Field(
        title="固定电话",
    )

    mobile: Optional[str] = Field(
        title="手机号码",
    )

    sex: Optional[int] = Field(
        title="用户性别:1=男,2=女,3=未知",
    )

    avatar: Optional[str] = Field(
        title="头像路径",
    )

    remain_point: Optional[float] = Field(default=0, title="用户剩余使用时间")


class ReqUserUpdateSchema(BaseModel):
    """
    用户信息表 更新入参参数&校验模型
    """

    id: Optional[int] = Field(
        title="用户ID",
    )

    login_name: Optional[str] = Field(
        title="登录账号",
    )

    login_pwd: Optional[str] = Field(
        title="密码",
    )

    user_name: Optional[str] = Field(
        title="用户昵称",
    )

    user_type: Optional[int] = Field(
        title="用户类型:1=系统用户,2=普通用户",
    )

    email: Optional[str] = Field(
        title="用户邮箱",
    )

    phone: Optional[str] = Field(
        title="固定电话",
    )

    mobile: Optional[str] = Field(
        title="手机号码",
    )

    sex: Optional[int] = Field(
        title="用户性别:1=男,2=女,3=未知",
    )

    avatar: Optional[str] = Field(
        title="头像路径",
    )

    salt: Optional[str] = Field(
        title="盐加密",
    )

    status: Optional[int] = Field(
        title="帐号状态:1=正常,2=禁用", default='1',
    )

    del_flag: Optional[int] = Field(
        title="删除标志:1=代表存在,2=代表删除", default='1',
    )

    login_ip: Optional[str] = Field(
        title="最后登陆IP",
    )

    login_date: Optional[datetime] = Field(
        title="最后登陆时间",
    )

    create_by: Optional[str] = Field(
        title="创建者",
    )

    created_at: Optional[datetime] = Field(
        title="创建时间",
    )

    update_by: Optional[str] = Field(
        title="更新者",
    )

    updated_at: Optional[datetime] = Field(
        title="更新时间",
    )

    remark: Optional[str] = Field(
        title="备注",
    )

    remain_point: Optional[float] = Field(title="用户剩余使用时间")


class ResUserDetailSchema(BaseModel):
    """
    用户信息表 出参参数&校验模型
    """

    id: Optional[int] = Field(
        default=None, title="用户ID",
    )

    login_name: Optional[str] = Field(
        default=None, title="登录账号",
    )

    login_pwd: Optional[str] = Field(
        default=None, title="密码",
    )

    user_name: Optional[str] = Field(
        default=None, title="用户昵称",
    )

    user_type: Optional[int] = Field(
        default=None, title="用户类型:1=系统用户,2=普通用户",
    )

    email: Optional[str] = Field(
        default=None, title="用户邮箱",
    )

    phone: Optional[str] = Field(
        default=None, title="固定电话",
    )

    mobile: Optional[str] = Field(
        default=None, title="手机号码",
    )

    sex: Optional[int] = Field(
        default=None, title="用户性别:1=男,2=女,3=未知",
    )

    avatar: Optional[str] = Field(
        default=None, title="头像路径",
    )

    status: Optional[int] = Field(
        default=None, title="帐号状态:1=正常,2=禁用"
    )

    del_flag: Optional[int] = Field(
        default=None, title="删除标志:1=代表存在,2=代表删除"
    )
    create_by: Optional[str] = Field(
        default=None, title="创建者",
    )

    created_at: Optional[datetime] = Field(
        default=None, title="创建时间",
    )

    update_by: Optional[str] = Field(
        default=None, title="更新者",
    )

    updated_at: Optional[datetime] = Field(
        default=None, title="更新时间",
    )

    remark: Optional[str] = Field(
        default=None, title="备注",
    )

    remain_point: Optional[float] = Field(default=0, title="用户剩余使用时间")


class ReqChangePasswordSchema(BaseModel):
    """
    修改密码请求模型
    """
    old_password: str = Field(..., title="原密码")
    new_password: str = Field(..., title="新密码")


class ReqChangePasswordByMobileSchema(BaseModel):
    """
    通过手机号和验证码修改密码请求模型
    """
    mobile: str = Field(..., title="手机号")
    verify_code: str = Field(..., title="验证码")
    new_password: str = Field(..., title="新密码")


class ReqUserMobileLoginSchema(BaseModel):
    mobile: str
    verify_code: str


class ReqUserMobileRegisterSchema(BaseModel):
    mobile: str
    verify_code: str


class ReqAllocatePointsSchema(BaseModel):
    """
    用户分配点数请求模型
    """
    pid: int = Field(..., description="IP ID")
    points: float = Field(..., gt=0, description="要分配的点数")
