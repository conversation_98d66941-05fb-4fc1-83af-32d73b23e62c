from enum import Enum


class KnowledgePlatformEnums(Enum):
    DOUYIN = ('douyin.com', '我的抖音', 3)
    KUAISHOU = ('kuaishou.com', '我的快手', 2)
    # 待添加后续支持平台

    def __init__(self, url_identifier, knowledge_source, knowledge_type):
        self.url_identifier = url_identifier
        self.knowledge_source = knowledge_source
        self.knowledge_type = knowledge_type

    @classmethod
    def get_platform(cls, url):
        for platform in cls:
            if platform.url_identifier in url:
                return platform
        return None
