from datetime import datetime
from typing import Optional
from pydantic import BaseModel, Field


# -------------------------------- 核心 Schema --------------------------------
class ReqPrecontractSaveSchema(BaseModel):
    """预签约信息表 新增入参"""

    mobile: str = Field(
        ...,
        title="手机号（必填）",
        max_length=20,
        pattern=r"^1[3-9]\d{9}$",  # 手机号正则校验
    )
    name: Optional[str] = Field(default=None, title="姓名", max_length=50)
    business_name: Optional[str] = Field(default=None, title="公司名称", max_length=100)
    city: Optional[str] = Field(default=None, title="城市", max_length=50)
    verify_code: Optional[str] = Field(
        default=None,
        title="短信验证码",
        min_length=4,
        max_length=4,
        pattern=r"^\d{4}$",  # 可选添加6位数字校验
    )
    remark: Optional[str] = Field(default=None, title="备注", max_length=500)


class ReqPrecontractUpdateSchema(BaseModel):
    """预签约信息表 更新入参"""

    id: int = Field(..., title="记录ID")
    mobile: Optional[str] = Field(
        default=None, title="手机号", max_length=20, pattern=r"^1[3-9]\d{9}$"
    )
    name: Optional[str] = Field(default=None, title="姓名", max_length=50)
    company: Optional[str] = Field(default=None, title="公司名称", max_length=100)
    city: Optional[str] = Field(default=None, title="城市", max_length=50)
    remark: Optional[str] = Field(default=None, title="备注", max_length=500)


class ResPrecontractDetailSchema(BaseModel):
    """预签约信息表 详情出参"""

    id: Optional[int] = Field(default=None, title="记录ID")
    mobile: Optional[str] = Field(default=None, title="手机号")
    name: Optional[str] = Field(default=None, title="姓名")
    company: Optional[str] = Field(default=None, title="公司名称")
    city: Optional[str] = Field(default=None, title="城市")
    create_by: Optional[str] = Field(default=None, title="创建人")
    created_at: Optional[datetime] = Field(default=None, title="创建时间")
    update_by: Optional[str] = Field(default=None, title="更新人")
    updated_at: Optional[datetime] = Field(default=None, title="更新时间")
    remark: Optional[str] = Field(default=None, title="备注")


# -------------------------------- 业务扩展 Schema --------------------------------
class ReqBindPrecontractSchema(BaseModel):
    """预签约手机号绑定请求模型"""

    user_id: int = Field(..., title="用户ID")
    mobile: Optional[str] = Field(
        default=None, title="手机号", max_length=20, pattern=r"^1[3-9]\d{9}$"
    )
    verify_code: Optional[str] = Field(
        default=None,
        title="短信验证码",
        min_length=6,
        max_length=6,
        pattern=r"^\d{6}$",  # 可选添加6位数字校验
    )
