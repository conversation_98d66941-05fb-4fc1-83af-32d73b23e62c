from datetime import datetime
from typing import Optional

from pydantic import BaseModel, Field


class ReqIpPromptFindSchema(BaseModel):
    """查询入参"""
    id: Optional[int] = Field(default=None, title="ID")
    ip_name: Optional[str] = Field(default=None, title="IP名称")
    pid: Optional[int] = Field(default=None, title="IP ID")


class ReqIpPromptSaveSchema(BaseModel):
    """保存入参"""
    ip_name: str = Field(..., title="IP名称")
    json: Optional[str] = Field(default=None, title="JSON内容")
    summary: Optional[str] = Field(default=None, title="摘要")
    pid: int = Field(..., title="IP ID")


class ReqIpPromptUpdateSchema(BaseModel):
    """更新入参"""
    id: int = Field(..., title="ID")
    ip_name: Optional[str] = Field(default=None, title="IP名称")
    json: Optional[str] = Field(default=None, title="JSON内容") 
    summary: Optional[str] = Field(default=None, title="摘要")
    pid: Optional[int] = Field(default=None, title="IP ID")


class ResIpPromptDetailSchema(BaseModel):
    """返回详情"""
    id: int = Field(..., title="ID")
    ip_name: Optional[str] = Field(default=None, title="IP名称")
    json: Optional[str] = Field(default=None, title="JSON内容")
    summary: Optional[str] = Field(default=None, title="摘要")
    pid: Optional[int] = Field(default=None, title="IP ID")
    create_by: Optional[str] = Field(default=None, title="创建者")
    created_at: Optional[datetime] = Field(default=None, title="创建时间")
    update_by: Optional[str] = Field(default=None, title="更新者")
    updated_at: Optional[datetime] = Field(default=None, title="更新时间") 