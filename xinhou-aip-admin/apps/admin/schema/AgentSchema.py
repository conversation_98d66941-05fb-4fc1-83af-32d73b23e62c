from datetime import datetime
from typing import Optional, List

from pydantic import BaseModel, Field


class ReqAgentCreateSchema(BaseModel):
    """创建Agent请求模型"""
    agent_name_cn: str = Field(..., title="Agent中文名称")
    agent_name_en: str = Field(..., title="Agent英文名称")
    agent_code: str = Field(..., title="Agent编码")
    agent_type: int = Field(..., title="Agent类型:1=系统Agent,2=用户Agent")
    agent_role: int = Field(..., title="Agent角色:1=意图识别,2=路由,3=开始agent,4=执行,5=结束")
    agent_style: str = Field('USUALLY', title="Agent样式:USUALLY=通用输出,COMMAND=需求分析输出,TITLE_CHOOSE=选题专家输出")
    llm_id: int = Field(..., title="关联的LLM ID")
    influence_scope: Optional[str] = Field(None, title="影响范围Agent IDs")
    prompt_cn: Optional[str] = Field(None, title="中文prompt")
    prompt_en: Optional[str] = Field(None, title="英文prompt")
    description: Optional[str] = Field(None, title="描述")
    status: Optional[int] = Field(1, title="状态:1=正常,2=禁用")
    remark: Optional[str] = Field(None, title="备注")
    agent_action: Optional[str]


class ReqAgentUpdateSchema(BaseModel):
    """更新Agent请求模型"""
    id: int
    agent_name_cn: Optional[str] = None
    agent_name_en: Optional[str] = None
    agent_type: Optional[str] = None
    agent_role: Optional[str] = None
    agent_style: Optional[str] = None
    llm_id: Optional[int] = None
    influence_scope: Optional[str] = None
    prompt_cn: Optional[str] = None
    prompt_en: Optional[str] = None
    status: Optional[int] = None
    remark: Optional[str] = None
    agent_action: Optional[str]


class ResAgentSchema(BaseModel):
    """Agent响应模型"""
    id: int
    agent_name_cn: str
    agent_name_en: str
    agent_code: str
    agent_type: str
    agent_role: str
    agent_style: str
    llm_id: int
    influence_scope: Optional[str]
    prompt_cn: Optional[str]
    prompt_en: Optional[str]
    description: Optional[str]
    status: int
    remark: Optional[str]
    created_at: datetime
    updated_at: datetime
    agent_action: Optional[str]


class ReqAgentDeleteSchema(BaseModel):
    """删除Agent请求模型"""
    id: int


class AgentToolSchema(BaseModel):
    id: int
    tool_name: str
    tool_function: str
    

class AgentSchema(BaseModel):
    id: int
    agent_name_cn: str
    agent_code: str
    agent_type: str
    agent_role: str
    llm_id: int
    influence_scope: Optional[str]
    prompt_cn: Optional[str]
    prompt_en: Optional[str]
    agent_name_en: str
    description: Optional[str]
    agent_style: Optional[str] = 'USUALLY'
    tool_ids: Optional[str]
    tools: Optional[List[AgentToolSchema]]
    agent_action: Optional[str]

    class Config:
        orm_mode = True
