# !/usr/bin/python3
# -*- coding: utf-8 -*-


from typing import Optional

from pydantic import BaseModel, Field


class ReqCreateRpaRobotSchema(BaseModel):
    create_by: str = Field(title="创建人", default="admin")
    update_by: str = Field(title="更新人", default="admin")
    robot_name: str = Field(title="机器人名称")
    robot_type: str = Field(title="机器人类型")
    robot_platform: str = Field(title="机器人平台")
    robot_description: Optional[str] = Field(default="", title="机器人详情")
