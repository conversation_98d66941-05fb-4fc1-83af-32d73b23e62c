# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
功能描述
----------------------------------------------------
@Project :   xinhou-openai-embedding
@File    :   V4ReqTrainingFileSchema.py
@Contact :   <EMAIL>

@Modify Time      <AUTHOR>    @Desciption
------------      -------    --------    -----------
2023/6/5 21:26   logic   1.0         None
"""

from pydantic import BaseModel, Field
from pydantic.v1 import Required


class CommonV1ReqBGESchema(BaseModel):
    """
    文本训练入参模型
    """
    pid: int = Field(
        default=Required, title="pid"
    )


class CommonUploadV1ReqBGESchema(BaseModel):
    """
    文本训练入参模型
    """
    pid: int = Field(
        default=Required, title="pid"
    )
    json_str: str = Field(
        default=Required, title="json"
    )
