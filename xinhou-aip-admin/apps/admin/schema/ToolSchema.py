from datetime import datetime
from typing import Optional
from pydantic import BaseModel, Field


class ReqToolCreateSchema(BaseModel):
    """创建Tool请求模型"""
    tool_name: str = Field(..., title="工具名称")
    tool_function: str = Field(..., title="工具函数")
    description: Optional[str] = Field(None, title="工具描述")
    status: Optional[int] = Field(1, title="状态:1=正常,2=禁用")
    remark: Optional[str] = Field(None, title="备注")


class ReqToolUpdateSchema(BaseModel):
    """更新Tool请求模型"""
    id: int
    tool_name: Optional[str] = None
    tool_function: Optional[str] = None
    description: Optional[str] = None
    status: Optional[int] = None
    remark: Optional[str] = None


class ResToolSchema(BaseModel):
    """Tool响应模型"""
    id: int
    tool_name: str
    tool_type: int
    tool_function: str
    description: Optional[str]
    status: int
    remark: Optional[str]
    created_at: datetime
    updated_at: datetime

    class Config:
        orm_mode = True


class ReqToolDeleteSchema(BaseModel):
    """删除Tool请求模型"""
    id: int 