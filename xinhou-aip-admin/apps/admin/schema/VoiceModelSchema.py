from datetime import datetime
from typing import Optional

from pydantic import BaseModel, Field


class ResVoiceModelDetailSchema(BaseModel):
    id: int = Field(..., title="ID")
    voice_name: Optional[str] = Field(None, title="音色名称")
    voice_url: Optional[str] = Field(None, title="试听链接")
    status: Optional[int] = Field(1, title="音色状态")
    del_flag: Optional[int] = Field(1, title="删除标志")
    create_by: Optional[str] = Field(None, title="创建者")
    created_at: Optional[datetime] = Field(None, title="创建时间")
    update_by: Optional[str] = Field(None, title="更新者")
    updated_at: Optional[datetime] = Field(None, title="更新时间")
    remark: Optional[str] = Field(None, title="备注")
    pid: Optional[int] = Field(None, title="IPID")

    class Config:
        from_attributes = True


class ReqVoiceModelFindSchema(BaseModel):
    voice_name: Optional[str] = Field(None, title="音色名称")
    status: Optional[int] = Field(None, title="音色状态")
    del_flag: Optional[int] = Field(1, title="删除标志")
    pid: Optional[int] = Field(None, title="IPID")


class ReqVoiceModelSaveSchema(BaseModel):
    voice_name: str = Field(..., title="音色名称")
    voice_url: Optional[str] = Field(None, title="试听链接")
    status: Optional[int] = Field(1, title="音色状态")
    del_flag: Optional[int] = Field(1, title="删除标志")
    create_by: Optional[str] = Field(None, title="创建者")
    remark: Optional[str] = Field(None, title="备注")
    pid: int = Field(..., title="IPID")

class ReqVoiceModelSaveHistorySchema(BaseModel):
    """
    声音模型保存请求模型
    """
    voice_name: str = Field(..., title="模型名称")
    voice_url: str = Field(..., title="模型URL")
    new_voice_url: str = Field(..., title="新模型URL")
    uuid: Optional[str] = Field(..., title="历史记录UUID")
    task_id: Optional[int] = Field(..., title="任务ID")
    pid: Optional[int] = Field(..., title="项目ID")


class ReqVoiceModelUpdateSchema(BaseModel):
    id: int = Field(..., title="ID")
    voice_name: Optional[str] = Field(None, title="音色名称")
    voice_url: Optional[str] = Field(None, title="试听链接")
    status: Optional[int] = Field(None, title="音色状态")
    del_flag: Optional[int] = Field(1, title="删除标志")
    update_by: Optional[str] = Field(None, title="更新者")
    remark: Optional[str] = Field(None, title="备注")
    pid: Optional[int] = Field(None, title="IPID")


class ReqVideoTranscodeSchema(BaseModel):
    input_url: str = Field(..., title="输入视频URL")
    pid: Optional[int] = Field(None, title="IPID")
