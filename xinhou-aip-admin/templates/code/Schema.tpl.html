1# !/usr/bin/python3
# -*- coding: utf-8 -*-

"""
{{ table.table_comment }}模型类
----------------------------------------------------
@Project :   {{ table.project_name }}
@File    :   {{ table.file_name }}
@Contact :   {{ table.contact }}

@Modify Time      <AUTHOR>    @Desciption
------------      -------    --------    -----------
{{ table.modify_time }}  {{ table.author }}   {{ table.version }}     {{ table.desciption }}
"""

from datetime import datetime
from typing import Optional

from pydantic import BaseModel, Field


class {{ table.class_name }}Schema(BaseModel):
"""
{{ table.table_comment }}参数&校验模型
"""
{% for field in table.fields %}
{{ field.column_name }}: Optional[{{ field.column_type|mapping_schema_column_type }}] = Field(
title="{{ field.column_comment }}"{{ field.column_default|check_schema_column_default }},
)
{% endfor %}
