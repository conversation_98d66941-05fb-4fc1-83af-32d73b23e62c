# !/usr/bin/python3
# -*- coding: utf-8 -*-

"""
{{ table.table_comment }}模型类
----------------------------------------------------
@Project :   {{ table.project_name }}
@File    :   {{ table.file_name }}
@Contact :   {{ table.contact }}

@Modify Time      <AUTHOR>    @Desciption
------------      -------    --------    -----------
{{ table.modify_time }}  {{ table.author }}   {{ table.version }}     {{ table.desciption }}
"""

from sqlalchemy import ForeignKey
from sqlalchemy import func

from sqlalchemy import Column, Integer, String, Text, Numeric, TIMESTAMP
from xinhou_openai_framework.core.orm.entity.BaseEntity import BaseEntity


class {{ table.class_name }}(BaseEntity):
# 数据表名&字段
__tablename__ = '{{ table.table_name }}'
{% for field in table.fields %}{% if not field.column_name in table.ignore_fields %}
{{ field.column_name }} = Column({{ field.column_type|mapping_column_type }}{{ field.column_default|check_column_default }}, comment="{{ field.column_comment }}"){% endif %}{% endfor %}
