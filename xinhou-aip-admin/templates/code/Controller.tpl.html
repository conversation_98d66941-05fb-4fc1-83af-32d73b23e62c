# !/usr/bin/python3
# -*- coding: utf-8 -*-

"""
{{ table.table_comment }}控制器类
----------------------------------------------------
@Project :   {{ table.project_name }}
@File    :   {{ table.file_name }}
@Contact :   {{ table.contact }}

@Modify Time      <AUTHOR>    @Desciption
------------      -------    --------    -----------
{{ table.modify_time }}  {{ table.author }}   {{ table.version }}     {{ table.desciption }}
"""

from fastapi import APIRouter, Depends
from fastapi.params import Path
from loguru import logger
from sqlalchemy.orm import Session

from {{ table.package_schema }}.{{ table.class_name }}Schema import {{ table.class_name }}Schema
from {{ table.package_service }}.{{ table.class_name }}Service import {{ table.class_name }}Service
from {{ table.package_model }}.{{ table.class_name }} import {{ table.class_name }}
from xinhou_openai_framework.core.db.DatabaseManager import DatabaseManager
from xinhou_openai_framework.core.exception.GlobalExceptionType import ParameterException
from xinhou_openai_framework.core.reponse.R import R
from xinhou_openai_framework.pages.PageHelper import PageHelper

api = APIRouter()


@api.post('/{{ table.module_name }}/{{ table.clazz_name }}/find',
tags=["{{ table.module_name }}","{{ table.clazz_name }}"],
summary="查询{{ table.table_comment }}数据接口",
description="通过参数模型传递条件查询")
async def find(search: PageHelper[{{ table.class_name }}Schema], db: Session = Depends(DatabaseManager().get_session)):
logger.info("[find][request]:{}".format(search.model_dump()))
paginate = {{ table.class_name }}Service(db).find_by(search)
return R.SUCCESS(paginate)


@api.post('/{{ table.module_name }}/{{ table.clazz_name }}/findAll',
tags=["{{ table.module_name }}","{{ table.clazz_name }}"],
summary="查询所有{{ table.table_comment }}数据接口",
description="通过参数模型传递条件查询")
async def find_all(search: {{ table.class_name }}Schema, db: Session = Depends(DatabaseManager().get_session)):
logger.info("[findAll][request]:{}".format(search.model_dump()))
datas = {{ table.class_name }}Service(db).find_all(search)
return R.SUCCESS(datas)


@api.post("/{{ table.module_name }}/{{ table.clazz_name }}/save",
tags=["{{ table.module_name }}","{{ table.clazz_name }}"],
summary="保存{{ table.table_comment }}数据接口",
description="通过参数模型保存数据")
async def save(model: {{ table.class_name }}Schema, db: Session = Depends(DatabaseManager().get_session)):
logger.info("[save][request]:{}".format(model.json()))
return R.SUCCESS({{ table.class_name }}Service(db).save({{ table.class_name }}(**model.dict(exclude_unset=True))))


@api.get("/{{ table.module_name }}/{{ table.clazz_name }}/delete/{id}",
tags=["{{ table.module_name }}","{{ table.clazz_name }}"],
summary="删除{{ table.table_comment }}数据接口",
description="根据ID删除数据")
async def delete(id: int = Path(title="ID不能为空"), db: Session = Depends(DatabaseManager().get_session)):
logger.info("[detail][request]:{}".format({"id": id}))
if id is None:
raise ParameterException()
{{ table.class_name }}Service(db).delete({{ table.class_name }}(id=id))
return R.SUCCESS()


@api.post("/{{ table.module_name }}/{{ table.clazz_name }}/update",
tags=["{{ table.module_name }}","{{ table.clazz_name }}"],
summary="更新{{ table.table_comment }}数据接口",
description="根据ID更新数据")
async def update(model: {{ table.class_name }}Schema, db: Session = Depends(DatabaseManager().get_session)):
logger.info("[save][request]:{}".format(model.json()))
if model.id is None:
raise ParameterException()
return R.SUCCESS({{ table.class_name }}Service(db).update({{ table.class_name }}(**model.dict(exclude_unset=True))))


@api.get("/{{ table.module_name }}/{{ table.clazz_name }}/detail/{id}",
tags=["{{ table.module_name }}","{{ table.clazz_name }}"],
summary="获取{{ table.table_comment }}详情接口",
description="根据ID获取数据")
async def detail(id: int = Path(title="ID不能为空"), db: Session = Depends(DatabaseManager().get_session)):
logger.info("[detail][request]:{}".format({"id": id}))
if id is None:
raise ParameterException()
return R.SUCCESS({{ table.class_name }}Service(db).find_by_id({{ table.class_name }}(id=id)))


@api.post("/{{ table.module_name }}/{{ table.clazz_name }}/exists",
tags=["{{ table.module_name }}","{{ table.clazz_name }}"],
summary="判断{{ table.table_comment }}数据是否存在接口",
description="通过参数模型传递条件查询")
async def exists(model: {{ table.class_name }}Schema, db: Session = Depends(DatabaseManager().get_session)):
logger.info("[exists][request]:{}".format({"id": id}))
return R.SUCCESS({{ table.class_name }}Service(db).exists({{ table.class_name }}(**model.dict(exclude_unset=True))))

