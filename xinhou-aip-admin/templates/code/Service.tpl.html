# !/usr/bin/python3
# -*- coding: utf-8 -*-

"""
{{ table.table_comment }}服务类
----------------------------------------------------
@Project :   {{ table.project_name }}
@File    :   {{ table.file_name }}
@Contact :   {{ table.contact }}

@Modify Time      <AUTHOR>    @Desciption
------------      -------    --------    -----------
{{ table.modify_time }}  {{ table.author }}   {{ table.version }}     {{ table.desciption }}
"""

from sqlalchemy.orm import Session
from {{ table.package_model }}.{{ table.class_name }} import {{ table.class_name }}
from xinhou_openai_framework.core.orm.service.BaseServiceImpl import BaseServiceImpl
from xinhou_openai_framework.pages.PageHelper import PageHelper
from xinhou_openai_framework.pages.Paginate import Paginate


class {{ table.class_name }}Service(BaseServiceImpl[{{ table.class_name }}]):
"""
{{ table.table_comment }}服务类
"""

def __init__(self, db: Session):
super({{ table.class_name }}Service, self).__init__(db, {{ table.class_name }})

