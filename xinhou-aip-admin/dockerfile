FROM python:3.10-slim

# 设置环境变量避免 Python 生成 .pyc 文件和启用 unbuffered 模式
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONUNBUFFERED=1

# 设置时区
ENV TZ=Asia/Shanghai
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

WORKDIR /home/<USER>/xinhou-aip-admin

# 安装系统依赖
RUN apt-get update && apt-get install -y --no-install-recommends \
    # OpenCV 依赖
    libgl1-mesa-glx \
    libglib2.0-0 \
    # FFmpeg 依赖 (用于处理音频/视频)
    ffmpeg \
    # 构建工具（添加这些可能有助于解决构建问题）
    build-essential \
    # 清理 apt 缓存
    && rm -rf /var/lib/apt/lists/*

# 复制并安装 Python 依赖
COPY requirements.txt .

# 修改pip安装部分，单独处理有问题的包
RUN pip config set global.index-url https://pypi.tuna.tsinghua.edu.cn/simple \
    && pip install --no-cache-dir --upgrade pip wheel setuptools \
    && pip install --no-cache-dir aliyun-python-sdk-core --no-build-isolation \
    && pip install --no-cache-dir -r requirements.txt \
    && rm -rf /root/.cache/pip/*

# 复制应用代码
COPY . .

# 设置默认的命令
CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8080", "--workers", "11", "--limit-concurrency", "20",  "--loop", "asyncio", "--proxy-headers"]