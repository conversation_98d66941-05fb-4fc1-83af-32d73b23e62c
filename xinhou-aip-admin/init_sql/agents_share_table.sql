-- 创建Agent任务分享表
CREATE TABLE IF NOT EXISTS `t_agents_share` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `task_id` bigint(20) NOT NULL COMMENT '关联的任务ID',
  `pid` bigint(20) NOT NULL COMMENT '博主ID',
  `share_code` varchar(32) NOT NULL COMMENT '分享唯一标识码',
  `is_public` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否公开分享，1：公开，0：不公开',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态，1：正常，0：删除',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_by` varchar(30) DEFAULT NULL COMMENT '创建人',
  `update_by` varchar(30) DEFAULT NULL COMMENT '更新人',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_share_code` (`share_code`),
  KEY `idx_task_id` (`task_id`),
  KEY `idx_pid` (`pid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='Agent任务分享表';