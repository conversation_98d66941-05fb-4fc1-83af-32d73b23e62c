-- -------------------------------------------------------------
-- TablePlus 6.1.8(574)
--
-- https://tableplus.com/
--
-- Database: xinhou_aip_v2_db
-- Generation Time: 2025-04-28 15:13:12.4030
-- -------------------------------------------------------------


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;


CREATE TABLE `_prisma_migrations` (
  `id` varchar(36) COLLATE utf8mb4_unicode_ci NOT NULL,
  `checksum` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL,
  `finished_at` datetime(3) DEFAULT NULL,
  `migration_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `logs` text COLLATE utf8mb4_unicode_ci,
  `rolled_back_at` datetime(3) DEFAULT NULL,
  `started_at` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  `applied_steps_count` int unsigned NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `hot_title` (
  `id` int NOT NULL AUTO_INCREMENT,
  `folder_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
  `hot_time` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL,
  `hot_text` text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin COMMENT '热点文章',
  `hot_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '热点链接',
  `industry_tags` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '行业标签',
  `content_classify` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '内容分类',
  `create_by` varchar(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '创建者',
  `update_by` varchar(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '更新者',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;

CREATE TABLE `ip_prompt` (
  `id` int NOT NULL AUTO_INCREMENT,
  `ip_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL,
  `json` text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin,
  `summary` text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin,
  `create_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL,
  `pid` int DEFAULT NULL COMMENT 'IPID',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1500 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;

CREATE TABLE `t_admin_user` (
  `id` int NOT NULL AUTO_INCREMENT,
  `username` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL,
  `password` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
  `real_name` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `email` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `mobile` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `avatar` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `status` int NOT NULL DEFAULT '1',
  `role` int NOT NULL DEFAULT '3',
  `last_login` datetime DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `create_by` int DEFAULT NULL,
  `update_by` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `remark` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `t_admin_user_username_key` (`username`),
  KEY `idx_create_by` (`create_by`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `t_admin_user_customer` (
  `admin_user_id` int NOT NULL,
  `customer_id` int NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `created_by` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`admin_user_id`,`customer_id`),
  KEY `t_admin_user_customer_customer_id_fkey` (`customer_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `t_agent` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `agent_name_cn` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT 'Agent名称',
  `agent_code` varchar(100) NOT NULL COMMENT 'Agent编码',
  `agent_type` int NOT NULL COMMENT 'Agent类型:1=系统Agent,2=用户Agent,9=QueryAgent,3=搜索工具Agent,4=存储Agent,5=工具等待Agent,6=非工具Agent的暂停',
  `agent_role` int NOT NULL COMMENT 'Agent角色:1=意图识别,2=路由,3=开始agent,4=执行,5=结束',
  `llm_id` bigint NOT NULL COMMENT '关联的LLM ID',
  `influence_scope` varchar(255) DEFAULT NULL COMMENT '影响范围Agent IDs',
  `prompt_cn` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `status` int DEFAULT '1' COMMENT '任务状态:1=正常,2=禁用',
  `del_flag` int DEFAULT '1' COMMENT '删除标志:1=代表存在,2=代表删除',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `prompt_en` text CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci COMMENT '英文prompt',
  `agent_name_en` varchar(100) NOT NULL COMMENT 'Agent英文名称',
  `description` text COMMENT '描述',
  `agent_style` varchar(50) DEFAULT '1' COMMENT '-- agent_style 说明：\n-- USUALLY: 通用输出（用于观点专家、钩子专家、标题专家等）\n-- COMMAND: 需求分析输出\n-- USER: 用户代理\n-- TITLE_CHOOSE: 选题专家输出（返回选题列表）\n-- INTERVIEW: 采访编辑加载中\n-- DATA_LOADING_1: 数据分析\n-- DATA_LOADING_2: 数据清洗\n-- DATA: 数据专家输出（返回相关视频链接）\n-- STRUCT: 结构专家输出（返回内容结构）\n-- EMBELLISH: 润色专家输出 \n-- WRITER: 文案写手\n-- VOICE_1: 音频专家1（返回多个音频选项）\n-- VOICE_2: 音频专家2（生成音频进度）\n-- VOICE_3: 音频专家3（返回最终音频URL）\n-- VIDEO_1: 视频专家1（返回多个视频选项）\n-- VIDEO_2: 视频专家2（生成视频进度）\n-- VIDEO_3: 视频专家3（返回最终视频URL）',
  `tool_ids` varchar(500) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '关联工具IDs',
  `agent_action` varchar(500) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT '' COMMENT 'Agent行为',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_agent_code` (`agent_code`),
  KEY `idx_agent_type` (`agent_type`),
  KEY `idx_agent_role` (`agent_role`),
  KEY `idx_llm_id` (`llm_id`)
) ENGINE=InnoDB AUTO_INCREMENT=100 DEFAULT CHARSET=utf8mb3 COMMENT='Agent表';

CREATE TABLE `t_agent_tool` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `workflow_id` bigint NOT NULL COMMENT '工作流ID',
  `agent_id` bigint NOT NULL COMMENT 'Agent ID',
  `execution_order` int NOT NULL COMMENT '主体执行顺序',
  `status` int DEFAULT '1' COMMENT '任务状态:1=正常,2=禁用',
  `del_flag` int DEFAULT '1' COMMENT '删除标志:1=代表存在,2=代表删除',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_workflow_agent` (`workflow_id`,`agent_id`),
  KEY `idx_execution_order` (`execution_order`)
) ENGINE=InnoDB AUTO_INCREMENT=37 DEFAULT CHARSET=utf8mb3 COMMENT='工作流-Agent关联表';

CREATE TABLE `t_agents_history` (
  `id` int NOT NULL AUTO_INCREMENT,
  `task_id` int DEFAULT NULL COMMENT '口播稿的任务id',
  `pid` int DEFAULT NULL COMMENT '博主名称',
  `status` int DEFAULT '1' COMMENT '口播稿生成状态1:制作中,2:完成',
  `history` longblob COMMENT '每个agent的完成记录',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '备注',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL,
  `update_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=24903 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;

CREATE TABLE `t_cdk` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT 'CDK ID',
  `cdk_key` varchar(32) NOT NULL COMMENT 'CDK兑换码',
  `total_uses` int NOT NULL DEFAULT '1' COMMENT '可使用总次数',
  `used_count` int NOT NULL DEFAULT '0' COMMENT '已使用次数',
  `point` float NOT NULL DEFAULT '0' COMMENT '固定点数值',
  `used_time` int DEFAULT NULL COMMENT '有效天数',
  `status` int DEFAULT '1' COMMENT '状态:1=未使用,2=已使用,3=已过期,4=已禁用',
  `del_flag` int DEFAULT '1' COMMENT '删除标志:1=存在,2=删除',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_cdk_key` (`cdk_key`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=65 DEFAULT CHARSET=utf8mb3 COMMENT='CDK兑换码表';

CREATE TABLE `t_cdk_usage_log` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `cdk_id` int NOT NULL COMMENT 'CDK ID',
  `cdk_key` varchar(32) NOT NULL COMMENT 'CDK兑换码',
  `uid` int NOT NULL COMMENT '使用者用户ID',
  `pid` int NOT NULL COMMENT '使用者PID',
  `used_point` float NOT NULL DEFAULT '0' COMMENT '使用点数',
  `used_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '使用时间',
  `status` int DEFAULT '1' COMMENT '状态:1=成功,2=失败',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_cdk_id` (`cdk_id`) USING BTREE,
  KEY `idx_uid` (`uid`) USING BTREE,
  KEY `idx_cdk_key` (`cdk_key`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=28 DEFAULT CHARSET=utf8mb3 COMMENT='CDK使用记录表';

CREATE TABLE `t_comment` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '评论id',
  `task_id` int NOT NULL COMMENT '关联的任务id',
  `comment_type` int NOT NULL COMMENT '评论类型:1=作者自评,2=观众-搞笑,3=观众-讽刺,3=观众-鼓励,4=朋友圈文案,5=群聊转\n发',
  `content` blob COMMENT '神评论内容',
  `status` int DEFAULT '1' COMMENT '评论状态:1=正常,2=待审核,3=已删除',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`),
  KEY `idx_task_id` (`task_id`)
) ENGINE=InnoDB AUTO_INCREMENT=43359 DEFAULT CHARSET=utf8mb3 COMMENT='神评论表';

CREATE TABLE `t_customer` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
  `contact_name` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `contact_phone` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `address` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `create_by` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `update_by` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `remark` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `package_type` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `remain_points` int NOT NULL DEFAULT '0',
  `customer_type` int NOT NULL DEFAULT '1',
  `remain_user_count` int NOT NULL DEFAULT '1',
  `payment_status` int NOT NULL DEFAULT '0',
  `product_id` int DEFAULT NULL,
  `valid_days` int DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_product_id` (`product_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `t_embedding_document` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `embedding_file_id` int DEFAULT '0' COMMENT '训练文件ID',
  `page_content` blob COMMENT '训练分段内容',
  `metadata_source` varchar(128) DEFAULT '' COMMENT '元数据来源',
  `collection_name` varchar(128) DEFAULT NULL COMMENT '集合名称',
  `pk` bigint DEFAULT NULL COMMENT '训练分段ID',
  `create_by` varchar(11) DEFAULT NULL COMMENT '创建者',
  `update_by` varchar(11) DEFAULT NULL COMMENT '更新着',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`),
  KEY `t_embedding_document_embedding_file_id_pk_idx` (`embedding_file_id`,`pk`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=279783 DEFAULT CHARSET=utf8mb3 COMMENT='训练数据信息表';

CREATE TABLE `t_embedding_file` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `file_name` varchar(1688) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT '' COMMENT '文件名称',
  `is_delete` int DEFAULT '0' COMMENT '文件假删除标志：0=未删除，1=已删除',
  `file_name_uuid` varchar(100) DEFAULT '' COMMENT '文件UUID名称',
  `file_type` varchar(100) DEFAULT '' COMMENT '文件类型',
  `file_size` int DEFAULT '0' COMMENT '文件大小',
  `file_path` varchar(1688) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT '' COMMENT '文件访问路径',
  `file_url` varchar(1688) DEFAULT '' COMMENT '文件下载路径',
  `file_educate_status` int DEFAULT '0' COMMENT '文件训练状态:0=未训练,1=已训练,3=训练异常',
  `file_word_num` int DEFAULT '0' COMMENT '文件字数',
  `create_by` varchar(11) DEFAULT NULL COMMENT '创建者',
  `update_by` varchar(11) DEFAULT NULL COMMENT '更新者',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `pid` int DEFAULT NULL COMMENT 'IPID',
  `emb_type` int NOT NULL COMMENT '训练类型：1=用户知识;2=对标内容;3=热点数据;4=作品产出物;5=工作流产出',
  `oid` int DEFAULT NULL COMMENT '源文件id',
  `o_url` varchar(1688) DEFAULT NULL COMMENT '源url',
  `file_review_pic` text COMMENT '平台链接预览图',
  `source` varchar(100) DEFAULT NULL COMMENT '文件来源(暂时只有微信机器人)',
  `extra` varchar(255) DEFAULT NULL COMMENT '额外信息',
  `duration` int DEFAULT NULL COMMENT '媒体时长',
  PRIMARY KEY (`id`),
  KEY `t_embedding_file_pid_oid_idx` (`pid`,`oid`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=90492 DEFAULT CHARSET=utf8mb3 COMMENT='训练嵌入文件信息表';

CREATE TABLE `t_image_model` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `image_url` varchar(500) DEFAULT NULL COMMENT '图片URL',
  `model_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '专属模型类型(口播/访谈)',
  `speech_style` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '口播风格(激情/笑容/严肃/悲伤)',
  `interview_style` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '访谈风格(对谈/教育/知识分享)',
  `duration` varchar(20) NOT NULL COMMENT '模型时长(10-20s/20-30s/30-40s/40-50s)',
  `appearance_note` text COMMENT '形象要求备注',
  `status` int DEFAULT '1' COMMENT '状态:1=正常,2=禁用',
  `del_flag` int DEFAULT '1' COMMENT '删除标志:1=存在,2=删除',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `submit_status` int DEFAULT '1' COMMENT '提交状态',
  `upload_time` timestamp NULL DEFAULT NULL COMMENT '上传时间',
  `pid` int DEFAULT NULL COMMENT 'pid',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=189 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='图片制作专属模型表';

CREATE TABLE `t_ip` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT 'IPID',
  `uid` int DEFAULT NULL COMMENT '用户ID',
  `ip_name` varchar(30) DEFAULT NULL COMMENT 'IP名称',
  `avatar` varchar(500) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '头像路径',
  `status` int DEFAULT '1' COMMENT '帐号状态:1=正常,2=禁用',
  `del_flag` int DEFAULT '1' COMMENT '删除标志:1=代表存在,2=代表删除',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `group_id` varchar(256) DEFAULT NULL,
  `group_name` blob,
  `remain_point` float DEFAULT '0' COMMENT '剩余点数',
  `expire_time` timestamp NULL DEFAULT NULL COMMENT '对应IP到期时间',
  PRIMARY KEY (`id`),
  KEY `t_ip_uid_idx` (`uid`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1494 DEFAULT CHARSET=utf8mb3 COMMENT='IP信息表';

CREATE TABLE `t_ip_knowledge` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '与embedding_file表的id对应',
  `knowledge_source` text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
  `knowledge_type` tinyint NOT NULL COMMENT '知识来源：1=文件;3=抖音;2=快手',
  `account_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '个人社交媒体主页url',
  `pid` int NOT NULL,
  `create_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '创建者',
  `update_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '更新者',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `remark` text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin COMMENT '备注',
  `file_list` text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin COMMENT '关联的t_embedding_file id列表,eg:[50, 60, 95]',
  `avatar` text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin COMMENT '平台的头像',
  `aweme_id_list` text COLLATE utf8mb4_bin COMMENT '抖音视频id集合',
  `cover_url` text COLLATE utf8mb4_bin COMMENT '封面url',
  `is_myself` int NOT NULL COMMENT '是否是自己的媒体账号',
  PRIMARY KEY (`id`),
  CONSTRAINT `t_ip_knowledge_chk_1` CHECK ((`knowledge_type` in (1,2,3)))
) ENGINE=InnoDB AUTO_INCREMENT=1783 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;

CREATE TABLE `t_llm` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `llm_name` varchar(100) NOT NULL COMMENT 'LLM名称',
  `llm_code` varchar(100) NOT NULL COMMENT 'LLM编码',
  `model_name` varchar(100) NOT NULL COMMENT '模型名称',
  `api_url` varchar(255) NOT NULL COMMENT 'API接口地址',
  `api_key` varchar(255) DEFAULT NULL COMMENT 'API密钥',
  `api_config` json DEFAULT NULL COMMENT 'API配置信息',
  `status` int DEFAULT '1' COMMENT '任务状态:1=正常,2=禁用',
  `del_flag` int DEFAULT '1' COMMENT '删除标志:1=代表存在,2=代表删除',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_llm_code` (`llm_code`)
) ENGINE=InnoDB AUTO_INCREMENT=30 DEFAULT CHARSET=utf8mb3 COMMENT='LLM模型表';

CREATE TABLE `t_logs` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  `trace_id` varchar(64) DEFAULT NULL COMMENT '请求追踪ID',
  `user_id` bigint DEFAULT NULL COMMENT '调用用户ID',
  `user_name` varchar(50) DEFAULT NULL COMMENT '调用用户名称',
  `client_ip` varchar(50) DEFAULT NULL COMMENT '客户端IP',
  `user_agent` varchar(500) DEFAULT NULL COMMENT '用户代理信息',
  `request_url` varchar(255) NOT NULL COMMENT '请求URL',
  `request_method` varchar(10) NOT NULL COMMENT '请求方法(GET/POST等)',
  `request_params` text COMMENT '请求参数',
  `request_body` text COMMENT '请求体',
  `response_code` int DEFAULT NULL COMMENT '响应状态码',
  `response_data` text COMMENT '响应数据',
  `error_message` text COMMENT '错误信息',
  `execution_time` bigint DEFAULT NULL COMMENT '执行时间(毫秒)',
  `api_description` varchar(255) DEFAULT NULL COMMENT 'API描述',
  `module` varchar(50) DEFAULT NULL COMMENT '所属模块',
  `status` tinyint(1) DEFAULT '1' COMMENT '状态(1=成功,0=失败)',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_request_url` (`request_url`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_trace_id` (`trace_id`)
) ENGINE=InnoDB AUTO_INCREMENT=263941 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='接口调用日志表';

CREATE TABLE `t_manage_handle_log` (
  `id` int NOT NULL AUTO_INCREMENT,
  `customer_id` int NOT NULL,
  `handle_type` int NOT NULL,
  `points` int NOT NULL,
  `before_points` int NOT NULL,
  `after_points` int NOT NULL,
  `remark` text COLLATE utf8mb4_unicode_ci,
  `status` int NOT NULL DEFAULT '1',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `create_by` int DEFAULT NULL,
  `update_by` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_create_by` (`create_by`),
  KEY `idx_customer_id` (`customer_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `t_media_model` (
  `id` int NOT NULL AUTO_INCREMENT,
  `title` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '标题',
  `pic_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '封面图',
  `status` int DEFAULT '1' COMMENT '视频状态:1=正常,2=禁用',
  `media_url` varchar(4096) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '媒体URL',
  `del_flag` int DEFAULT '1' COMMENT '删除标志:1=代表存在,2=代表删除',
  `create_by` bigint DEFAULT NULL COMMENT '创建者',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` bigint DEFAULT NULL COMMENT '更新者',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `remark` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '备注',
  `pid` int DEFAULT NULL COMMENT 'IPID',
  `transcode` int DEFAULT '0' COMMENT '转码状态:0=转码中；1=转码失败；2=转码成功',
  `resolution` json DEFAULT NULL COMMENT '视频模型详细信息',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=3930 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='数字人多媒体表';

CREATE TABLE `t_payment_log` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `order_id` bigint NOT NULL COMMENT '关联的订单ID',
  `log_type` varchar(20) NOT NULL COMMENT '日志类型',
  `content` text NOT NULL COMMENT '日志内容',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT NULL,
  `create_by` varchar(64) DEFAULT NULL,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_order_id` (`order_id`)
) ENGINE=InnoDB AUTO_INCREMENT=149 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='支付日志表';

CREATE TABLE `t_payment_order` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `trade_no` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '平台订单号',
  `out_trade_no` varchar(64) NOT NULL COMMENT '商户订单号',
  `product_id` bigint DEFAULT NULL COMMENT '商品ID',
  `product_name` varchar(255) NOT NULL COMMENT '商品名称',
  `total_amount` decimal(10,2) NOT NULL COMMENT '订单金额',
  `payment_type` varchar(20) NOT NULL COMMENT '支付方式',
  `trade_status` varchar(20) NOT NULL COMMENT '交易状态',
  `pay_time` datetime DEFAULT NULL COMMENT '支付时间',
  `notify_time` datetime DEFAULT NULL COMMENT '通知时间',
  `notify_url` varchar(255) DEFAULT NULL COMMENT '异步通知地址',
  `return_url` varchar(255) DEFAULT NULL COMMENT '同步返回地址',
  `client_ip` varchar(64) DEFAULT NULL COMMENT '客户端IP',
  `device` varchar(20) DEFAULT NULL COMMENT '设备类型',
  `extra_param` text COMMENT '额外参数',
  `refund_amount` decimal(10,2) DEFAULT '0.00' COMMENT '退款金额',
  `refund_time` datetime DEFAULT NULL COMMENT '退款时间',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_by` varchar(64) DEFAULT NULL,
  `update_by` varchar(64) DEFAULT NULL,
  `user_id` int DEFAULT NULL COMMENT '用户ID',
  `point_amount` int DEFAULT NULL COMMENT '充值点数',
  `valid_days` int DEFAULT NULL COMMENT '有效期(天)',
  `expire_time` datetime DEFAULT NULL COMMENT '到期时间',
  `customer_id` int DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_out_trade_no` (`out_trade_no`),
  KEY `idx_trade_status` (`trade_status`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_customer_id` (`customer_id`)
) ENGINE=InnoDB AUTO_INCREMENT=138 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='支付订单表';

CREATE TABLE `t_precontract` (
  `id` int NOT NULL AUTO_INCREMENT,
  `mobile` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '手机号（唯一标识）',
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '姓名',
  `business_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '公司名称',
  `city` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '城市',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '备注',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '创建人',
  `update_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '更新人',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=77 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='用户基础信息表（手机号/姓名/公司/城市）';

CREATE TABLE `t_product` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '产品id',
  `name` varchar(255) DEFAULT NULL COMMENT '产品名称',
  `money` decimal(10,2) DEFAULT '0.00' COMMENT '产品价格',
  `status` int DEFAULT '1' COMMENT '产品状态:1=正常,2=禁用',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `unit` varchar(64) DEFAULT NULL COMMENT '单位(秒/次)',
  `features` varchar(500) DEFAULT NULL COMMENT '特点',
  `desc` varchar(500) DEFAULT NULL COMMENT '描述',
  `name_en` varchar(255) DEFAULT NULL,
  `money_en` decimal(10,2) DEFAULT NULL,
  `unit_en` varchar(64) DEFAULT NULL COMMENT '单位(英文)',
  `features_en` varchar(500) DEFAULT NULL,
  `desc_en` varchar(500) DEFAULT NULL,
  `point_amount` int NOT NULL DEFAULT '0' COMMENT '充值点数',
  `product_type` tinyint NOT NULL DEFAULT '1' COMMENT '产品类型:1=时长充值,2=其他',
  `valid_days` int DEFAULT '0' COMMENT '有效期(天)',
  `sort` int DEFAULT '0' COMMENT '排序号',
  `is_show_in_official` int DEFAULT '0',
  `service_to` int NOT NULL DEFAULT '1',
  `user_count` int DEFAULT '0',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=409 DEFAULT CHARSET=utf8mb3 COMMENT='产品表';

CREATE TABLE `t_rpa_robot` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '机器人id',
  `create_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '创建者',
  `update_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '更新者',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `robot_name` varchar(255) DEFAULT NULL COMMENT '机器人名称',
  `robot_type` varchar(255) DEFAULT NULL COMMENT '机器人类型',
  `robot_platform` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '机器人平台',
  `robot_description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin COMMENT '机器人描述',
  `is_delete` int(10) unsigned zerofill NOT NULL COMMENT '机器人是否删除',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb3;

CREATE TABLE `t_rpa_task` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '机器人运行任务id',
  `create_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '创建者',
  `update_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '更新者',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `robot_id` int DEFAULT NULL COMMENT '与rpa_robot表的id对应',
  `robot_params` json DEFAULT NULL COMMENT '机器人入参',
  `robot_status` varchar(255) DEFAULT NULL COMMENT '机器人状态',
  `ip_name` varchar(255) DEFAULT NULL COMMENT '运行机器人的ip',
  `result_info` json DEFAULT NULL COMMENT '机器人运行结果信息',
  `result_file` varchar(255) DEFAULT NULL COMMENT '机器人运行产生的文件oss链接信息',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1331 DEFAULT CHARSET=utf8mb3;

CREATE TABLE `t_sales_record` (
  `id` int NOT NULL AUTO_INCREMENT,
  `customer_id` int NOT NULL,
  `contract_no` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `contract_amount` decimal(10,2) NOT NULL DEFAULT '0.00',
  `sign_date` date DEFAULT NULL,
  `start_date` date DEFAULT NULL,
  `end_date` date DEFAULT NULL,
  `sales_type` int NOT NULL DEFAULT '1',
  `status` int NOT NULL DEFAULT '1',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `create_by` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `update_by` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `remark` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_customer_id` (`customer_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `t_task` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '任务id',
  `pid` int DEFAULT NULL COMMENT 'IPID',
  `status` int DEFAULT '1' COMMENT '任务状态:1=正常,2=禁用',
  `del_flag` int DEFAULT '1' COMMENT '删除标志:1=代表存在,2=代表删除',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `progress` int NOT NULL DEFAULT '0' COMMENT '任务进度:0=口播稿初始化,1=生成口播稿中,2=完成口播稿,3=口播稿制作失败4=音频生成中,5=完成音频,6=音频制作失败,7=视频生成中,8=视频制作完成,9=视频制作失败',
  `title` blob COMMENT '标题',
  `audio_job_id` varchar(500) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '音频任务 id',
  `video_job_id` varchar(500) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '视频任务 id',
  `audio_model_id` varchar(500) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '音频模型 id',
  `video_model_id` varchar(500) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '视频模型 id',
  `task_knowledge_ids` varchar(500) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '任务知识库 id',
  `doc_length` int DEFAULT NULL COMMENT '口播稿文本长度',
  `language` varchar(500) DEFAULT NULL COMMENT '语言',
  `is_person` int DEFAULT NULL COMMENT '启动人设',
  `is_search` int DEFAULT NULL COMMENT '启动搜索',
  `style` varchar(500) DEFAULT NULL COMMENT '设置风格',
  `read_score` int DEFAULT NULL COMMENT '易读得分',
  `is_rag` int DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=113675 DEFAULT CHARSET=utf8mb3 COMMENT='任务表';

CREATE TABLE `t_tools` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `tool_name` varchar(100) NOT NULL COMMENT '工具名称',
  `tool_function` varchar(100) NOT NULL COMMENT '工具函数',
  `llm_id` bigint DEFAULT NULL COMMENT '关联的LLM ID',
  `del_flag` int DEFAULT '1' COMMENT '删除标志:1=代表存在,2=代表删除',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  `updated_at` timestamp NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `description` varchar(500) DEFAULT NULL COMMENT '描述',
  `status` int DEFAULT '1' COMMENT '帐号状态:1=正常,2=禁用',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=12 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE `t_user` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `login_name` varchar(30) DEFAULT NULL COMMENT '登录账号',
  `login_pwd` varchar(50) DEFAULT NULL COMMENT '密码',
  `user_name` varchar(30) DEFAULT NULL COMMENT '用户昵称',
  `user_type` int DEFAULT NULL COMMENT '用户类型:1=系统用户,2=普通用户',
  `email` varchar(50) DEFAULT NULL COMMENT '用户邮箱',
  `phone` varchar(20) DEFAULT NULL COMMENT '固定电话',
  `mobile` varchar(11) DEFAULT NULL COMMENT '手机号码',
  `sex` int DEFAULT NULL COMMENT '用户性别:1=男,2=女,3=未知',
  `avatar` varchar(512) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '头像路径',
  `status` int DEFAULT '1' COMMENT '帐号状态:1=正常,2=禁用',
  `del_flag` int DEFAULT '1' COMMENT '删除标志:1=代表存在,2=代表删除',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `remain_point` float DEFAULT '0' COMMENT '剩余点数',
  `balance` decimal(10,2) NOT NULL DEFAULT '0.00',
  `customer_id` int DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_login_name` (`login_name`),
  KEY `idx_customer_id` (`customer_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1532 DEFAULT CHARSET=utf8mb3 COMMENT='用户信息表';

CREATE TABLE `t_virtual_human` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `model_type` varchar(50) NOT NULL COMMENT '超虚拟人制作类型(口播/访谈)',
  `speech_style` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '口播风格(激情/笑容/严肃/悲伤)',
  `interview_style` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '访谈风格(对谈/教育/知识分享)',
  `duration` varchar(20) NOT NULL COMMENT '模型时长(10-20s/20-30s/30-40s/40-50s)',
  `appearance_note` text COMMENT '形象要求备注',
  `status` int DEFAULT '1' COMMENT '状态:1=正常,2=禁用',
  `del_flag` int DEFAULT '1' COMMENT '删除标志:1=存在,2=删除',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `submit_status` int DEFAULT '1' COMMENT '提交状态',
  `upload_time` timestamp NULL DEFAULT NULL COMMENT '上传时间',
  `pid` int DEFAULT NULL COMMENT 'pid',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=25 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='超虚拟人模型制作表';

CREATE TABLE `t_voice_model` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '音色ID',
  `voice_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '音色名称',
  `voice_url` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '试听链接',
  `status` int DEFAULT '1' COMMENT '音色状态:1=正常,2=禁用',
  `clone_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '声音克隆生成的名称，目前是阿里云返回',
  `del_flag` int DEFAULT '1' COMMENT '删除标志:1=代表存在,2=代表删除',
  `create_by` bigint DEFAULT NULL COMMENT '创建者',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` bigint DEFAULT (now()) COMMENT '更新者',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `remark` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '备注',
  `pid` int DEFAULT NULL COMMENT 'IPID',
  `ref_text` varchar(1024) DEFAULT NULL COMMENT '参考音频对应文本内容',
  `back_clone_name` varchar(1024) DEFAULT NULL COMMENT 'fish服务报错调用硅流api',
  `back_voice_url` varchar(512) DEFAULT NULL COMMENT '备选服务url',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=2454 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='声音模型';

CREATE TABLE `t_work` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '作品id',
  `task_id` int NOT NULL COMMENT '关联的任务id',
  `work_type` int NOT NULL COMMENT '作品类型:1=文本,2=音频,3=视频,4=选题内容',
  `content` longblob COMMENT '作品内容(用于文本类型)',
  `file_id` int DEFAULT NULL COMMENT '关联的文件id(用于音频和视频类型)',
  `status` int DEFAULT '1' COMMENT '作品状态:1=正常,2=待审核,3=已删除',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `pid` int NOT NULL COMMENT 'ipid',
  `title` blob COMMENT '标题',
  `audio_model_id` varchar(500) DEFAULT NULL COMMENT '音频模型 id',
  `video_model_id` varchar(500) DEFAULT NULL COMMENT '视频模型 id',
  PRIMARY KEY (`id`),
  KEY `idx_task_id` (`task_id`),
  KEY `idx_file_id` (`file_id`),
  KEY `idx_pid_id` (`pid`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=83542 DEFAULT CHARSET=utf8mb3 COMMENT='作品表';

CREATE TABLE `t_workflow` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `workflow_name` varchar(100) NOT NULL COMMENT '工作流名称',
  `workflow_code` varchar(100) NOT NULL COMMENT '工作流编码',
  `description` varchar(500) DEFAULT NULL COMMENT '工作流描述',
  `status` int DEFAULT '1' COMMENT '任务状态:1=正常,2=禁用',
  `del_flag` int DEFAULT '1' COMMENT '删除标志:1=代表存在,2=代表删除',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `is_default` int DEFAULT '0' COMMENT '是否默认',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_workflow_code` (`workflow_code`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb3 COMMENT='工作流表';

CREATE TABLE `t_workflow_agent` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `workflow_id` bigint NOT NULL COMMENT '工作流ID',
  `agent_id` bigint NOT NULL COMMENT 'Agent ID',
  `execution_order` int NOT NULL COMMENT '主体执行顺序',
  `status` int DEFAULT '1' COMMENT '任务状态:1=正常,2=禁用',
  `del_flag` int DEFAULT '1' COMMENT '删除标志:1=代表存在,2=代表删除',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_workflow_agent` (`workflow_id`,`agent_id`),
  KEY `idx_execution_order` (`execution_order`)
) ENGINE=InnoDB AUTO_INCREMENT=94 DEFAULT CHARSET=utf8mb3 COMMENT='工作流-Agent关联表';

CREATE TABLE `t_workflow_pid` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `workflow_id` bigint NOT NULL COMMENT '工作流ID',
  `pid` bigint NOT NULL COMMENT 'IP的ID',
  `status` int DEFAULT '1' COMMENT '状态:1=正常,2=禁用',
  `del_flag` int DEFAULT '1' COMMENT '删除标志:1=代表存在,2=代表删除',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_workflow_pid` (`workflow_id`,`pid`),
  KEY `idx_pid` (`pid`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb3 COMMENT='工作流-IP关联表';



/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;