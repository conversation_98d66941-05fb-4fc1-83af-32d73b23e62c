# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
功能描述
----------------------------------------------------
@Project :   xinhou-openai-admin
@File    :   main.py
@Contact :   <EMAIL>

@Modify Time      <AUTHOR>    @Desciption
------------      -------    --------    -----------
2023/2/10 14:19   logic   1.0         None
"""
import uvicorn
from fastapi.middleware.cors import CORSMiddleware
from xinhou_openai_framework.core.context.model.AppContext import AppContext
from xinhou_openai_framework.core.context.model.SystemContext import ctx
from xinhou_openai_framework.core.init.AppManager import AppManager

# 创建应用程序
app = AppManager.create_app()

# 本地环境添加跨域
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 确保应用程序完全初始化后再导入和注册中间件和控制器
from common.middleware.ApiLogMiddleware import ApiLogMiddleware
from apps.admin.controller.ApiLogController import api as api_log_api

# 添加API日志中间件（放在最前面，以便记录所有请求）
app.add_middleware(ApiLogMiddleware)

# 手动注册API日志控制器
app.include_router(api_log_api)

if __name__ == "__main__":
    context: AppContext = ctx.__getattr__("context")  # 全局变量
    uvicorn.run(
        app="main:app",
        reload=context.application.server.reload,
        host=context.application.server.host,
        port=context.application.server.post
    )
