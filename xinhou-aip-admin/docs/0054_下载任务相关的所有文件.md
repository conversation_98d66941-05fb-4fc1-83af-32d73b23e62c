 Update Todos
 
     ☒ 分析需求并了解当前代码结构
     ☒ 查看WorkService的相关方法
     ☒ 查看EmbeddingFileService以了解文件下载相关功能
     ☒ 查看FilesUtils以了解文件处理方法
     ☒ 实现查询所有work的方法
     ☒ 实现获取文件下载地址方法
     ☒ 实现文件下载和打包功能
     ☒ 编写新的API端点

⏺ 已完成新功能的开发，现在可以通过访问 /admin/work/downloadAll/{task_id} 接口来下载任务相关的所有文件。

  具体实现包括：

  1. 在 WorkService 中添加了 query_all_works_by_task_id 方法，用于获取任务关联的所有作品
  2. 在 EmbeddingFileService 中添加了 get_download_url 方法，用于获取文件下载地址
  3. 在 FilesUtils 中添加了 download_files_to_zip 方法，用于下载多个文件并打包成 zip
  4. 最后在 WorkController 中添加了 /admin/work/downloadAll/{task_id} 接口，实现了通过 task_id 获取所有相关文件并打包下载的功能

  当用户请求该接口时，系统会：
  1. 根据 task_id 查找所有相关的作品
  2. 从每个作品中提取文件 ID 并获取下载地址
  3. 将所有文件下载并打包成一个 zip 文件
  4. 返回 zip 文件供用户下载
