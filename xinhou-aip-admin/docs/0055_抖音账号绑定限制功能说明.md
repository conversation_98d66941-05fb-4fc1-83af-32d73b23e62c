# 抖音账号绑定限制功能说明

## 需求描述

在知识库的"我的账号"和"对标账号专家组"模块中，当用户绑定的抖音账号数量达到或超过3个（包含绑定中和绑定成功的账号）时，系统需要限制用户继续添加新的抖音账号。当用户点击【+】按钮尝试添加新账号时，后端和前端都应该显示错误提示："抱歉，当前已达账号最大绑定额度~"。

## 实现方案

### 后端实现

1. 在`KnowledgeService`类中添加了`count_douyin_accounts`方法，用于统计特定IP已绑定的抖音账号数量：

```python
def count_douyin_accounts(self, pid: int) -> int:
    """
    统计用户已绑定的抖音账号数量（包括绑定中和绑定成功的）
    :param pid: IP ID
    :return: 绑定的抖音账号数量
    """
    return self.db.query(IpKnowledge).filter(
        IpKnowledge.pid == pid,
        IpKnowledge.knowledge_type == 3
    ).count()
```

2. 修改了`add_social_media_account`方法，在创建新的抖音账号前，先检查已绑定的账号数量是否已达上限：

```python
# 如果是抖音平台，检查已绑定账号数量
if platform.knowledge_type==3:
    account_count = self.count_douyin_accounts(pid)
    if account_count >= 3:
        raise GlobalBusinessException(400, "抱歉，当前已达账号最大绑定额度~")
```

3. 当账号数量达到或超过3个时，会抛出HTTP 400错误，并返回相应的错误信息。

### 前端实现（建议）

前端在调用添加社交媒体账号的API时，应该处理可能返回的错误信息，并在用户界面上显示错误提示：

```javascript
// 示例：调用添加账号API并处理错误
async function addSocialMediaAccount(pid, accountUrl, isMyself) {
  try {
    const response = await api.post('/api/social-media/accounts', {
      pid,
      account_url: accountUrl,
      is_myself: isMyself
    });
    return response.data;
  } catch (error) {
    if (error.response && error.response.data && error.response.data.error) {
      // 显示后端返回的错误信息
      message.error(error.response.data.error);
    } else {
      message.error('添加账号失败，请稍后重试');
    }
    throw error;
  }
}
```

## 测试方案

已创建完整的测试用例(`test_knowledge_service.py`)，测试内容包括：

1. 测试`count_douyin_accounts`方法能否正确统计账号数量
2. 测试当账号数量未达上限时，能否成功添加新账号
3. 测试当账号数量已达上限时，是否会返回正确的错误信息
4. 测试不支持的平台是否会返回正确的错误信息

## 注意事项

1. 该限制仅针对抖音平台的账号，其他平台的账号不受此限制。
2. 账号数量统计包括所有状态的抖音账号，包括绑定中和绑定成功的账号。
3. 当用户删除某个抖音账号后，可以重新添加新的账号，只要总数不超过3个。

## API响应示例

当尝试添加第4个抖音账号时，API将返回以下响应：

```json
{
  "code": 400,
  "message": "PARAMETER_ERROR",
  "data": {
    "error": "抱歉，当前已达账号最大绑定额度~"
  }
}
```