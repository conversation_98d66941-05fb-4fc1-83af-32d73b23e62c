import asyncio
import unittest
from unittest.mock import MagicMock, patch, AsyncMock

import pytest
from fastapi import HTTPException
from sqlalchemy.orm import Session
from xinhou_openai_framework.core.exception.GlobalBusinessException import GlobalBusinessException

from apps.admin.schema.KnowledgePlatformSchema.KnowledgePlatformEnums import KnowledgePlatformEnums
from common.entity.IpKnowledge import IpKnowledge
from common.service.KnowledgeService import KnowledgeService


class TestKnowledgeService(unittest.TestCase):
    """知识库服务测试类"""

    def setUp(self):
        """初始化测试环境"""
        self.mock_db = MagicMock(spec=Session)
        self.knowledge_service = KnowledgeService(self.mock_db)
        
        # 模拟Platform类
        self.mock_platform = MagicMock()
        self.mock_platform.knowledge_source = '我的抖音'
        self.mock_platform.knowledge_type = 1
        
        # 创建测试用的IP ID
        self.test_pid = 1001
        self.test_account_url = "https://douyin.com/user/123456"

    def test_count_douyin_accounts(self):
        """测试统计抖音账号数量的方法"""
        # 设置模拟查询结果
        query_mock = self.mock_db.query.return_value
        filter_mock = query_mock.filter.return_value
        filter_mock.count.return_value = 2
        
        # 调用方法并验证结果
        result = self.knowledge_service.count_douyin_accounts(self.test_pid)
        
        # 断言结果正确
        self.assertEqual(result, 2)
        
        # 验证调用过程
        self.mock_db.query.assert_called_once_with(IpKnowledge)
        query_mock.filter.assert_called_once()

    @patch('apps.admin.schema.KnowledgePlatformSchema.KnowledgePlatformEnums.get_platform')
    @patch.object(KnowledgeService, 'count_douyin_accounts')
    @patch.object(KnowledgeService, 'save')
    @patch.object(KnowledgeService, '_process_douyin_account')
    async def test_add_social_media_account_success(self, mock_process, mock_save, mock_count, mock_get_platform):
        """测试成功添加社交媒体账号"""
        # 设置各个模拟对象的行为
        mock_get_platform.return_value = self.mock_platform
        mock_count.return_value = 2  # 返回当前有2个账号，未达到上限
        
        # 模拟创建的知识库对象
        mock_knowledge = MagicMock(spec=IpKnowledge)
        mock_knowledge.id = 1
        mock_save.return_value = mock_knowledge
        
        # 设置_process_douyin_account为异步模拟对象
        mock_process.return_value = asyncio.Future()
        mock_process.return_value.set_result(None)
        
        # 调用方法
        result = await self.knowledge_service.add_social_media_account(
            self.test_pid, self.test_account_url, is_myself=1
        )
        
        # 验证结果
        self.assertEqual(result, mock_knowledge)
        
        # 验证调用过程
        mock_get_platform.assert_called_once_with(self.test_account_url)
        mock_count.assert_called_once_with(self.test_pid)
        mock_save.assert_called_once()
        mock_process.assert_called_once_with(mock_knowledge, self.test_account_url)

    @patch('apps.admin.schema.KnowledgePlatformSchema.KnowledgePlatformEnums.get_platform')
    @patch.object(KnowledgeService, 'count_douyin_accounts')
    @patch.object(KnowledgeService, 'save')
    async def test_add_social_media_account_exceed_limit(self, mock_save, mock_count, mock_get_platform):
        """测试添加社交媒体账号超过限制"""
        # 设置各个模拟对象的行为
        mock_get_platform.return_value = self.mock_platform
        mock_count.return_value = 3  # 已达到上限
        
        # 调用方法，应该抛出异常
        with pytest.raises(GlobalBusinessException) as excinfo:
            await self.knowledge_service.add_social_media_account(
                self.test_pid, self.test_account_url, is_myself=1
            )
        
        # 验证异常内容
        self.assertEqual(str(excinfo.value), "抱歉，当前已达账号最大绑定额度~")
        self.assertEqual(excinfo.value.code, 400)
        
        # 验证调用过程
        mock_get_platform.assert_called_once_with(self.test_account_url)
        mock_count.assert_called_once_with(self.test_pid)
        mock_save.assert_not_called()  # 不应该调用save方法

    @patch('apps.admin.schema.KnowledgePlatformSchema.KnowledgePlatformEnums.get_platform')
    async def test_add_social_media_account_unsupported_platform(self, mock_get_platform):
        """测试添加不支持的平台账号"""
        # 设置模拟对象的行为
        mock_get_platform.return_value = None
        
        # 调用方法，应该抛出异常
        with pytest.raises(GlobalBusinessException) as excinfo:
            await self.knowledge_service.add_social_media_account(
                self.test_pid, "https://unsupported.com/user/123", is_myself=1
            )
        
        # 验证异常内容
        self.assertEqual(str(excinfo.value), "不支持的平台")
        self.assertEqual(excinfo.value.code, 400)


if __name__ == '__main__':
    unittest.main()