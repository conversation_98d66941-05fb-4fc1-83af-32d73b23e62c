from loguru import logger
from xinhou_openai_framework.utils.StrUtil import StrUtil


if __name__ == '__main__':
    StrUtil.progress_bar()
    logger.info(StrUtil.to_camel_case('UserLoginCount'))  # UserLoginCount
    logger.info(StrUtil.to_camel_case('userLoginCount'))  # userLoginCount
    logger.info(StrUtil.to_camel_case('user_login_count'))  # userLoginCount
    logger.info(None)
    logger.info(StrUtil.to_upper_camel_case('UserLoginCount'))  # UserLoginCount
    logger.info(StrUtil.to_upper_camel_case('userLoginCount'))  # UserLoginCount
    logger.info(StrUtil.to_upper_camel_case('user_login_count'))  # UserLoginCount
    logger.info(None)
    logger.info(StrUtil.to_lower_camel_case('UserLoginCount'))  # userLoginCount
    logger.info(StrUtil.to_lower_camel_case('userLoginCount'))  # userLoginCount
    logger.info(StrUtil.to_lower_camel_case('user_login_count'))  # userLoginCount
    #
    # logger.info()
    #
    # short_name = StrUtil.sub_str_after('t_user_login_count', 't_', True)
    # logger.info(short_name)
    # logger.info(StrUtil.to_upper_camel_case(short_name))  # userLoginCount
    # logger.info(StrUtil.to_lower_camel_case(short_name))  # userLoginCount
    #
    # logger.info(StrUtil.sub_str_after("/Users/<USER>/Softs/workspaces-py/xinhou-openai-admin", "/", True))
    #
    # db_url = "mysql+pymysql://139.196.122.29:33306/fastapi_admin_db?charset=utf8mb4"
    # db_url_list = db_url.split("//")
    # logger.info("{0}//{1}:{2}@{3}".format(db_url_list[0], "root", "123456", db_url_list[1]))