/**
 * 配置管理
 * 根据环境变量加载对应的配置
 */

import { BaseConfig } from './base';
import localConfig from './local';
import developmentConfig from './development';
import productionConfig from './production';

// 环境类型
type Environment = 'local' | 'development' | 'production';

// 获取当前环境
const getEnvironment = (): Environment => {
  // 优先使用环境变量
  if (typeof process !== 'undefined' && process.env) {
    // Next.js 环境变量
    if (process.env.NEXT_PUBLIC_APP_ENV) {
      return process.env.NEXT_PUBLIC_APP_ENV as Environment;
    }
    
    // Node.js 环境变量
    if (process.env.NODE_ENV === 'production') {
      return 'production';
    } else if (process.env.NODE_ENV === 'development') {
      return 'development';
    }
  }
  
  // 默认使用本地环境
  return 'local';
};

// 根据环境获取配置
const getConfig = (): BaseConfig => {
  const env = getEnvironment();
  
  switch (env) {
    case 'local':
      return localConfig;
    case 'development':
      return developmentConfig;
    case 'production':
      return productionConfig;
    default:
      return localConfig;
  }
};

// 导出当前环境的配置
const config = getConfig();

export default config;
