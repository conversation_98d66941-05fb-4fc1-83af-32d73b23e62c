/**
 * 基础配置
 * 包含所有环境共享的配置项
 */

export interface BaseConfig {
  // API相关配置
  api: {
    // 基础URL
    baseUrl: string;
    // 超时时间（毫秒）
    timeout: number;
    // 是否自动处理错误
    handleError: boolean;
  };
  
  // 应用相关配置
  app: {
    // 应用名称
    name: string;
    // 应用版本
    version: string;
  };
}

// 默认基础配置
const baseConfig: BaseConfig = {
  api: {
    baseUrl: 'https://www.tcai.asia', // 默认API基础URL
    timeout: 10000, // 默认超时时间：10秒
    handleError: true, // 默认自动处理错误
  },
  app: {
    name: 'AI Web Admin',
    version: '1.0.0',
  },
};

export default baseConfig;
