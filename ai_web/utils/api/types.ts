/**
 * API响应类型定义
 */

// 通用API响应结构
export interface ApiResponse<T = any> {
  code: number;
  data: T;
  message: string;
  success: boolean;
}

// 分页数据结构
export interface PaginatedData<T> {
  list: T[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
}

// 分页请求参数
export interface PaginationParams {
  page?: number;
  pageSize?: number;
  sortField?: string;
  sortOrder?: 'ascend' | 'descend';
}

// 用户信息
export interface UserInfo {
  id: string;
  username: string;
  nickname?: string;
  avatar?: string;
  email?: string;
  phone?: string;
  role?: string;
  permissions?: string[];
  createTime?: string;
  updateTime?: string;
}

// 登录请求参数
export interface LoginParams {
  username: string;
  password: string;
}

// 登录响应
export interface LoginResponse {
  token: string;
  userInfo: UserInfo;
}

// 作品信息
export interface WorkItem {
  id: string;
  title: string;
  description?: string;
  coverUrl?: string;
  author?: string;
  createTime?: string;
  updateTime?: string;
  status?: 'draft' | 'published' | 'archived';
  tags?: string[];
}

// 音频信息
export interface AudioItem {
  id: string;
  title: string;
  description?: string;
  url: string;
  duration?: number;
  size?: number;
  format?: string;
  createTime?: string;
  updateTime?: string;
}
