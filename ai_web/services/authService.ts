/**
 * 认证相关API服务
 */

import api from '../utils/api/fetch';
import { ApiResponse, LoginParams, LoginResponse, UserInfo } from '../utils/api/types';

/**
 * 用户登录
 * @param params 登录参数
 */
export async function login(params: LoginParams): Promise<ApiResponse<LoginResponse>> {
  return api.post<LoginResponse>('/api/auth/login', params);
}

/**
 * 用户登出
 */
export async function logout(): Promise<ApiResponse<null>> {
  return api.post<null>('/api/auth/logout');
}

/**
 * 获取当前用户信息
 */
export async function getCurrentUser(): Promise<ApiResponse<UserInfo>> {
  return api.get<UserInfo>('/api/auth/current-user');
}

/**
 * 刷新token
 */
export async function refreshToken(): Promise<ApiResponse<{ token: string }>> {
  return api.post<{ token: string }>('/api/auth/refresh-token');
}

/**
 * 修改密码
 */
export async function changePassword(
  oldPassword: string,
  newPassword: string
): Promise<ApiResponse<null>> {
  return api.post<null>('/api/auth/change-password', {
    oldPassword,
    newPassword,
  });
}

const authService = {
  login,
  logout,
  getCurrentUser,
  refreshToken,
  changePassword,
};

export default authService;
