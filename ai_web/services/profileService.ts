/**
 * 个人中心相关API服务
 */

import api from "../utils/api/fetch";
import { ApiResponse, UserInfo } from "../utils/api/types";

/**
 * 获取用户个人资料
 */
export async function getUserProfile(): Promise<ApiResponse<UserInfo>> {
  return api.get<UserInfo>("/api/profile");
}

/**
 * 更新用户个人资料
 * @param data 用户资料数据
 */
export async function updateUserProfile(
  data: Partial<UserInfo>
): Promise<ApiResponse<UserInfo>> {
  return api.put<UserInfo>("/api/profile", data);
}

/**
 * 上传用户头像
 * @param file 头像文件
 */
export async function uploadAvatar(
  file: File
): Promise<ApiResponse<{ avatarUrl: string }>> {
  const formData = new FormData();
  formData.append("avatar", file);

  // 上传文件时不需要手动设置Content-Type，浏览器会自动设置为multipart/form-data
  return api.post<{ avatarUrl: string }>("/api/profile/avatar", formData);
}

/**
 * 获取用户通知消息
 */
export async function getUserNotifications(): Promise<
  ApiResponse<
    Array<{
      id: string;
      title: string;
      content: string;
      isRead: boolean;
      createTime: string;
    }>
  >
> {
  return api.get<
    Array<{
      id: string;
      title: string;
      content: string;
      isRead: boolean;
      createTime: string;
    }>
  >("/api/profile/notifications");
}

/**
 * 标记通知为已读
 * @param id 通知ID
 */
export async function markNotificationAsRead(
  id: string
): Promise<ApiResponse<null>> {
  return api.put<null>(`/api/profile/notifications/${id}/read`);
}

/**
 * 标记所有通知为已读
 */
export async function markAllNotificationsAsRead(): Promise<ApiResponse<null>> {
  return api.put<null>("/api/profile/notifications/read-all");
}

const profileService = {
  getUserProfile,
  updateUserProfile,
  uploadAvatar,
  getUserNotifications,
  markNotificationAsRead,
  markAllNotificationsAsRead,
};

export default profileService;
