/**
 * 音频管理相关API服务
 */

import api from "../utils/api/fetch";
import {
  ApiResponse,
  AudioItem,
  PaginatedData,
  PaginationParams,
} from "../utils/api/types";

/**
 * 获取音频列表（分页）
 * @param params 分页参数
 */
export async function getAudioList(
  params: PaginationParams & { keyword?: string }
): Promise<ApiResponse<PaginatedData<AudioItem>>> {
  return api.get<PaginatedData<AudioItem>>("/api/audio", params);
}

/**
 * 获取音频详情
 * @param id 音频ID
 */
export async function getAudioDetail(
  id: string
): Promise<ApiResponse<AudioItem>> {
  return api.get<AudioItem>(`/api/audio/${id}`);
}

/**
 * 创建音频
 * @param data 音频数据
 */
export async function createAudio(
  data: Partial<AudioItem>
): Promise<ApiResponse<AudioItem>> {
  return api.post<AudioItem>("/api/audio", data);
}

/**
 * 更新音频
 * @param id 音频ID
 * @param data 音频数据
 */
export async function updateAudio(
  id: string,
  data: Partial<AudioItem>
): Promise<ApiResponse<AudioItem>> {
  return api.put<AudioItem>(`/api/audio/${id}`, data);
}

/**
 * 删除音频
 * @param id 音频ID
 */
export async function deleteAudio(id: string): Promise<ApiResponse<null>> {
  return api.delete<null>(`/api/audio/${id}`);
}

/**
 * 批量删除音频
 * @param ids 音频ID数组
 */
export async function batchDeleteAudio(
  ids: string[]
): Promise<ApiResponse<null>> {
  return api.post<null>("/api/audio/batch-delete", { ids });
}

/**
 * 上传音频文件
 * @param file 文件对象
 */
export async function uploadAudioFile(
  file: File
): Promise<ApiResponse<{ url: string; duration: number; size: number }>> {
  const formData = new FormData();
  formData.append("file", file);

  // 上传文件时不需要手动设置Content-Type，浏览器会自动设置为multipart/form-data
  return api.post<{ url: string; duration: number; size: number }>(
    "/api/audio/upload",
    formData
  );
}

const audioService = {
  getAudioList,
  getAudioDetail,
  createAudio,
  updateAudio,
  deleteAudio,
  batchDeleteAudio,
  uploadAudioFile,
};

export default audioService;
