/**
 * 作品管理相关API服务
 */

import api from '../utils/api/fetch';
import { ApiResponse, PaginatedData, PaginationParams, WorkItem } from '../utils/api/types';

/**
 * 获取作品列表（分页）
 * @param params 分页参数
 */
export async function getWorksList(
  params: PaginationParams & { keyword?: string; status?: string; tags?: string[] }
): Promise<ApiResponse<PaginatedData<WorkItem>>> {
  return api.get<PaginatedData<WorkItem>>('/api/works', params);
}

/**
 * 获取作品详情
 * @param id 作品ID
 */
export async function getWorkDetail(id: string): Promise<ApiResponse<WorkItem>> {
  return api.get<WorkItem>(`/api/works/${id}`);
}

/**
 * 创建作品
 * @param data 作品数据
 */
export async function createWork(data: Partial<WorkItem>): Promise<ApiResponse<WorkItem>> {
  return api.post<WorkItem>('/api/works', data);
}

/**
 * 更新作品
 * @param id 作品ID
 * @param data 作品数据
 */
export async function updateWork(
  id: string,
  data: Partial<WorkItem>
): Promise<ApiResponse<WorkItem>> {
  return api.put<WorkItem>(`/api/works/${id}`, data);
}

/**
 * 删除作品
 * @param id 作品ID
 */
export async function deleteWork(id: string): Promise<ApiResponse<null>> {
  return api.delete<null>(`/api/works/${id}`);
}

/**
 * 批量删除作品
 * @param ids 作品ID数组
 */
export async function batchDeleteWorks(ids: string[]): Promise<ApiResponse<null>> {
  return api.post<null>('/api/works/batch-delete', { ids });
}

/**
 * 更新作品状态
 * @param id 作品ID
 * @param status 状态
 */
export async function updateWorkStatus(
  id: string,
  status: 'draft' | 'published' | 'archived'
): Promise<ApiResponse<WorkItem>> {
  return api.put<WorkItem>(`/api/works/${id}/status`, { status });
}

const worksService = {
  getWorksList,
  getWorkDetail,
  createWork,
  updateWork,
  deleteWork,
  batchDeleteWorks,
  updateWorkStatus,
};

export default worksService;
