.container {
  padding: 0;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.title {
  color: white;
  margin-bottom: 0 !important;
}

.searchContainer {
  display: flex;
  gap: 16px;
}

.cardGrid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 24px;
  margin-top: 24px;
}

.audioCard {
  background-color: #1f1f1f;
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.3s ease;
  border: 1px solid #303030;
}

.audioCard:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
  border-color: #1668dc;
}

.cardContent {
  padding: 16px;
}

.audioTitle {
  color: white;
  margin-bottom: 8px;
  font-size: 16px;
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.audioInfo {
  color: rgba(255, 255, 255, 0.65);
  font-size: 12px;
  margin-bottom: 16px;
}

.audioControls {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
}

.playButton {
  background-color: #1668dc;
  color: white;
  border: none;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.playButton:hover {
  background-color: #1890ff;
}

.progressContainer {
  flex: 1;
  height: 4px;
  background-color: #303030;
  border-radius: 2px;
  overflow: hidden;
  position: relative;
}

.progressBar {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  background-color: #1668dc;
  border-radius: 2px;
}

.timeInfo {
  display: flex;
  justify-content: space-between;
  color: rgba(255, 255, 255, 0.45);
  font-size: 12px;
}

.audioIcon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 140px;
  background-color: #141414;
  color: #1668dc;
  font-size: 48px;
}

.cardActions {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #303030;
}

.pagination {
  margin-top: 24px;
  text-align: center;
}

.emptyContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 48px 0;
  color: rgba(255, 255, 255, 0.45);
}

.uploadButton {
  margin-top: 16px;
}
