"use client";

import React from "react";
// Import only the components we need
import Card from "antd/es/card";
import Typography from "antd/es/typography";
import Row from "antd/es/row";
import Col from "antd/es/col";
import Statistic from "antd/es/statistic";
// Import only the icons we need
import UserOutlined from "@ant-design/icons/UserOutlined";
import ShoppingCartOutlined from "@ant-design/icons/ShoppingCartOutlined";
import DollarOutlined from "@ant-design/icons/DollarOutlined";
import FileOutlined from "@ant-design/icons/FileOutlined";

const { Title } = Typography;

export default function AdminDashboard() {
  return (
    <div>
      <Title level={2} style={{ color: "white" }}>
        仪表盘
      </Title>

      <Row gutter={[16, 16]}>
        <Col xs={24} sm={12} md={6}>
          <Card bordered={false}>
            <Statistic
              title={<span style={{ color: "#a3a3a3" }}>用户数</span>}
              value={1128}
              valueStyle={{ color: "#fff" }}
              prefix={<UserOutlined style={{ color: "#1668dc" }} />}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card bordered={false}>
            <Statistic
              title={<span style={{ color: "#a3a3a3" }}>订单数</span>}
              value={93}
              valueStyle={{ color: "#fff" }}
              prefix={<ShoppingCartOutlined style={{ color: "#1668dc" }} />}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card bordered={false}>
            <Statistic
              title={<span style={{ color: "#a3a3a3" }}>收入</span>}
              value={15680}
              valueStyle={{ color: "#fff" }}
              prefix={<DollarOutlined style={{ color: "#1668dc" }} />}
              precision={2}
              suffix="¥"
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card bordered={false}>
            <Statistic
              title={<span style={{ color: "#a3a3a3" }}>报表</span>}
              value={42}
              valueStyle={{ color: "#fff" }}
              prefix={<FileOutlined style={{ color: "#1668dc" }} />}
            />
          </Card>
        </Col>
      </Row>

      <div style={{ marginTop: "24px" }}>
        <Card
          title={<span style={{ color: "#fff" }}>欢迎使用后台管理系统</span>}
          bordered={false}
        >
          <p style={{ color: "#a3a3a3" }}>
            这是一个使用 Ant Design 创建的基础后台管理界面。
          </p>
          <p style={{ color: "#a3a3a3" }}>
            您可以根据需要自定义此页面并添加更多组件。
          </p>
        </Card>
      </div>
    </div>
  );
}
