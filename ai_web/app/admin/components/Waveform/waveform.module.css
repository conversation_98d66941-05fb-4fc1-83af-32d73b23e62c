.waveformContainer {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #141414;
  overflow: hidden;
}

.waveform {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 80%;
  height: 70%;
  gap: 3px;
}

.bar {
  width: 3px;
  background-color: #1668dc;
  border-radius: 1px;
  transition: height 0.3s ease;
  opacity: 0.7;
}

/* Add a glow effect to the bars */
.playing .bar {
  box-shadow: 0 0 5px rgba(22, 104, 220, 0.5);
  opacity: 1;
}

.playing .bar {
  animation: pulse 1.2s ease-in-out infinite;
}

@keyframes pulse {
  0%,
  100% {
    transform: scaleY(0.6);
  }
  50% {
    transform: scaleY(1);
  }
}

/* Delay each bar's animation */
.bar:nth-child(1) {
  animation-delay: -1.2s;
}
.bar:nth-child(2) {
  animation-delay: -1.1s;
}
.bar:nth-child(3) {
  animation-delay: -1s;
}
.bar:nth-child(4) {
  animation-delay: -0.9s;
}
.bar:nth-child(5) {
  animation-delay: -0.8s;
}
.bar:nth-child(6) {
  animation-delay: -0.7s;
}
.bar:nth-child(7) {
  animation-delay: -0.6s;
}
.bar:nth-child(8) {
  animation-delay: -0.5s;
}
.bar:nth-child(9) {
  animation-delay: -0.4s;
}
.bar:nth-child(10) {
  animation-delay: -0.3s;
}
.bar:nth-child(11) {
  animation-delay: -0.2s;
}
.bar:nth-child(12) {
  animation-delay: -0.1s;
}
.bar:nth-child(13) {
  animation-delay: 0s;
}
.bar:nth-child(14) {
  animation-delay: -0.1s;
}
.bar:nth-child(15) {
  animation-delay: -0.2s;
}
.bar:nth-child(16) {
  animation-delay: -0.3s;
}
.bar:nth-child(17) {
  animation-delay: -0.4s;
}
.bar:nth-child(18) {
  animation-delay: -0.5s;
}
.bar:nth-child(19) {
  animation-delay: -0.6s;
}
.bar:nth-child(20) {
  animation-delay: -0.7s;
}
.bar:nth-child(21) {
  animation-delay: -0.8s;
}
.bar:nth-child(22) {
  animation-delay: -0.9s;
}
.bar:nth-child(23) {
  animation-delay: -1s;
}
.bar:nth-child(24) {
  animation-delay: -1.1s;
}
.bar:nth-child(25) {
  animation-delay: -1.2s;
}
