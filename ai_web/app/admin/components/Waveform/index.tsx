"use client";

import React from "react";
import styles from "./waveform.module.css";

interface WaveformProps {
  isPlaying?: boolean;
}

const Waveform: React.FC<WaveformProps> = ({ isPlaying = false }) => {
  // Generate a more realistic waveform pattern
  const generateWaveformPattern = () => {
    // Create a pattern that resembles an audio waveform
    // Higher in the middle, lower at the edges
    const heights = [];
    const numBars = 25;

    for (let i = 0; i < numBars; i++) {
      // Calculate distance from center (0 to 1)
      const distFromCenter = Math.abs(
        (i - (numBars - 1) / 2) / ((numBars - 1) / 2)
      );

      // Base height decreases as we move away from center
      const baseHeight = 100 - distFromCenter * 50;

      // Add some randomness
      const randomFactor = Math.random() * 30 - 15; // -15 to +15

      // Calculate final height (between 20% and 100%)
      const height = Math.max(20, Math.min(100, baseHeight + randomFactor));

      heights.push(height);
    }

    return heights;
  };

  // Memoize the heights so they don't change on re-render
  const barHeights = React.useMemo(() => generateWaveformPattern(), []);

  return (
    <div className={styles.waveformContainer}>
      <div className={`${styles.waveform} ${isPlaying ? styles.playing : ""}`}>
        {barHeights.map((height, index) => (
          <div
            key={index}
            className={styles.bar}
            style={{ height: `${height}%` }}
          />
        ))}
      </div>
    </div>
  );
};

export default Waveform;
