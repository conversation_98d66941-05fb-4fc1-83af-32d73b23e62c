"use client";

import React from "react";
// Import only the components we need
import Spin from "antd/es/spin";
// Import only the icons we need
import LoadingOutlined from "@ant-design/icons/LoadingOutlined";
// Import CSS Module
import styles from "./loadingSpinner.module.css";

interface LoadingSpinnerProps {
  tip?: string;
  size?: "small" | "default" | "large";
  fullScreen?: boolean;
}

const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({
  tip = "加载中...",
  size = "large",
  fullScreen = false,
}) => {
  const iconClassName =
    size === "large" ? styles.iconLarge : styles.iconDefault;
  const antIcon = <LoadingOutlined className={iconClassName} spin />;

  const containerClassName = fullScreen ? styles.fullScreen : styles.container;

  return (
    <div className={containerClassName}>
      <Spin indicator={antIcon} size={size} />
      {tip && (
        <div style={{ marginTop: 16, color: "rgba(255, 255, 255, 0.65)" }}>
          {tip}
        </div>
      )}
    </div>
  );
};

export default LoadingSpinner;
