"use client";

import React from "react";
// Import only the components we need
import Layout from "antd/es/layout";
import Menu from "antd/es/menu";
// Import only the icons we need
import DashboardOutlined from "@ant-design/icons/DashboardOutlined";
import AppstoreOutlined from "@ant-design/icons/AppstoreOutlined";
import UserOutlined from "@ant-design/icons/UserOutlined";
import SoundOutlined from "@ant-design/icons/SoundOutlined";
import EditOutlined from "@ant-design/icons/EditOutlined";
import ExperimentOutlined from "@ant-design/icons/ExperimentOutlined";
import Link from "next/link";
import { usePathname } from "next/navigation";
// Import CSS Module
import styles from "./sidebar.module.css";

const { Sider } = Layout;

interface SidebarProps {
  collapsed: boolean;
}

export default function Sidebar({ collapsed }: SidebarProps) {
  const pathname = usePathname();

  const items = [
    {
      key: "/admin",
      icon: <DashboardOutlined />,
      label: <Link href="/admin">仪表盘</Link>,
    },
    {
      key: "/admin/works",
      icon: <AppstoreOutlined />,
      label: <Link href="/admin/works">作品管理</Link>,
    },
    {
      key: "/admin/profile",
      icon: <UserOutlined />,
      label: <Link href="/admin/profile">个人中心</Link>,
    },
    {
      key: "/admin/audio",
      icon: <SoundOutlined />,
      label: <Link href="/admin/audio">声音管理</Link>,
    },
    {
      key: "/admin/prompts",
      icon: <EditOutlined />,
      label: <Link href="/admin/prompts">修改Prompt</Link>,
    },
    {
      key: "/admin/test",
      icon: <ExperimentOutlined />,
      label: <Link href="/admin/test">测试</Link>,
    },
  ];

  // Find the selected key based on the current pathname
  const selectedKey =
    items.find((item) => pathname === item.key)?.key || "/admin";

  return (
    <Sider
      collapsible
      collapsed={collapsed}
      trigger={null}
      className={styles.sider}
    >
      <div className={styles.logo}>
        {!collapsed ? (
          <h1 className={styles.titleExpanded}>后台管理系统</h1>
        ) : (
          <h1 className={styles.titleCollapsed}>后台</h1>
        )}
      </div>
      <Menu
        theme="dark"
        mode="inline"
        defaultSelectedKeys={[selectedKey]}
        items={items}
      />
    </Sider>
  );
}
