'use client';

import React from 'react';
import config from '../../../../utils/config';
import styles from './envInfo.module.css';

const EnvInfo: React.FC = () => {
  // 获取当前环境
  const getEnvironment = () => {
    if (typeof process !== 'undefined' && process.env) {
      return process.env.NEXT_PUBLIC_APP_ENV || process.env.NODE_ENV || 'unknown';
    }
    return 'unknown';
  };

  return (
    <div className={styles.envInfo}>
      <div className={styles.envBadge}>
        环境: {getEnvironment()}
      </div>
      <div className={styles.apiUrl}>
        API: {config.api.baseUrl}
      </div>
    </div>
  );
};

export default EnvInfo;
