"use client";

import React from "react";
// Import only the components we need
import Layout from "antd/es/layout";
import Button from "antd/es/button";
import theme from "antd/es/theme";
import Dropdown from "antd/es/dropdown";
import Space from "antd/es/space";
import Avatar from "antd/es/avatar";
import type { MenuProps } from "antd/es/menu";
// Import only the icons we need
import MenuFoldOutlined from "@ant-design/icons/MenuFoldOutlined";
import MenuUnfoldOutlined from "@ant-design/icons/MenuUnfoldOutlined";
import UserOutlined from "@ant-design/icons/UserOutlined";
import LogoutOutlined from "@ant-design/icons/LogoutOutlined";
import BellOutlined from "@ant-design/icons/BellOutlined";
import SettingOutlined from "@ant-design/icons/SettingOutlined";
// Import CSS Module
import styles from "./header.module.css";

const { Header: AntHeader } = Layout;

interface HeaderProps {
  collapsed: boolean;
  setCollapsed: (collapsed: boolean) => void;
}

export default function Header({ collapsed, setCollapsed }: HeaderProps) {
  const {
    token: { colorBgContainer, borderRadiusLG },
  } = theme.useToken();

  const items: MenuProps["items"] = [
    {
      key: "1",
      label: "个人信息",
      icon: <UserOutlined />,
    },
    {
      key: "2",
      label: "设置",
      icon: <SettingOutlined />,
    },
    {
      type: "divider",
    },
    {
      key: "3",
      label: "退出登录",
      icon: <LogoutOutlined />,
      danger: true,
    },
  ];

  return (
    <AntHeader className={styles.header}>
      <div className={styles.container}>
        <Button
          type="text"
          icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
          onClick={() => setCollapsed(!collapsed)}
          className={styles.toggleButton}
        />

        <div className={styles.rightContainer}>
          <Button
            type="text"
            icon={<BellOutlined className={styles.notificationIcon} />}
            className={styles.notificationButton}
          />

          <Dropdown menu={{ items }} placement="bottomRight">
            <Space className={styles.userDropdown}>
              <Avatar
                className={styles.avatar}
                icon={<UserOutlined />}
              />
              <span className={styles.username}>管理员</span>
            </Space>
          </Dropdown>
        </div>
      </div>
    </AntHeader>
  );
}
