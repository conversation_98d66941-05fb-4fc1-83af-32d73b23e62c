"use client";

import React, { useState } from "react";
import dynamic from "next/dynamic";
// Import only the components we need
import Layout from "antd/es/layout";
import ConfigProvider from "antd/es/config-provider";
import theme from "antd/es/theme";
// Import CSS Module
import styles from "./layout.module.css";

import LoadingSpinner from "./components/LoadingSpinner/index";
import EnvInfo from "./components/EnvInfo/index";

// Dynamically import components with loading fallbacks and proper typing
const Sidebar = dynamic(
  () =>
    import("./components/Sidebar/index").then((mod) => {
      const Component = mod.default;
      return (props: { collapsed: boolean }) => <Component {...props} />;
    }),
  {
    loading: () => <LoadingSpinner tip="加载侧边栏..." />,
    ssr: false, // Disable server-side rendering for this component
  }
);

const Header = dynamic(
  () =>
    import("./components/Header/index").then((mod) => {
      const Component = mod.default;
      return (props: {
        collapsed: boolean;
        setCollapsed: (collapsed: boolean) => void;
      }) => <Component {...props} />;
    }),
  {
    loading: () => <LoadingSpinner tip="加载头部..." />,
    ssr: false,
  }
);

const Footer = dynamic(() => import("./components/Footer/index"), {
  loading: () => <LoadingSpinner tip="加载底部..." />,
  ssr: false,
});

const { Content } = Layout;

export default function AdminLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const [collapsed, setCollapsed] = useState(false);

  return (
    <ConfigProvider
      theme={{
        algorithm: theme.darkAlgorithm,
        token: {
          colorPrimary: "#1668dc",
          colorBgBase: "#141414",
          colorTextBase: "#fff",
          colorBgContainer: "#1f1f1f",
          colorBgElevated: "#1f1f1f",
          colorBgLayout: "#000000",
        },
      }}
    >
      <Layout className={styles.layout}>
        <Sidebar collapsed={collapsed} />
        <Layout>
          <Header collapsed={collapsed} setCollapsed={setCollapsed} />
          <Content className={styles.content}>{children}</Content>
          <Footer />
        </Layout>
        <EnvInfo />
      </Layout>
    </ConfigProvider>
  );
}
