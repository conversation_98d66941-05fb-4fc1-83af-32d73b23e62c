"use client";

import React, { useState, useEffect } from "react";
import Typography from "antd/es/typography";
import Table from "antd/es/table";
import Button from "antd/es/button";
import Space from "antd/es/space";
import message from "antd/es/message";
import { worksService } from "../../../services";
import { WorkItem } from "../../../utils/api/types";

const { Title, Text } = Typography;

export default function WorksManagement() {
  const [loading, setLoading] = useState<boolean>(false);
  const [works, setWorks] = useState<WorkItem[]>([]);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
  });

  // 获取作品列表
  const fetchWorks = async (page = 1, pageSize = 10) => {
    try {
      setLoading(true);
      const response = await worksService.getWorksList({
        page,
        pageSize,
      });

      if (response.success) {
        setWorks(response.data.list);
        setPagination({
          current: response.data.page,
          pageSize: response.data.pageSize,
          total: response.data.total,
        });
      } else {
        message.error(response.message || "获取作品列表失败");
      }
    } catch (error) {
      console.error("获取作品列表出错:", error);
      message.error("获取作品列表出错");
    } finally {
      setLoading(false);
    }
  };

  // 首次加载时获取数据
  useEffect(() => {
    fetchWorks();
  }, []);

  // 表格列定义
  const columns = [
    {
      title: "标题",
      dataIndex: "title",
      key: "title",
    },
    {
      title: "描述",
      dataIndex: "description",
      key: "description",
      ellipsis: true,
    },
    {
      title: "作者",
      dataIndex: "author",
      key: "author",
    },
    {
      title: "状态",
      dataIndex: "status",
      key: "status",
      render: (status: string) => {
        const statusMap = {
          draft: "草稿",
          published: "已发布",
          archived: "已归档",
        };
        return statusMap[status as keyof typeof statusMap] || status;
      },
    },
    {
      title: "创建时间",
      dataIndex: "createTime",
      key: "createTime",
    },
    {
      title: "操作",
      key: "action",
      render: (_: any, record: WorkItem) => (
        <Space size="middle">
          <Button type="link" size="small">
            查看
          </Button>
          <Button type="link" size="small">
            编辑
          </Button>
          <Button type="link" danger size="small">
            删除
          </Button>
        </Space>
      ),
    },
  ];

  // 处理表格分页变化
  const handleTableChange = (pagination: any) => {
    fetchWorks(pagination.current, pagination.pageSize);
  };

  return (
    <div>
      <Title level={2} style={{ color: "white" }}>
        作品管理
      </Title>

      <div style={{ marginBottom: 16 }}>
        <Button type="primary">新增作品</Button>
      </div>

      {/* 这里我们只展示UI，实际上API还未实现，所以表格会是空的 */}
      <Table
        columns={columns}
        dataSource={works}
        rowKey="id"
        pagination={pagination}
        loading={loading}
        onChange={handleTableChange}
        style={{
          background: "#1f1f1f",
          borderRadius: "8px",
        }}
      />

      <div style={{ marginTop: 16 }}>
        <Text style={{ color: "#a3a3a3" }}>
          注意：这是一个示例页面，展示了如何使用封装的API服务。实际上，API尚未实现，所以表格是空的。
        </Text>
      </div>
    </div>
  );
}
