"use client";

import React, { useState } from "react";
import Typography from "antd/es/typography";
import Tabs from "antd/es/tabs";
import Input from "antd/es/input";
import Button from "antd/es/button";
import Card from "antd/es/card";
import message from "antd/es/message";
import SaveOutlined from "@ant-design/icons/SaveOutlined";
import ReloadOutlined from "@ant-design/icons/ReloadOutlined";
import styles from "./prompts.module.css";

const { Title, Paragraph } = Typography;
const { TextArea } = Input;

// 示例默认Prompt内容
const defaultPrompts = {
  content: `你是一个专业的内容创作者，擅长撰写高质量的文章。
请根据以下主题创作一篇内容丰富、逻辑清晰的文章：
[主题]

要求：
1. 文章应该有吸引人的标题
2. 内容应该包含引言、主体和结论
3. 使用事实和数据支持你的观点
4. 文章长度在800-1200字之间`,

  polish: `你是一个专业的文案润色专家，擅长优化文本使其更加流畅、专业和有吸引力。
请对以下文案进行润色和优化：
[原文案]

润色要求：
1. 保持原文的核心信息和主要观点
2. 改进语言表达，使其更加流畅自然
3. 优化段落结构和逻辑连贯性
4. 增强文案的吸引力和说服力`,

  topic: `你是一个创意选题专家，擅长发现有价值、有吸引力的内容主题。
请根据以下领域/目标受众，提供10个有潜力的内容选题：
[领域/受众]

要求：
1. 每个选题都应该有明确的标题和简短说明
2. 选题应该具有时效性和相关性
3. 考虑目标受众的兴趣和需求
4. 选题应该有足够的深度，能够展开为完整内容`,
};

export default function PromptsPage() {
  // 状态管理
  const [activeKey, setActiveKey] = useState("content");
  const [contentPrompt, setContentPrompt] = useState(defaultPrompts.content);
  const [polishPrompt, setPolishPrompt] = useState(defaultPrompts.polish);
  const [topicPrompt, setTopicPrompt] = useState(defaultPrompts.topic);

  // 处理标签页切换
  const handleTabChange = (key: string) => {
    setActiveKey(key);
  };

  // 保存Prompt
  const savePrompt = (type: string) => {
    let content = "";
    switch (type) {
      case "content":
        content = contentPrompt;
        break;
      case "polish":
        content = polishPrompt;
        break;
      case "topic":
        content = topicPrompt;
        break;
      default:
        break;
    }

    // 这里应该添加实际的保存逻辑，例如调用API
    // 目前只是显示一个成功消息
    message.success(
      `${
        type === "content" ? "文案" : type === "polish" ? "润色文案" : "选题"
      }Prompt保存成功！`
    );
  };

  // 重置Prompt
  const resetPrompt = (type: string) => {
    switch (type) {
      case "content":
        setContentPrompt(defaultPrompts.content);
        break;
      case "polish":
        setPolishPrompt(defaultPrompts.polish);
        break;
      case "topic":
        setTopicPrompt(defaultPrompts.topic);
        break;
      default:
        break;
    }
    message.info(
      `已重置为默认${
        type === "content" ? "文案" : type === "polish" ? "润色文案" : "选题"
      }Prompt`
    );
  };

  return (
    <div className={styles.container}>
      <Title level={2} style={{ color: "white" }}>
        修改Prompt
      </Title>

      <Card className={styles.card}>
        <Paragraph className={styles.description}>
          在这里，您可以修改系统使用的各种Prompt模板。这些模板将用于生成内容、润色文案和选择主题。
          修改后的Prompt将立即生效，请确保您的修改符合预期需求。
        </Paragraph>

        <Tabs
          activeKey={activeKey}
          onChange={handleTabChange}
          className={styles.tabsContainer}
          type="card"
          items={[
            {
              key: "content",
              label: "文案Prompt",
              children: (
                <div className={styles.tabContent}>
                  <TextArea
                    value={contentPrompt}
                    onChange={(e) => setContentPrompt(e.target.value)}
                    className={styles.promptTextarea}
                    placeholder="请输入文案生成Prompt..."
                    autoSize={{ minRows: 12, maxRows: 20 }}
                  />
                  <div className={styles.actionButtons}>
                    <Button
                      icon={<ReloadOutlined />}
                      onClick={() => resetPrompt("content")}
                    >
                      重置
                    </Button>
                    <Button
                      type="primary"
                      icon={<SaveOutlined />}
                      onClick={() => savePrompt("content")}
                    >
                      保存
                    </Button>
                  </div>
                </div>
              ),
            },
            {
              key: "polish",
              label: "润色文案Prompt",
              children: (
                <div className={styles.tabContent}>
                  <TextArea
                    value={polishPrompt}
                    onChange={(e) => setPolishPrompt(e.target.value)}
                    className={styles.promptTextarea}
                    placeholder="请输入润色文案Prompt..."
                    autoSize={{ minRows: 12, maxRows: 20 }}
                  />
                  <div className={styles.actionButtons}>
                    <Button
                      icon={<ReloadOutlined />}
                      onClick={() => resetPrompt("polish")}
                    >
                      重置
                    </Button>
                    <Button
                      type="primary"
                      icon={<SaveOutlined />}
                      onClick={() => savePrompt("polish")}
                    >
                      保存
                    </Button>
                  </div>
                </div>
              ),
            },
            {
              key: "topic",
              label: "选题Prompt",
              children: (
                <div className={styles.tabContent}>
                  <TextArea
                    value={topicPrompt}
                    onChange={(e) => setTopicPrompt(e.target.value)}
                    className={styles.promptTextarea}
                    placeholder="请输入选题Prompt..."
                    autoSize={{ minRows: 12, maxRows: 20 }}
                  />
                  <div className={styles.actionButtons}>
                    <Button
                      icon={<ReloadOutlined />}
                      onClick={() => resetPrompt("topic")}
                    >
                      重置
                    </Button>
                    <Button
                      type="primary"
                      icon={<SaveOutlined />}
                      onClick={() => savePrompt("topic")}
                    >
                      保存
                    </Button>
                  </div>
                </div>
              ),
            },
          ]}
        />
      </Card>
    </div>
  );
}
