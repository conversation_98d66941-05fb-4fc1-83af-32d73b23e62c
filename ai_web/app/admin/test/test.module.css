.container {
  padding: 0;
}

.tabsContainer {
  margin-top: 20px;
}

.tabContent {
  margin-top: 20px;
}

.textAreaContainer {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

@media (min-width: 768px) {
  .textAreaContainer {
    flex-direction: row;
  }
}

.textAreaWrapper {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.textAreaLabel {
  margin-bottom: 8px;
  color: white;
  font-weight: 500;
}

.inputTextArea {
  min-height: 300px;
  font-family: monospace;
  margin-bottom: 10px;
  flex: 1;
}

.outputTextArea {
  min-height: 300px;
  font-family: monospace;
  background-color: #1a1a1a;
  color: #d9d9d9;
  flex: 1;
}

.actionButtons {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
  gap: 10px;
}

.card {
  margin-bottom: 20px;
}

.cardTitle {
  color: white;
  margin-bottom: 16px;
}

.description {
  color: rgba(255, 255, 255, 0.65);
  margin-bottom: 20px;
}

.loadingContainer {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300px;
}
