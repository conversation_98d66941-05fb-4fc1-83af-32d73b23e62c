@import "tailwindcss";

/* We'll load Ant Design styles through the next-plugin-antd-less */

:root {
  --background: #000000;
  --foreground: #ffffff;
  --primary-color: #1668dc;
  --secondary-color: #177ddc;
  --bg-container: #141414;
  --bg-elevated: #1f1f1f;
  --border-color: #303030;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
}

/* Admin layout specific styles */
.admin-logo {
  height: 32px;
  margin: 16px;
  background: rgba(255, 255, 255, 0.1);
}

.site-layout .site-layout-background {
  background: var(--bg-container);
}

/* Dark theme specific styles */
.ant-layout {
  background: var(--background);
}

.ant-layout-sider {
  background: var(--bg-elevated);
  border-right: 1px solid var(--border-color);
}

.ant-layout-header {
  background: var(--bg-elevated);
  border-bottom: 1px solid var(--border-color);
}

.ant-card {
  background: var(--bg-elevated);
  border-color: var(--border-color);
}

.ant-table {
  background: var(--bg-elevated);
  color: var(--foreground);
}

.ant-menu-dark {
  background: var(--bg-elevated);
}

/* Custom scrollbar for dark theme */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--background);
}

::-webkit-scrollbar-thumb {
  background: #444;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #555;
}
