<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Document</title>
  </head>
  <body>
    <textarea id="inputEle" style="width: 300px; height: 100px"></textarea
    ><br />
    <button id="btn">创建口播稿</button>
    <button id="CheckArticle">查询文章</button>
    <button id="btn2">创建选题</button>
    <div id="data"></div>
    <br />
    <button id="btn4">初始化表格</button>
    <br /><br />
    <input id="inputEle2" type="file" />
    <button id="btn3" type="button">上传音频</button>
    <br /><br />
    <input id="inputEle3" type="file" />
    <button id="btn5" type="button">上传视频</button>
    <br /><br />
    <button id="btn6" type="button">查询视频信息</button>
    <button id="btn7" type="button">查询音频信息</button>
    <br /><br />
    <textarea id="runseValue" style="width: 300px; height: 100px"></textarea>
    <button id="runseBtn">润色文案</button>
    <div id="runseResult"></div>
    <!-- 

   
    <div id="result"></div>
    <button id="btn4" type="button">上传文件</button> -->
  </body>
  <script>
    /** 润色内容 */
    function runse() {}

    // const path = "http://************"; // "http://localhost:3000";
    // const path = "http://www.tcai.asia:8080";
    const path = "http://127.0.0.1";
    // const path = "http://*************";
    let val = "";
    const dataDiv = document.getElementById("data");
    function ParseSSE(data, elementId) {
      console.log("data", data);
      if (data.event == "start") {
        val += "开始生成.../n";
      } else if (data.event == "message") {
        val += data.data.message;
      }
      if (elementId) {
        console.log("写入信息:", val);
        document.getElementById(elementId).innerText = val;
      } else {
        dataDiv.innerText = val;
      }
    }
    const SSEData = (response, elementId) => {
      const reader = response.body.getReader();
      const decoder = new TextDecoder();
      function read() {
        reader.read().then(({ done, value }) => {
          if (done) return;
          let buffer = "";
          let parseData = {};
          //   const chunk = decoder.decode(value);
          buffer += decoder.decode(value, { stream: true });
          const lines = buffer.split("\n");
          buffer = lines.pop() || "";
          for (const line of lines) {
            if (line.includes("event:")) {
              const eventType = line.split(":")[1].trim();
              parseData.event = eventType;
            } else if (line.includes("data:")) {
              let eventData = line.split("data:")[1].trim();
              try {
                eventData = JSON.parse(eventData);
              } catch (error) {
                eventData = {};
              }
              parseData.data = eventData;
            } else {
              ParseSSE(parseData, elementId);
              parseData = {};
            }
          }
          // 自定义数据解析逻辑
          read(); // 递归读取
        });
      }
      read();
    };
    const inputEle = document.getElementById("inputEle");
    const btn = document.getElementById("btn");
    const btn2 = document.getElementById("btn2");
    const btn3 = document.getElementById("btn3");

    btn.addEventListener("click", () => {
      const value = inputEle.value;
      fetch(path + "/ai/writing", {
        // fetch("http://127.0.0.1:5001/ai/writing", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          value: value,
        }),
      }).then((response) => {
        SSEData(response);
      });
    });
    btn2.addEventListener("click", () => {
      const value = inputEle.value;
      fetch(path + "/ai/selectTopic", {
        // fetch("http://127.0.0.1:5001/ai/selectTopic", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          value: value,
        }),
      }).then((response) => {
        SSEData(response);
      });
    });

    // 处理润色内容
    document.getElementById("runseBtn").addEventListener("click", () => {
      const value = inputEle.value;
      document.getElementById("runseBtn").innerHTML = "润色中...";
      document.getElementById("runseBtn").disabled = true;
      fetch(path + "/ai/writing/modify", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          content: document.getElementById("runseValue").value,
        }),
      })
        .then((response) => {
          SSEData(response, "runseResult");
        })
        .finally(() => {
          document.getElementById("runseBtn").disabled = false;
          document.getElementById("runseBtn").innerHTML = "润色文案";
        });
    });
    let selectedFile = null;
    // 文件选择处理
    function handleFileSelection(file) {
      if (!file) return;
      selectedFile = file;
    }
    inputEle2.addEventListener("change", (e) => {
      console.log(e);
      handleFileSelection(e.target.files[0]);
    });
    inputEle3.addEventListener("change", (e) => {
      handleFileSelection(e.target.files[0]);
    });
    async function uploadFileAudio() {
      const formData = new FormData();
      const fileInput = document.getElementById("inputEle2");
      formData.append("file", fileInput.files[0]);
      const xhr = new XMLHttpRequest();
      xhr.open("POST", path + "/ai/createAudioModel", true);
      xhr.onload = function () {
        if (xhr.status === 200) {
          try {
            const response = JSON.parse(xhr.responseText);
            console.log("上传成功", response);
            // 在这里更新UI而不是刷新页面
            document.getElementById("result").textContent =
              "上传成功: " + response.message;
          } catch (e) {
            console.error("解析响应失败", e);
          }
        } else {
          console.error("上传失败", xhr.statusText);
        }
      };
      xhr.onerror = function () {
        console.error("请求出错");
      };
      xhr.send(formData);
    }
    // 上传视频
    async function uploadFileVideo() {
      const formData = new FormData();
      const fileInput = document.getElementById("inputEle3");
      formData.append("file", fileInput.files[0]);
      const xhr = new XMLHttpRequest();
      xhr.open("POST", path + "/ai/createVideoModel", true);
      xhr.onload = function () {
        if (xhr.status === 200) {
          try {
            const response = JSON.parse(xhr.responseText);
            console.log("上传成功", response);
            // 在这里更新UI而不是刷新页面
            document.getElementById("result").textContent =
              "上传成功: " + response.message;
          } catch (e) {
            console.error("解析响应失败", e);
          }
        } else {
          console.error("上传失败", xhr.statusText);
        }
      };
      xhr.onerror = function () {
        console.error("请求出错");
      };
      xhr.send(formData);
    }

    // 使用按钮触发上传而不是表单提交
    document.getElementById("btn3").addEventListener("click", uploadFileAudio);
    document.getElementById("btn5").addEventListener("click", uploadFileVideo);

    document.getElementById("btn4").addEventListener("click", function () {
      fetch(path + "/ai/init", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          tableName: ["user", "video", "audio", "article"],
        }),
      });
    });
    document.getElementById("btn6").addEventListener("click", function () {
      fetch(path + "/ai/getVideoModel", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({}),
      })
        .then((response) => response.json())
        .then((data) => {
          console.log("视频信息", data);
        });
    });
    document
      .getElementById("CheckArticle")
      .addEventListener("click", function () {
        fetch(path + "/ai/getArticle", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({}),
        })
          .then((response) => response.json())
          .then((data) => {
            console.log("文章信息", data);
          });
      });
    document.getElementById("btn7").addEventListener("click", function () {
      fetch(path + "/ai/queryAudioModel", {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
          // Authorization: `Bearer 80ace87e18d0489da6f96f33d51f8023`,
        },
      })
        .then((response) => response.json())
        .then((data) => {
          console.log("音频信息", data);
        });
      // fetch("https://api.fish.audio/model", {
      //   method: "GET",
      //   headers: {
      //     "Content-Type": "application/json",
      //     Authorization: `Bearer 80ace87e18d0489da6f96f33d51f8023`,
      //   },
      // })
      //   .then((response) => response.json())
      //   .then((data) => {
      //     console.log("音频信息", data);
      //   });
    });
  </script>
</html>
