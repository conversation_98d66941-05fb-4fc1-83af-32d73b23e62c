import { path as ffprobe<PERSON>ath } from "ffprobe-static";
import ffmpegStatic from "ffmpeg-static";
import { exec } from "child_process";
import { promisify } from "util";
import path from "path";
import fs from "fs/promises";

const execAsync = promisify(exec);
interface formatSSEParams {
  status: "start" | "message" | "end" | "finish" | "error";
  timestamp: string;
  message: string;
}
/**
 * 格式化 SSE 数据
 * @params data: formatSSEParams
 * @params event: any
 * @returns string
 */
export function formatSSE(data: formatSSEParams, event: any = null) {
  let output = "";
  if (event) {
    output += `event: ${event}\n`;
  }
  output += `data: ${JSON.stringify(data)}\n\n`;
  return output;
}
export function formatWSS(data: formatSSEParams, event: any = null) {
  // let output = {
  //   event: event,
  //   data: data,
  // };
  return JSON.stringify(data);
}
interface LLMStreamParams {
  url: string;
  header?: any;
  body: any;
  keys: any;
  start?: (data?: any) => void;
  end?: (data?: any) => void;
  error?: (data: any) => void;
  message?: (data: { [key: string]: any }) => void;
  finish?: (data?: any) => void;
}
/**
 * LLM流式请求
 * @param LLMParams
 * @returns
 */
export async function LLMStream(LLMParams: LLMStreamParams) {
  const { url, header, body, keys, start, end, error, message, finish } =
    LLMParams;
  let fullData = ""; // 请求的完整数据
  let fullValue = {
    thinking: "",
    text: "",
  };
  // 停止请求
  const stop = async () => {
    controller.abort();
  };
  const controller = new AbortController();
  if (typeof start === "function") {
    start();
  }
  fetch(url, {
    method: "POST",
    signal: controller.signal,
    headers: {
      "content-type": "application/json",
      Authorization: `Bearer ${keys}`,
      ...header,
    },
    body: JSON.stringify({
      stream: true, // 开启流式传输
      ...body,
    }),
  })
    .then(async (response) => {
      if (!response.ok) {
        console.log("请求失败：", response.statusText);
        if (typeof error === "function") {
          error({
            state: response.status,
            message: response.statusText,
          });
        }
        throw new Error(
          `大模型 API 错误: ${response.status}${JSON.stringify(response)}`
        );
      } else {
        try {
          const reader = response.body?.getReader();
          const decoder = new TextDecoder("utf-8");
          while (true) {
            if (reader) {
              const { done, value } = await reader.read();
              // 结束流数据
              if (done) {
                console.log("结束调用大模型API结束");
                if (typeof end === "function") {
                  end({ value: fullData });
                }
                break;
              }
              // 解码数据块
              const chunk = decoder.decode(value);
              // 处理数据块
              const lines = chunk
                .split("\n")
                .filter((line) => line.trim() !== "");
              // 训话解析数据
              lines.forEach((line) => {
                if (line === "data: [DONE]") {
                } else {
                  // 解析数据
                  const data = line.replace("data: ", "");
                  const obj = JSON.parse(`${data}`).choices[0].delta;
                  let v = "";
                  if (obj.reasoning_content) {
                    // v = obj.reasoning_content;
                    fullValue.thinking += v;
                  } else if (obj.content) {
                    v = obj.content;
                    fullValue.text += v;
                  }
                  if (typeof message === "function") {
                    message({ message: v });
                  }
                  fullData += v;
                }
              });
            } else {
              console.log("reader 错误");
              break;
            }
          }
        } catch (error) {
          if (typeof error === "function") {
            error({
              state: 500,
              message: `解析失败：${JSON.stringify(error)}`,
            });
          }
        }
      }
    })
    .finally(() => {
      if (typeof finish === "function") {
        finish({
          value: fullData,
          thinking: fullValue.thinking,
          text: fullValue.text,
        });
      }
    });
  return {
    stop,
    output: fullData,
  };
}
/**
 * 确保上传目录存在
 * @param path
 * @returns
 * */
export async function ensureUploadDir(path: string) {
  try {
    await fs.access(path);
  } catch {
    await fs.mkdir(path, { recursive: true });
  }
}

/**
 * 生成安全文件名
 * @param originalName
 * @returns
 */
export function generateSafeFilename(originalName: string) {
  const timestamp = Date.now();
  const randomString = Math.random().toString(36).substring(2, 8);
  const sanitizedName = originalName.replace(/[^a-zA-Z0-9_.-]/g, "_");
  return `${timestamp}-${randomString}-${sanitizedName}`;
}
/**
 * 将秒数格式化为 MM:SS 形式
 * @param {number} seconds 秒数
 * @returns {string} 格式化后的时间字符串，如 "03:45"
 */
function formatDuration(seconds: number) {
  if (isNaN(seconds) || seconds < 0) {
    return "00:00";
  }

  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = Math.floor(seconds % 60);

  // 补零操作，确保两位数显示
  const paddedMinutes = String(minutes).padStart(2, "0");
  const paddedSeconds = String(remainingSeconds).padStart(2, "0");

  return `${paddedMinutes}:${paddedSeconds}`;
}
/**
 * 获取音视频文件信息
 * @param file
 */
export async function mediaInfo(
  filePath: string
): Promise<{ duration: number; durationFormat: string }> {
  const { stdout } = await execAsync(
    `"${ffprobePath}" -v error -show_entries format=duration -of json "${filePath}"`
  );
  const { format } = JSON.parse(stdout);
  return {
    duration: Math.floor(format.duration),
    durationFormat: formatDuration(format.duration),
  };
}
/**
 * 获取视频封面
 * @param {string} videoPath 视频文件路径
 * @param {string} outputDir 封面输出目录
 * @param {number} [time=1] 截取封面的时间点(秒)
 * @returns {Promise<string>} 返回封面图片路径
 */
export async function extractVideoThumbnail({
  videoPath,
  outputDir,
  time = 1,
}: {
  videoPath: string;
  outputDir: string;
  time?: number;
}) {
  try {
    const ffmpegPath = ffmpegStatic;
    // 确保输出目录存在
    await ensureUploadDir(outputDir);

    // 生成输出文件名
    const videoName = path.basename(videoPath, path.extname(videoPath));
    const outputPath = path.join(outputDir, `${videoName}-thumbnail.jpg`);
    console.log();
    // 使用ffmpeg截取封面
    await execAsync(
      `"${ffmpegPath}" -ss ${time} -i "${videoPath}" -vframes 1 -q:v 2 "${outputPath}"`
    );

    return outputPath;
  } catch (error) {
    console.error("提取视频封面失败:", error);
    throw error;
  }
}
