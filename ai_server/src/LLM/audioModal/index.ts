import { MultipartFile } from "@fastify/multipart";
import { FastifyRequest } from "fastify";
import { VideoConfig } from "../config";
import pump from "pump";
import path from "path";
import fs from "fs";
import { generateSafeFilename, ensureUploadDir, mediaInfo } from "../common";
import { AudioConfig, CloneAudioConfig } from "../config";
import {
  CreateModelTask,
  GetModelList,
  GetModelDetail,
  TextToAudio,
} from "./Fish";
import { HiflyCreateAudioModelTask, HiflyCreateByTts } from "./Feiying";
import { insertAudioOne } from "../../models/audio";
import { Readable } from "stream";
import { uploadLocalFileToOSS } from "../../oss/index";
interface CreateAudioModelProps {
  file: MultipartFile;
  request: FastifyRequest;
  type: string; //"FH" | "FISH";
}
export const createAudioModel = async ({
  file,
  request,
  type,
}: CreateAudioModelProps) => {

  try {
    const { filename, mimetype, fields } = file;
    // 获取文件的参数
    const filesParams: { [key: string]: any } = {};
    const arr = Object.entries(fields);
    arr.forEach(([key, value]: [key: string, value: any], index) => {
      if (value.type !== "file") {
        filesParams[key] = value.value;
      }
    });
    request.log.info("上传参数：", filesParams);

    request.log.info("验证文件类型" + mimetype);
    // 验证文件类型
    if (!AudioConfig.allowedMimeTypes.includes(mimetype)) {
      // request.log.error("不支持的文件类型");
      return {
        code: 500,
        message: "不支持的文件类型",
      };
    }
    // 生成安全文件名
    const safeFilename = await generateSafeFilename(filename);
    // request.log.info(`文件名字：${safeFilename}`);
    // 判断文件夹是否存在不存在，则创建
    await ensureUploadDir(`${AudioConfig.uploadDir}`);
    const tempFilePath = path.join(AudioConfig.uploadDir, safeFilename);
    request.log.info(`存储的文件地址：${tempFilePath}`);
    // 保存文件到本地
    await new Promise<void>((resolve, reject) => {
      pump(file.file, fs.createWriteStream(tempFilePath), (err) => {
        if (err) {
          request.log.error(`文件保存本地失败`);
          reject(err.message);
        }
        resolve();
      });
    });
    // 获取音频时常
    const mediaData = await mediaInfo(
      `${AudioConfig.uploadDir}/${safeFilename}`
    );
    console.log("mediaData:", mediaData);
    // 判断使用的平台
    if (type === "FH") {
      // 克隆音频模型，使用的是飞影
      request.log.info("开始创建音频模型");
      // const data: any = await HiflyCreateAudioModelTask({
      //   audio_url: [`${AudioConfig.uploadDir}/${safeFilename}`],
      //   title: filesParams.title,
      //   request,
      // });
      // request.log.info(`创建音频模型成功:${data}`);
    } else if (type === "FISH") {
      // 使用fish平台
      // const data: any = await CreateModelTask({
      //   paths: [`${AudioConfig.uploadDir}/${safeFilename}`],
      //   title: "测试",
      //   visibility: "private",
      //   request,
      // });
    }


    // const data: any = await HiflyCreateAudioModelTask({
    //   audio_url: [`${AudioConfig.uploadDir}/${safeFilename}`],
    //   title: filesParams.title,
    //   request,
    // });
    // request.log.info(`创建音频模型成功:${data}`);

    // const data: any = await CreateModelTask({
    //   paths: [`${AudioConfig.uploadDir}/${safeFilename}`],
    //   title: "测试",
    //   visibility: "private",
    //   request,
    // });

    // 上传的音频上传到OSS
    const ossResult = await uploadLocalFileToOSS(tempFilePath, "audio");
    console.log("ossResult:", ossResult);
    // 数据库参数
    const params: any = {
      open_id: filesParams.open_id,
      title: filesParams.title,
      description: filesParams.description || "",
      oss_url: ossResult.url,
      oss_path: ossResult.ossPath,
      file_size: file.file.bytesRead,
      duration: mediaData.duration,
      status: "model",
      format: mimetype,
      modelInfo: {}, //data as { [key: string]: any },
      model_id: "", //data.model_id,
      model_status: "", //data.task_id,
    };
    // request.log.info(`将音频信息存入数据库参数信息：`, params);

    const result = await insertAudioOne({ ...params });
    // request.log.info(`插入数据库成功`);
    return {
      code: 200,
      filename: safeFilename,
      mimetype,
      message: "上传成功",
      oss_url: ossResult.url,
      duration: mediaData.duration,
      ...result,
    };
  } catch (error: any) {
    return {
      code: 500,
      message: error.message || "上传失败",
    };
  }
};

// export const checkAudioTask = async(task_id:string){
//   if(!task_id){
//     return {
//       code: 500,
//       message:"缺少task_id"
//     }
//   }
//   if(GlobalConfig.getSyntheticAudioType() === "FH"){

//   }
// }
export const getFishModel = async (params: {}) => {
  const result = await GetModelList(params);
  console.log("音频模型列表:", result);
  return result;
};
export const getFishModelDetail = async (modelId: string) => {
  try {
    const result = await GetModelDetail(modelId);
    console.log("音频模型详情:", result);
    return {
      code: 200,
      data: result,
    };
  } catch (error: any) {
    return {
      code: 500,
      message: `${modelId}获取模型详情失败: ${error.message}`,
    };
  }
};
export const getFishTextToAudio = async (params: {
  text: string;
  task_id: string;
  format?: "wav" | "mp3";
  type: string;
}) => {
  const TemporaryVar: { [key: string]: any } = {
    oss_url: "",
    oss_path: "",
    // 时长
    duration: 0,
    //0:未开始 1:等待中 2:处理中 3:完成 4:失败
    status_code: 0,
  };
  if (params.type === "FH") {
    const result = await HiflyCreateByTts({
      voice: params.task_id,
      text: params.text,
      title: params.text.substring(0, 19),
    });
    const response: any = await fetch(result.demo_url);
    if (!response.ok) {
      return {
        code: 500,
        message: "获取音频失败",
      };
    }
    // 生成安全文件名
    const safeFilename = await generateSafeFilename("fish.mp4");
    // 保存文件
    await ensureUploadDir(`${VideoConfig.uploadDir}`);

    // 保存文件到本地
    await new Promise((resolve, reject) => {
      pump(
        response.body,
        fs.createWriteStream(`${VideoConfig.uploadDir}/${safeFilename}`),
        (err) => {
          if (err) {
            reject(err.message);
            return {
              code: 500,
              message: "保存本地失败",
            };
          }
          resolve({});
        }
      );
    });
    // 调用飞影的接口
  } else if (params.type === "FISH") {
    // 调用fish的接口
    var result = await TextToAudio({
      text: params.text,
      reference_id: params.task_id,
    });
    // 生成安全文件名
    const safeFilename = await generateSafeFilename(
      `${params.task_id}.${CloneAudioConfig.fileType}`
    );
    `${params.task_id}.${CloneAudioConfig.fileType}`;
    console.log("AudioConfig.uploadDir:", CloneAudioConfig.uploadDir);
    // 判断文件夹是否存在不存在，则创建
    await ensureUploadDir(`${CloneAudioConfig.uploadDir}`);
    // 将 Web ReadableStream 转换为 Node.js Readable 流
    const nodeReadable = Readable.fromWeb(result.data.body);
    // 保存文件到本地
    await new Promise((resolve, reject) => {
      pump(
        nodeReadable,
        fs.createWriteStream(`${CloneAudioConfig.uploadDir}/${safeFilename}`),
        (err) => {
          if (err) {
            console.log("保存本地错误:", err);
            reject(err.message);
            return;
          }
          resolve({});
        }
      );
    });
    // 上传到oss
    const ossResult = await uploadLocalFileToOSS(
      `${CloneAudioConfig.uploadDir}/${safeFilename}`,
      "audio"
    );
    return {
      code: 200,
      data: {
        path: ossResult.url,
      },
    };
  }
  // 存储数据库
  const data = await insertAudioOne({
    open_id: "admin",
    title: params.text,
    description: params.text,
    duration: TemporaryVar.duration,
    model_id: TemporaryVar.model_id,
    model_status: TemporaryVar.model_status,
    oss_url: TemporaryVar.oss_url,
    oss_path: TemporaryVar.oss_path,
    format: TemporaryVar.format,
  });
  return {
    code: 200,
    data: {
      path: TemporaryVar.oss_url,
    },
  };
};
