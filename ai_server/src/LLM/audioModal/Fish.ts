import fs from "fs/promises";
import { SimpleConsoleLogger } from "typeorm";
import { FastifyRequest } from "fastify";
// 80ace87e18d0489da6f96f33d51f8023
const FISH_API_KEY = "80ace87e18d0489da6f96f33d51f8023";
const FISH_API_URL = "https://api.fish.audio";
/**
 * 获取模型列表
 * @param options
 * @returns
 */
interface GetModelListOptions {
  page_size?: number;
  page_number?: number;
  sort_by?: "score" | "task_count" | "created_at";
}
const GetModelList = async (options: GetModelListOptions) => {
  console.log("开始查询模型列表");
  const url = new URL(`${FISH_API_URL}/model/`);
  url.searchParams.append("self", "true");
  return new Promise((resolve, reject) => {
    fetch(url, {
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${FISH_API_KEY}`,
      },
    })
      .then((res) => res.json())
      .then((res) => {
        if (res.code === 200) {
          resolve(res.data);
        } else {
          reject(res);
        }
      })
      .catch((err) => {
        console.log("err:", err);
        reject(err);
      });
  });
};
/**
 * 获取模型详情
 * @param modelId
 * @returns
 */
const GetModelDetail = async (modelId: string) => {
  const response = await fetch(`${FISH_API_URL}/model/${modelId}`, {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${FISH_API_KEY}`,
    },
  });
  const data = await response.json();
  return data;
};

interface CreateModelTaskOptions {
  paths: string[];
  title: string;
  visibility: "private" | "public";
  request: FastifyRequest;
}
/**
 * 创建模型任务
 * @ param modelId
 * @param options
 */
const CreateModelTask = async ({
  paths,
  title,
  visibility,
  request,
}: CreateModelTaskOptions) => {
  const stats = await fs.readFile(paths[0]);
  request.log.info(`获取音频数据:${stats}`);
  const formData = new FormData();
  formData.append("voices", new Blob([stats]));
  formData.append("type", "tts");
  formData.append("title", title);
  formData.append("train_mode", "fast");
  formData.append("visibility", visibility ?? "private");
  request.log.info(`${FISH_API_URL}/model`);
  return new Promise((resolve, reject) => {
    fetch(`${FISH_API_URL}/model`, {
      method: "POST",
      headers: {
        Authorization: `Bearer ${FISH_API_KEY}`,
      },
      body: formData,
    })
      .then((response) => {
        return response.json();
      })
      .then((data) => {
        console.log("创建模型:", data);
        if (data.state === 200) {
          resolve({
            ...data,
          });
        } else {
          reject(data);
        }
      })
      .catch((error) => {
        console.log("失败", error);
        reject(error);
      });
  });
};
/**
 * 删除模型
 * @param modelId
 */
const DeleteModel = async (modelId: string) => {
  const response = await fetch(`${FISH_API_URL}/model/${modelId}`, {
    method: "DELETE",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${FISH_API_KEY}`,
    },
  });
  const data = await response.json();
  return data;
};
interface TextToAudioProps {
  text: string;
  reference_id: string;
  format?: "wav" | "mp3";
}
/**
 * 文字转音频
 * @param modelId
 */
const TextToAudio = async (
  options: TextToAudioProps
): Promise<{ code: number; [key: string]: any }> => {
  console.log("开始：文字转音频");
  return await new Promise((resolve, reject) => {
    fetch(`${FISH_API_URL}/v1/tts`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${FISH_API_KEY}`,
      },
      body: JSON.stringify({
        ...options,
      }),
    })
      .then((response) => {
        console.log("文字转音频结果:", response);
        resolve({
          code: 200,
          data: response,
        });
      })
      .catch((error) => {
        console.log("失败", error);
        reject({
          code: 500,
          message: error.message,
        });
      });
  });
};

export {
  GetModelList,
  GetModelDetail,
  CreateModelTask,
  DeleteModel,
  TextToAudio,
};
