import { FastifyInstance } from "fastify";
import { initSchema, writingModifySchema, userInfoSchema } from "./schema";
import {
  InitArticleDB,
  InitAudioDB,
  InitUserDB,
  InitVideoDB,
  InitWorkflowDB,
} from "@/models";
import { streamHeader } from "../z_config";
import {
  GetDeepSeekStreamModifArticle,
  GetDeepSeekStreamSelectTitle,
  GetDeepSeekStreamWriterExpert,
} from "@/LLM";
import { getArticleAll } from "@/models/article";
import { user_information } from "@/controllers/users";

export async function userRoutes(fastify: FastifyInstance) {
  /** 获取用户信息 */
  fastify.post(
    "/ai/user/information",
    { schema: userInfoSchema },
    async (request, reply) => {
      const params: any = request.body;
      // 用户基本信息
      const result = await user_information({ ...params });
      // 用户文章、视频的数量统计
      reply.send({
        code: 200,
        data: result,
      });
    }
  );
  /** 获取用户充值历史 */
  fastify.get("/ai/user/history", async (request, reply) => {
    reply.send({
      code: 200,
      data: {
        list: [{}],
      },
    });
  });
  /** 初始化表 */
  fastify.post("/ai/init", { schema: initSchema }, async (request, reply) => {
    const body: any = request.body;
    const resultArr: any = [];
    body.tableName.forEach(async (item: any) => {
      if (item === "article") {
        resultArr.push(InitArticleDB(fastify));
      }
      if (item === "user") {
        resultArr.push(InitUserDB(fastify));
      }
      if (item === "video") {
        resultArr.push(InitVideoDB(fastify));
      }
      if (item === "audio") {
        resultArr.push(InitAudioDB(fastify));
      }
      if (item === "workflow") {
        resultArr.push(InitWorkflowDB(fastify));
      }
    });
    const data = await Promise.all(resultArr);
    return reply.send({
      success: true,
      message: "初始化成功",
      data: data,
    });
    // const tables = body.tableName;
    // InitArticleDB(fastify);
  });

  /** 创建选题 */
  fastify.post("/ai/selectTopic", async (request, reply) => {
    reply.raw.writeHead(200, streamHeader);
    const params: any = request.body;
    console.log("params:", params);
    await GetDeepSeekStreamSelectTitle({
      reply,
      params: {
        prompt: params?.value ?? "",
        openId: params?.openId ?? "",
        number: params?.number ?? 6,
      },
    });
  });
  /** 创建文案 */
  fastify.post("/ai/writing", async (request, reply) => {
    reply.raw.writeHead(200, streamHeader);
    const params: any = request.body;
    console.log("params:", typeof params);
    await GetDeepSeekStreamWriterExpert({
      reply,
      params: {
        prompt: params?.value ?? "",
        openId: params?.openId ?? "",
        number: params?.number ?? 6,
      },
    });
  });
  /** 文案润色、文案改写 */
  fastify.post(
    "/ai/writing/modify",
    { schema: writingModifySchema },
    async (request, reply) => {
      reply.raw.writeHead(200, streamHeader);
      const body: any = request.body;
      await GetDeepSeekStreamModifArticle({
        params: body,
        reply,
      });
    }
  );

  /** 文章查询 */
  fastify.post("/ai/getArticle", async (request, reply) => {
    const params: any = request.body;
    const result = await getArticleAll(params);
    console.log("params:", params);
    return {
      success: true,
      message: "查询成功",
      data: result,
    };
  });

  /** 测试接口 */
  fastify.get("/ai/test", async (request, reply) => {
    return {
      success: true,
      message: "测试成功",
    };
  });
}
