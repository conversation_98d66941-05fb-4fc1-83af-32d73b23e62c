import {
  createAudioModel,
  getFishModel,
  getFishModelDetail,
  getFishTextToAudio,
} from "@/LLM/audioModal";
import { MultipartFile } from "@fastify/multipart";
import { AudioDB } from "@/models";
import { FastifyInstance, FastifyReply } from "fastify";
import { textToAudioSchema } from "./schema";
import GlobalConfig from "@/globalConfig";

export async function audioRoutes(fastify: FastifyInstance) {
  /** 生成音频模型 */
  fastify.post("/ai/createAudioModel", async (request, reply: FastifyReply) => {
    try {
      const file = await request.file();
      // 判断是否有上传的文件
      if (!file) {
        reply.status(400).send({
          success: false,
          message: "请上传文件",
        });
        return;
      }
      // 开始处理上传的文件
      const result = await createAudioModel({
        file,
        request,
        type: GlobalConfig.getSyntheticAudioType(),
      });
      if (result) {
        console.log("result:", result);
        if (result.code === 200) {
          return reply.send({
            success: true,
            message: "文件上传成功",
            data: result,
          });
        } else {
          return reply.send({
            code: result.code,
            success: false,
            message: result.message ?? "文件上传失败",
            data: result,
          });
        }
      }
    } catch (error: any) {
      reply.code(500).header("Connection", "close").send({
        success: false,
        message: "文件上传失败",
        error: error?.message,
      });
      return;
    }
  });
  /** 查询第三方音频模型数据 */
  fastify.get("/ai/queryAudioModel", async (request, reply) => {
    const data = await getFishModel({});
    return reply.send({
      success: true,
      message: "查询成功",
      data: data,
    });
  });
  /** 查询音频模型数据 */
  fastify.post("/ai/getAudioModel/list", async (request, reply) => {
    const params: any = request.body;
    const data = await AudioDB.getAudioPagination(params);
    return reply.send({
      success: true,
      message: "查询成功",
      data: data,
    });
  });
  /** 查询音频模型详情信息 */
  fastify.get("/ai/queryAudioModelDetail/:id", async (request, reply) => {
    const params: any = request.params;
    const data = await getFishModelDetail(params.id);
    return reply.send({
      success: true,
      message: "查询成功",
      ...data,
    });
  });
  /** 删除音频模型数据 */
  fastify.get("/ai/deleteAudioModel/:id", async (request, reply) => {
    const params: any = request.params;
    const data = await AudioDB.deleteAudioOne(params.id);
    return reply.send({
      success: true,
      ...data,
    });
  });
  /** 文字合成音频 */
  fastify.post(
    "/ai/textToAudio",
    { schema: textToAudioSchema },
    async (request, reply) => {
      try {
        const body: any = request.body;
        console.log("body:", body);
        const data = await getFishTextToAudio({
          text: body.text,
          task_id: body.modelId,
          format: body.format || "mp3",
          type: GlobalConfig.getSyntheticAudioType(),
        });
        return reply.send({
          success: true,
          message: "查询成功",
          ...data,
        });
      } catch (error: any) {
        return reply.code(500).send({
          success: true,
          message: error.message,
        });
      }
    }
  );
}
