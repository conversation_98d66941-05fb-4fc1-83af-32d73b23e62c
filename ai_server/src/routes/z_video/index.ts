import { createVideoModel } from "@/LLM/videoModal";
import { getVideoAll } from "@/models/video";
import { FastifyInstance, FastifyReply } from "fastify";
import { mergeAudioVideoSchema } from "./schema";
import { VideoDB } from "@/models";

export async function videoRoutes(fastify: FastifyInstance) {
  /** 创建视频模型 */
  fastify.post("/ai/createVideoModel", async (request, reply) => {
    try {
      const files = await request.file();
      if (!files) {
        reply.status(400).send({
          success: false,
          message: "请上传文件",
          data: {},
        });
        return;
      }
      const result = await createVideoModel(files);
      if (result.code === 200) {
        return reply.send({
          success: true,
          message: "上传成功",
          data: result,
        });
      } else {
        return reply.send({
          code: result.code,
          success: false,
          message: result.message ?? "文件上传失败",
          data: {},
        });
      }
    } catch (error: any) {
      return reply.status(500).send({
        code: 500,
        success: false,
        message: "文件上传失败",
      });
    }
  });
  /** 查询第三方视频模型列表信息 */
  fastify.post("/ai/getVideoModel", async (request, reply: FastifyReply) => {
    try {
      const params: any = request.body;
      const result = await getVideoAll(params);
      console.log("result:", result);
      return reply.send({
        success: true,
        message: "查询成功",
        data: result,
      });
    } catch (error: any) {
      return reply.send({
        success: false,
        message: "查询失败",
        error: error.message,
      });
    }
  });
  /** 飞影 - 音视频合成 */
  fastify.post(
    "/ai/mergeAudioVideo",
    { schema: mergeAudioVideoSchema },
    async (request, reply) => {
      const body = request.body;
      return {
        success: true,
        message: "合成成功",
      };
    }
  );
  /** 查询视频模型数据列表 */
  fastify.post("/ai/getVideoModel/list", (request, reply) => {
    const params: any = request.body;
    const result = VideoDB.getVideoPagination(params);
    return {
      success: true,
      message: "查询成功",
      data: result,
    };
  });
  /** 删除视频模型数据 */
  fastify.get("/ai/deleteVideoModel/:id", async (request, reply) => {
    const params: any = request.params;
    const data = await VideoDB.deleteVideoOne(params.id);
    return reply.send({
      success: true,
      ...data,
    });
  });
}
