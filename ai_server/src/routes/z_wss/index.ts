import configObj from "@/globalConfig";
import {
  GetDeepSeekStreamModifArticle,
  GetDeepSeekStreamSelectTitle,
  GetDeepSeekStreamWriterExpert,
} from "@/LLM";
import { FastifyInstance } from "fastify";
import { v4 as uuidv4 } from "uuid";

export async function wssUserRoutes(fastify: FastifyInstance) {
  /** 创建选题 */
  fastify.get(
    "/wss/ai/selectTopic",
    { websocket: true },
    async (socket, req) => {
      // 连接建立时发送欢迎消息
      // const welcomeMsg = {
      //   type: "notification",
      //   content: "Welcome to the chat!",
      //   timestamp: Date.now(),
      // };
      // socket.send(JSON.stringify(welcomeMsg));
      // 为每个连接生成唯一ID
      const connectionId = uuidv4();
      // 存储socket实例
      configObj.setConnection(connectionId, socket);
      socket.on("open", () => {
        console.log("open:连接成功");
      });
      socket.on("message", (message: any) => {
        try {
          const data = JSON.parse(message.toString("utf8"));
          // console.log("message:", message, message.toString("utf8"));
          if (data && data.type === "selectTopic") {
            GetDeepSeekStreamSelectTitle({
              params: {
                openId: data.openId,
                prompt: data.prompt,
                number: data.number,
                ipSetting: data.ipSetting,
              },
              socket: socket,
              wss: true,
            });
          }
        } catch (error) {
          console.log(error);
        }
      });
      socket.on("close", () => {
        configObj.deleteConnection(connectionId);
        console.log("close:");
      });
    }
  );
  /** 创建文案 */
  fastify.get(
    "/wss/ai/createWriting",
    { websocket: true },
    async (socket, reply) => {
      socket.on("message", (message: any) => {
        try {
          const params = JSON.parse(message.toString("utf8"));
          if (params && params.type === "createWriting") {
            GetDeepSeekStreamWriterExpert({
              socket: socket,
              wss: true,
              params: {
                prompt: params?.value ?? "",
                openId: params?.openId ?? "",
              },
            });
          }
        } catch (error) {
          console.log("error:", error);
        }
      });
      socket.on("open", () => {
        console.log("open:连接成功");
      });
      socket.on("close", () => {
        console.log("close:");
      });
    }
  );
  /** 文案润色、文案改写 */
  fastify.get(
    "/wss/ai/writing/modify",
    { websocket: true },
    async (socket, reply) => {
      // reply.raw.writeHead(200, streamHeader);
      // const body: any = request.body;
      socket.on("message", (message: any) => {
        console.log("message:", message);
        try {
          const params = JSON.parse(message.toString("utf8"));
          if (params && params.type === "writingModify") {
            GetDeepSeekStreamModifArticle({
              params: {
                openId: params.openId,
                write: params.content,
                config: params.config,
              },
              socket: socket,
              wss: true,
            });
          }
        } catch (error) {
          console.log("error:", error);
        }
        // console.log("parasm:", parasm);
      });
      socket.on("open", () => {
        console.log("open:连接成功");
      });
      socket.on("close", () => {
        console.log("close:");
      });
    }
  );
}
