import { uploadFileToOSS } from "@/oss";
import { FastifyInstance } from "fastify";

export async function ossUserRoutes(fastify: FastifyInstance) {
  /** 上传文件到oss */
  fastify.post("/oss/upload", async (request, reply) => {
    try {
      const files = await request.file();
      if (!files) {
        reply.status(400).send({
          code: 400,
          success: false,
          message: "请上传文件",
        });
        return;
      }
      const result = await uploadFileToOSS(files);
      if (result.code === 200) {
        return reply.send({
          code: result.code,
          success: true,
          message: "文件上传成功",
          data: {
            ...result,
          },
        });
      } else {
        return reply.send({
          code: result.code,
          success: false,
          message: "文件上传失败",
          data: result,
        });
      }
    } catch (error: any) {
      return reply.code(500).send({
        code: 500,
        success: false,
        message: error.message || "文件上传失败",
      });
    }
  });
}
