import { getWorkflowPagination, getWorkflowById } from "@/models/workflow";
import { FastifyInstance } from "fastify";
import { MultipartFile } from "@fastify/multipart";
import GlobalConfig from "@/globalConfig";
import {
  createWorkflowSchema,
  workflowDetailSchema,
  workflowListSchema,
} from "./schema";
import { createWorkflow, continueWorkflow } from "@/controllers/workflow";
import {
  GetDeepSeekStreamSelectTitle,
  GetDeepSeekStreamWriterExpert,
} from "@/LLM";
import { getFishTextToAudio } from "@/LLM/audioModal";

export async function workflowRoutes(fastify: FastifyInstance) {
  /** 分页查询工作流 */
  fastify.post(
    "/ai/workflow/list",
    { schema: workflowListSchema },
    async (request, reply) => {
      const params: any = request.body;
      const result = await getWorkflowPagination(params);
      return reply.send({
        success: true,
        message: "查询成功",
        data: result,
      });
    }
  );
  /** 获取单个工作流详情 */
  fastify.post(
    "/ai/workflow/detail",
    { schema: workflowDetailSchema },
    async (request, reply) => {
      const params: any = request.body;
      const result = getWorkflowById(params.id);
      return reply.send({
        success: true,
        message: "查询成功！",
        data: result,
      });
    }
  );
  /** 创建工作流 */
  fastify.post(
    "/ai/workflow/create",
    { schema: createWorkflowSchema },
    async (request, reply) => {
      const params: any = request.body;
      const result = await createWorkflow(params);
      return reply.send({
        success: true,
        message: "创建成功",
        data: result,
      });
    }
  );
  /** 继续工作流 */
  fastify.get(
    "/wss/ai/continue",
    { websocket: true },
    async (socket, reply) => {
      socket.on("open", () => {
        console.log("open:连接成功");
      });
      socket.on("message", async (message: any) => {
        // 获取前端传输的参数
        try {
          /**
           * {
           *    type: "selected_topic",
           *    uuid: "123456"
           *    openId: "123456"
           *
           *    prompt:"",
           *    ipSetting:{}
           *    number:6,
           *
           *    text:""
           *
           * }
           */
          const data = JSON.parse(message.toString("utf8"));
          if (!data.openId) {
          }
          if (data.type === "selected_topic") {
            // 创建选题
            GetDeepSeekStreamSelectTitle({
              params: {
                openId: data.openId,
                prompt: data.prompt,
                number: data.number,
                ipSetting: data.ipSetting,
              },
              socket: socket,
              wss: true,
            });
          } else if (data.type === "copywriting") {
            // 创建文案
            GetDeepSeekStreamWriterExpert({
              socket: socket,
              wss: true,
              params: {
                prompt: data?.prompt ?? "",
                openId: data?.openId ?? "",
              },
            });
          } else if (data.type === "create_audio") {
            // 合成音频
            const result = await getFishTextToAudio({
              text: data?.text as string,
              reference_id: data.modelId,
              format: data.format || "mp3",
              type: GlobalConfig.getSyntheticAudioType(),
            });
          } else if (data.type === "create_video") {
          }
        } catch (error) {
          console.log("error:", error);
          socket.close();
        }
      });
      socket.on("close", () => {
        console.log("close:");
      });
    }
  );
}
