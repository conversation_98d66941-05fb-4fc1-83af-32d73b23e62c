import globalObj from "../globalConfig";
import { v4 as uuidv4 } from "uuid";
import { VideoTable } from "@/types/table";
import dayjs from "dayjs";
const timeFormatter = () => {
  return dayjs().format("YYYY-MM-DD HH:mm:ss");
};

/** 视频数据参数接口 */
export interface VideoParams extends VideoTable {
  uuid?: string;
  open_id: string;
}

/** 插入视频数据 */
export async function insertVideoOne(params: VideoParams) {
  const db = globalObj
    .getFastifyInstance()
    .mongo?.db?.collection("video_table");
  const uuid = params.uuid || uuidv4();
  const result = await db?.insertOne({
    uuid: uuid,
    open_id: params.open_id,
    title: params.title,
    description: params.description || "",
    oss_url: params.oss_url,
    oss_path: params.oss_path,
    format: params.format,
    duration: params.duration,
    model_id: params.model_id,
    model_status: params.model_status,
    cover: params.cover || "",
    create_at: timeFormatter(),
    update_at: timeFormatter(),
    delete_at: null,
  });
  return { id: result?.insertedId, uuid: uuid };
}

/** 更新视频参数接口 */
export interface UpdateVideoParams {
  uuid: string;
  title?: string;
  description?: string;
  oss_url?: string;
  oss_path?: string;
  format?: string;
  duration?: number;
  model_id?: string | null;
  model_status?: string;
  cover?: string;
}

/** 更新视频信息 */
export async function updateVideoOne(params: UpdateVideoParams) {
  const db = globalObj
    .getFastifyInstance()
    .mongo?.db?.collection("video_table");
  const result = await db?.updateOne(
    { uuid: params.uuid, delete_at: null },
    {
      $set: {
        ...params,
        update_at: timeFormatter(),
      },
    }
  );
  if (result?.matchedCount === 0) {
    return { code: 404, message: "没有找到数据" };
  }
  return { code: 200, message: "更新成功" };
}

/** 查询视频参数接口 */
export interface GetVideoParams {
  uuid?: string;
  open_id?: string;
  title?: string;
  model_status?: string;
  page?: number;
  pageSize?: number;
}

/** 获取所有视频信息 */
export async function getVideoAll(params = {} as GetVideoParams) {
  const db = globalObj
    .getFastifyInstance()
    .mongo?.db?.collection("video_table");

  // 构建查询条件
  const query: any = { delete_at: null };

  if (params.uuid) query.uuid = params.uuid;
  if (params.open_id) query.open_id = params.open_id;
  if (params.title) query.title = { $regex: params.title, $options: "i" };
  if (params.model_status) query.model_status = params.model_status;

  const result = await db?.find(query).toArray();
  return result;
}

/** 分页查询视频信息 */
export async function getVideoPagination(params = {} as GetVideoParams) {
  const page = params.page || 1;
  const pageSize = params.pageSize || 10;
  const skip = (page - 1) * pageSize;

  const db = globalObj
    .getFastifyInstance()
    .mongo?.db?.collection("video_table");

  // 构建查询条件
  const query: any = { delete_at: null };

  if (params.uuid) query.uuid = params.uuid;
  if (params.open_id) query.open_id = params.open_id;
  if (params.title) query.title = { $regex: params.title, $options: "i" };
  if (params.model_status) query.model_status = params.model_status;

  // 移除分页参数
  const queryParams = { ...params };
  delete queryParams.page;
  delete queryParams.pageSize;
  const total = await db?.countDocuments(query);
  const result = await db
    ?.find(query)
    .sort({ create_at: -1 })
    .skip(skip)
    .limit(pageSize)
    .toArray();
  console.log("query:", query, total, result);

  return {
    list: result,
    pagination: {
      total,
      page,
      pageSize,
      totalPages: Math.ceil(total ?? 0 / pageSize),
    },
  };
}

/** 根据ID获取单个视频信息 */
export async function getVideoById(uuid: string) {
  const db = globalObj
    .getFastifyInstance()
    .mongo?.db?.collection("video_table");

  const result = await db?.findOne({ uuid: uuid, delete_at: null });

  if (!result) {
    return { code: 404, message: "没有找到数据" };
  }

  return result;
}

/** 删除视频信息（软删除） */
export async function deleteVideoOne(uuid: string) {
  const db = globalObj
    .getFastifyInstance()
    .mongo?.db?.collection("video_table");

  const result = await db?.updateOne(
    { uuid: uuid, delete_at: null },
    {
      $set: {
        delete_at: timeFormatter(),
        update_at: timeFormatter(),
      },
    }
  );

  if (result?.matchedCount === 0) {
    return { code: 404, message: "没有找到数据" };
  }

  return { code: 200, message: "删除成功" };
}

/** 物理删除视频信息（谨慎使用） */
export async function permanentDeleteVideo(uuid: string) {
  const db = globalObj
    .getFastifyInstance()
    .mongo?.db?.collection("video_table");

  const result = await db?.deleteOne({ uuid: uuid });

  if (result?.deletedCount === 0) {
    return { code: 404, message: "没有找到数据" };
  }

  return { code: 200, message: "永久删除成功" };
}

export default {
  insertVideoOne,
  updateVideoOne,
  getVideoAll,
  getVideoPagination,
  getVideoById,
  deleteVideoOne,
  permanentDeleteVideo,
};
