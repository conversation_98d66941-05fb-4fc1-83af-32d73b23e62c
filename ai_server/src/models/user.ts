import { v4 as uuidv4 } from "uuid";
import globalObj from "../globalConfig";
import { UserTable } from "@/types/table";
import dayjs from "dayjs";

const timeFormatter = () => {
  return dayjs().format("YYYY-MM-DD HH:mm:ss");
};

/** 插入用户信息 */
export async function insertUserDB(params: UserTable) {
  const db = await globalObj
    .getFastifyInstance()
    .mongo?.db?.collection("user_table");
  const uuid = params.uuid || uuidv4();
  const result = await db?.insertOne({
    uuid: uuid,
    open_id: params.open_id,
    name: params.name,
    password: params.password,
    role: params.role,
    tokens: params.tokens,
    create_at: timeFormatter(),
    update_at: timeFormatter(),
    delete_at: null,
  });
  return { id: result?.insertedId, uuid: uuid };
}

interface GetUserParams {
  uuid?: string;
  open_id?: string;
  name?: string;
}

/** 查询所有用户数据 */
export async function getUserAll(params = {} as GetUserParams) {
  const db = await globalObj
    .getFastifyInstance()
    .mongo?.db?.collection("user_table");
  const result = await db?.find(params).toArray();
  return result;
}

/** 查询单个用户数据 */
export async function getUserOne(params: GetUserParams) {
  const db = await globalObj
    .getFastifyInstance()
    .mongo?.db?.collection("user_table");
  const result = await db?.findOne(params);
  return result;
}

/** 更新用户数据 */
export async function updateUserDB(
  filter: { uuid: string; open_id?: string },
  update: Partial<
    Omit<
      UserTable,
      "uuid" | "open_id" | "create_at" | "update_at" | "delete_at"
    >
  >
) {
  const db = await globalObj
    .getFastifyInstance()
    .mongo?.db?.collection("user_table");

  // 确保至少有一个更新字段
  if (Object.keys(update).length === 0) {
    return { code: 400, message: "参数错误，至少需要一个更新字段" };
  }

  // 执行更新操作
  const result = await db?.updateOne(filter, {
    $set: {
      ...update,
      update_at: timeFormatter(),
    },
  });

  if (result?.matchedCount === 0) {
    return { code: 404, message: "没有找到数据" };
  }

  if (result?.modifiedCount === 0) {
    return { code: 200, message: "数据未发生变化" };
  }

  return { code: 200, message: "更新成功" };
}

/** 删除用户数据 */
export async function deleteUserDB({
  uuid,
  open_id,
}: {
  uuid: string;
  open_id?: string;
}) {
  const db = await globalObj
    .getFastifyInstance()
    .mongo?.db?.collection("user_table");

  const query: { uuid: string; open_id?: string } = { uuid };
  if (open_id) query.open_id = open_id;

  const result = await db?.deleteOne(query);

  if (result?.deletedCount === 0) {
    return { code: 404, message: "没有找到数据" };
  }

  return { code: 200, message: "删除成功" };
}

/** 查询用户数据总数 */
export async function countUsers(params = {} as GetUserParams) {
  const db = await globalObj
    .getFastifyInstance()
    .mongo?.db?.collection("user_table");
  const count = await db?.countDocuments(params);
  return { count };
}

/** 分页查询用户数据 */
export async function getUserPagination(
  params = {} as GetUserParams & { page?: number; pageSize?: number }
) {
  const page = params.page || 1;
  const pageSize = params.pageSize || 10;
  const skip = (page - 1) * pageSize;

  const db = await globalObj
    .getFastifyInstance()
    .mongo?.db?.collection("user_table");

  // Remove pagination params from the query
  const query = { ...params };
  delete query.page;
  delete query.pageSize;

  const total = await db?.countDocuments(query);
  const result = await db
    ?.find(query)
    .sort({ create_at: -1 })
    .skip(skip)
    .limit(pageSize)
    .toArray();

  return {
    data: result,
    pagination: {
      total,
      page,
      pageSize,
      totalPages: Math.ceil(total ?? 0 / pageSize),
    },
  };
}

export default {
  insertUserDB,
  getUserAll,
  getUserOne,
  updateUserDB,
  deleteUserDB,
  countUsers,
  getUserPagination,
};
