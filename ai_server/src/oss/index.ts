import OSS from "ali-oss";
import fs from "fs";
import path from "path";
import { generateSafeFilename, ensureUploadDir } from "../LLM/common";
import configObj from "../globalConfig";
import { MultipartFile } from "@fastify/multipart";
import pump from "pump";

// OSS配置
export const OSSConfig = {
  // OSS区域，例如：oss-cn-hangzhou
  region: process.env.OSS_REGION || "oss-cn-shanghai",
  // 阿里云账号AccessKey
  accessKeyId: process.env.OSS_ACCESS_KEY_ID || "LTAI5tBk8NKhdjUSCP2fbXjZ",
  // 阿里云账号AccessKey Secret
  accessKeySecret:
    process.env.OSS_ACCESS_KEY_SECRET || "******************************",
  // OSS存储空间名称
  bucket: process.env.OSS_BUCKET || "tw-cache-data-xxwwt",
  // 临时文件存储目录
  tempDir: path.join(configObj.systemConfig.rootPath, "/temp"),
  // 允许的文件类型
  allowedMimeTypes: [
    // 图片
    "image/jpeg",
    "image/png",
    "image/gif",
    "image/webp",
    "image/svg+xml",
    // 文档
    "application/pdf",
    "application/msword",
    "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
    "application/vnd.ms-excel",
    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
    // 音频
    "audio/mpeg",
    "audio/wav",
    "audio/ogg",
    "audio/flac",
    "audio/webm",
    // 视频
    "video/mp4",
    "video/webm",
    "video/ogg",
  ],
  // 最大文件大小 (100MB)
  maxFileSize: 100 * 1024 * 1024,
};

/**
 * 初始化OSS客户端
 * @returns OSS客户端实例
 */
export function initOSSClient(): OSS {
  return new OSS({
    region: OSSConfig.region,
    accessKeyId: OSSConfig.accessKeyId,
    accessKeySecret: OSSConfig.accessKeySecret,
    bucket: OSSConfig.bucket,
  });
}

/**
 * 上传文件到OSS
 * @param file 文件对象
 * @param directory 存储目录（可选）
 * @returns 上传结果
 */
export async function uploadFileToOSS(
  file: MultipartFile,
  directory: string = "uploads"
): Promise<{
  code: number;
  url?: string;
  ossPath?: string;
  error?: string;
  filename?: string;
  mimetype?: string;
  size?: number;
}> {
  try {
    const { filename, mimetype } = file;

    // 验证文件类型
    if (!OSSConfig.allowedMimeTypes.includes(mimetype)) {
      throw new Error("不支持的文件类型");
    }

    // 生成安全文件名
    const safeFilename = await generateSafeFilename(filename);

    // 确保临时目录存在
    await ensureUploadDir(OSSConfig.tempDir);

    // 临时文件路径
    const tempFilePath = path.join(OSSConfig.tempDir, safeFilename);

    // 保存文件到临时目录
    await new Promise<void>((resolve, reject) => {
      pump(file.file, fs.createWriteStream(tempFilePath), (err) => {
        if (err) {
          reject(err);
          return;
        }
        resolve();
      });
    });

    // 初始化OSS客户端
    const client = initOSSClient();

    // OSS存储路径
    const ossPath = `${directory}/${safeFilename}`;

    // 上传文件到OSS
    const result = await client.put(ossPath, tempFilePath);

    // 删除临时文件
    fs.unlinkSync(tempFilePath);

    return {
      code: 200,
      url: result.url,
      ossPath,
      filename: safeFilename,
      mimetype,
      size: file.file.bytesRead,
    };
  } catch (error: any) {
    return {
      code: 500,
      error: error.message || "上传文件失败",
    };
  }
}

/**
 * 从本地路径上传文件到OSS
 * @param localFilePath 本地文件路径
 * @param directory 存储目录（可选）
 * @returns 上传结果
 */
export async function uploadLocalFileToOSS(
  localFilePath: string,
  directory: string = "uploads"
): Promise<{
  success: boolean;
  url?: string;
  ossPath?: string;
  error?: string;
}> {
  try {
    // 检查文件是否存在
    if (!fs.existsSync(localFilePath)) {
      throw new Error("文件不存在");
    }

    // 获取文件名
    const filename = path.basename(localFilePath);

    // 生成安全文件名
    const safeFilename = await generateSafeFilename(filename);

    // 初始化OSS客户端
    const client = initOSSClient();

    // OSS存储路径
    const ossPath = `${directory}/${safeFilename}`;

    // 上传文件到OSS
    const result = await client.put(ossPath, localFilePath);

    return {
      success: true,
      url: result.url,
      ossPath,
    };
  } catch (error: any) {
    return {
      success: false,
      error: error.message || "上传文件失败",
    };
  }
}

/**
 * 获取OSS文件URL
 * @param ossPath OSS文件路径
 * @param expires URL过期时间（秒），默认3600秒
 * @returns 签名URL
 */
export async function getOSSFileURL(
  ossPath: string,
  expires: number = 3600
): Promise<string> {
  try {
    const client = initOSSClient();
    return client.signatureUrl(ossPath, { expires });
  } catch (error) {
    throw new Error("获取文件URL失败");
  }
}

/**
 * 删除OSS文件
 * @param ossPath OSS文件路径
 * @returns 删除结果
 */
export async function deleteOSSFile(ossPath: string): Promise<{
  success: boolean;
  error?: string;
}> {
  try {
    const client = initOSSClient();
    await client.delete(ossPath);
    return { success: true };
  } catch (error: any) {
    return {
      success: false,
      error: error.message || "删除文件失败",
    };
  }
}

/**
 * 检查OSS文件是否存在
 * @param ossPath OSS文件路径
 * @returns 是否存在
 */
export async function checkOSSFileExists(ossPath: string): Promise<boolean> {
  try {
    const client = initOSSClient();
    await client.head(ossPath);
    return true;
  } catch (error) {
    return false;
  }
}
