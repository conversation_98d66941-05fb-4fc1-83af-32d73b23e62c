import path from "path";
import { FastifyInstance } from "fastify";
/** 全局配置信息 */
class GlobalConfig {
  systemConfig: { [key: string]: any };
  private FastifyInstance: FastifyInstance;
  private Connections: Map<string, any>;
  private SyntheticAudioType: string;
  constructor() {
    this.FastifyInstance = {} as FastifyInstance;
    this.Connections = new Map<string, any>();
    this.systemConfig = {
      rootPath: path.join(__dirname, "/public", "/uploads"),
    };
    this.SyntheticAudioType = "FH";
  }
  setFastifyInstance(fastify: FastifyInstance) {
    this.FastifyInstance = fastify;
  }
  getFastifyInstance(): FastifyInstance {
    return this.FastifyInstance;
  }
  setConnection(key: string, value: any) {
    this.Connections.set(key, value);
  }
  getConnection(key: string) {
    return this.Connections.get(key);
  }
  deleteConnection(key: string) {
    this.Connections.delete(key);
  }
  getSyntheticAudioType() {
    return this.SyntheticAudioType;
  }
  setSyntheticAudioType(type: string) {
    this.SyntheticAudioType = type;
  }
}
const configObj = new GlobalConfig();
export default configObj;
