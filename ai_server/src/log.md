1. 工作流使用 按步骤执行的方式进行呈现
2. 所有入口都在首页
3. 涉及到选题、输入的时候，都需要先进入输入页面，然后才进入工作流
4. 选题的输入，需求、人设、高级：包含其他信息

5. ~~oss 欠费问题处理，了解 oss 基本情况（根据提供的链接充值）~~
6. ~~https:// 配置 OK~~
7. 阿里云上面 node 守护进程 pm2 安装
8. 阿里云 mongdb 安装，连接到 node 项目运行，并初始化表格

9. ~~生成选题、生成文案迁移到小程序云开发~~  云开发没有 websocket、sse 等，websocket 需要中间转

所以云开发可以做成获取用户的 appId、openId 等信息的时候可以用，还有登录状态的时候可以用

6. 用户每次请求使用云开发进行校验，用户状态，
7. 创建选题之后需要做到记录：

   - ~~选题消耗多少 token、选题内容、用户 openId 等~~

8. 创建文案

   - ~~文案消耗多少 token、文案内容、用户 openId 等~~

9. 视频模型

   - 使用 openId 标识属于哪个用户
   - 视频记录的是“飞影” task_id 用来在 合成视频的时候作为数字人标识

10. 音频模型

    - 使用 openId 标识属于哪个用户
    - 音频记录的是“fish” \_id 用来在 文案合成音频的时候作为模型 标识

11. 人设主要内容是一个 json,人设接口 增加（增加人设）、修改（修改是整体数据修改）、删除（删除当前整条人设）

```json
[
  {
    "label": "",
    "value": ""
  },
  {
    "label": "",
    "value": ""
  }
]
```

12. 查询音频模型、删除音频模型（同时删除 oss 里面的数据）
13. 查询视频模型、删除视频模型（同时删除 oss 里面的数据）
14. 文字合成音频，结果需要判断成功之后返回数据流：1、下载到本地 2、上传到 oss、3、拿到 oss 地址更新到数据库 4、返回数据结果。失败情况判断状态返回结果
15. 音频合成视频，1、 合成的音频 oss 地址+飞影的视频模型 id 合成视频，2、调用合成接口获取任务 id 3、根据任务 id 获取合成状态 4、 如果成功下载视频并存储到 oss

16. 创建流状态接口： 思考

17. 存储 agent 的表 增加字段 result：最后的结果、progress：进度。 每次查询结果的时候直接查字段就可以了

18. 音频模型改成音色模型

19. 小程序页面流程重新梳理
20. 接口重新梳理：
21. 获取小程序 UUID 作为登陆用户信息
22. 创建文案
23. 创建选题

24. Ip 人设增删改查
25. 创建文案的时候，人设数据增加
26. 创建选题的时候人设的数据增加
