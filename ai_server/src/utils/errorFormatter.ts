// utils/errorFormatter.ts
import { FastifyError, FastifyRequest, FastifyReply } from "fastify";
// Mock数据
const mockIPData = {
  // 人设定位
  ip_name: "小李老师",
  gender: "女",
  age: 28,
  current_job: "在线教育讲师",
  career_experience:
    "5年教育行业经验，曾在知名培训机构担任高级讲师，擅长K12数学教学",
  life_experience: "985高校数学系毕业，热爱教育事业，曾获得省级优秀教师称号",
  interests_skills: "数学建模、编程、阅读、瑜伽，擅长将复杂数学概念简化讲解",

  // 商业定位
  products_services: "提供K12数学在线课程、一对一辅导服务、数学思维训练营",
  competitive_advantages:
    "独创的数学可视化教学法，学生成绩提升率达90%以上，拥有丰富的教学资源库",
  monetization_method: "直接挂车卖课程，同时引流到微信群进行高价值服务转化",

  // 账号设定
  target_age_groups: ["18-24", "25-30", "31-35"],
  target_gender: "男女不限",
  target_identity: "学生家长、在校大学生、数学学习爱好者",
  target_interests: "关注孩子教育、数学学习困难、提升学习成绩",
  value_proposition:
    "帮助解决数学学习难题，提供高效学习方法，提升数学成绩和思维能力",

  // 内容定位
  content_direction: "数学知识讲解、学习方法分享、教育理念传播、学习技巧演示",
  content_style: "轻松、幽默",
  catchphrase: "数学其实很简单，跟着小李老师一起来探索数学的奥秘吧！",
};

interface ValidationErrorItem {
  message: string;
  params: Record<string, any>;
  instancePath: string;
}

export function formatValidationError(
  error: FastifyError,
  request: FastifyRequest,
  reply: FastifyReply
) {
  if (error.validation) {
    const validationErrors = error.validation as ValidationErrorItem[];
    const messages = validationErrors.map((err) => {
      // 尝试从schema中提取自定义错误信息
      if (err.params.errors && err.params.errors[0]?.message) {
        return err.params.errors[0].message;
      }
      return err.message || "参数验证失败";
    });

    reply.status(400).send({
      code: 400,
      error: "Bad Request",
      message: "参数验证错误",
      details: messages,
      timestamp: new Date().toISOString(),
      path: request.url,
    });
  } else {
    reply.send(error); // 其他类型的错误保持原样
  }
}
