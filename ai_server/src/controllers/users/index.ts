import { countUsers, getUserOne } from "../../models/user";
import { countArticles } from "../../models/article";
import { countWorkflows } from "../../models/workflow";
import { getIPSettingOne } from "../../models/ipSetting";
const user_information = async ({
  open_id,
  process,
}: {
  open_id: string;
  process: number;
}) => {
  const userDetail = await getUserOne({ open_id });
  const articleCount = await countArticles({ open_id });
  const videoCount = await countWorkflows({ open_id, process });
  const defaultIpSetting = await getIPSettingOne({ open_id, default: true });
  return {
    userDetail,
    articleCount,
    videoCount,
    defaultIpSetting,
  };
};
export { user_information };
