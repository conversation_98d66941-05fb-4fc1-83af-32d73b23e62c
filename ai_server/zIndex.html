<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>User Information Form</title>
    <style>
      body {
        font-family: Arial, sans-serif;
        max-width: 600px;
        margin: 0 auto;
        padding: 20px;
      }
      .form-group {
        margin-bottom: 15px;
      }
      textarea {
        width: 100%;
        height: 150px;
        padding: 8px;
        box-sizing: border-box;
        border: 1px solid #ccc;
        border-radius: 4px;
        resize: vertical;
      }
      button {
        background-color: #4caf50;
        color: white;
        padding: 10px 15px;
        border: none;
        border-radius: 4px;
        cursor: pointer;
      }
      button:hover {
        background-color: #45a049;
      }
      #response {
        margin-top: 20px;
        padding: 10px;
        border: 1px solid #ddd;
        border-radius: 4px;
        display: none;
      }
    </style>
  </head>
  <body>
    <h1>User Information Form</h1>
    <div class="form-group">
      <label for="userInfo">Enter your information:</label>
      <textarea
        id="userInfo"
        placeholder="Type your information here..."
      ></textarea>
    </div>
    <button id="submitBtn">Submit</button>
    <div id="response"></div>

    <hr style="margin: 30px 0" />

    <h2>File Upload Section</h2>

    <!-- General OSS Upload -->
    <div
      style="
        margin-bottom: 30px;
        padding: 15px;
        border: 1px solid #ddd;
        border-radius: 4px;
      "
    >
      <h3>General File Upload</h3>
      <div class="form-group">
        <label for="ossFileUpload">Select File:</label>
        <input type="file" id="ossFileUpload" />
      </div>
      <button id="ossUploadBtn">Upload to OSS</button>
      <div id="ossResponse" style="margin-top: 10px"></div>
    </div>

    <!-- Audio Model Upload -->
    <div
      style="
        margin-bottom: 30px;
        padding: 15px;
        border: 1px solid #ddd;
        border-radius: 4px;
      "
    >
      <h3>Audio Model Upload</h3>
      <div class="form-group">
        <label for="audioOpenId">Open ID:</label>
        <input
          type="text"
          id="audioOpenId"
          value="user123"
          style="
            width: 100%;
            padding: 8px;
            box-sizing: border-box;
            border: 1px solid #ccc;
            border-radius: 4px;
          "
        />
      </div>
      <div class="form-group">
        <label for="audioTitle">Title:</label>
        <input
          type="text"
          id="audioTitle"
          placeholder="Enter title"
          style="
            width: 100%;
            padding: 8px;
            box-sizing: border-box;
            border: 1px solid #ccc;
            border-radius: 4px;
          "
        />
      </div>
      <div class="form-group">
        <label for="audioDescription">Description:</label>
        <textarea
          id="audioDescription"
          placeholder="Enter description"
          style="height: 80px"
        ></textarea>
      </div>
      <div class="form-group">
        <label for="audioFileUpload">Select Audio File:</label>
        <input type="file" id="audioFileUpload" accept="audio/*" />
      </div>
      <button id="audioUploadBtn">Upload Audio Model</button>
      <div id="audioResponse" style="margin-top: 10px"></div>
    </div>

    <!-- Video Model Upload -->
    <div
      style="
        margin-bottom: 30px;
        padding: 15px;
        border: 1px solid #ddd;
        border-radius: 4px;
      "
    >
      <h3>Video Model Upload</h3>
      <div class="form-group">
        <label for="videoOpenId">Open ID:</label>
        <input
          type="text"
          id="videoOpenId"
          value="user123"
          style="
            width: 100%;
            padding: 8px;
            box-sizing: border-box;
            border: 1px solid #ccc;
            border-radius: 4px;
          "
        />
      </div>
      <div class="form-group">
        <label for="videoTitle">Title:</label>
        <input
          type="text"
          id="videoTitle"
          placeholder="Enter title"
          style="
            width: 100%;
            padding: 8px;
            box-sizing: border-box;
            border: 1px solid #ccc;
            border-radius: 4px;
          "
        />
      </div>
      <div class="form-group">
        <label for="videoDescription">Description:</label>
        <textarea
          id="videoDescription"
          placeholder="Enter description"
          style="height: 80px"
        ></textarea>
      </div>
      <div class="form-group">
        <label for="videoFileUpload">Select Video File:</label>
        <input type="file" id="videoFileUpload" accept="video/*" />
      </div>
      <button id="videoUploadBtn">Upload Video Model</button>
      <div id="videoResponse" style="margin-top: 10px"></div>
    </div>

    <hr style="margin: 30px 0" />

    <h2>API Requests</h2>

    <!-- Select Topic API -->
    <div
      style="
        margin-bottom: 30px;
        padding: 15px;
        border: 1px solid #ddd;
        border-radius: 4px;
      "
    >
      <h3>Select Topic API</h3>
      <button id="selectTopicButton">请求接口</button>
    </div>

    <!-- Workflow Detail API -->
    <div
      style="
        margin-bottom: 30px;
        padding: 15px;
        border: 1px solid #ddd;
        border-radius: 4px;
      "
    >
      <h3>Workflow Detail API</h3>
      <div class="form-group">
        <label for="workflowId">Workflow ID:</label>
        <input
          type="text"
          id="workflowId"
          placeholder="Enter workflow ID"
          style="
            width: 100%;
            padding: 8px;
            box-sizing: border-box;
            border: 1px solid #ccc;
            border-radius: 4px;
          "
        />
      </div>
      <button id="workflowDetailBtn">Get Workflow Detail</button>
      <div
        id="workflowResponse"
        style="
          margin-top: 10px;
          padding: 10px;
          border: 1px solid #ddd;
          border-radius: 4px;
          white-space: pre-wrap;
        "
      ></div>
    </div>
    <script>
      var path = "http://localhost";
    </script>
    <script>
      // OSS Upload
      document
        .getElementById("ossUploadBtn")
        .addEventListener("click", function () {
          const fileInput = document.getElementById("ossFileUpload");
          const responseDiv = document.getElementById("ossResponse");

          if (!fileInput.files || fileInput.files.length === 0) {
            alert("Please select a file to upload");
            return;
          }

          const file = fileInput.files[0];
          const formData = new FormData();
          formData.append("file", file);

          // Show loading state
          this.disabled = true;
          this.textContent = "Uploading...";
          responseDiv.textContent = "Uploading file...";

          fetch(path + "/oss/upload", {
            method: "POST",
            body: formData,
          })
            .then((response) => {
              if (!response.ok) {
                throw new Error("Network response was not ok");
              }
              return response.json();
            })
            .then((data) => {
              responseDiv.textContent =
                "Upload successful: " + JSON.stringify(data);
              console.log("File uploaded successfully:", data);
            })
            .catch((error) => {
              responseDiv.textContent = "Error: " + error.message;
              console.error("Error uploading file:", error);
            })
            .finally(() => {
              // Reset button state
              this.disabled = false;
              this.textContent = "Upload to OSS";
            });
        });

      // Audio Model Upload
      document
        .getElementById("audioUploadBtn")
        .addEventListener("click", function () {
          const openId = document.getElementById("audioOpenId").value;
          const title = document.getElementById("audioTitle").value;
          const description = document.getElementById("audioDescription").value;
          const fileInput = document.getElementById("audioFileUpload");
          const responseDiv = document.getElementById("audioResponse");

          if (!openId.trim() || !title.trim()) {
            alert("Please fill in Open ID and Title fields");
            return;
          }

          if (!fileInput.files || fileInput.files.length === 0) {
            alert("Please select an audio file to upload");
            return;
          }

          const file = fileInput.files[0];
          const formData = new FormData();
          formData.append("file", file);
          formData.append("open_id", openId);
          formData.append("title", title);
          formData.append("description", description);

          // Show loading state
          this.disabled = true;
          this.textContent = "Uploading...";
          responseDiv.textContent = "Uploading audio model...";

          fetch("http://localhost/ai/createAudioModel", {
            method: "POST",
            body: formData,
          })
            .then((response) => {
              if (!response.ok) {
                throw new Error("Network response was not ok");
              }
              return response.json();
            })
            .then((data) => {
              responseDiv.textContent =
                "Upload successful: " + JSON.stringify(data);
              console.log("Audio model uploaded successfully:", data);
            })
            .catch((error) => {
              responseDiv.textContent = "Error: " + error.message;
              console.error("Error uploading audio model:", error);
            })
            .finally(() => {
              // Reset button state
              this.disabled = false;
              this.textContent = "Upload Audio Model";
            });
        });

      // Video Model Upload
      document
        .getElementById("videoUploadBtn")
        .addEventListener("click", function () {
          const openId = document.getElementById("videoOpenId").value;
          const title = document.getElementById("videoTitle").value;
          const description = document.getElementById("videoDescription").value;
          const fileInput = document.getElementById("videoFileUpload");
          const responseDiv = document.getElementById("videoResponse");

          if (!openId.trim() || !title.trim()) {
            alert("Please fill in Open ID and Title fields");
            return;
          }

          if (!fileInput.files || fileInput.files.length === 0) {
            alert("Please select a video file to upload");
            return;
          }

          const file = fileInput.files[0];
          const formData = new FormData();
          formData.append("file", file);
          formData.append("open_id", openId);
          formData.append("title", title);
          formData.append("description", description);

          // Show loading state
          this.disabled = true;
          this.textContent = "Uploading...";
          responseDiv.textContent = "Uploading video model...";

          fetch("http://localhost/ai/createVideoModel", {
            method: "POST",
            body: formData,
          })
            .then((response) => {
              if (!response.ok) {
                throw new Error("Network response was not ok");
              }
              return response.json();
            })
            .then((data) => {
              responseDiv.textContent =
                "Upload successful: " + JSON.stringify(data);
              console.log("Video model uploaded successfully:", data);
            })
            .catch((error) => {
              responseDiv.textContent = "Error: " + error.message;
              console.error("Error uploading video model:", error);
            })
            .finally(() => {
              // Reset button state
              this.disabled = false;
              this.textContent = "Upload Video Model";
            });
        });
    </script>
    <script>
      document
        .getElementById("selectTopicButton")
        .addEventListener("click", function () {
          // 这里需要替换为实际的值
          const prompt = "your_prompt";
          const openId = "your_openId";
          const number = 1;

          const data = {
            prompt: prompt,
            openId: openId,
            number: number,
          };

          fetch("http://localhost:2000/ai/selectTopic", {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify(data),
          })
            .then((response) => {
              if (!response.ok) {
                throw new Error("网络响应失败");
              }
              const reader = response.body.getReader();
              let result = "";

              return new ReadableStream({
                start(controller) {
                  function push() {
                    reader.read().then(({ done, value }) => {
                      if (done) {
                        controller.close();
                        return;
                      }
                      result += new TextDecoder().decode(value);
                      controller.enqueue(value);
                      push();
                    });
                  }
                  push();
                },
              });
            })
            .then((stream) => {
              return new Response(stream, {
                headers: { "Content-Type": "text/html" },
              }).text();
            })
            .then((result) => {
              console.log("成功:", result);
            })
            .catch((error) => {
              console.error("错误:", error);
            });
        });
    </script>
    <div id="response"></div>

    <script>
      document
        .getElementById("submitBtn")
        .addEventListener("click", function () {
          const userInfo = document.getElementById("userInfo").value;
          const responseDiv = document.getElementById("response");

          if (!userInfo.trim()) {
            alert("Please enter some information");
            return;
          }

          // Show loading state
          this.disabled = true;
          this.textContent = "Submitting...";
          responseDiv.style.display = "block";
          responseDiv.textContent = "Processing your request...";

          fetch("http://localhost:2000/ai/user/information", {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({ information: userInfo }),
          })
            .then((response) => {
              if (!response.ok) {
                throw new Error("Network response was not ok");
              }
              return response.json();
            })
            .then((data) => {
              responseDiv.textContent = "Response: " + JSON.stringify(data);
            })
            .catch((error) => {
              responseDiv.textContent = "Error: " + error.message;
            })
            .finally(() => {
              // Reset button state
              this.disabled = false;
              this.textContent = "Submit";
            });
        });
    </script>

    <!-- Workflow Detail API Script -->
    <script>
      document
        .getElementById("workflowDetailBtn")
        .addEventListener("click", function () {
          const workflowId = document.getElementById("workflowId").value;
          const responseDiv = document.getElementById("workflowResponse");

          if (!workflowId.trim()) {
            alert("Please enter a workflow ID");
            return;
          }

          // Show loading state
          this.disabled = true;
          this.textContent = "Loading...";
          responseDiv.textContent = "Fetching workflow details...";

          fetch("http://localhost:2000/ai/workflow/detail", {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({ id: workflowId }),
          })
            .then((response) => {
              if (!response.ok) {
                throw new Error("Network response was not ok");
              }
              return response.json();
            })
            .then((data) => {
              // Format the JSON response for better readability
              responseDiv.textContent = JSON.stringify(data, null, 2);
              console.log("Workflow detail:", data);
            })
            .catch((error) => {
              responseDiv.textContent = "Error: " + error.message;
              console.error("Error fetching workflow detail:", error);
            })
            .finally(() => {
              // Reset button state
              this.disabled = false;
              this.textContent = "Get Workflow Detail";
            });
        });
    </script>

    <!-- Workflow List API -->
    <div
      style="
        margin-bottom: 30px;
        padding: 15px;
        border: 1px solid #ddd;
        border-radius: 4px;
      "
    >
      <h3>Workflow List API</h3>
      <div class="form-group">
        <label for="workflowOpenId">Open ID:</label>
        <input
          type="text"
          id="workflowOpenId"
          placeholder="Enter Open ID"
          style="
            width: 100%;
            padding: 8px;
            box-sizing: border-box;
            border: 1px solid #ccc;
            border-radius: 4px;
          "
        />
      </div>
      <div class="form-group">
        <label for="workflowType">Type:</label>
        <input
          type="text"
          id="workflowType"
          placeholder="Enter Type"
          style="
            width: 100%;
            padding: 8px;
            box-sizing: border-box;
            border: 1px solid #ccc;
            border-radius: 4px;
          "
        />
      </div>
      <button id="workflowListBtn">Get Workflow List</button>
      <div
        id="workflowListResponse"
        style="
          margin-top: 10px;
          padding: 10px;
          border: 1px solid #ddd;
          border-radius: 4px;
          white-space: pre-wrap;
        "
      ></div>
    </div>

    <script>
      // Workflow List API Script
      document
        .getElementById("workflowListBtn")
        .addEventListener("click", function () {
          const openId = document.getElementById("workflowOpenId").value;
          const type = document.getElementById("workflowType").value;
          const responseDiv = document.getElementById("workflowListResponse");

          if (!openId.trim() || !type.trim()) {
            alert("请输入 Open ID 和 Type");
            return;
          }

          // Show loading state
          this.disabled = true;
          this.textContent = "加载中...";
          responseDiv.textContent = "正在获取工作流列表...";

          fetch("http://localhost/ai/workflow/list", {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({ open_id: openId, type: type }),
          })
            .then((response) => {
              if (!response.ok) {
                throw new Error("网络响应失败");
              }
              return response.json();
            })
            .then((data) => {
              // 格式化 JSON 响应以便更好阅读
              responseDiv.textContent = JSON.stringify(data, null, 2);
              console.log("工作流列表:", data);
            })
            .catch((error) => {
              responseDiv.textContent = "错误: " + error.message;
              console.error("获取工作流列表时出错:", error);
            })
            .finally(() => {
              // 重置按钮状态
              this.disabled = false;
              this.textContent = "Get Workflow List";
            });
        });
    </script>

    <!-- Workflow Create API -->
    <div
      style="
        margin-bottom: 30px;
        padding: 15px;
        border: 1px solid #ddd;
        border-radius: 4px;
      "
    >
      <h3>Create Workflow</h3>
      <div class="form-group">
        <label for="createWorkflowOpenId">Open ID:</label>
        <input
          type="text"
          id="createWorkflowOpenId"
          placeholder="Enter Open ID"
          style="
            width: 100%;
            padding: 8px;
            box-sizing: border-box;
            border: 1px solid #ccc;
            border-radius: 4px;
          "
        />
      </div>
      <div class="form-group">
        <label for="createWorkflowType">Type:</label>
        <input
          type="text"
          id="createWorkflowType"
          placeholder="Enter Type"
          style="
            width: 100%;
            padding: 8px;
            box-sizing: border-box;
            border: 1px solid #ccc;
            border-radius: 4px;
          "
        />
      </div>
      <button id="createWorkflowBtn">Create Workflow</button>
      <div
        id="createWorkflowResponse"
        style="
          margin-top: 10px;
          padding: 10px;
          border: 1px solid #ddd;
          border-radius: 4px;
          white-space: pre-wrap;
        "
      ></div>
    </div>

    <script>
      // Create Workflow API Script
      document
        .getElementById("createWorkflowBtn")
        .addEventListener("click", function () {
          const openId = document.getElementById("createWorkflowOpenId").value;
          const type = document.getElementById("createWorkflowType").value;
          const responseDiv = document.getElementById("createWorkflowResponse");

          if (!openId.trim() || !type.trim()) {
            alert("请输入 Open ID 和 Type");
            return;
          }

          // 显示加载状态
          this.disabled = true;
          this.textContent = "创建中...";
          responseDiv.textContent = "正在创建工作流...";

          fetch(path + "/ai/workflow/create", {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({ open_id: openId, type: type }),
          })
            .then((response) => {
              if (!response.ok) {
                throw new Error("网络响应失败");
              }
              return response.json();
            })
            .then((data) => {
              // 格式化 JSON 响应以便更好阅读
              responseDiv.textContent = JSON.stringify(data, null, 2);
              console.log("工作流创建结果:", data);
            })
            .catch((error) => {
              responseDiv.textContent = "错误: " + error.message;
              console.error("创建工作流时出错:", error);
            })
            .finally(() => {
              // 重置按钮状态
              this.disabled = false;
              this.textContent = "Create Workflow";
            });
        });
    </script>
  </body>
</html>
