const nextConfig = {
  async rewrites() {
    return [
      {
        source: '/home',
        destination: '/app/home', // 将根路径重写到/app
      }
    ]
  },
  /* config options here */
  // turbo: true,
  // basePath: "/app",
  reactStrictMode: false,
  typescript: {
    // 忽略 TypeScript 构建错误
    ignoreBuildErrors: true,
  },
  eslint: {
    // 忽略 ESLint 错误
    ignoreDuringBuilds: true,
  },
};

export default nextConfig;
