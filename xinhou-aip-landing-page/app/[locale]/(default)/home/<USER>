"use client";
// import PriceSection from "@/components/PriceSection";
// import ContactUsSection from "@/components/ContactUsSection";
// import FAQSection from "@/components/FAQSection";
// import CaseSection from "@/components/CaseSection";
// import FeatureSection from "@/components/FeatureSection";
// import CoreSection from "@/components/CoreSection";
// import HeroSection from "@/components/HeroSection";
// import TitleSection from "@/components/TitleSection";
// import FreeAnalyzeSection from "@/components/FreeAnalyzeSection";
// import GroupBlock from "@/components/Group";
import Banner from "@/components/Banner";
import Modal from "@/components/Modal";
import SelectTitle from "@/components/SelectTitle";
import AISwiper from "@/components/AISwiper";
import CustomerReviews from "@/components/CustomerReviews";
import Reservation from "@/components/Reservation";
import IPSelect from "@/components/IP";
import Framework from "@/components/Framework";
import Partnership from "@/components/Partnership";

// import { useModalStore } from "@/store/modal";
import { useEffect, useState } from "react";
import Footer from "@/components/Footer";

export default function Page() {
  // const [language, setLanguage] = useState("en");
  // useEffect(() => {
  //   const url = location.pathname.split("/");
  //   if (url.includes("en")) {
  //     setLanguage("en");
  //   } else {
  //     setLanguage("zh");
  //   }
  // }, []);
  return (
    <main
      className="flex flex-col items-center overflow-hidden"
      style={{ background: "#f4f5f6" }}
    >
      <Banner />
      <Framework />
      <Modal />
      <SelectTitle />
      <AISwiper />
      <Partnership />
      <CustomerReviews />
      <Reservation />
      <IPSelect />
      <Footer />
      {/* <TitleSection />
      <FreeAnalyzeSection />
      <HeroSection />
      <GroupBlock />
      <CoreSection />
      <FeatureSection />

      <FAQSection />
      <CaseSection />
      <ContactUsSection /> */}
    </main>
  );
}
