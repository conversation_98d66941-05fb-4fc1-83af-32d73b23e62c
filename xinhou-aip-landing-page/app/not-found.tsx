import Link from 'next/link'
 
export default function NotFound() {
  return (
    // <div>
    //   <h2>Not Found</h2>
    //   <p>Could not find requested resource</p>
    //   <Link href="/">Return Home</Link>
    // </div>

    (
        <div className="min-h-screen bg-gray-100 flex items-center justify-center px-4">
          <div className="max-w-xl text-center">
            <h1 className="text-[120px] font-bold text-gray-300 leading-none">404</h1>
            <h2 className="text-3xl font-semibold text-gray-800 mt-2">页面未找到</h2>
            <p className="text-gray-500 mt-4">你访问的页面不存在或已被移动。</p>
            
            {/* 如果你使用 React Router */}
            <Link
              href="/"
              className="inline-block mt-6 px-6 py-3 bg-blue-600 text-white font-medium rounded-full hover:bg-blue-700 transition"
            >
              返回首页
            </Link>

          </div>
        </div>
      )
  )
}