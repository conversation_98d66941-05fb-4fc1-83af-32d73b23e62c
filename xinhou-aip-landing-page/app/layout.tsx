import "@/app/globals.css";
import { Metadata, Viewport } from "next";
import Favicon from "./favicon.ico";

export const viewport: Viewport = {
  width: "device-width",
  initialScale: 1,
  maximumScale: 1,
  minimumScale: 1,
  userScalable: false,
};

export async function generateMetadata({
  params: { locale },
}: {
  params: { locale: string };
}): Promise<Metadata> {
  return {
    title: "用AI一站式创作IP短视频",
    description: "从策划、文案到音视频生成，体验全新的短视频创作方式",
    icons: [{ rel: "icon", url: Favicon.src }],
    referrer: "no-referrer",
  };
}
export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang={"zh"}>
      <body>
        <section>{children}</section>
      </body>
    </html>
  );
}
