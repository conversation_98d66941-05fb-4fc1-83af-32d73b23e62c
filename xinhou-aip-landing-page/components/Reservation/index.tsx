import { Button, Form, Input, Space, message, Spin } from "antd";
import styles from "./style.module.css";
import { BASE_URL } from "@/constants/config";
import { useState } from "react";
import { useCountDown } from "ahooks";
import { LoadingOutlined } from "@ant-design/icons";
const Reservation = () => {
  const [messageApi, contextHolder] = message.useMessage();
  const [loading, setLoading] = useState(false);
  const [form] = Form.useForm();
  const [targetDate, setTargetDate] = useState<number>();
  const [countdown] = useCountDown({
    targetDate,
    onEnd: () => {
      setTargetDate(undefined);
    },
  });

  // 提交数据
  const reservationEve = () => {
    form.validateFields().then(async (values) => {
      try {
        setLoading(true);
        fetch(`${BASE_URL}/admin/precontract/create`, {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify(values),
        })
          .then((res) => res.json())
          .then((data) => {
            if (data?.code === 200) {
              messageApi.info("预约成功，请耐心等待客服联系");
              form.resetFields();
            } else {
              messageApi.error(data?.data?.message);
            }
          })
          .finally(() => {
            setLoading(false);
          });
      } catch (error) {
        setLoading(false);
        messageApi.error(`预约失败${error}`);
        console.error("预约失败", error);
      }
    });
  };

  // 获取验证码
  const getCode = () => {
    form.validateFields(["mobile"]).then(async () => {
      const mobileValue = form.getFieldValue("mobile");
      try {
        const res = await fetch(
          `${BASE_URL}/admin/user/send-verify-code?mobile=${mobileValue}`,
          {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({}),
          }
        );
        const data = await res.json();
        if (data?.code === 200) {
          setTargetDate(Date.now() + 60 * 1000);
        } else {
          messageApi.error(data?.data?.message);
        }
      } catch (error) {
        console.error("发送验证码失败", error);
      }
    });
  };
  return (
    <section className="w-full md:h-[870px] h-[1213px] pt-[113px] mt-[162px] flex flex-col items-center bg-[linear-gradient(69deg,_#1b73ff_0%,_#3684ff_102.22%)] min-h-96" id="reservation">
      {contextHolder}
      <div className={`${styles.reservation_title} text-[28px] md:text-5xl`}>预约咨询</div>
      <div className="md:flex gap-[170px] md:w-[980px] w-full md:p-0 p-[13px] mt-[56px]">
        <Form className="md:w-[495px] w-full" layout="vertical" form={form}>
          <Form.Item
            style={{ marginBottom: "36" }}
            label={<div className="text-white font-[PingFang SC] md:text-[20px] text-[16px] font-medium leading-normal pb-[8px]">手机号</div>}
            required
          >
            <Form.Item
              style={{ marginBottom: "16px" }}
              name="mobile"
              rules={[
                { required: true, message: "请输入手机号" },
                {
                  pattern: /^1[3-9]\d{9}$/,
                  message: "请输入有效的手机号",
                },
              ]}
            >
              <Input className="md:h-[56px] rounded-xl h-[48px]" placeholder="请输入手机号" />
            </Form.Item>
            {/* <Form.Item
              name="verify_code"
              rules={[{ required: true, message: "请输入验证码" }]}
            >
              <Input
                className="md:h-[56px] rounded-xl h-[48px]" 
                placeholder="请输入"
                suffix={
                  <span className="text-[#3870ff] text-right font-pingfang-sc md:text-base text-[14px] font-normal cursor-pointer"
 onClick={getCode}>
                    {countdown > 0
                      ? `${Math.round(countdown / 1000)}s`
                      : "获取验证码"}
                  </span>
                }
              />
            </Form.Item> */}
          </Form.Item>
          <div className="text-white font-[PingFang SC] md:text-[20px] text-[16px] font-medium leading-normal mb-[16px]">
            提供更多信息，我们为您提前规划方案
          </div>
          <Form.Item style={{ marginBottom: "36px" }}>
            <Form.Item
              style={{ marginBottom: "16px" }}
              name="business_name"
              // rules={[
              //   {
              //     required: true,
              //     message: "请输入公司名称",
              //   },
              // ]}
            >
              <Input className="md:h-[56px] rounded-xl h-[48px]"  placeholder="请输入公司名称" />
            </Form.Item>
            <div className={styles.space}>
              <Form.Item
                style={{ width: "50%" }}
                name="name"
                // rules={[
                //   {
                //     required: true,
                //     message: "请输入姓名",
                //   },
                // ]}
              >
                <Input
                  className="md:h-[56px] rounded-xl h-[48px]" 
                  placeholder="请输入联系人姓名"
                />
              </Form.Item>
              <Form.Item
                style={{ width: "50%" }}
                name="city"
                // rules={[
                //   {
                //     required: true,
                //     message: "请输入城市",
                //   },
                // ]}
              >
                <Input className="md:h-[56px] rounded-xl h-[48px]"  placeholder="请输入城市" />
              </Form.Item>
            </div>
          </Form.Item>
          <Form.Item>
            <div
              className="md:w-[495px] w-full md:h-[60px] h-[48px] flex-shrink-0 rounded-[6px] bg-[#00174d] text-white text-center font-[PingFang SC] text-[18px] font-medium md:leading-[60px] leading-[50px] border-none hover:bg-[#0a2e8a]"
              onClick={() => {
                if (loading) {
                } else {
                  reservationEve();
                }
              }}
            >
              {loading ? (
                <>
                  <Spin
                    indicator={
                      <LoadingOutlined
                        spin
                        style={{ color: "#fff", marginRight: "10px" }}
                      />
                    }
                  />
                  提交中...
                </>
              ) : (
                "提交"
              )}
            </div>
          </Form.Item>
        </Form>
        <div className="md:w-[316px] w-full rounded-[8px] pt-[62px]">
          <div className="flex md:w-[300px] md:ml-0 ml-[60px] w-[220px] md:h-[300px] h-[220px] justify-center items-center aspect-[1/1] rounded-[8px] border-[10px] border-white/40 shadow-[0px_20px_40px_0px_rgba(0,0,0,0.05)]">
            <div className="md:w-full w-[220px] md:h-full bg-white rounded-none">
              <img src="/home/<USER>" alt="code" />
            </div>
          </div>
          <div className="text-white text-center font-[PingFang SC] md:text-[24px] text-[16px] font-normal leading-[40px] mt-[24px]">添加客服微信</div>
        </div>
      </div>
    </section>
  );
};
export default Reservation;
