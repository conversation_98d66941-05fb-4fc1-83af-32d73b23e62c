.banner_main {
  width: 100%;
}
.banner_content {
  position: relative;
  border-radius: 0px 0px 40px 40px;
  height: 800px;
  width: 100%;
  background-image: linear-gradient(45deg, #00174d 0%, #08215b 100%);
}
.circle {
  position: absolute;
  z-index: 1;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  pointer-events: none;
}
.baner_number {
  position: relative;
  z-index: 2;
  display: flex;
  align-items: center;
  height: 200px;
  width: calc(100% - 240px);
  border-radius: 8px;
  background: linear-gradient(246deg, #3986fe 0%, #0060f8 100%);
  margin: -100px auto 0;
}
.number_block {
  display: flex;
  flex-direction: column;
  width: 25%;
  align-items: center;
  justify-content: baseline;
}
.number_text {
  display: flex;
  align-items: baseline;
}
.number_text_num {
  color: #fff;
  font-family: "PingFang SC";
  font-size: 50px;
  font-style: normal;
  font-weight: 600;
  line-height: 48px; /* 82.759% */
}
.number_currency {
  color: #fff;
  font-family: "PingFang SC";
  font-size: 18px;
  font-style: normal;
  font-weight: 500;
  line-height: 22px; /* 122.222% */
}
.number_text_title {
  color: rgba(255, 255, 255, 0.65);
  font-family: "PingFang SC";
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: 18px; /* 100% */
  margin-top: 24px;
}
/**===========================================*/
.nav {
  visibility: hidden;
  display: flex;
  min-height: 50px;
  justify-content: space-between;
  align-items: center;
  width: calc(100% - 240px);
  margin: 0 auto;
  padding-top: 40px;
}
.nav_menu {
  display: flex;
  align-items: center;
  gap: 50px;
}
.nav_menu_item {
  color: #fff;
  font-family: "PingFang SC";
  font-size: 18px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  cursor: pointer;
}
.nav_menu_item.active {
  color: #3870ff;
}
.nav_btn {
  border-radius: 40px;
  border: 2px solid #fff;
  width: 130px;
  height: 50px;
  flex-shrink: 0;
  color: #fff;
  text-align: center;
  font-family: "PingFang SC";
  font-size: 18px;
  font-style: normal;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}
.title_main {
  text-align: center;
  padding-top: 119px;
}
.banner_content_title {
  color: #fff;
  text-align: center;
  font-family: "PingFang SC";
  font-size: 72px;
  font-style: normal;
  font-weight: 300;
  line-height: normal;
}
.banner_content_color {
  word-spacing: 2px;
}
.banner_content_color span:first-child {
  text-align: center;
  font-family: "PingFang SC";
  font-size: 72px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
  background: linear-gradient(
    265deg,
    #43a7ff 47.95%,
    #7ce9fe 79.94%,
    #fff 97.12%
  );
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
.banner_content_color span:nth-child(2) {
  text-align: center;
  font-family: "PingFang SC";
  font-size: 80px;
  font-style: normal;
  font-weight: 600;
  line-height: 80px; /* 100% */
  background: linear-gradient(
    265deg,
    #ff8077 10.2%,
    #bd6bfb 15.38%,
    #43a7ff 47.95%,
    #7ce9fe 79.94%,
    #fff 97.12%
  );
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
.banner_content_color span:last-child {
  text-align: center;
  font-family: "PingFang SC";
  font-size: 72px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
  background: linear-gradient(
    265deg,
    #ff8077 10.2%,
    #bd6bfb 15.38%,
    #7ce9fe 79.94%
  );
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
.banner_title_subtitle {
  color: #fff;
  text-align: center;
  font-family: "PingFang SC";
  font-size: 24px;
  font-style: normal;
  font-weight: 300;
  line-height: 40px; /* 166.667% */
}
.banner_title_btn {
  position: relative;
  flex-shrink: 0;
  border-radius: 40px;
  width: 200px;
  color: #fff;
  height: 72.138px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 52px auto 0;
  cursor: pointer;
  border: 2px solid #fff;
}
.banner_title_btn_item {
  position: absolute;
  width: 100%;
  height: 100%;
  z-index: 100%;
  border-radius: 40px;
  font-family: "PingFang SC";
  font-size: 18px;
  font-style: normal;
  font-weight: 500;
  line-height: 72.138px;
}



@media only screen and (max-width: 767px) {
  /* 针对宽度小于或等于 767px 的设备 */
  /* .banner_main {
    width: 100%;
  } */
  /* .banner_content {
    position: relative;
    border-radius: 0px 0px 20px 20px;
    height: 500px;
    width: 100%;
    background-image: linear-gradient(45deg, #00174d 0%, #08215b 100%);
  } */
}
