import { Statistic, StatisticProps } from "antd";
import styles from "./style.module.css";
import CountUp from "react-countup";
import { useState } from "react";
import { cn } from "@/lib/utils";
import { IoMenu, IoClose } from "react-icons/io5";

const Banner = () => {
  const [link, setLink] = useState("#home");
  const [isOpen, setIsopen ] = useState(false)
  const formatter: StatisticProps["formatter"] = (value) => (
    <CountUp end={value as number} separator="," className="md:text-[58px] text-[32px]"/>
  );
  const linkEvent = (id: string) => {
    setLink(id);
    location.href = `${location.origin}/home${id}`;
    if(isOpen){
      setIsopen(!isOpen)
    }
  };

  return (
    <section className={styles.banner_main} id="home">
      <div className="relative rounded-b-[40px] md:h-[800px] h-[640px] w-full bg-[linear-gradient(45deg,_#00174d_0%,_#08215b_100%)]">

          <img
            src="/home/<USER>"
            alt="circle"
            className="hidden md:block absolute z-[1] w-full h-full top-0 left-0 pointer-events-none"
          />
          <img
            src="/home/<USER>"
            alt="circle"
            className="block md:hidden absolute z-[1] w-full h-full top-10 left-0 pointer-events-none"
          />
        <div className="flex min-h-[50px] justify-between items-center md:w-[calc(100%-240px)] w-full mx-auto md:pt-10  md:px-5 p-5 md:static fixed md:bg-transparent bg-[linear-gradient(45deg,_#00174d_0%,_#08215b_100%)] duration-300 z-100" >
          <div >
            <img src="/home/<USER>" alt="logo" />
          </div>
          <div className="hidden md:flex items-center gap-[50px]">
            <div
              className={`${styles.nav_menu_item} ${
                link === "#home" ? styles.active : ""
              }`}
              onClick={() => {
                linkEvent("#home");
              }}
            >
              首页
            </div>
            <div
              className={`${styles.nav_menu_item} ${
                link === "#product" ? styles.active : ""
              }`}
              onClick={() => {
                linkEvent("#product");
              }}
            >
              产品介绍
            </div>
            <div
              className={`${styles.nav_menu_item} ${
                link === "#service" ? styles.active : ""
              }`}
              onClick={() => {
                linkEvent("#service");
              }}
            >
              服务案例
            </div>
            <div
              className={`${styles.nav_menu_item} ${
                link === "#reservation" ? styles.active : ""
              }`}
              onClick={() => {
                linkEvent("#reservation");
              }}
            >
              预约咨询
            </div>
          </div>


          <div className={cn("md:hidden w-full h-screen fixed left-0  duration-300  z-10", !isOpen ? "top-[-200%]": "top-17")} style={{transform: "none"}}>
          
            <div className="relative w-full flex flex-col gap-[50px] items-center justify-center h-screen bg-[#08215b]">
            <img src="/home/<USER>" alt="circle" className={styles.circle} />
            {/* <IoClose className="text-3xl text-white cursor-pointer absolute top-10 right-6"  onClick={() => setIsopen(!isOpen)}/> */}
            <div
              className={`${styles.nav_menu_item} ${
                link === "#home" ? styles.active : ""
              }`}
              onClick={() => {
                linkEvent("#home");
              }}
            >
              首页
            </div>
            <div
              className={`${styles.nav_menu_item} ${
                link === "#product" ? styles.active : ""
              }`}
              onClick={() => {
                linkEvent("#product");
              }}
            >
              产品介绍
            </div>
            <div
              className={`${styles.nav_menu_item} ${
                link === "#service" ? styles.active : ""
              }`}
              onClick={() => {
                linkEvent("#service");
              }}
            >
              服务案例
            </div>
            <div
              className={`${styles.nav_menu_item} ${
                link === "#reservation" ? styles.active : ""
              }`}
              onClick={() => {
                linkEvent("#reservation");
              }}
            >
              预约咨询
            </div>
            </div>
          </div>



          <div
            className="hidden rounded-[40px] border-2 border-white w-[130px] h-[50px] shrink-0 text-white text-center font-medium text-[18px] md:flex items-center justify-center cursor-pointer font-[PingFang\ SC]"
            onClick={() => {
              window.open("https://fengshenai.cn/app/home");
            }}
          >
            登录
          </div>
          <IoMenu className={cn("md:hidden text-3xl text-white cursor-pointer ml-auto",isOpen ? "hidden": "")} onClick={() => setIsopen(!isOpen)}/>
          <IoClose className={cn("md:hidden  text-3xl text-white cursor-pointer absolute top-6 right-6 duration-300",!isOpen ? "hidden": "")}  onClick={() => setIsopen(!isOpen)}/>
        </div>

        <div className="text-center md:pt-[119px] pt-[160px]">
          <div className="text-white text-center font-[PingFang\ SC] md:text-[72px] text-3xl font-light leading-none">封神AI智能体</div>
          <div className="text-center font-[PingFang\ SC] space-x-2">
            <span className=" text-3xl md:text-[72px] font-semibold bg-[linear-gradient(265deg,_#43a7ff_47.95%,_#7ce9fe_79.94%,_#fff_97.12%)] bg-clip-text text-transparent">赋能</span>
            <span className=" text-3xl md:text-[80px] font-semibold leading-[80px] bg-[linear-gradient(265deg,_#ff8077_10.2%,_#bd6bfb_15.38%,_#43a7ff_47.95%,_#7ce9fe_79.94%,_#fff_97.12%)] bg-clip-text text-transparent">IP</span>
            <span className=" text-3xl md:text-[72px] font-semibold bg-[linear-gradient(265deg,_#ff8077_10.2%,_#bd6bfb_15.38%,_#7ce9fe_79.94%)] bg-clip-text text-transparent">流量增长</span>
          </div>
          <div className="text-white text-center font-[PingFang\ SC] md:text-[24px] text-lg font-light leading-[40px]">
            用好AI，IP打造弯道超车
          </div>
          {/* <div className="relative shrink-0 md:rounded-[40px] rounded-[20px] md:w-[200px] w-[164px] md:h-[72.138px] h-[60px] border-2  border-white text-white flex items-center justify-center mt-[52px] mx-auto cursor-pointer"> */}
          <button
          className="relative inline-block p-[2px] rounded-full bg-[linear-gradient(90deg,_#B07AFB_0%,_#7BEBFE_50%,_#43A7FF_75%,_#9A7BFC_100%)] mt-10"
          onClick={() => {
            linkEvent("#reservation");
          }}
          >
            <span className="block w-full h-full rounded-full bg-[#08215b] text-white px-12 py-4 text-base hover:bg-gray-800 transition-colors duration-300">
              预约咨询
            </span>
          </button>
            {/* <div
              className="absolute inset-0 z-[100] rounded-[40px] font-[PingFang\ SC] text-[18px] font-medium leading-[72.138px] text-center"
              onClick={() => {
                linkEvent("#reservation");
              }}
            >
              预约咨询
            </div> */}
          {/* </div> */}
        </div>
      </div>


      <div className="relative z-[2] flex md:items-center flex-wrap md:h-[200px] h-[240px]  md:w-[calc(100%-240px)] w-[calc(100%-48px)] mx-auto -mt-[120px] rounded-lg bg-gradient-to-r from-[#3986fe] to-[#0060f8]" id="baner_number">
        <div className="flex flex-col md:w-1/4 w-1/2 items-center">
          <div className="flex items-baseline">
            <span className="text-white font-semibold md:text-[50px] md:p-0 p-[13px] leading-[48px]">
              <Statistic value={1167} formatter={formatter} />
            </span>
            <span className="text-white text-[18px] font-medium leading-[22px] md:ml-1 text-sm">万+</span>
          </div>
          <div className="text-white/65 md:text-[16px] text-sm font-normal leading-[18px] md:mt-[24px] mt-[0px]">创意生成总量</div>
        </div>
        <div className="flex flex-col md:w-1/4 w-1/2 items-center justify-start">
          <div className="flex items-baseline">
            <span className="text-white font-semibold md:text-[50px] md:p-0 p-[13px] leading-[48px]">
              <Statistic value={82} formatter={formatter} />
            </span>
            <span className="text-white text-[18px] font-medium leading-[22px] md:ml-1 text-sm">万+</span>
          </div>
          <div className="text-white/65 md:text-[16px] text-sm font-normal leading-[18px] md:mt-[24px] mt-[0px]">视频生成总量</div>
        </div>
        <div className="flex flex-col md:w-1/4 w-1/2 items-center justify-start">
          <div className="flex items-baseline">
            <span className="text-white font-semibold md:text-[50px] md:p-0 p-[13px] leading-[48px]">
              <Statistic value={1502} formatter={formatter} />
            </span>
            <span className="text-white text-[18px] font-medium leading-[22px] md:ml-1 text-sm">万+</span>
          </div>
          <div className="text-white/65 md:text-[16px] text-sm font-normal leading-[18px] md:mt-[24px] mt-[0px]">触达用户总数</div>
        </div>
        <div className="flex flex-col md:w-1/4 w-1/2 items-center justify-start">
          <div className="flex items-baseline">
            <span className="text-white font-semibold md:text-[50px] md:p-0 p-[13px] leading-[48px]">
              <Statistic value={1000} formatter={formatter} />
            </span>
            <span className="text-white text-[18px] font-medium leading-[22px] md:ml-1 text-sm">万+</span>
          </div>
          <div className="text-white/65 md:text-[16px] text-sm font-normal leading-[18px] md:mt-[24px] mt-[0px]">服务企业</div>
        </div>
        </div>
  



      {/* <div className={styles.baner_number} id="baner_number">
        <div className={styles.number_block}>
          <div className={styles.number_text}>
            <span className={styles.number_text_num}>
              <Statistic value={1167} formatter={formatter} />
            </span>
            <span className={styles.number_currency}>万+</span>
          </div>
          <div className={styles.number_text_title}>创意生成总量</div>
        </div>
        <div className={styles.number_block}>
          <div className={styles.number_text}>
            <span className={styles.number_text_num}>
              <Statistic value={82} formatter={formatter} />
            </span>
            <span className={styles.number_currency}>万+</span>
          </div>
          <div className={styles.number_text_title}>视频生成总量</div>
        </div>
        <div className={styles.number_block}>
          <div className={styles.number_text}>
            <span className={styles.number_text_num}>
              <Statistic value={1502} formatter={formatter} />
            </span>
            <span className={styles.number_currency}>万+</span>
          </div>
          <div className={styles.number_text_title}>触达用户总数</div>
        </div>
        <div className={styles.number_block}>
          <div className={styles.number_text}>
            <span className={styles.number_text_num}>
              <Statistic value={1000} formatter={formatter} />
            </span>
            <span className={styles.number_currency}>万+</span>
          </div>
          <div className={styles.number_text_title}>服务企业</div>
        </div>
      </div> */}
    </section>
  );
};
export default Banner;
