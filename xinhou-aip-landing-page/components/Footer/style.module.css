.footer_main {
  position: relative;
  width: 100%;
  height: 444px;
  background: #00174d;
  padding: 80px 120px 32px;
}
.footer_content {
  display: flex;
  width: 100%;
  justify-content: space-between;
  align-items: flex-start;
  border-bottom: 1px solid rgba(255, 255, 255, 0.15);
  padding-bottom: 36px;
}
.footer_logo {
  width: 274px;
  height: 80px;
  background: url(https://aip.bdstatic.com/static/landing-page/img/footer_logo.6f7f4f9.png)
    no-repeat;
  background-size: 100%;
}
.footer_links {
  display: flex;
  gap: 80px;
}
.footer_links_item span:first-child {
  display: block;
  color: var(--Neutral-white-200, #f6f6f7);
  font-family: "PingFang SC";
  font-size: 20px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
  letter-spacing: -0.6px;
}
.footer_links_item span:last-child {
  display: block;
  color: #8f9bb7;
  font-family: "PingFang SC";
  font-size: 16px;
  font-style: normal;
  font-weight: 500;
  line-height: normal;
  margin-top: 16px;
}
.footer_links_icon_item {
  display: flex;
  width: 140px;
  height: 140px;
  padding: 2px 0.187px;
  justify-content: center;
  align-items: center;
  aspect-ratio: 1/1;
  border-radius: 8px;
  border: 4px solid #ad79fb;
  background: linear-gradient(0deg, #fff 0%, #fff 100%);
}
.footer_txt {
  color: var(--Neutral-gray-300, #8f9bb7);
  text-align: center;
  font-family: "PingFang SC";
  font-size: 20px;
  font-style: normal;
  font-weight: 500;
  line-height: 24px; /* 120% */
  margin-top: 16px;
}
.footer_input {
  padding: 24px 0 32rpx 0;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.footer_copyright {
  padding-top: 24px;
  color: #fff;
  text-align: center;
  font-family: "PingFang SC";
  font-size: 14px;
  font-style: normal;
  font-weight: 300;
  line-height: normal;
}
.footer_input_info {
  display: flex;
  gap: 16px;
  height: 48px;
  margin-top: 24px;
  align-items: center;
}
.footer_color_txt {
  display: flex;
  color: #fff;
  font-family: "PingFang SC";
  font-size: 20px;
  font-style: normal;
  /* font-weight: 600; */
  line-height: normal;
}
.footer_color_1 {
  text-align: center;
  font-family: "PingFang SC";
  font-size: 20px;
  font-style: normal;
  font-weight: 600;
  background: linear-gradient(
    265deg,
    #ff8077 10.2%,
    #bd6bfb 15.38%,
    #43a7ff 47.95%,
    #7ce9fe 79.94%,
    #fff 97.12%
  );
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
.footer_color_2 {
  text-align: center;
  font-family: "PingFang SC";
  font-size: 20px;
  font-style: normal;
  font-weight: 600;
  background: linear-gradient(
    265deg,
    #ff8077 10.2%,
    #bd6bfb 15.38%,
    #43a7ff 47.95%,
    #7ce9fe 79.94%,
    #fff 97.12%
  );
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
.footer_input_form {
  display: flex;
  height: 100%;
  gap: 16px;
}
.footer_input_form_item {
  position: relative;
  display: flex;
  align-items: center;
  white-space: nowrap;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.1);
  padding: 0 12px;
  color: #fff;
}
.footer_input_form_item input {
  height: 100%;
  outline: none;
  color: rgba(255, 255, 255, 0.8);
  padding: 0 8px;
}
.footer_input_form_item input::placeholder {
  color: #8f9bb7;
  font-weight: 300;
}
.footer_input_form_item:focus {
  background: rgba(255, 255, 255, 0.1);
}
.footer_input_form_item:hover {
  background: rgba(255, 255, 255, 0.1);
}
.footer_btn {
  flex-shrink: 0;
  flex-grow: 0;
  cursor: pointer;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 48px;
  width: 180px;
  border-radius: 24px;
  border: 2px solid #fff;
  color: #fff;
  font-family: "PingFang SC";
  font-size: 16px;
  font-style: normal;
  font-weight: 600;
}
.code {
  color: #3870ff;
  text-align: right;
  font-family: "PingFang SC";
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  cursor: pointer;
}
.footer_bg {
  position: absolute;
  left: 0;
  bottom: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}
