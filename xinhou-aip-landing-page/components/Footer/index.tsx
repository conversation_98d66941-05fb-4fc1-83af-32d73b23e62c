import { Form, Input, message, Spin } from "antd";
import styles from "./style.module.css";
import Agreement from "../Agreement";
import { LoadingOutlined, UserOutlined } from "@ant-design/icons";
import { useState } from "react";
import { BASE_URL } from "@/constants/config";
import { useCountDown } from "ahooks";
const Footers = () => {
  const [open, setOpen] = useState(false);
  const [checked, setChecked] = useState(false); //协议
  const [mobileValue, setMobileValue] = useState("");
  const [code, setCode] = useState("");
  const [messageApi, contextHolder] = message.useMessage(); //消息提示
  const [targetDate, setTargetDate] = useState<number>();
  const [btnLoading, setBtnLoading] = useState(true);
  const [countdown] = useCountDown({
    targetDate,
    onEnd: () => {
      setTargetDate(undefined);
    },
  });
  // 获取验证码
  const getCode = async () => {
    if (mobileValue === "") {
      messageApi.error("手机号不能为空");
      return;
    }
    const regex = /^1[3-9]\d{9}$/;
    if (!regex.test(mobileValue)) {
      messageApi.error("请输入正确的手机号");
      return;
    }
    try {
      const res = await fetch(
        `${BASE_URL}/admin/user/send-verify-code?mobile=${mobileValue}`,
        {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({}),
        }
      );
      const data = await res.json();
      if (data?.code === 200) {
        setTargetDate(Date.now() + 60 * 1000);
      } else {
        messageApi.error(data?.data?.message);
      }
    } catch (error) {
      console.error("发送验证码失败", error);
    }
  };
  /** 预约咨询 */
  const reservationEve = () => {
    if (mobileValue === "") {
      messageApi.error("手机号不能为空");
      return;
    }
    const regex = /^1[3-9]\d{9}$/;
    if (!regex.test(mobileValue)) {
      messageApi.error("请输入正确的手机号");
      return;
    }
    // if (code === "") {
    //   messageApi.error("验证码不能为空");
    //   return;
    // }
    try {
      setBtnLoading(false);
      fetch(`${BASE_URL}/admin/precontract/create`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ mobile: mobileValue }),
        // body: JSON.stringify({ mobile: mobileValue, verify_code: code }),
      })
        .then((res) => res.json())
        .then((data) => {
          if (data?.code === 200) {
            messageApi.info("预约成功，请耐心等待客服联系");
            setMobileValue("");
            setCode("");
          } else {
            messageApi.error(data?.data?.message);
          }
        })
        .finally(() => {
          setBtnLoading(true);
        });
    } catch (error) {
      setBtnLoading(true);
      messageApi.error(`预约失败${error}`);
      console.error("预约失败", error);
    }
  };
  return (
    <section className="relative w-full md:h-[444px] h-[706px] bg-[#00174d] md:px-[120px] px-[20px] md:pt-[80px] pt-[40px] pb-[32px]">
      <img src="/home/<USER>" alt="bottom" className="md:block hidden absolute left-0 bottom-0 w-full h-full pointer-events-none" />
      <img src="/home/<USER>" alt="bottom" className="md:hidden block absolute left-0 bottom-0 w-full h-full pointer-events-none" />
      {contextHolder}
      <div className="md:flex w-full justify-between items-start border-b border-white/15 pb-9">
        <div className={styles.footer_logo}>
          <img src="/home/<USER>" alt="logo" />
        </div>
        <div className="flex gap-[80px] ">
          <div className={styles.footer_links_item}>
            <span className="block text-[color:#f6f6f7] font-semibold md:text-[20px] leading-none tracking-[-0.6px]">法律与合规</span>
            <span className="block text-[#8f9bb7] font-medium md:text-[16px] leading-none mt-4"
              style={{ cursor: "pointer" }}
              onClick={() => {
                setChecked(true);
              }}
            >
              用户协议
            </span>
          </div>
          <div className={styles.footer_links_icon}>
            <div className={styles.footer_links_icon_item}>
              <img src="/home/<USER>" alt="code" />
            </div>
            <div className="mt-4 text-center md:text-[20px] text-[16px] font-medium leading-6 text-[#8f9bb7] font-pingfang">添加客服微信</div>
          </div>
        </div>
      </div>
      <div className={styles.footer_input}>
        <div className="pt-6 text-white text-center text-[14px] md:block flex flex-col justify-center items-center font-light md:leading-none leading-[20px]">
        <span>©2025 All rights reserved.</span>
        <span>杭州封神人工智能科技有限公司</span>
        </div>
        <div className="md:flex items-center gap-4 h-12 mt-6">
          <div className="flex text-white text-[20px] font-normal leading-none">
            <div className={styles.footer_color_1}>AI</div>
            <div className={styles.footer_color_txt_item}>时代，用</div>
            <div className={styles.footer_color_2}>封神AI</div>
            <div className={styles.footer_color_txt_item}>
              无限放大品牌影响力
            </div>
          </div>
          <form className="md:flex h-full gap-4">
            <div className="relative flex items-center whitespace-nowrap rounded-[12px] bg-white/10 px-3 md:py-0 py-3 text-white md:mt-0 mt-6">
              <UserOutlined style={{ color: "rgba(255,255,255,0.6)" }} />
              <input
                placeholder="请输入手机号"
                value={mobileValue}
                onChange={(e) => setMobileValue(e.target.value)}
                className="w-full outline-none bg-transparent pl-5"
              />
            </div>
            {/* <div className="relative flex items-center whitespace-nowrap rounded-[12px] bg-white/10 px-3 md:py-0 py-3 text-white md:mt-0 mt-4">
              <input
                placeholder="请输入验证码"
                style={{ width: 130 }}
                value={code}
                onChange={(e) => setCode(e.target.value)}
              />
              <span className="ml-auto text-[#3870ff] font-pingfang-sc md:text-base text-[14px] font-normal cursor-pointer" onClick={getCode}>
                {countdown > 0
                  ? `${Math.round(countdown / 1000)}s`
                  : "获取验证码"}
              </span>
            </div> */}
            <div className="md:mt-0 mt-6">
              <div
                className="flex md:text-2xl justify-center items-center md:h-[48px] h-[38] md:w-[180px] w-full md:border-2 border-1 border-white rounded-[24px] text-white font-pingfang-sc text-base font-semibold cursor-pointer flex-shrink-0 flex-grow-0"
                onClick={() => {
                  if (btnLoading) {
                    reservationEve();
                  }
                }}
              >
                {btnLoading ? (
                  "预约咨询"
                ) : (
                  <>
                    <Spin
                      indicator={<LoadingOutlined spin />}
                      style={{ color: "#fff", marginRight: 8, fontSize: 12 }}
                    />
                    <span>预约中...</span>
                  </>
                )}
              </div>
            </div>
          </form>
        </div>
      </div>
      <Agreement
        checked={checked}
        onChange={(val: boolean) => {
          setChecked(val);
        }}
      />
    </section>
  );
};
export default Footers;
