.AI_main {
  max-width: 1440px;
  width: 100%;
  overflow: hidden;
  margin-top: 196px;
}
.title {
  color: #000;
  font-family: "PingFang SC";
  font-size: 48px;
  font-style: normal;
  font-weight: 500;
  line-height: 68px; /* 141.667% */
  text-align: center;
}
.subtitle {
  color: #999;
  text-align: center;
  font-family: "PingFang SC";
  font-size: 20px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  margin-top: 12px;
}
.swiper_loop {
  position: relative;
  width: 100%;
  overflow: hidden;
  margin-top: 56px;
}
.swiper_item {
  flex-shrink: 0;
  flex-grow: 0;
  width: 320px;
  height: 575px;
  aspect-ratio: 64/115;
  border-radius: 16px;

  overflow: hidden;
  position: relative;
}
.swiper_item::after {
  position: absolute;
  content: "";
  top: 0;
  left: 0;
  z-index: 10;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    181deg,
    rgba(56, 112, 255, 0) 46.45%,
    rgba(56, 112, 255, 0.55) 99.38%
  );
}
.swiper_item img {
  width: 100%;
  height: 100%;
}
.swiper_loop {
  position: relative;
  display: flex;
  width: 100%;
}
.swiper_content {
  position: relative;
  left: 0px;
  display: flex;
  gap: 24px;
  animation: loopPlan 8s linear infinite;
}
@keyframes loopPlan {
  0% {
    left: 0;
  }
  100% {
    left: -1376px;
  }
}
.swiper_loop::after,
.swiper_loop::before {
  top: 0;
  position: absolute;
  z-index: 12;
  content: "";
  width: 180px;
  height: 600px;
  flex-shrink: 0;
}
.swiper_loop::after {
  right: 0;
  background: linear-gradient(
    to right,
    rgba(244, 245, 246, 0) 2.82%,
    rgba(244, 245, 246, 0.65) 40.99%,
    #f4f5f6 74.62%
  );
}
.swiper_loop::before {
  left: 0;
  background: linear-gradient(
    to left,
    rgba(244, 245, 246, 0) 2.82%,
    rgba(244, 245, 246, 0.65) 40.99%,
    #f4f5f6 74.62%
  );
}


@media only screen and (max-width: 767px) {
  /* 针对宽度小于或等于 767px 的设备 */
  .swiper_content {
    position: relative;
    left: 0px;
    display: flex;
    gap: 10px;
    animation: loopPlan 8s linear infinite;
  }
}
