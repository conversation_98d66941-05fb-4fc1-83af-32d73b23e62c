import styles from "./style.module.css";
// import { useTranslations } from "next-intl";

const METADATA = [
  "/case/image1.png",
  "/case/image2.png",
  "/case/image3.png",
  "/case/image4.png",
  "/case/image1.png",
  "/case/image2.png",
  "/case/image3.png",
  "/case/image4.png",
];
const CaseSection = () => {
  //   const t = useTranslations("home");
  return (
    <section className="max-w-[1440px] w-full overflow-hidden mt-[196px]" id="service">
      <div className="text-black text-center font-[PingFang SC] md:text-[48px] text-[28px] md:w-full w-[195px] mx-auto font-medium md:leading-[68px]">告别创作瓶颈 AI带你轻松日更</div>
      <div className="text-[#999999] text-center font-[PingFang SC] md:text-[20px] text-[14px] font-normal mt-[12px]">让创作更智能，日更从此不费力</div>
      <div className={styles.swiper_loop}>
        <div className={styles.swiper_content}>
          {METADATA.map((item, index) => {
            return (
              <div className="relative flex-shrink-0 flex-grow-0 md:w-[320px] w-[200px] md:h-[575px] h-[360px] aspect-[64/115] rounded-[16px] overflow-hidden after:absolute after:content-[''] after:top-0 after:left-0 after:z-[10] after:w-full after:h-full after:bg-[linear-gradient(181deg,rgba(56,112,255,0)_46.45%,rgba(56,112,255,0.55)_99.38%)]" key={index}>
                <img src={item} alt="" />
              </div>
            );
          })}
        </div>
      </div>
    </section>
  );
};

export default CaseSection;
