import styles from "./style.module.css";
const data = [
  {
    icon: "/home/<USER>",
    title: "热点研究",
    desc: "支持爬取全网海量实时热点，再也不怕灵感来源枯竭，降低追踪热点成本",
  },
  {
    icon: "/home/<USER>",
    title: "国际化支持",
    desc: "微调专属语言，模型，支持中文、英文、西班牙语，一键生成外语短视频",
  },
  {
    icon: "/home/<USER>",
    title: "自动出选题",
    desc: "根据热点、需求等一键出选题，AI帮想方向，选题创作不再困难",
  },
  {
    icon: "/home/<USER>",
    title: "热点研究",
    desc: "支持爬取全网海量实时热点，再也不怕灵感来源枯竭，降低追踪热点成本",
  },
  {
    icon: "/home/<USER>",
    title: "二次创作",
    desc: "支持粘贴文案/链接一键进行二次创作，再根据创作好的文案合成音频、视频",
  },
  {
    icon: "/home/<USER>",
    title: "智能Agent系统",
    desc: "三层Agent处理协同架构：意图识别层、内容生产层、工具服务层，创意生产质量高",
  },
];
const SelectTitle = () => {
  return (
    <section className="md:w-[1120px] w-[342px] mt-[220px] mx-auto">
      <div className="text-[#121b2a] text-center font-semibold md:text-[48px] text-[28px] leading-[120%] tracking-[-2px] font-[PingFang SC]">输入选题，一站式生成短视频</div>
      <div className="relative md:w-[1120px] w-[342px] md:h-[700px] h-[214px] flex-shrink-0 rounded-[32px] md:border-[12px] border-[6px] border-[#121b2a] flex items-end justify-center pb-4  mt-[56px] after:absolute after:z-[10] after:content-[''] after:w-[calc(100%+24px)] after:h-[80%] after:bottom-[-12px] after:left-[-12px] after:bg-[linear-gradient(to_top,#f4f5f6_0%,#f4f5f6_20%,transparent)]">
        <div className="md:w-[956.8px] w-[278.4px] md:h-[598px] h-[174px] shrink-0 aspect-[956.8/598] mx-auto rounded-[25px] border-[10px] border-[rgba(56,112,255,0.35)]">
          <img src="/home/<USER>" alt="" />
        </div>
      </div>
      <div className="flex flex-wrap md:gap-[32px] gap-[16px] mt-[56px]">
        {data.map((item, index) => {
          return (
            <div className="flex md:w-[352px] w-[163px] p-[32px] flex-col items-start gap-[16px] shrink-0 rounded-[16px] bg-[#edeff4]" key={index}>
              <div className="flex w-[48px] h-[48px] flex-col justify-center items-center rounded-[16px] border border-[#c5c6c9] bg-[#f4f6f6]">
                <div className="flex md:w-[38.4px] w-[36px] md:h-[38.4px] h-[36px] px-[7.68px] py-[5.76px] justify-center items-center gap-[3.84px] shrink-0 rounded-[12px] bg-[linear-gradient(217deg,#6e96ff_7.18%,#2c67ff_119.61%)] shadow-[0_4px_4px_0_rgba(32,42,68,0.15)]">
                  <img
                    src={item.icon}
                    alt=""
                    style={{
                      width: "100%",
                      height: "100%",
                      objectFit: "contain",
                    }}
                  />
                </div>
              </div>
              <div className="text-[#04201a] font-medium md:text-[20px] text-[16px] leading-[130%] tracking-[-0.4px] mt-[16px] font-[PingFang SC]">{item.title}</div>
              <div className="text-[#2a3735] font-normal md:text-[14px] text-[12px] leading-[154%] font-[PingFang SC]">{item.desc}</div>
            </div>
          );
        })}
      </div>
    </section>
  );
};
export default SelectTitle;
