import styles from "./styles.module.css";
const IPSelect = () => {
  return (
    <section className="py-[100px] md:flex md:flex-col md:items-center">
      <div className="text-[#04201a] text-center font-[PingFang SC] md:text-[48px] text-[24px] font-semibold leading-[120%] tracking-[-2px]">IP基于企业的意义？</div>
      <div className="text-center font-[PingFang SC] md:text-[20px] text-[14px] font-normal leading-normal mt-[10px] text-[#999999]">
        通过IP为企业代言是老板最低营销成本
      </div>
      <div className="md:flex gap-[24px] mt-[57px] md:p-0 p-[20px]">
        <div className="md:w-[424px] w-full">
          <div className="md:w-[424px] w-full md:h-[283px] aspect-[424/283] rounded-[16px] overflow-hidden">
            <img src="/home/<USER>" alt="IP_1" />
          </div>
          <div className="text-[#04201a] font-[PingFang SC] md:text-[20px] text-[] font-medium leading-[130%] tracking-[-0.4px] mt-[24px]">
            IP形象背后资本揭秘：李子柒3年全球粉丝过亿...
          </div>
          <div
            className="cursor-pointer flex items-center text-[#2a3735] font-[PingFang SC] text-[16px] font-normal leading-[152%] mt-[20px] gap-[6px] hover:opacity-[0.8]"
            onClick={() => {
              window.open("https://www.jiemian.com/article/4348462.html");
            }}
          >
            <span>阅读更多</span>
            <img src="/home/<USER>" alt="ArrowRight" />
          </div>
        </div>




        <div className="md:w-[424px] w-full md:mt-0 mt-[60px]">
          <div className="md:w-[424px] w-full md:h-[283px] aspect-[424/283] rounded-[16px] overflow-hidden">
            <img src="/home/<USER>" alt="IP_1" />
          </div>
          <div className="text-[#04201a] font-[PingFang SC] md:text-[20px] text-[] font-medium leading-[130%] tracking-[-0.4px] mt-[24px]">
            企业家如何打造个人IP？
          </div>
          <div
            className="cursor-pointer flex items-center text-[#2a3735] font-[PingFang SC] text-[16px] font-normal leading-[152%] mt-[20px] gap-[6px] hover:opacity-[0.8]"
            onClick={() => {
              window.open("https://www.sohu.com/a/781303879_327908");
            }}
          >
            <span>阅读更多</span>
            <img src="/home/<USER>" alt="ArrowRight" />
          </div>
        </div>
        {/* <div className="w-[424px]">
          <div className={styles.IPMainItemImg}>
            <img src="/home/<USER>" alt="IP_2" />
          </div>
          <div className={styles.IPMainItemTitle}>
            周鸿祎：企业家如何打造个人IP？ 
          </div>
          <div
            className={styles.IPMainItemMore}
            onClick={() => {
              window.open("https://www.sohu.com/a/781303879_327908");
            }}
          >
            <span>阅读更多</span>
            <img src="/home/<USER>" alt="ArrowRight" />
          </div>
        </div> */}
      </div>
    </section>
  );
};
export default IPSelect;
