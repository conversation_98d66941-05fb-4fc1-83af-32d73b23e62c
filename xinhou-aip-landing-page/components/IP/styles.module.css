.IPMain {
  padding: 140px 0;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.IPMainTitle {
  color: var(--Green-70, #04201a);
  text-align: center;

  /* H2 */
  font-family: "PingFang SC";
  font-size: 48px;
  font-style: normal;
  font-weight: 600;
  line-height: 120%; /* 57.6px */
  letter-spacing: -2px;
}
.IPMainSubtitle {
  color: #999;
  text-align: center;
  font-family: "PingFang SC";
  font-size: 20px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  margin-top: 10px;
}
.IPMainContent {
  display: flex;
  gap: 24px;
  margin-top: 57px;
}
.IPMainItem {
  width: 424px;
}
.IPMainItemImg {
  width: 424px;
  height: 283px;
  aspect-ratio: 424/283;
  border-radius: 16px;
  overflow: hidden;
}
.IPMainItemImg img {
  background-color: #f2f2f2;
  width: 100%;
  height: 100%;
}
.IPMainItemTitle {
  color: var(--Green-70, #04201a);

  /* H6 */
  font-family: "PingFang SC";
  font-size: 20px;
  font-style: normal;
  font-weight: 500;
  line-height: 130%; /* 26px */
  letter-spacing: -0.4px;
  margin-top: 24px;
}
.IPMainItemMore {
  cursor: pointer;
  display: flex;
  align-items: center;
  color: var(--Shades-60, #2a3735);

  /* Body 1 */
  font-family: "PingFang SC";
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: 152%; /* 24.32px */
  margin-top: 20px;
  gap: 6px;
}
.IPMainItemMore:hover {
  opacity: 0.8;
}
