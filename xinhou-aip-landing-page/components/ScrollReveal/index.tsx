import { FC, ReactNode, useEffect } from 'react';
import { motion, useAnimation } from 'framer-motion';
import { useInView } from 'react-intersection-observer';

interface IProps {
  children: ReactNode;
  duration?: number;
  delay?: number;
  withY?: boolean;
  withX?: boolean;
  className?: string;
  y?: number;
  x?: number;
  [key: string]: any;
}

const ScrollReveal: FC<IProps> = ({
  children,
  duration = 0.5,
  delay = 0.2,
  withY = true,
  withX = false,
  className = '',
  y = 50,
  x = 50,
  ...props
}) => {
  const controls = useAnimation();
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.2,
  });

  useEffect(() => {
    if (inView) {
      controls.start('visible');
    }
    return () => {
      controls.stop();
    };
  }, [controls, inView]);

  return (
    <motion.div
      {...props}
      ref={ref}
      animate={controls}
      initial="hidden"
      variants={{
        visible: {
          opacity: 1,
          y: withY ? 0 : undefined,
          x: withX ? 0 : undefined
        },
        hidden: {
          opacity: 0,
          y: withY ? y : undefined,
          x: withX ? x : undefined
        }
      }}
      transition={{ duration, delay }}
      className={className}
    >
      {children}
    </motion.div>
  );
};

export default ScrollReveal;