.customer_reviews_main {
  margin-top: 196px;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.customer_reviews_title {
  color: #000;
  font-family: "PingFang SC";
  font-size: 48px;
  font-style: normal;
  font-weight: 500;
  line-height: 68px; /* 141.667% */
}
.customer_reviews_subtitle {
  color: #999;
  text-align: center;
  font-family: "PingFang SC";
  font-size: 20px;
  font-style: normal;
  font-weight: 300;
  line-height: normal;
  margin-top: 12px;
}

.customer_reviews_box {
  display: flex;
  padding: 40px;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
  flex: 1 0 0;
  border-radius: 32px;
  background: #fff;
}
.customer_reviews_box_title {
  color: #000;
  font-family: "PingFang SC";
  font-size: 24px;
  font-style: normal;
  font-weight: 500;
  line-height: 33px; /* 137.5% */
}
.customer_reviews_box_subtitle {
  color: #999;
  font-family: "PingFang SC";
  font-size: 16px;
  font-style: normal;
  border-radius: var(--radius-lg, 16px);
  border: 1px solid var(--Shades-20, #eff0f0);
  background: var(--Shades-10, #f4f6f6);
  box-shadow: 0px 4px var(--spacing-3, 12px) 0px rgba(26, 38, 37, 0.04);
}
.customer_reviews_container {
  display: flex;
  width: 928px;
  padding: var(--spacing-9, 36px);
  align-items: flex-start;
  gap: var(--spacing-9, 36px);
  border-radius: var(--spacing-8, 32px);
  background: #eef0f4;
  height: 400px;
  margin-top: 56px;
}
.customer_reviews_box {
  display: flex;
  padding: 40px;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
  /* gap: 32px; */
  /* flex: 1 0 0; */
  border-radius: var(--radius-lg, 16px);
  border: 1px solid var(--Shades-20, #eff0f0);
  background: var(--Shades-10, #f4f6f6);
  box-shadow: 0px 4px var(--spacing-3, 12px) 0px rgba(26, 38, 37, 0.04);
}
.customer_reviews_left {
  display: flex;
  width: 265px;
  padding: var(--spacing-10, 40px);
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
  gap: var(--spacing-6, 24px);
  flex-shrink: 0;
  align-self: stretch;
  border-radius: 16px;
  background: #f4f6f6;
  box-shadow: 0px 4px var(--spacing-3, 12px) 0px rgba(26, 38, 37, 0.04);
}
.box_txt {
  color: #121b2a;
  font-family: "PingFang SC";
  font-size: 20px;
  font-style: normal;
  font-weight: 400;
  line-height: 130%; /* 26px */
  letter-spacing: -0.4px;
}
.box_xing {
  display: flex;
  height: 24px;
}
.box_txt {
  color: #121b2a;
  font-family: "PingFang SC";
  font-size: 20px;
  font-style: normal;
  font-weight: 400;
  line-height: 130%; /* 26px */
  letter-spacing: -0.4px;
  margin-top: 32px;
}
.box_info {
  display: flex;
  margin-top: 32px;
  gap: 10px;
}
.box_img {
  width: 52px;
  height: 52px;
  border-radius: 50%;
  overflow: hidden;
  background-color: #222;
}
.box_name {
  padding-top: 5px;
}
.box_name > h4 {
  color: var(--Green-70, #04201a);

  /* H6 */
  font-family: "PingFang SC";
  font-size: 20px;
  font-style: normal;
  font-weight: 500;
  line-height: 130%; /* 26px */
  letter-spacing: -0.4px;
}
.box_name > h5 {
  color: var(--Shades-50, #485151);

  /* Body 2 */
  font-family: "PingFang SC";
  font-size: 14px;
  font-style: normal;
  font-weight: 300;
  line-height: 154%; /* 21.56px */
}
.left_block {
  padding-bottom: 24px;
  width: 100%;
}
.left_block:first-child {
  border-bottom: 1px solid #e2e3e3;
  padding-top: 20px;
}
.number {
  display: flex;
  align-items: flex-start;
}
.number_text {
  color: var(--Green-70, #04201a);

  /* H2 */
  font-family: "PingFang SC";
  font-size: 48px;
  font-style: normal;
  font-weight: 600;
  line-height: 120%; /* 57.6px */
  letter-spacing: -2px;
}
.number_arrow {
  padding: 14px 0 0 4px;
}
.number_subtext {
  color: var(--Green-70, #04201a);

  /* Body 2 */
  font-family: "PingFang SC";
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 154%; /* 21.56px */
  margin-top: 4px;
}
