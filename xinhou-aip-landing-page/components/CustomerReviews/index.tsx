import styles from "./style.module.css";
const CustomerReviews = () => {
  return (
    <section className="mt-[196px]  flex flex-col items-center">
      <div className="md:text-[48px] text-[28px] md:leading-[68px]   font-medium text-black text-center">客户评价</div>
      <div className="md:text-[20px] text-[14px] text-[#999] text-center font-light mt-3">
        <span>
          AI短视频平台让客户用1/10的成本和1/10的时间，获得<br />10倍的内容产能和精
          准流量转化能力，同时规避传统<br />创作中的创意枯竭、人员波动等风险
        </span>
      </div>
      <div className="md:flex md:w-[928px] w-[342px] md:h-[400px] h-[613px] mt-[56px] p-[36px] gap-[36px] bg-[#eef0f4] md:rounded-[32px] rounded-[22px]">
        <div className="flex flex-col justify-center items-start p-[40px] bg-[#f4f6f6] rounded-[16px] border border-[#eff0f0] shadow-[0px_4px_12px_rgba(26,38,37,0.04)]">
          <div className="flex h-[24px]"></div>
          <div className="text-[#121b2a] font-[PingFang\ SC] md:text-[20px] text-[14px] not-italic font-normal leading-[130%] tracking-[-0.4px]">
            自从用了封神这个AI短视频平台，简直是打开了新世界的大门！现在团队2个人就能搞定过去10人的工作量，老板终于不用亲自上镜尬演，省下的精力可以干更多有价值的事~
          </div>
          <div className="flex gap-[10px] mt-[32px]">
            <div className="md:w-[52px] w-[48px] md:h-[52px] h-[48px] md:rounded-full rounded-[100%] overflow-hidden bg-[#222]">
              <img
                src="/home/<USER>"
                alt=""
                className="w-full h-full object-cover"
              />
            </div>
            <div className="pt-[5px]">
              <div className="md:text-[20px] text-[16px]">李总</div>
              <div className="md:text-[14px] text-[12px]">某健康食品品牌创始人</div>
            </div>
          </div>
        </div>
        <div className="md:mt-0 mt-[40px] flex w-[265px] md:p-[40px] p-[20px] flex-col justify-center items-start md:gap-[24px] gap-[20px] shrink-0 self-stretch rounded-[16px] bg-[#f4f6f6] shadow-[0_4px_12px_0_rgba(26,38,37,0.04)]">
          <div className="md:pb-[24px] pb-[20px] w-full md:pt-20px border-b border-[#e2e3e3]">
            <div className="flex ">
              <div className="text-[#04201a] font-[PingFang\ SC] md:text-[48px] text-[32px] not-italic font-semibold leading-[120%] tracking-[-2px]">87%</div>
              <div className="pt-[14px] pr-0 pb-0 pl-[4px]">
                <img src="/home/<USER>" alt=""  className="w-[22px]"/>
              </div>
            </div>
            <div className="text-[color:var(--Green-70,#04201a)] font-[PingFang\ SC] text-[14px] font-normal leading-[154%] mt-1">降低短视频创作成本</div>
          </div>


          <div className="md:pb-[24px] w-full">
            <div className="flex">
              <div className="text-[#04201a] font-[PingFang\ SC] md:text-[48px] text-[32px] not-italic font-semibold leading-[120%] tracking-[-2px]">1000%</div>
              <div className="pt-[14px] pr-0 pb-0 pl-[4px]">
                <img src="/home/<USER>" alt=""  className="w-[22px]"/>
              </div>
            </div>
            <div className="text-[color:var(--Green-70,#04201a)] font-[PingFang\ SC] text-[14px] font-normal leading-[154%] mt-1">提高短视频创作效率</div>
          </div>
          {/* <div className={styles.left_block}>
            <div className={styles.number}>
              <div className={styles.number_text}>1000%</div>
              <div className={styles.number_arrow}>
                <img src="/home/<USER>" alt="" />
              </div>
            </div>
            <div className={styles.number_subtext}>提高短视频创作效率</div>
          </div> */}
        </div>
      </div>
    </section>
  );
};
export default CustomerReviews;
