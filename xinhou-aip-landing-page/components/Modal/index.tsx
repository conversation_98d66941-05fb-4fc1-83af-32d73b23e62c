import styles from "./style.module.css";
const data = [
  {
    icon: "/home/<USER>",
    title: "一站式短视频创作",
    img: "",
    text: "国内首个使用multi-agent，25位数字员工，7*24小时随时待命，支持一站式生成选题、文案、音频、视频，让短视频创作流程极简、高效",
  },
  {
    icon: "/home/<USER>",
    title: "封神AI知识库",
    img: "",
    text: "知识库首创“RAG+Graph RAG”抽取技术应用于口播稿，采用“选题-知识点-文案”逻辑，让口播稿“说人话”且言之有物、不胡编乱造",
  },
  {
    icon: "/home/<USER>",
    title: "声音+形象模型",
    img: "",
    text: "专属微调声音+形象模型，100%还原您的声音（音高、音色等）、形象（口型），实现您的差异化短视频作品",
  },
  {
    icon: "/home/<USER>",
    title: "IP策划案",
    img: "",
    text: "自研封神AI策划专家模型。深度学习10W+全球经典影视剧本和顶级IP定位逻辑，173个字",
  },
];
const Modal = () => {
  return (
    <section className="md:pt-[190px] pt-[100px] w-full" id="product">
      <div className={styles.modal_header}>
        <div className="text-[#121b2a] text-center font-[PingFang SC] md:text-[48px] text-[28px] font-semibold leading-[120%] tracking-[-2px]">国内首个IP垂类模型</div>
        {/* text-sm */}
        <div className="text-[#999] font-[PingFang SC] md:text-[20px] text-sm font-normal leading-normal mt-[8px] mx-auto md:w-full w-[232px]">
          自研aip-8x7b-instruct-v1模型，又称IP操盘助手模型
        </div>
      </div>
      <div className="flex flex-wrap md:justify-self-start justify-center md:gap-[56px] gap:0 md:w-[928px] md:mt-[56px] mx-auto">
        {data.map((item, index) => {
          return (
            <div className="md:w-[436px] w-[342px] overflow-hidden md:mt-0 mt-[40px]" key={`${index}_`}>
              <div className="w-full md:h-[304px] h-[238px] overflow-hidden rounded-[16px] bg-[#eef0f4] pt-[36px]">
                <img
                  src={item.icon}
                  alt=""
                  style={{
                    margin: "0 auto",
                  }}
                />
              </div>
              <div className={styles.modal_block_text}>
                <span className="flex items-center text-[#04201a] font-[PingFang SC] md:text-[24px] text-base font-medium leading-[125%] tracking-[-1px] md:mt-[32px] mt-[24px]">
                  {item.title}
                </span>
                <span className="text-[#2a3735] font-[PingFang SC] text-[14px] font-normal md:leading-[154%] leading-[19%] mt-[12px] h-[44px] overflow-hidden">
                  {item.text}
                </span>
              </div>
            </div>
          );
        })}
      </div>
    </section>
  );
};
export default Modal;
